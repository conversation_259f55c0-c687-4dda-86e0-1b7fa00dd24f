# 服务异常码和端口号划分

| 服务名                       | 异常码区间段        | 端口号                 |
|---------------------------|---------------|---------------------|
| ykl-app-server            | 100000-109999 | 10009               |
| ykl-auth-service          | 110000-119999 | 10001               |
| ykl-user-service          | 130000-139999 | 10003               |
| ykl-websocket-service     | 140000-149999 | 10004               |
| ykl-push-service          | 150000-159999 | 10005               |
| ykl-patient-service       | 160000-169999 | 10006               |
| ykl-mix-service           | 170000-179999 | 10007               |
| ykl-file-service          | 180000-189999 | 10008               |
| ykl-admin-server          | 190000-199999 | 10010               |
| ykl-web-server            | 200000-209999 | 10011               |
| ykl-master-data-service   | 210000-219999 | 10022               |
| ykl-product-service       | 220000-229999 | 10012               |
| ykl-order-service         | 230000-239999 | 10013               |
| ykl-rehab-service         | 240000-249999 | 10014               |
| ykl-livekit-service       | 250000-259999 | 10015               |
| ykl-edu-service           | 260000-269999 | 10016               |
| ykl-task-server           | 270000-279999 | 10017（10018作为执行器端口） |
| ykl-records-service       | 280000-289999 | 10019               |
| ykl-payment-service       | 290000-299999 | 10020               |
| ykl-shift-server          | 300000-309999 | 10023               |
| ykl-log-service           | 310000-319999 | 10021               |
| ykl-medical-order-service | 320000-329999 | 10024               |
| ykl-symptoms-service      | 330000-339999 | 10025               |
| ykl-nutritional-service   | 340000-349999 | 10026               |
| ykl-tag-service           | 350000-359999 | 10027               |
| ykl-followup-service      | 360000-369999 | 10028               |
| ykl-pharmacy-server       | 370000-379999 | 10029               |
| ykl-doctors-service       | 380000-389999 | 10030               |
| ykl-pharmacy-service      | 390000-399999 | 10031               |
| ykl-monitor-service       | 400000-409999 | 10032               |
| ykl-mini-service          | 410000-419999 | 10033               |

# 项目打包命令
clean
deploy
-Dmaven.test.skip=true
-Dmaven.repo.url=http://***************:8081 （这里替换成相应nexus）

# jenkins目录
${service}-service/${service}-biz/target/${service}-biz.jar
${service}-service/${service}-biz/target

远程目录
/app/java/${service}-biz/target
cd /app/java