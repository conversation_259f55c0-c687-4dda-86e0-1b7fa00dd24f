package com.ykl.med.application.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.ykl.med.application.controller.AppController;
import com.ykl.med.application.entity.HttpRequestData;
import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.application.entity.model.Drug;
import com.ykl.med.application.mapper.ASqlExecService;
import com.ykl.med.application.repository.MapperRepository;
import com.ykl.med.application.repository.ServiceRepository;
import com.ykl.med.config.SimpleException;
import com.ykl.med.enums.ResponseCode;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.pharmacy.entity.dto.*;
import com.ykl.med.pharmacy.entity.vo.*;
import com.ykl.med.util.PublicUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DrugService implements IService {
    @Override
    public String serviceName() {
        return "drug";
    }

    /**
     * 单表增删改查
     * */
    public ResponseModel crud(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, NoSuchFieldException, ParseException {
        /**
         * 加工值对象: 对象 + 传入对象 ( 第一个参数表示要转换的值对象结构 )
         * */
        MapperRepository mapperRepository = new MapperRepository();
        HttpRequestData httpRequestData = mapperRepository.inputParamsMap(new Drug(), requestObj); // 获取值对象
        ResponseModel responseModel = mapperRepository.batchCheckValueObjectField(httpRequestData); // 判断必填参数
        if ( responseModel.getCode() != 0 ){
            return responseModel;
        }

        // 查询特殊处理
        if ( StringUtils.isNotBlank(httpRequestData.getMethod()) && httpRequestData.getMethod().toLowerCase().equals("get") ){
            return this.query(requestObj);
        }

        /**
         * 获取SQL执行服务, 并执行值对象
         * */
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        return aSqlExecService.sqlExec(httpRequestData.getValueObjectList().get(0),httpRequestData.getPageRecord());
    }

    /**
     * 查询药品数据
     * */
    public ResponseModel query(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, NoSuchFieldException, ParseException {

        // 获取服务仓库句柄
        ServiceRepository serviceRepository = SpringContentUtils.getBean(ServiceRepository.class);
        // 创建值对象处理句柄
        MapperRepository mapperRepository = new MapperRepository();
        // 获取传入对象数据
        HttpRequestData requestData = mapperRepository.getInputParamMap(requestObj);

        // 获取SQL执行服务句柄
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        // 传入数据转对象
        DrugQueryDTO drugQueryDTO = mapperRepository.getBizObject(requestData, new TypeReference<DrugQueryDTO>() {});

        if (StringUtils.isNotBlank(drugQueryDTO.getName())){
            String wordStr = drugQueryDTO.getName();
            drugQueryDTO.setName(wordStr+",SORT:ASC:CHAR_LENGTH#"+wordStr+",linkOR:name#goods_name");
            drugQueryDTO.setGoodsName(wordStr+",SORT:ASC:CHAR_LENGTH#"+wordStr);
        }

        // 构造值对象
        HttpRequestData httpRequestData = mapperRepository.createValueObject(new Drug(),"get", drugQueryDTO);
        if ( httpRequestData.getResponseModel() != null ){
            return httpRequestData.getResponseModel();
        }
        // 执行SQL
        ResponseModel responseModel1 = aSqlExecService.sqlExec(httpRequestData.getValueObjectList().get(0), requestData.getPageRecord());
        if ( responseModel1.getCode() != 0 ){
            return responseModel1;
        }

        PageResult<DrugVO> drugVOPageResult = PublicUtil.parseObject(responseModel1.getData(), new TypeReference<PageResult<DrugVO>>() {});
        if ( drugVOPageResult.getList() == null || drugVOPageResult.getList().size() == 0 ){
            return responseModel1;
        }

        /**
         * 获取靶点数据
         * */
        // 靶点ID列表
        List<String> targetSpotIdList = new ArrayList<>();
        // 药品说明书为null的药品名称列表
        List<String> drugNameList = new ArrayList<>();
        // 厂家ID列表
        List<String> producerIdList = new ArrayList<>();
        for ( DrugVO drugVO : drugVOPageResult.getList() ){

            if ( drugVO.getTargetSpot() != null && drugVO.getTargetSpot().size() != 0 ){
                targetSpotIdList.addAll(drugVO.getTargetSpot());
            }

            if ( this.isNullDrugManual(drugVO.getProductManual()) && StringUtils.isNotBlank(drugVO.getName())  ) {
                drugNameList.add(drugVO.getName());
            }

            if ( StringUtils.isNotBlank(drugVO.getProducerId()) ){
                producerIdList.add(drugVO.getProducerId());
            }
        }

        // 去重: 靶点
        targetSpotIdList = targetSpotIdList.stream().distinct().collect(Collectors.toList());

        // 去重: 药品名称
        drugNameList = drugNameList.stream().distinct().collect(Collectors.toList());

        // 去重: 厂家ID
        producerIdList = producerIdList.stream().distinct().collect(Collectors.toList());

        // 厂商ID信息map
        Map<String, ProducerVO> producerQueryDTOMap = new HashMap<>();
        if ( producerIdList.size() != 0 ){
            ProducerQueryDTO producerQueryDTO = new ProducerQueryDTO();
            producerQueryDTO.setId(String.join(",", producerIdList));
            PageResult<ProducerVO> producerVOPageResult =
                    new AppController().serviceMethodCall(serviceRepository, "producer", "crud",
                            "get", producerQueryDTO, new TypeReference<PageResult<ProducerVO>>() {});
            if ( producerVOPageResult.getList() != null && producerVOPageResult.getList().size() != 0 ){
                producerQueryDTOMap = producerVOPageResult.getList().stream().collect(Collectors.toMap(
                        ProducerVO::getId, Function.identity(), (key1, key2) -> key2));
            }
        }

        // 靶点ID map
        Map<String, TargetSpotVO> targetSpotVOMap = new HashMap<>();
        if ( targetSpotIdList.size() != 0 ) {
            TargetSpotQueryDTO targetSpotQueryDTO = new TargetSpotQueryDTO();
            targetSpotQueryDTO.setId(String.join(",", targetSpotIdList));
            PageResult<TargetSpotVO> targetSpotVOPageResult =
                    new AppController().serviceMethodCall(serviceRepository, "targetSpot", "crud",
                            "GET", targetSpotQueryDTO, new TypeReference<PageResult<TargetSpotVO>>() {});
            if (targetSpotVOPageResult.getList() != null && targetSpotVOPageResult.getList().size() != 0) {
                targetSpotVOMap = targetSpotVOPageResult.getList().stream().collect(Collectors.toMap(
                        TargetSpotVO::getId, Function.identity(), (key1, key2) -> key2));
            }
        }

        /**
         * 获取药品名称的药品说明书
         * */
        // 药品说明书ID map
        Map<String, DrugManualVO> drugManualVOMap = new HashMap<>();
        if ( drugNameList.size() != 0 ) {
            DrugManualQueryDTO drugManualQueryDTO = new DrugManualQueryDTO();
            drugManualQueryDTO.setName("fullMatch,"+String.join(",", drugNameList));
            drugManualQueryDTO.setStatus("ENABLE");
            PageResult<DrugManualVO> drugManualVOPageResult =
                    new AppController().serviceMethodCall(serviceRepository, "drugManual", "crud",
                            "GET", drugManualQueryDTO, new TypeReference<PageResult<DrugManualVO>>() {});
            if (drugManualVOPageResult.getList() != null && drugManualVOPageResult.getList().size() != 0) {
                drugManualVOMap = drugManualVOPageResult.getList().stream().collect(Collectors.toMap(
                        DrugManualVO::getName, Function.identity(), (key1, key2) -> key2));
            }
        }

        // 循环赋值: 靶点
        for ( DrugVO drugVO : drugVOPageResult.getList() ){

            // 说明书为空, 且有公共说明书
            if ( this.isNullDrugManual(drugVO.getProductManual())
                    && StringUtils.isNotBlank(drugVO.getName())
                    && drugManualVOMap.containsKey(drugVO.getName()) ){
                drugVO.setProductManual( PublicUtil.parseObject(drugManualVOMap.get(drugVO.getName()), new TypeReference<DrugManualBaseDTO>() {}) );
            }

            // 赋值: 厂家名称
            if ( StringUtils.isNotBlank(drugVO.getProducerId()) && producerQueryDTOMap.containsKey(drugVO.getProducerId()) ){
                drugVO.setProducerName(producerQueryDTOMap.get(drugVO.getProducerId()).getName());
                drugVO.setProducerValue(producerQueryDTOMap.get(drugVO.getProducerId()).getValue());
            }

            // 赋值: 靶点
            if ( drugVO.getTargetSpot() != null && drugVO.getTargetSpot().size() != 0 ) {
                drugVO.setTargetSpotInfos(new ArrayList<>());
                Map<String, List<VariationVO>> variationListMap = new HashMap<>();
                for (String targetSpotId : drugVO.getTargetSpot()) {
                    if ( !targetSpotVOMap.containsKey(targetSpotId) ){
                        continue;
                    }

                    TargetSpotVO targetSpotVO = targetSpotVOMap.get(targetSpotId);

                    if ( !variationListMap.containsKey(targetSpotVO.getGeneName()) ){
                        variationListMap.put(targetSpotVO.getGeneName(),new ArrayList<>());
                    }

                    VariationVO variationVO = new VariationVO();
                    variationVO.setId(targetSpotVO.getId());
                    variationVO.setGeneName(targetSpotVO.getGeneName());
                    variationVO.setTargetType(targetSpotVO.getTargetType());
                    variationVO.setVariationType(targetSpotVO.getVariationType());
                    variationListMap.get(targetSpotVO.getGeneName()).add(variationVO);
                }

                for ( Map.Entry<String, List<VariationVO>> variations : variationListMap.entrySet() ){
                    GeneVO geneVO = new GeneVO();
                    geneVO.setGeneName(variations.getKey());
                    geneVO.setVariations(variations.getValue());

                    drugVO.getTargetSpotInfos().add(geneVO);
                }
            }
        }

        ResponseModel responseModel2 = new ResponseModel();
        responseModel2.setData(drugVOPageResult);

        return responseModel2;
    }

    public ResponseModel queryManual(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, NoSuchFieldException, ParseException {

        // 获取服务仓库句柄
        ServiceRepository serviceRepository = SpringContentUtils.getBean(ServiceRepository.class);
        // 创建值对象处理句柄
        MapperRepository mapperRepository = new MapperRepository();
        // 获取传入对象数据
        HttpRequestData requestData = mapperRepository.getInputParamMap(requestObj);

        // 获取SQL执行服务句柄
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        // 获取传入对象
        DrugManualQueryDTO drugManualQueryDTO = mapperRepository.getBizObject(requestData, new TypeReference<DrugManualQueryDTO>() {});

        // 初始化返回结果
        PageResult<DrugManualVO> drugManualVOPageResult = new PageResult<>();
        drugManualVOPageResult.setTotal(0L);
        drugManualVOPageResult.setList(new ArrayList<>());

        if ( StringUtils.isBlank(drugManualQueryDTO.getDrugId()) ){
            return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, "drugId:"+ResponseCode.INVALID_ARGUMENT.getMessage());
        }

        DrugQueryDTO drugQueryDTO = new DrugQueryDTO();
        drugQueryDTO.setId(drugManualQueryDTO.getDrugId());
        // 构造值对象
        HttpRequestData httpRequestData = mapperRepository.createValueObject(new Drug(), "get", drugQueryDTO);
        if ( httpRequestData.getResponseModel() != null ){
            return httpRequestData.getResponseModel();
        }
        // 执行SQL
        ResponseModel responseModel = aSqlExecService.sqlExec(httpRequestData.getValueObjectList().get(0), requestData.getPageRecord());
        if ( responseModel.getCode() != 0 ){
            return responseModel;
        }

        // 转换结果
        PageResult<DrugVO> drugVOPageResult = PublicUtil.parseObject(responseModel.getData(), new TypeReference<PageResult<DrugVO>>() {});

        // 药品信息
        DrugVO drugVO = drugVOPageResult.getList().get(0);
        if ( !this.isNullDrugManual(drugVO.getProductManual()) ){ // 不为空: 直接使用自己的数据
            DrugManualVO drugManualVO = PublicUtil.parseObject(drugVO.getProductManual(), new TypeReference<DrugManualVO>() {});
            drugManualVO.setName(drugVO.getName());
            drugManualVO.setApprovalDocumentNo(drugVO.getApprovalDocumentNo());
            drugManualVO.setNameEn(drugVO.getNameEn());
            drugManualVO.setNamePy(drugVO.getNameShort());
            drugManualVOPageResult.getList().add(drugManualVO);
            drugManualVOPageResult.setTotal(1L);

            ResponseModel responseModel1 = new ResponseModel();
            responseModel1.setData(drugManualVOPageResult);
            return responseModel1;
        }

        /**
         * 根据药品名称获取"通用药品说明书"
         * */
        if ( StringUtils.isBlank(drugVO.getName()) ){
            return new ResponseModel();
        }

        DrugManualQueryDTO drugManualQueryDTO1 = new DrugManualQueryDTO();
        drugManualQueryDTO1.setName("fullMatch,"+drugVO.getName());
        drugManualQueryDTO1.setStatus("ENABLE");
        PageResult<DrugManualVO> drugManualVOPageResult1 = new AppController().serviceMethodCall(serviceRepository, "drugManual", "crud",
                "GET", drugManualQueryDTO1, new TypeReference<PageResult<DrugManualVO>>() {});

        ResponseModel responseModel1 = new ResponseModel();
        responseModel1.setData(drugManualVOPageResult1);

        return responseModel1;
    }

    public ResponseModel syncManual(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, NoSuchFieldException, ParseException {

        // 获取服务仓库句柄
        //ServiceRepository serviceRepository = SpringContentUtils.getBean(ServiceRepository.class);
        // 创建值对象处理句柄
        MapperRepository mapperRepository = new MapperRepository();
        // 获取传入对象数据
        HttpRequestData requestData = mapperRepository.getInputParamMap(requestObj);

        // 获取SQL执行服务句柄
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        // 获取传入对象
        DrugManualSyncDTO drugManualQueryDTO = mapperRepository.getBizObject(requestData, new TypeReference<DrugManualSyncDTO>() {});

        if ( drugManualQueryDTO.getNameList() == null || drugManualQueryDTO.getNameList().size() == 0 ){
            return new ResponseModel();
        }

        // 名字去重去Null
        drugManualQueryDTO.setNameList(drugManualQueryDTO.getNameList().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));

        // 没有数据
        if ( drugManualQueryDTO.getNameList().size() == 0 ){
            return new ResponseModel();
        }

        /**
         * 获取名字对应的药品ID列表
         * */
        DrugQueryDTO drugQueryDTO = new DrugQueryDTO();
        drugQueryDTO.setName("fullMatch,"+String.join(",", drugManualQueryDTO.getNameList()));

        // 构造值对象
        HttpRequestData httpRequestData = mapperRepository.createValueObject(new Drug(), "get", drugQueryDTO);
        if ( httpRequestData.getResponseModel() != null ){
            return httpRequestData.getResponseModel();
        }
        // 执行SQL
        ResponseModel responseModel = aSqlExecService.sqlExec(httpRequestData.getValueObjectList().get(0),httpRequestData.getPageRecord());
        if ( responseModel.getCode() != 0 ){
            return responseModel;
        }

        PageResult<DrugVO> drugVOPageResult = PublicUtil.parseObject(responseModel.getData(), new TypeReference<PageResult<DrugVO>>() {});
        if ( drugVOPageResult.getList() == null || drugVOPageResult.getList().size() == 0 ){
            return responseModel;
        }

        // 药品ID列表
        List<String> drugIdList = drugVOPageResult.getList().stream().map(DrugVO::getId).
                filter(Objects::nonNull).distinct().collect(Collectors.toList());

        /**
         * 将对应药品名称的私有药品说明书清空(null)
         * */
        DrugAddDTO drugAddDTO = new DrugAddDTO();
        drugAddDTO.setId(String.join(",", drugIdList));
        drugAddDTO.setProductManual(new DrugManualBaseDTO());
        // 构造值对象
        HttpRequestData httpRequestData1 = mapperRepository.createValueObject(new Drug(), "put", drugAddDTO);
        if ( httpRequestData1.getResponseModel() != null ){
            return httpRequestData1.getResponseModel();
        }
        // 执行SQL
        return aSqlExecService.sqlExec(httpRequestData1.getValueObjectList().get(0),requestData.getPageRecord());
    }

    public boolean isNullDrugManual(DrugManualBaseDTO drugManualBaseDTO){
        if ( drugManualBaseDTO == null ){
            return true;
        }

        // 所有的多为空
        if ( StringUtils.isBlank(drugManualBaseDTO.getSpecifications())
                && StringUtils.isBlank(drugManualBaseDTO.getCharacter())
                && StringUtils.isBlank(drugManualBaseDTO.getComponent())
                && StringUtils.isBlank(drugManualBaseDTO.getIndication())
                && StringUtils.isBlank(drugManualBaseDTO.getUsageAndDosage())
                && StringUtils.isBlank(drugManualBaseDTO.getAdverseReactions())
                && StringUtils.isBlank(drugManualBaseDTO.getNote())
                && StringUtils.isBlank(drugManualBaseDTO.getInteraction())
                && StringUtils.isBlank(drugManualBaseDTO.getPharmacology())
                && StringUtils.isBlank(drugManualBaseDTO.getPharmacokinetics())
                && StringUtils.isBlank(drugManualBaseDTO.getGeriatric())
                && StringUtils.isBlank(drugManualBaseDTO.getChildren())
                && StringUtils.isBlank(drugManualBaseDTO.getPregnant())
                && StringUtils.isBlank(drugManualBaseDTO.getOverdose())
                && StringUtils.isBlank(drugManualBaseDTO.getTaboo())
                && StringUtils.isBlank(drugManualBaseDTO.getStorage())
                && StringUtils.isBlank(drugManualBaseDTO.getDrugPackage())
                && StringUtils.isBlank(drugManualBaseDTO.getPeriod())
        ){
            return true;
        }

        return false;
    }
}
