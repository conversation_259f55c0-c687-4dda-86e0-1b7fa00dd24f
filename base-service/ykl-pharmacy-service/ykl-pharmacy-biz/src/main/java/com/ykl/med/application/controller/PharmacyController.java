package com.ykl.med.application.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ykl.med.application.repository.ServiceRepository;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.pharmacy.api.PharmacyFeign;
import com.ykl.med.pharmacy.entity.dto.PharmacyAddDTO;
import com.ykl.med.pharmacy.entity.dto.PharmacyBatchAlterDTO;
import com.ykl.med.pharmacy.entity.dto.PharmacyDeleteDTO;
import com.ykl.med.pharmacy.entity.dto.PharmacyQueryDTO;
import com.ykl.med.pharmacy.entity.vo.PharmacyVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "药房接口")
@RestController
@RequestMapping("/pharmacy")
@Validated
public class PharmacyController implements PharmacyFeign {
    /**
     * 服务仓库对象
     * */
    @Autowired
    ServiceRepository serviceRepository;

    @Override
    @PostMapping("/query")
    public PageResult<PharmacyVO> query(@RequestBody PharmacyQueryDTO requestBody) {
        if ( requestBody.getIds() != null && requestBody.getIds().size() != 0 ){
            requestBody.setId(String.join(",",requestBody.getIds()));
        }
        return new AppController().serviceMethodCall(serviceRepository, "pharmacy", "crud",
                "get", requestBody, new TypeReference<PageResult<PharmacyVO>>() {});
    }

    @Override
    @PostMapping("/querySingle")
    public PharmacyVO querySingle(@RequestBody Long pharmacyId) {
        PharmacyQueryDTO pharmacyQueryDTO = new PharmacyQueryDTO();
        pharmacyQueryDTO.setId(String.valueOf(pharmacyId));
        PageResult<PharmacyVO> pharmacyVOPageResult = new AppController().serviceMethodCall(serviceRepository, "pharmacy", "crud",
                "get", pharmacyQueryDTO, new TypeReference<PageResult<PharmacyVO>>() {});
        if ( pharmacyVOPageResult.getList() != null && pharmacyVOPageResult.getList().size() != 0 ){
            return pharmacyVOPageResult.getList().get(0);
        }

        return null;
    }

    @Override
    @PostMapping("/add")
    public void add(@RequestBody PharmacyAddDTO requestBody) {
        new AppController().serviceMethodCall(serviceRepository, "pharmacy","crud",
                "post", requestBody, null);
    }

    @Override
    @PostMapping("/update")
    public void update(@RequestBody PharmacyAddDTO requestBody) {
        new AppController().serviceMethodCall(serviceRepository, "pharmacy","crud",
                "PUT",requestBody, null);
    }

    @Override
    @PostMapping("/delete")
    public void delete(@RequestBody PharmacyDeleteDTO requestBody) {
        if ( requestBody.getIds() != null && requestBody.getIds().size() != 0 ){
            requestBody.setId(String.join(",",requestBody.getIds()));
        }
        new AppController().serviceMethodCall(serviceRepository,"pharmacy","crud",
                "delete",requestBody,null);
    }

    @Override
    @PostMapping("/batchAlter")
    public void batchAlter(PharmacyBatchAlterDTO requestBody) {
        if ( requestBody.getIds() != null && requestBody.getIds().size() != 0 ){
            requestBody.setId(String.join(",",requestBody.getIds()));
        }
        new AppController().serviceMethodCall(serviceRepository, "pharmacy", "crud",
                "put", requestBody, null);
    }
}
