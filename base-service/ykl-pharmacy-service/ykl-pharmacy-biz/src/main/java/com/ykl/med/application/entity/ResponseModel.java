package com.ykl.med.application.entity;

import com.ykl.med.enums.ResponseCode;
import lombok.Getter;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/23 19:36
 */
@Getter
public class ResponseModel implements Serializable {
    private static final long serialVersionUID = -93848171763036634L;
    private Integer code = ResponseCode.SUCCESS.getCode();
    private String msg = "";
    private Object data = null;

    public ResponseModel response(ResponseCode code, String message) {
        this.code = code.getCode();
        this.msg = message;
        return this;
    }

    public ResponseModel response(ResponseCode code) {
        this.code = code.getCode();
        this.msg = code.getMessage();
        return this;
    }

    public void responseCode(Integer code) {
        this.code = code;
    }

    public void failResponse(String message) {
        this.code = ResponseCode.FAIL.getCode();
        this.msg = message;
    }

    public ResponseModel failParamsNotNull(String paramName){
        this.code = ResponseCode.FAIL.getCode();
        this.msg = paramName+"不能为空";
        return this;
    }

    public void putData(String key, Object value) {
        if ( data instanceof LinkedHashMap){
            ((LinkedHashMap) data).put(key, value);
        } else {
            Map<String, Object> dataMap = new LinkedHashMap<>();
            dataMap.put(key, value);
            data = dataMap;
        }
    }

    public void setData(Object object) {
        this.data = object;
    }

    public static ResponseModel createModel() {
        return new ResponseModel();
    }
}
