package com.ykl.med.application.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.ykl.med.application.entity.HttpRequestData;
import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.application.entity.model.TargetSpot;
import com.ykl.med.application.mapper.ASqlExecService;
import com.ykl.med.application.repository.MapperRepository;
import com.ykl.med.config.SimpleException;
import com.ykl.med.enums.ResponseCode;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.pharmacy.entity.dto.TargetSpotQueryDTO;
import com.ykl.med.pharmacy.entity.vo.GeneVO;
import com.ykl.med.pharmacy.entity.vo.TargetSpotVO;
import com.ykl.med.pharmacy.entity.vo.VariationVO;
import com.ykl.med.util.PublicUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

public class TargetSpotService implements IService {
    @Override
    public String serviceName() {
        return "targetSpot";
    }

    /**
     * 单表增删改查
     * */
    public ResponseModel crud(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, NoSuchFieldException, ParseException {
        /**
         * 加工值对象: 对象 + 传入对象 ( 第一个参数表示要转换的值对象结构 )
         * */
        MapperRepository mapperRepository = new MapperRepository();
        HttpRequestData httpRequestData = mapperRepository.inputParamsMap(new TargetSpot(), requestObj); // 获取值对象
        ResponseModel responseModel = mapperRepository.batchCheckValueObjectField(httpRequestData); // 判断必填参数
        if ( responseModel.getCode() != 0 ){
            return responseModel;
        }

        /**
         * 获取SQL执行服务, 并执行值对象
         * */
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        return aSqlExecService.sqlExec(httpRequestData.getValueObjectList().get(0),httpRequestData.getPageRecord());
    }

    /**
     * 查询基因靶点数据
     * */
    public ResponseModel queryGeneVariation(HashMap requestObj)
            throws IllegalAccessException, InstantiationException, SimpleException, NoSuchFieldException, ParseException {

        // 获取服务仓库句柄
        //ServiceRepository serviceRepository = SpringContentUtils.getBean(ServiceRepository.class);
        // 创建值对象处理句柄
        MapperRepository mapperRepository = new MapperRepository();
        // 获取传入对象数据
        HttpRequestData requestData = mapperRepository.getInputParamMap(requestObj);

        // 获取SQL执行服务句柄
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        /**
         * 处理业务逻辑
         * 1. 写入数据：将多个写操作的值对象加入到同一个writeRequestData->valueObjectList中,
         *       最后执行：aSqlExecService.batchSqlExec(writeRequestData), 保证事务一致性
         * 2. 读取数据：定义个HttpRequestData对象, 加工值对象：mapperRepository.inputParamsMap(Object SObj, HashMap requestObj)
         *             SObj：数据对象；requestObj：http请求对象；
         *      判断加工好的值对象必填字段方法：mapperRepository.batchCheckValueObjectField(httpRequestData)
         * */

        /**
         * 初始化写入操作值对象列表的执行数据对象（如果没有数据要写入,可以注释掉以下代码）
         * */
        //HttpRequestData writeRequestData = new HttpRequestData();
        //writeRequestData.setValueObjectList(new ArrayList<>());

        TargetSpotQueryDTO targetSpotQueryDTO = mapperRepository.getBizObject(requestData, new TypeReference<TargetSpotQueryDTO>() {});
        if ( StringUtils.isBlank(targetSpotQueryDTO.getGeneName()) ){
            targetSpotQueryDTO.setGeneName("GROUP:QUERY");
        } else {
            targetSpotQueryDTO.setGeneName(targetSpotQueryDTO.getGeneName()+",GROUP:QUERY");
        }

        // 构造值对象
        HttpRequestData httpRequestData = mapperRepository.createValueObject(new TargetSpot(),"get", targetSpotQueryDTO);
        if ( httpRequestData.getResponseModel() != null ){
            return httpRequestData.getResponseModel();
        }
        // 执行sql语句
        ResponseModel responseModel1 = aSqlExecService.sqlExec(httpRequestData.getValueObjectList().get(0),requestData.getPageRecord());
        // 执行错误:直接返回
        if ( responseModel1.getCode() != 0 || responseModel1.getData() == null ){
            return responseModel1;
        }

        // 获取数据信息
        PageResult<TargetSpotVO> targetSpotVOPageResult = PublicUtil.parseObject(responseModel1.getData(), new TypeReference<PageResult<TargetSpotVO>>() {});

        // 基因名称列表
        List<String> geneNameList = targetSpotVOPageResult.getList().stream().map(TargetSpotVO::getGeneName).
                filter(Objects::nonNull).distinct().collect(Collectors.toList());

        /**
         * 获取基因的突变数据
         * */
        // 基因突变ID map
        Map<String, List<TargetSpotVO>> targetSpotVOMap = new HashMap<>();
        TargetSpotQueryDTO targetSpotQueryDTO1 = new TargetSpotQueryDTO();
        targetSpotQueryDTO1.setPageNo(1);
        targetSpotQueryDTO1.setPageSize(9999);
        targetSpotQueryDTO1.setGeneName(String.join(",", geneNameList));
        // 构造值对象
        HttpRequestData httpRequestData1 = mapperRepository.createValueObject(new TargetSpot(),"get", targetSpotQueryDTO1);
        if ( httpRequestData1.getResponseModel() != null ){
            return httpRequestData1.getResponseModel();
        }
        // 执行SQL
        ResponseModel responseModel3 = aSqlExecService.sqlExec(httpRequestData1.getValueObjectList().get(0),requestData.getPageRecord());
        PageResult<TargetSpotVO> targetSpotVOPageResult1 = PublicUtil.parseObject(responseModel3.getData(), new TypeReference<PageResult<TargetSpotVO>>() {});
        if ( targetSpotVOPageResult1.getList() != null && targetSpotVOPageResult1.getList().size() != 0 ){
            targetSpotVOMap = targetSpotVOPageResult1.getList().stream().collect(Collectors.groupingBy(TargetSpotVO::getGeneName));
        }

        PageResult<GeneVO> geneVOPageResult = new PageResult<>();
        geneVOPageResult.setTotal(0L);
        geneVOPageResult.setList(new ArrayList<>());

        for ( TargetSpotVO targetSpotVO : targetSpotVOPageResult.getList() ){
            GeneVO geneVO = new GeneVO();
            geneVO.setGeneName(targetSpotVO.getGeneName());
            if ( targetSpotVOMap.containsKey(targetSpotVO.getGeneName()) ){
                geneVO.setVariations(PublicUtil.parseObject(targetSpotVOMap.get(targetSpotVO.getGeneName()),
                        new TypeReference<List<VariationVO>>() {}));
            }
            geneVOPageResult.getList().add(geneVO);
        }
        geneVOPageResult.setTotal(targetSpotVOPageResult.getTotal());

        ResponseModel responseModel4 = new ResponseModel();
        responseModel4.setData(geneVOPageResult);

        return responseModel4;
    }
}
