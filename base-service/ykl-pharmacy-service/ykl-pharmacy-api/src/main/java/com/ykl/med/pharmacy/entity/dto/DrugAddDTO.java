package com.ykl.med.pharmacy.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 药品表;
 * <AUTHOR> xkli
 * @date : 2023-11-30
 */
@Data
@Schema(description = "药品表")
public class DrugAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "本位码（全国唯一）", example = "")
    private String standardCode ;

    @Schema(description = "条形码", example = "")
    private String barCode ;

    @Schema(description = "药品名称（通用名）", example = "")
    private String name ;

    @Schema(description = "药品英文名称", example = "")
    private String nameEn ;

    @Schema(description = "药品名称简拼码", example = "")
    private String nameShort ;

    @Schema(description = "药品化学名称", example = "")
    private String chemicalName ;

    @Schema(description = "药品化学名称简拼码", example = "")
    private String chemicalNameShort ;

    @Schema(description = "商品名称", example = "")
    private String goodsName ;

    @Schema(description = "商品名称简拼码", example = "")
    private String goodsNameShort ;

    @Schema(description = "药品类别（西药/中药）【字典】", example = "")
    private String drugClass ;

    @Schema(description = "药品类型（json：字典值列表）", example = "")
    private List<String> drugType ;

    @Schema(description = "靶点(json:ID列表)", example = "")
    private List<String> targetSpot ;

    @Schema(description = "抗菌药物等级【字典】", example = "")
    private String antimicrobialDrugClass;

    @Schema(description = "药品剂型（片剂/胶囊）【字典】", example = "")
    private String dosageForm ;

    @Schema(description = "药品功效【字典】", example = "")
    private String efficacy ;

    @Schema(description = "包装规格（20mg*5袋/盒）", example = "")
    private String packageSpec ;

    @Schema(description = "包装规格单位（可售卖最小单位）【字典】", example = "")
    private String packageSpecUnit ;

    @Schema(description = "基本剂量（如：20）", example = "")
    private String dosage ;

    @Schema(description = "基本剂量单位（如：mg）【字典】", example = "")
    private String dosageUnit ;

    @Schema(description = "基本制剂数量（包装剂量，如：5）", example = "")
    private String preparation ;

    @Schema(description = "基本制剂单位（最小包装单位，如：袋）【字典】", example = "")
    private String preparationUnit ;

    @Schema(description = "医嘱开立单位（一般为：基本制剂单位，如：袋）", example = "")
    private String medicalOrderUnit ;

    @Schema(description = "采购价格（单位：分）", example = "")
    private String purchasePrice ;

    @Schema(description = "零售价格（单位：分）", example = "")
    private String retailPrice ;

    @Schema(description = "产地（一般录入国家）【字典】", example = "")
    private String country ;

    @Schema(description = "生产厂商ID", example = "")
    private String producerId ;

    @Schema(description = "生产厂商字典值", example = "")
    private String producerValue ;

    @Schema(description = "供应商ID【实体】", example = "")
    private String supplierId ;

    @Schema(description = "批准文号", example = "")
    private String approvalDocumentNo ;

    @Schema(description = "生产地址", example = "")
    private String productionAddress ;

    @Schema(description = "医保国家编码", example = "")
    private String medicalInsuranceCode ;

    @Schema(description = "药品说明书ID", example = "")
    private String manualId ;

    @Schema(description = "药品说明书", example = "")
    private DrugManualBaseDTO productManual ;

    @Schema(description = "默认用法名称（口服/注射）（给药途径）", example = "")
    private String usageName ;

    @Schema(description = "默认单次剂量（单次用量）", example = "")
    private String onceDosage ;

    @Schema(description = "默认单次剂量单位", example = "")
    private String onceDosageUnit ;

    @Schema(description = "默认使用频次ID", example = "")
    private String frequencyId ;

    @Schema(description = "默认使用频次名称", example = "")
    private String frequencyName ;

    @Schema(description = "默认使用周期（天）", example = "")
    private String usageCycleDays ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

    @Schema(description = "处方标志; 0非处方药, 1处方药", example = "")
    private String prescriptionFlag ;

    @Schema(description = "隐藏标志；0未隐藏，1已隐藏", example = "")
    private String hideFlag ;

}