package com.ykl.med.pharmacy.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2024-7-4
 */
@Data
@Schema(description = "药品说明书")
public class DrugManualVO {

    @Schema(description = "",example = "")
    private String id ;

    @Schema(description = "批准文号",example = "")
    private String approvalDocumentNo ;

    @Schema(description = "通用名称",example = "")
    private String name ;

    @Schema(description = "汉语拼音",example = "")
    private String namePy ;

    @Schema(description = "英文名称",example = "")
    private String nameEn ;

    @Schema(description = "规格",example = "")
    private String specifications ;

    @Schema(description = "性状",example = "")
    private String character ;

    @Schema(description = "成份",example = "")
    private String component ;

    @Schema(description = "适应症",example = "")
    private String indication ;

    @Schema(description = "用法用量",example = "")
    private String usageAndDosage ;

    @Schema(description = "不良反应",example = "")
    private String adverseReactions ;

    @Schema(description = "注意事项",example = "")
    private String note ;

    @Schema(description = "药物相互作用",example = "")
    private String interaction ;

    @Schema(description = "药理毒理",example = "")
    private String pharmacology ;

    @Schema(description = "药代动力学",example = "")
    private String pharmacokinetics ;

    @Schema(description = "老年用药",example = "")
    private String geriatric ;

    @Schema(description = "儿童用药",example = "")
    private String children ;

    @Schema(description = "孕妇及哺乳期妇女用药",example = "")
    private String pregnant ;

    @Schema(description = "药物过量",example = "")
    private String overdose ;

    @Schema(description = "禁忌",example = "")
    private String taboo ;

    @Schema(description = "贮藏",example = "")
    private String storage ;

    @Schema(description = "包装",example = "")
    private String drugPackage ;

    @Schema(description = "有效期",example = "")
    private String period ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

}
