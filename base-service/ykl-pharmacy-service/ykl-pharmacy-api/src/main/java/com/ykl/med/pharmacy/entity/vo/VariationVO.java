package com.ykl.med.pharmacy.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2024-7-5
 */
@Data
@Schema(description = "变异数据对象")
public class VariationVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "名称（字典:基因,蛋白）",example = "")
    private String geneName ;

    @Schema(description = "类型（字典:基因,蛋白）",example = "")
    private String targetType ;

    @Schema(description = "变异类型（文本）",example = "")
    private String variationType ;
}
