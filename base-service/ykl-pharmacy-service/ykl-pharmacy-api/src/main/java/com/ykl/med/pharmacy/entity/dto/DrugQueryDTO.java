package com.ykl.med.pharmacy.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DrugQueryDTO extends CommonDoctorIdVO {

    private Integer pageSize;
    private Integer pageNo;

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "本位码（全国唯一）", example = "")
    private String standardCode ;

    @Schema(description = "条形码", example = "")
    private String barCode ;

    @Schema(description = "药品名称（通用名）", example = "")
    private String name ;

    @Schema(description = "药品名称简拼码", example = "")
    private String nameShort ;

    @Schema(description = "药品化学名称", example = "")
    private String chemicalName ;

    @Schema(description = "商品名称", example = "")
    private String goodsName ;

    @Schema(description = "药品类别（西药/中药）【字典】", example = "")
    private String drugClass ;

    @Schema(description = "药品类型（json：字典值列表）", example = "")
    private List<String> drugType ;

    @Schema(description = "靶点(json:ID列表)", example = "")
    private List<String> targetSpot ;

    @Schema(description = "抗菌药物等级【字典】", example = "")
    private String antimicrobialDrugClass;

    @Schema(description = "默认用法名称（口服/注射）（给药途径）", example = "")
    private String usageName ;

    @Schema(description = "默认使用频次名称", example = "")
    private String frequencyName ;

    @Schema(description = "厂家ID", example = "")
    private String producerId ;

    @Schema(description = "创建时间", example = "")
    private String createTime ;

    @Schema(description = "更新时间", example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

    @Schema(description = "隐藏标志；0未隐藏，1已隐藏", example = "")
    private String hideFlag ;

    /**
     * 其他字段
     * */
    @Schema(description = "id列表", example = "")
    private List<String> ids;
}
