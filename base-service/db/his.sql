-- 医生
alter table `ykl_doctors`.`t_doctor_info`    add his_id  bigint(20)  	DEFAULT NULL  COMMENT 'hisId';
-- 科室
alter table `ykl_doctors`.`t_section`   add his_id   bigint(20) 	DEFAULT NULL  COMMENT 'hisId';
update `ykl_doctors`.`t_section` set his_id='1873553887490662401' where name like '%妇产科%';
update `ykl_doctors`.`t_section` set his_id='1873543726717919234' where name like '%信息部门%';
update `ykl_doctors`.`t_section` set his_id='1873553337202171906' where name like '%临床部门%';
update `ykl_doctors`.`t_section` set his_id='1873553400292892673' where name like '%内科%';
update `ykl_doctors`.`t_section` set his_id='1873553827793133569' where name like '%外科%';
update `ykl_doctors`.`t_section` set his_id='1873553887490662401' where name like '%妇产科%';
update `ykl_doctors`.`t_section` set his_id='1873553934341038082' where name like '%中西医结合科%';
update `ykl_doctors`.`t_section` set his_id='1873554015777644546' where name like '%全科医学%';
update `ykl_doctors`.`t_section` set his_id='1873554117170749442' where name like '%药械部门%';
update `ykl_doctors`.`t_section` set his_id='1873554143087353857' where name like '%药房%';
update `ykl_doctors`.`t_section` set his_id='1873554204303220738' where name like '%财务部门%';
update `ykl_doctors`.`t_section` set his_id='1873554233575268353' where name like '%财务室%';
update `ykl_doctors`.`t_section` set his_id='1877623269191749633' where name like '%医技部门%';
update `ykl_doctors`.`t_section` set his_id='1877623372291936257' where name like '%检验科%';


-- 频次
alter table `ykl_master_data`.`t_frequency`  add his_id   bigint(20) 	DEFAULT NULL  COMMENT 'hisId';

-- 医嘱项目 （药品/诊疗）
alter table `ykl_order`.`t_sku`  add his_id     bigint(20) 	DEFAULT NULL  COMMENT 'hisId';
alter table `ykl_order`.`t_sku`  add his_goods_id   bigint(20) 	DEFAULT NULL  COMMENT 'his药品id';

-- 问诊
alter table `ykl_shift`.`t_consult`  add his_visit_id     bigint(20) 	DEFAULT NULL  COMMENT '就诊id-his';
alter table `ykl_shift`.`t_consult`  add his_visit_no     varchar(100) 	DEFAULT NULL  COMMENT '就诊号-his';
alter table `ykl_shift`.`t_consult`  add his_patient_id   bigint(20) 	DEFAULT NULL  COMMENT '就诊患者id-his';

-- 医嘱项目
alter table `ykl_medical_order`.`t_medical_advice_item`  add his_orditem_id      bigint(20) 	    DEFAULT NULL  COMMENT 'his医嘱项目Id(冗余字段，来源sku表)';
alter table `ykl_medical_order`.`t_medical_advice_item`  add his_goods_id        bigint(20) 	    DEFAULT NULL  COMMENT 'his药品id(冗余字段，来源sku表)';
alter table `ykl_medical_order`.`t_medical_advice_item`  add his_order_id        bigint(20) 	    DEFAULT NULL  COMMENT '医嘱记录ID（创建医嘱his返回的医嘱记录id）';



/* 字典值-his系统 */
drop table if exists `ykl_master_data`.`t_dict_his`;
CREATE TABLE `ykl_master_data`.`t_dict_his`(
    id 			            bigint(20)	 	NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT 'id',
    type                    varchar(100)    NULL NULL    COMMENT '类型,性别、用法、计量单位、婚姻状况',
    dict_key                varchar(50)     NULL NULL    COMMENT '字典值',
    his_id                  varchar(50)     NULL NULL    COMMENT 'hisId',
    his_name 	            varchar(50)	 	NULL NULL 	 COMMENT 'his名称'
)ENGINE=InnoDB CHARSET=utf8mb4 COMMENT='-字典值-his系统';

-- 性别
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('GENDER', 'SEX_male', '1', '男性');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('GENDER', 'SEX_female', '2', '女性');

-- 婚姻状况
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('MARRY', 'MARITAL_STATUSES_SINGLE', '10', '未婚');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('MARRY', 'MARITAL_STATUSES_MARRIED', '20', '已婚');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('MARRY', 'MARITAL_STATUSES_WIDOWED', '30', '丧偶');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('MARRY', 'MARITAL_STATUSES_DIVORCED', '40', '离婚');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('MARRY', 'MARITAL_STATUSES_OTHER', '90', '其他');

-- 用法
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_ORAL', '1664475145814646786', '口服');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_INTRAVENOUS_INJECTION', '1858439982042091521', '静脉注射（血透室）');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_INTRAVENOUS_INFUSION', '1846373220693876738', '静脉滴注');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_SUBCUTANEOUS_INJECTION', '1664475344310083586', '皮下注射');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_NASAL_DROP', '1731592449698140162', '滴鼻');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_ENEMA', '1701520272357629953', '灌肠');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_RECTAL_SUPPOSITORY', '1732759045216976898', '肛塞');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_NASAL_FEEDING', '1690191686791081986', '鼻饲');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_ARTERIAL_25', '1732755187585564674', '漱口');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_INTRACARDIAC_INJECTION', '1732759447454924801', '心内注射');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_INTRADERMAL_INJECTION', '1664475772401721346', '皮内注射');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_GASTRIC_FEEDING', '1878616960639340546', '管饲');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_BONE_MARROW_INJECTION', '1878617302718386177', '骨髓注射');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_TRANSDERMAL_ADMINISTRATION', '1878617384876412930', '透皮给药');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_SHEATH_INJECTION', '1878617485741035521', '鞘内注射');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_ARTERIAL_INJECTION', '1878617607010947073', '动脉注射');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_EAR_DROP', '1878617714817142786', '滴耳');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_OPHTHALMIC_ADMINISTRATION', '1878627072959483905', '眼部给药');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_NASAL_ADMINISTRATION', '1878627140630384642', '鼻腔给药');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_INTRAMUSCULAR_INJECTION', '1878627217834938370', '肌肉注射');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_RECTAL_ADMINISTRATION', '1878627283601625089', '直肠给药');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_TOPICAL_ADMINISTRATION', '1878627356234387457', '皮肤给药');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_SUBLINGUAL_ADMINISTRATION', '1878627414950449153', '舌下给药');
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_INHALATION_ADMINISTRATION', '', '吸入给药'); -- his缺失
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('USAGE', 'ADMINISTRATION_ROUTES_MUCOSAL_ADMINISTRATION', '', '黏膜给药'); -- his缺失

-- 剂量单位
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'DOSAGE_UNITS1', '2254754144138410048', 'mg');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'DOSAGE_UNITS2', '1596225299505169915', 'ml');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'DOSAGE_UNITS3', '427747395545183100', 'g');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'DOSAGE_UNITS4', '8157434542194052179', 'ug');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'DOSAGE_UNITS5', 'xfhxb2', 'U');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'DOSAGE_UNITS6', '313380151206524881', 'IU');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'DOSAGE_UNITS7', '8608945623111647641', '万IU');

INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_SHEET', '4009448091837221891', '张');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_MATTER', '7465411635760642491', '件');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_FILM', '5383730270512234558', '膜');
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_SET2', '', '联'); --his缺失
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_BUBBLE', '6100258991234567', '泡');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_TABLET2', '2559459162592724323', '片');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_CAN', '8993443111948711524', '罐');
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_CUP', '', '杯');--his缺失
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_BOWL', '', '碗');--his缺失
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_TUBE2', '276678454029299865', '管');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_GRANULE', '7415177349274223835', '粒');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_TABLET1', '6722627923809356252', '丸');
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_MIDDLE_BAG', '', '中袋');--his缺失
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_PLATE', '3301563929353668089', '板');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_SET1', '2050883912087128276', '套');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_CYLINDER', '4852998426508374616', '筒');
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_PENG', '', '盆');--his缺失
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_LARGE_BAG', '', '大袋');--his缺失
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_JAR', '', '听');--his缺失
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_CASE', '4943389657678592230', '箱');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_BUCKET', '8050493152642185667', '桶');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_PACK', '8914409725418424177', '包');
# INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_MIDDLE_BOX', '', '中盒');--his缺失
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_PIECE', '2079442136607179316', '枚');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_BAG', '1294070393119226019', '袋');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_TUBE1', '105070843695251154', '支');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_BOX', '6247600535673851134', '盒');
INSERT INTO `ykl_master_data`.`t_dict_his`(`type`, `dict_key`, `his_id`, `his_name`) VALUES ('UNIT', 'MINIMUM_PACKAGING_UNITS_BOTTLE', '1727275618161046821', '瓶');





