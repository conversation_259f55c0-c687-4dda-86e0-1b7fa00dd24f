package com.ykl.med.user.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.mybatis.core.type.EncryptTypeHandler;
import com.ykl.med.user.entity.UserDO;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.mapper.UserMapper;
import com.ykl.med.user.vo.UserAuthorityVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户缓存服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "user")
public class UserCacheServiceImpl implements UserCacheService {

    private final UserMapper userMapper;

    @Override
    @Cacheable(key = "#username + ':' + #userType", unless = "#result == null")
    public UserAuthorityVO getByUsernameAndUserType(String username, UserType userType) {
        UserDO userDO = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getUsername, username)
                .eq(UserDO::getUserType, userType)
        );
        if (Objects.isNull(userDO)) {
            return null;
        }
        return toUserAuthorityVO(userDO);
    }

    @Override
    @Cacheable(key = "#mobile + ':' + #userType", unless = "#result == null")
    public UserAuthorityVO getByMobileAndUserType(String mobile, UserType userType) {
        UserDO userDO = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getMobile, EncryptTypeHandler.encrypt(mobile))
                .eq(UserDO::getUserType, userType));
        if (Objects.isNull(userDO)) {
            return null;
        }
        return toUserAuthorityVO(userDO);
    }

    @Override
    @Cacheable(key = "#id", unless = "#result == null")
    public UserAuthorityVO getById(Long id) {
        UserDO userDO = userMapper.selectById(id);
        if (Objects.isNull(userDO)) {
            throw new UsernameNotFoundException(id + "账号不存在");
        }
        return toUserAuthorityVO(userDO);
    }

    private UserAuthorityVO toUserAuthorityVO(UserDO userDO) {
        UserAuthorityVO userAuthorityVO = CopyPropertiesUtil.copy(userDO, UserAuthorityVO::new);

        Set<String> permissions = userMapper.selectPermissionsByUserId(userDO.getId()).stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        userAuthorityVO.setAuthorities(permissions);

        // 删除了获取医疗组权限列表代码:medicalTeamIds

        return userAuthorityVO;
    }

}
