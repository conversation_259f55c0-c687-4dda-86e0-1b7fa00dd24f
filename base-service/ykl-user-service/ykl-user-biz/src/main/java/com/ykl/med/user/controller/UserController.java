package com.ykl.med.user.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.entity.UserDO;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.service.UserCacheService;
import com.ykl.med.user.service.UserService;
import com.ykl.med.user.vo.UserAuthorityVO;
import com.ykl.med.user.vo.req.*;
import com.ykl.med.user.vo.resp.LoginOrRegisterRespVO;
import com.ykl.med.user.vo.resp.UserInfoRespVO;
import com.ykl.med.user.vo.resp.UserRolesRespVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 用户控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
@Validated
public class UserController implements UserFeign {

    private final UserService userService;

    private final UserCacheService userCacheService;

    private static final String SINGLE_LOGIN_REDIS_KEY_PREFIX = "singleLogin:";

    private final StringRedisTemplate redisTemplate;

    @PostMapping("/getByUsernameAndUserType")
    public UserAuthorityVO getByUsernameAndUserType(@RequestParam("username") String username,
                                                    @RequestParam("userType") UserType userType) {
        return userCacheService.getByUsernameAndUserType(username, userType);
    }

    @PostMapping("/getByMobileAndUserType")
    public UserAuthorityVO getByMobileAndUserType(@RequestParam("mobile") String mobile,
                                                  @RequestParam("userType") UserType userType) {
        return userCacheService.getByMobileAndUserType(mobile, userType);
    }

    @PostMapping("/getById")
    public UserAuthorityVO getById(@RequestParam("id") Long id) {
        return userCacheService.getById(id);
    }

    @PostMapping("/loginOrRegister")
    public LoginOrRegisterRespVO loginOrRegister(@RequestBody LoginOrRegisterReqVO reqVO) {
        LoginOrRegisterRespVO respVO = userService.loginOrRegister(reqVO);
        Optional.ofNullable(respVO)
                .map(LoginOrRegisterRespVO::getUserInfo)
                .map(UserInfoRespVO::getId)
                .ifPresent(id -> {
                    String userKey = SINGLE_LOGIN_REDIS_KEY_PREFIX + id + ":" + reqVO.getClientId();
                    long expire = respVO.getExpiresIn();
                    redisTemplate.opsForValue().set(userKey, respVO.getAccessToken(), expire, TimeUnit.SECONDS);
                    //这俩货互相挤下线
                    if ("ykl-mini-server".equals(reqVO.getClientId())) {
                        redisTemplate.opsForValue().set(SINGLE_LOGIN_REDIS_KEY_PREFIX + id + ":" + "ykl-web-server", respVO.getAccessToken(), expire, TimeUnit.SECONDS);
                    } else if ("ykl-web-server".equals(reqVO.getClientId())) {
                        redisTemplate.opsForValue().set(SINGLE_LOGIN_REDIS_KEY_PREFIX + id + ":" + "ykl-mini-server", respVO.getAccessToken(), expire, TimeUnit.SECONDS);
                    }
                });

        return respVO;
    }

    @Override
    @PostMapping("/refreshToken")
    public LoginOrRegisterRespVO refreshToken(@Valid @RequestBody RefreshTokenReqVO reqVO) {
        LoginOrRegisterRespVO respVO = userService.refreshToken(reqVO);
        Optional.ofNullable(respVO)
                .map(LoginOrRegisterRespVO::getUserInfo)
                .map(UserInfoRespVO::getId)
                .ifPresent(id -> {
                    String userKey = SINGLE_LOGIN_REDIS_KEY_PREFIX + id + ":" + reqVO.getClientId();
                    long expire = respVO.getExpiresIn();
                    redisTemplate.opsForValue().set(userKey, respVO.getAccessToken(), expire, TimeUnit.SECONDS);
                    //这俩货互相挤下线
                    if ("ykl-mini-server".equals(reqVO.getClientId())) {
                        redisTemplate.opsForValue().set(SINGLE_LOGIN_REDIS_KEY_PREFIX + id + ":" + "ykl-web-server", respVO.getAccessToken(), expire, TimeUnit.SECONDS);
                    } else if ("ykl-web-server".equals(reqVO.getClientId())) {
                        redisTemplate.opsForValue().set(SINGLE_LOGIN_REDIS_KEY_PREFIX + id + ":" + "ykl-mini-server", respVO.getAccessToken(), expire, TimeUnit.SECONDS);
                    }
                });

        return respVO;
    }

    @PostMapping("/updateBaseInfo")
    public void update(@Valid @RequestBody UserBaseInfoUpdateReqVO reqVO) {
        UserDO userDO = userService.updateBaseInfo(reqVO);
        log.info("用户基本信息更新成功，用户id：{}，用户姓名：{}", userDO.getId(), userDO.getName());
    }

    @Override
    @PostMapping("/updatePassword")
    public void updatePassword(@Valid @RequestBody UserPasswordUpdateReqVO reqVO) {
        UserDO userDO = userService.updatePassword(reqVO);
        log.info("用户密码更新成功，用户id：{}，用户姓名：{}", userDO.getId(), userDO.getName());
    }

    @Override
    @PostMapping("/updateMyPassword")
    public void updateMyPassword(@Valid @RequestBody MyPasswordUpdateReqVO reqVO) {
        UserDO userDO = userService.updateMyPassword(reqVO);
        log.info("用户密码更新成功，用户id：{}，用户姓名：{}", userDO.getId(), userDO.getName());
    }

    @Override
    @PostMapping("/ignorePassword")
    public void ignorePassword(@Valid @RequestBody UserPasswordUpdateReqVO reqVO) {
        UserDO userDO = userService.ignorePassword(reqVO);
        log.info("用户密码更新成功，用户id：{}，用户姓名：{}", userDO.getId(), userDO.getName());
    }

    @PostMapping("/patientRegister")
    public UserSimpleVO patientRegister(@RequestBody LoginOrRegisterReqVO reqVO) {
        return userService.patientRegister(reqVO);
    }

    @Override
    @GetMapping("/getByUserId")
    public UserSimpleVO getByUserId(@RequestParam("userId") Long userId) {
        return userService.getByUserId(userId);
    }

    @PostMapping("/listByUserIds")
    public List<UserSimpleVO> listByUserIds(@RequestBody IdListReqVO reqVO) {
        return userService.listByUserIds(reqVO);
    }

    @PostMapping("/createUser")
    public void createUser(@Valid @RequestBody UserCreateReqVO reqVO) {
        userService.createUser(reqVO);
    }

    @Override
    @PostMapping("/page")
    public PageResult<UserSimpleVO> page(@RequestBody SimpleUserPageReqVO reqVO) {
        return userService.page(reqVO);
    }

    @Override
    @PostMapping("/pageWithoutAuthority")
    public PageResult<UserSimpleVO> pageWithoutAuthority(@RequestBody SimpleUserPageReqVO reqVO) {
        return userService.pageWithoutAuthority(reqVO);
    }

    @Override
    @PostMapping("/getUserRoles")
    public UserRolesRespVO getUserRoles(@RequestBody UserRolesReqVO reqVO) {
        return userService.getUserRoles(reqVO);
    }

    @Override
    @PostMapping("/updateStatus")
    public void updateStatus(@Valid @RequestBody UserStatusUpdateReqVO reqVO) {
        UserDO userDO = userService.updateStatus(reqVO);
        log.info("用户状态更新成功，用户id：{}，用户状态：{}", userDO.getId(), userDO.getStatus());
    }

}
