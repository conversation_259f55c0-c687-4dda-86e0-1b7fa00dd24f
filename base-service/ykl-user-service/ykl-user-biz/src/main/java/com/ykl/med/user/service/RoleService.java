package com.ykl.med.user.service;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.user.vo.req.*;
import com.ykl.med.user.vo.resp.RoleListRespVO;

import java.util.List;

/**
 * 角色服务接口
 *
 * <AUTHOR>
 */
public interface RoleService {

    /**
     * 创建角色
     *
     * @param reqVO 创建角色的请求信息，包含可能的创建信息，如名称、描述等
     */
    void saveOrUpdateRole(SaveOrUpdateRoleReqVO reqVO);

    /**
     * 删除角色
     *
     * @param reqVO 删除角色的请求信息，包含角色id
     */
    void deleteRole(DeleteRoleReqVO reqVO);

    /**
     * 分页查询角色列表
     *
     * @param reqVO 分页查询角色列表的请求信息
     * @return 角色列表分页结果
     */
    PageResult<RoleListRespVO> pageRoles(RolePageReqVO reqVO);

    /**
     * 获取角色列表
     *
     * @param reqVO 获取角色列表的请求信息
     * @return 角色列表
     */
    List<RoleListRespVO> listRoles(ListRolesReqVO reqVO);

    /**
     * 给某个用户分配角色
     *
     * @param reqVO 分配角色的请求信息，包含用户id和角色id列表
     */
    void bindRoles(BindRolesReqVO reqVO);

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleListRespVO> listByUserId(Long userId);
}