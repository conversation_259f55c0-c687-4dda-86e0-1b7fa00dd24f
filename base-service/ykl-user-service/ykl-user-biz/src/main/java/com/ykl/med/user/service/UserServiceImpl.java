package com.ykl.med.user.service;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.auth.api.AuthFeign;
import com.ykl.med.auth.vo.RegisterReqVO;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.AuthorityUtils;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.validation.ValidationUtils;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.user.constants.UserErrorCode;
import com.ykl.med.user.entity.RoleDO;
import com.ykl.med.user.entity.UserDO;
import com.ykl.med.user.entity.UserRoleDO;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.mapper.RoleMapper;
import com.ykl.med.user.mapper.UserMapper;
import com.ykl.med.user.mapper.UserRoleMapper;
import com.ykl.med.user.vo.UserAuthorityVO;
import com.ykl.med.user.vo.req.*;
import com.ykl.med.user.vo.resp.LoginOrRegisterRespVO;
import com.ykl.med.user.vo.resp.UserInfoRespVO;
import com.ykl.med.user.vo.resp.UserRolesRespVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 用户服务接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, UserDO> implements UserService {

    private final UserMapper userMapper;

    private final AuthFeign authFeign;

    private final RoleMapper roleMapper;

    private final IdServiceImpl idService;

    private final UserRoleMapper userRoleMapper;

    private final UserCacheService userCacheService;

    private final BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();

    private static final DateTimeFormatter FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = DateTimeFormatter
            .ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public LoginOrRegisterRespVO loginOrRegister(LoginOrRegisterReqVO reqVO) {
        boolean smsLogin = isSmsLogin(reqVO);
        UserAuthorityVO userAuthorityVO = findUser(reqVO);
        if (userAuthorityVO == null) {
            if (smsLogin) {
                registerUser(reqVO);
                userAuthorityVO = findUser(reqVO);
            } else {
                throw new ServiceException(UserErrorCode.USERNAME_OR_PASSWORD_ERROR);
            }
        } else {
            updateUserLastLogin(userAuthorityVO.getId(), reqVO.getIp());
        }

        OAuth2AccessToken token = authenticateUser(reqVO, userAuthorityVO);
        return from(token);
    }

    private UserAuthorityVO findUser(LoginOrRegisterReqVO reqVO) {
        if (StringUtils.isNotBlank(reqVO.getUsername())) {
            return userCacheService.getByUsernameAndUserType(reqVO.getUsername(), reqVO.getUserType());
        } else {
            return userCacheService.getByMobileAndUserType(reqVO.getMobile(), reqVO.getUserType());
        }
    }

    private boolean isSmsLogin(LoginOrRegisterReqVO reqVO) {
        return StringUtils.isNotBlank(reqVO.getMobile()) && StringUtils.isNotBlank(reqVO.getCode());
    }

    private void registerUser(LoginOrRegisterReqVO reqVO) {
        Boolean registered = authFeign.register(new RegisterReqVO()
                .setUserType(reqVO.getUserType().name())
                .setIp(reqVO.getIp())
                .setMobile(reqVO.getMobile()));
        if (!registered) {
            throw new ServiceException(UserErrorCode.REGISTER_ERROR);
        }
    }

    private OAuth2AccessToken authenticateUser(LoginOrRegisterReqVO reqVO, UserAuthorityVO userAuthorityVO) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("client_id", reqVO.getClientId());
        parameters.put("client_secret", reqVO.getClientSecret());

        if (isSmsLogin(reqVO)) {
            // For SMS-based authentication
            parameters.put("grant_type", "sms");
            parameters.put("mobile", reqVO.getMobile());
            parameters.put("code", reqVO.getCode());
            parameters.put("user_type", reqVO.getUserType().name());
        } else {
            // For password-based authentication
            parameters.put("grant_type", "password");
            parameters.put("username", userAuthorityVO.getUsername());
            parameters.put("password", reqVO.getPassword());
        }

        // Call the authentication service to get the token
        OAuth2AccessToken token = authFeign.getToken(parameters);
        if (token == null) {
            throw new ServiceException(UserErrorCode.AUTHENTICATION_FAILED);
        }
        return token;
    }


    @Override
    public LoginOrRegisterRespVO refreshToken(RefreshTokenReqVO reqVO) {
        UserDO userDO = userMapper.selectById(reqVO.getUserId());
        if (userDO == null) {
            throw new ServiceException(UserErrorCode.USER_NOT_EXIST);
        }

        Map<String, String> parameters = new HashMap<>();
        parameters.put("client_id", reqVO.getClientId());
        parameters.put("client_secret", reqVO.getClientSecret());
        parameters.put("grant_type", "sms");
        parameters.put("mobile", userDO.getMobile());
        parameters.put("user_type", reqVO.getUserType().name());
        OAuth2AccessToken token = authFeign.getToken(parameters);
        if (token == null) {
            throw new ServiceException(UserErrorCode.AUTHENTICATION_FAILED);
        }
        return from(token);
    }

    private void updateUserLastLogin(Long userId, String ip) {
        super.lambdaUpdate()
                .set(UserDO::getLastLoginTime, LocalDateTime.now())
                .set(UserDO::getLastLoginIp, ip)
                .eq(UserDO::getId, userId)
                .update();
    }

    @SuppressWarnings("unchecked")
    public static Optional<Map<String, Object>> safeCastToMap(Object obj) {
        return obj instanceof Map ? Optional.of((Map<String, Object>) obj) : Optional.empty();
    }

    public static Optional<Map<String, Object>> getUserInfo(Map<String, Object> additionalInfo) {
        return Optional.ofNullable(additionalInfo)
                .map(info -> info.get("user_info"))
                .flatMap(UserServiceImpl::safeCastToMap);
    }

    private static LoginOrRegisterRespVO from(OAuth2AccessToken oAuth2AccessToken) {
        // 安全地从oAuth2AccessToken获取"user_info"，使用Optional避免潜在的ClassCastException
        Optional<Map<String, Object>> userInfoOptional = getUserInfo(oAuth2AccessToken.getAdditionalInformation());

        // 利用Optional构建UserInfoRespVO对象
        UserInfoRespVO userInfoRespVO = userInfoOptional.map(userInfo -> {
            String userId = Optional.ofNullable(userInfo.get("id")).map(Object::toString).orElse("defaultId");
            String userMobile = Optional.ofNullable(userInfo.get("mobile")).map(Object::toString)
                    .orElse("defaultMobile");
            return new UserInfoRespVO()
                    .setId(userId)
                    .setMobile(userMobile);
        }).orElse(new UserInfoRespVO()); // 如果userInfo不存在，可以返回一个默认的UserInfoRespVO对象

        // 构建LoginOrRegisterRespVO对象并返回
        return new LoginOrRegisterRespVO()
                .setAccessToken(oAuth2AccessToken.getValue())
                .setTokenType(oAuth2AccessToken.getTokenType())
                .setRefreshToken(Optional.ofNullable(oAuth2AccessToken.getRefreshToken())
                        .map(OAuth2RefreshToken::getValue).orElse(null))
                .setExpiresIn((long) oAuth2AccessToken.getExpiresIn())
                .setScope(String.join(" ", oAuth2AccessToken.getScope()))
                .setJti(Optional.ofNullable(oAuth2AccessToken.getAdditionalInformation().get("jti"))
                        .map(Object::toString).orElse(null))
                .setUserInfo(userInfoRespVO);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(value = "user", key = "#result.id"),
            @CacheEvict(value = "user", key = "#result.username + ':' + #result.userType"),
            @CacheEvict(value = "user", key = "#result.mobile+ ':' + #result.userType")
    })
    public UserDO updateBaseInfo(UserBaseInfoUpdateReqVO reqVO) {
        UserDO oldUserDO = userMapper.selectById(reqVO.getId());
        if (oldUserDO == null) {
            throw new ServiceException(UserErrorCode.USER_NOT_EXIST);
        }

        // 为null时,使用旧数据
        if (StringUtils.isBlank(reqVO.getMedicalCategory())) {
            reqVO.setMedicalCategory(oldUserDO.getMedicalCategory());
        }
        if (StringUtils.isBlank(reqVO.getRemark())) {
            reqVO.setRemark(oldUserDO.getRemark());
        }
        if (StringUtils.isBlank(reqVO.getMobile())) {
            reqVO.setMobile(oldUserDO.getMobile());
        }
        if (StringUtils.isBlank(reqVO.getEmail())) {
            reqVO.setEmail(oldUserDO.getEmail());
        }
        if (StringUtils.isBlank(reqVO.getAvatar())) {
            reqVO.setAvatar(oldUserDO.getAvatar());
        }

        CopyPropertiesUtil.copyAndConvert(reqVO, () -> oldUserDO, null);
        userMapper.updateById(oldUserDO);
        return oldUserDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "user", key = "#result.id"),
            @CacheEvict(value = "user", key = "#result.username + ':' + #result.userType"),
            @CacheEvict(value = "user", key = "#result.mobile+ ':' + #result.userType")
    })
    public UserDO updatePassword(UserPasswordUpdateReqVO reqVO) {
        log.info("Updating user password, reqVO: {}", JSON.toJSONString(reqVO));

        // Retrieve the user by ID.
        UserDO userDO = userMapper.selectById(reqVO.getId());
        AssertUtils.notNull(userDO, UserErrorCode.USER_NOT_EXIST);

        if (!ValidationUtils.isPassword(reqVO.getPassword())) {
            throw new ServiceException(UserErrorCode.INVALID_PASSWORD);
        }
        // Encode the new password and update the user record.
        userDO.setPassword(bCryptPasswordEncoder.encode(reqVO.getPassword()));
        userMapper.updateById(userDO);
        return userDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "user", key = "#result.id"),
            @CacheEvict(value = "user", key = "#result.username + ':' + #result.userType"),
            @CacheEvict(value = "user", key = "#result.mobile+ ':' + #result.userType")
    })
    public UserDO ignorePassword(UserPasswordUpdateReqVO reqVO) {
        log.info("Ignoring password, reqVO: {}", JSON.toJSONString(reqVO));

        // Retrieve the user by mobile and user type.
        UserDO userDO = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getMobile, reqVO.getMobile())
                .eq(UserDO::getUserType, UserType.PATIENT));
        if (userDO == null) {
            throw new ServiceException(UserErrorCode.USER_NOT_EXIST);
        }

        if (!ValidationUtils.isPassword(reqVO.getPassword())) {
            throw new ServiceException(UserErrorCode.INVALID_PASSWORD);
        }
        // Encode the new password and update the user record.
        userDO.setPassword(bCryptPasswordEncoder.encode(reqVO.getPassword()));
        userMapper.updateById(userDO);
        return userDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUser(UserCreateReqVO reqVO) {
        log.info("创建用户, reqVO: {}", JSON.toJSONString(reqVO));
        boolean usernameDuplicate = userMapper.selectCount(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getUsername, reqVO.getUsername())) > 0;
        if (usernameDuplicate) {
            throw new ServiceException(UserErrorCode.USERNAME_ALREADY_EXISTS);
        }

        boolean mobileAndUserTypeDuplicate = userMapper.selectCount(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getMobile, reqVO.getMobile())
                .eq(UserDO::getUserType, UserType.DOCTOR)) > 0;
        if (mobileAndUserTypeDuplicate) {
            throw new ServiceException(UserErrorCode.MOBILE_ALREADY_EXISTS);
        }

        if (!ValidationUtils.isPassword(reqVO.getPassword())) {
            throw new ServiceException(UserErrorCode.INVALID_PASSWORD);
        }
        UserDO userDO = new UserDO();
        CopyPropertiesUtil.copyAndConvert(reqVO, () -> userDO, (source, target) -> {
            target.setUserType(UserType.DOCTOR);
            target.setStatus(CommonStatusEnum.ENABLE);
            target.setId(idService.nextId());
            target.setPassword(bCryptPasswordEncoder.encode(reqVO.getPassword()));
        });
        userMapper.insert(userDO);
        // 如果角色id不为空，则绑定角色
        if (!CollectionUtils.isEmpty(reqVO.getRoleIds())) {
            userRoleMapper.insertBatch(reqVO.getRoleIds().stream().map(roleId -> {
                UserRoleDO userRoleDO = new UserRoleDO();
                userRoleDO.setUserId(userDO.getId());
                userRoleDO.setRoleId(Long.valueOf(roleId));
                return userRoleDO;
            }).collect(Collectors.toList()));
        }
    }

    public UserSimpleVO patientRegister(LoginOrRegisterReqVO reqVO) {
        UserDO userDO = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getMobile, reqVO.getMobile())
                .eq(UserDO::getUserType, reqVO.getUserType()));
        if (userDO != null) {
            throw new ServiceException(UserErrorCode.USER_ALREADY_EXISTS);
        }
        if (StringUtils.isBlank(reqVO.getPassword())) {
            //设置一个默认随机密码
            reqVO.setPassword(RandomUtil.randomString(8));
        } else {
            if (reqVO.getPassword().length() < 6 || reqVO.getPassword().length() > 20) {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "密码长度必须在6-20位之间");
            }
            if (!ValidationUtils.isPassword(reqVO.getPassword())) {
                throw new ServiceException(UserErrorCode.INVALID_PASSWORD);
            }
        }
        userDO = new UserDO();
        userDO.setId(idService.nextId());
        userDO.setMobile(reqVO.getMobile());
        userDO.setUserType(UserType.PATIENT);
        userDO.setRegisterIp(reqVO.getIp());
        String mobileSysSha256 = DigestUtil.sha256Hex(reqVO.getMobile() + reqVO.getUserType());
        userDO.setUsername(mobileSysSha256);
        userDO.setPassword(bCryptPasswordEncoder.encode(reqVO.getPassword()));
        userDO.setStatus(CommonStatusEnum.ENABLE);
        userMapper.insert(userDO);
        return CopyPropertiesUtil.copyAndConvert(userDO, UserSimpleVO::new, null);
    }

    @Override
    public List<UserSimpleVO> listByUserIds(IdListReqVO reqVO) {
        return userMapper.selectBatchIds(reqVO.getIdList()).stream()
                .map(userDO -> CopyPropertiesUtil.copyAndConvert(userDO, UserSimpleVO::new, null))
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<UserSimpleVO> page(SimpleUserPageReqVO reqVO) {
        LambdaQueryWrapperX<UserDO> queryWrapper = new LambdaQueryWrapperX<UserDO>()
                .likeIfPresent(UserDO::getName, reqVO.getName())
                .likeIfPresent(UserDO::getMobile, reqVO.getMobile())
                .eqIfPresent(UserDO::getStatus, reqVO.getStatus())
                .eqIfPresent(UserDO::getUserType, reqVO.getUserType())
                .eqIfPresent(UserDO::getMedicalCategory, reqVO.getMedicalCategory());
        // 账号ID
        if (reqVO.getIds() != null && reqVO.getIds().size() != 0) {
            queryWrapper.in(UserDO::getId, reqVO.getIds());
        }
        // 排查相关账号ID
        if (reqVO.getExcludeIds() != null && reqVO.getExcludeIds().size() != 0) {
            queryWrapper.notIn(UserDO::getId, reqVO.getExcludeIds());
        }
        if (reqVO.getCreateTime() != null && !reqVO.getCreateTime().isEmpty()) {
            queryWrapper
                    .ge(UserDO::getCreateTime,
                            LocalDateTime.parse(reqVO.getCreateTime().get(0),
                                    FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND))
                    .le(UserDO::getCreateTime, LocalDateTime.parse(reqVO.getCreateTime().get(1),
                            FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        }
        PageResult<UserDO> userPageResult = baseMapper.selectPage(reqVO, queryWrapper);

        if (CollectionUtils.isEmpty(userPageResult.getList())) {
            return PageResult.empty();
        }

        return new PageResult<>(
                CopyPropertiesUtil.copyAndConvertList(userPageResult.getList(), UserSimpleVO::new, null), userPageResult.getTotal());
    }

    @Override
    public PageResult<UserSimpleVO> pageWithoutAuthority(SimpleUserPageReqVO reqVO) {
        LambdaQueryWrapperX<UserDO> queryWrapper = new LambdaQueryWrapperX<UserDO>()
                .likeIfPresent(UserDO::getName, reqVO.getName())
                .likeIfPresent(UserDO::getMobile, reqVO.getMobile())
                .eqIfPresent(UserDO::getStatus, reqVO.getStatus())
                .eqIfPresent(UserDO::getUserType, reqVO.getUserType());
        if (reqVO.getCreateTime() != null && !reqVO.getCreateTime().isEmpty()) {
            queryWrapper
                    .ge(UserDO::getCreateTime,
                            LocalDateTime.parse(reqVO.getCreateTime().get(0), FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND))
                    .le(UserDO::getCreateTime, LocalDateTime.parse(reqVO.getCreateTime().get(1),
                            FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        }
        PageResult<UserDO> userPageResult = baseMapper.selectPage(reqVO, queryWrapper);

        if (CollectionUtils.isEmpty(userPageResult.getList())) {
            return PageResult.empty();
        }

        return new PageResult<>(
                CopyPropertiesUtil.copyAndConvertList(userPageResult.getList(), UserSimpleVO::new, null), userPageResult.getTotal());
    }

    @Override
    public List<UserSimpleVO> list(SimpleUserListReqVO reqVO) {
        List<UserDO> users = baseMapper.selectList(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getUserType, reqVO.getUserType()));
        return CopyPropertiesUtil.copyAndConvertList(users, UserSimpleVO::new, null);
    }

    @Override
    public UserRolesRespVO getUserRoles(UserRolesReqVO reqVO) {
        // Fetch user and check existence
        UserDO userDO = userMapper.selectById(reqVO.getUserId());
        if (userDO == null) {
            throw new ServiceException(UserErrorCode.USER_NOT_EXIST);
        }

        // Fetch user roles for the user
        List<UserRoleDO> userRoles = userRoleMapper.selectList(
                Wrappers.<UserRoleDO>lambdaQuery().eq(UserRoleDO::getUserId, userDO.getId()));

        if (userRoles.isEmpty()) {
            // If the user has no roles, return an empty list
            return new UserRolesRespVO()
                    .setUserId(userDO.getId())
                    .setApplicationId(reqVO.getApplicationId())
                    .setRoles(Collections.emptyList());
        }

        // Extract role IDs from user roles
        List<Long> roleIds = userRoles.stream().map(UserRoleDO::getRoleId).distinct().collect(Collectors.toList());

        // Fetch roles based on role IDs and application ID
        List<RoleDO> roles = roleMapper.selectList(
                new LambdaQueryWrapper<RoleDO>()
                        .in(RoleDO::getId, roleIds)
                        .eq(RoleDO::getApplicationId, reqVO.getApplicationId()));

        // Map roles to response VO
        List<UserRolesRespVO.RoleRespVO> roleRespVOs = roles.stream()
                .map(roleDO -> new UserRolesRespVO.RoleRespVO()
                        .setRoleId(roleDO.getId())
                        .setRoleName(roleDO.getName()))
                .collect(Collectors.toList());

        // Return the response
        return new UserRolesRespVO()
                .setUserId(userDO.getId())
                .setApplicationId(reqVO.getApplicationId())
                .setRoles(roleRespVOs);
    }

    @Override
    public UserSimpleVO getByUserId(Long userId) {
        UserDO userDO = userMapper.selectById(userId);
        if (userDO == null) {
            throw new ServiceException(UserErrorCode.USER_NOT_EXIST);
        }
        return CopyPropertiesUtil.copyAndConvert(userDO, UserSimpleVO::new, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "user", key = "#result.id"),
            @CacheEvict(value = "user", key = "#result.username + ':' + #result.userType"),
            @CacheEvict(value = "user", key = "#result.mobile+ ':' + #result.userType")
    })
    public UserDO updateStatus(UserStatusUpdateReqVO reqVO) {
        log.info("更新用户启用禁用状态, reqVO: {}", JSON.toJSONString(reqVO));
        UserDO userDO = userMapper.selectById(reqVO.getId());
        if (userDO == null) {
            throw new ServiceException(UserErrorCode.USER_NOT_EXIST);
        }

        userDO.setStatus(reqVO.getStatus());
        userMapper.updateById(userDO);
        return userDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "user", key = "#result.id"),
            @CacheEvict(value = "user", key = "#result.username + ':' + #result.userType"),
            @CacheEvict(value = "user", key = "#result.mobile+ ':' + #result.userType")
    })
    public UserDO updateMyPassword(MyPasswordUpdateReqVO reqVO) {
        if (!Objects.equals(reqVO.getNewPassword(), reqVO.getConfirmPassword())) {
            throw new ServiceException(UserErrorCode.PASSWORD_NOT_SAME);
        }

        UserDO userDO = userMapper.selectById(AuthorityUtils.INSTANCE.get().getUserId());
        if (userDO == null) {
            throw new ServiceException(UserErrorCode.USER_NOT_EXIST);
        }
        if (!bCryptPasswordEncoder.matches(reqVO.getOldPassword(), userDO.getPassword())) {
            throw new ServiceException(UserErrorCode.USERNAME_OR_PASSWORD_ERROR);
        }
        if (!ValidationUtils.isPassword(reqVO.getNewPassword())) {
            throw new ServiceException(UserErrorCode.INVALID_PASSWORD);
        }

        userDO.setPassword(bCryptPasswordEncoder.encode(reqVO.getNewPassword()));
        userMapper.updateById(userDO);
        return userDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "user", allEntries = true)
    public void bindMedicalTeam(BindMedicalTeamReqVO reqVO) {
        log.info("绑定医疗组，请求参数：{}", JSON.toJSONString(reqVO));
        UserDO userDO = userMapper.selectById(reqVO.getUserId());
        // 系统管理员不允许修改医疗组
        if (Objects.equals(userDO.getUsername(), "admin")) {
            throw new ServiceException(UserErrorCode.ACCESS_DENIED.getCode(), "系统管理员不允许修改医疗组");
        }

        //userDO.setMedicalTeamId(reqVO.getMedicalTeamId());
        //userMapper.updateById(userDO);
    }

}
