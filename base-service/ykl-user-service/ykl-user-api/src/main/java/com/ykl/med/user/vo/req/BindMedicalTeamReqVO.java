package com.ykl.med.user.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * 绑定医疗组请求对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "绑定医疗组请求对象")
public class BindMedicalTeamReqVO implements Serializable {

    private static final long serialVersionUID = -4349826160018989953L;

    @Schema(description = "用户ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "医疗组ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long medicalTeamId;

    @Schema(description = "有权限操作的医疗组id集合", hidden = true)
    private Set<Long> medicalTeamIds;
}