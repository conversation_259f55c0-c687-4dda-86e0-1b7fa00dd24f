package com.ykl.med.user.vo.resp;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.user.enums.RoleType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "角色列表响应值对象")
public class RoleListRespVO implements Serializable {

    private static final long serialVersionUID = 1472014831365736050L;

    @Stringify
    @Schema(description = "标识符")
    private Long id;

    /**
     * 角色code
     */
    @Schema(description = "角色code")
    private String code;

    /**
     * 角色名
     */
    @Schema(description = "角色名")
    private String name;

    /**
     * 角色排序
     */
    @Schema(description = "角色排序")
    private Integer sort;

    /**
     * 角色类型
     */
    @Schema(description = "角色类型")
    private RoleType type;

    /**
     * 角色状态
     */
    @Schema(description = "角色状态")
    private CommonStatusEnum status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Stringify
    @Schema(description = "医疗组id")
    private Long medicalTeamId;

    @Schema(description = "医疗组名称")
    private String medicalTeamName;

    @Stringify
    @Schema(description = "应用id")
    private Long applicationId;
}