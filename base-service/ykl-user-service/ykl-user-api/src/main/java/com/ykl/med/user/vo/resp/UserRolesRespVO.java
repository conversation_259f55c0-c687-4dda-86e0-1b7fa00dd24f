package com.ykl.med.user.vo.resp;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户角色详情响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "用户角色详情响应")
public class UserRolesRespVO implements Serializable {

    private static final long serialVersionUID = 6255178383613179502L;

    @Schema(description = "用户id")
    @Stringify
    private Long userId;

    @Schema(description = "应用id")
    @Stringify
    private Long applicationId;

    @Schema(description = "角色详情")
    private List<RoleRespVO> roles;

    @Data
    @Schema(description = "用户角色详情响应")
    public static class RoleRespVO implements Serializable {

        private static final long serialVersionUID = -8629605665146958039L;

        @Schema(description = "角色id")
        @Stringify
        private Long roleId;

        @Schema(description = "角色名称")
        private String roleName;

    }


}
