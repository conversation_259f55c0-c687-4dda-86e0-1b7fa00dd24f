package com.ykl.med.user.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/28
 */
@Data
@Schema(description = "通知设置")
public class NoticeSettingVO {

    @Schema(description = "待办推送(false-关闭、true-开启)")
    private Boolean todoPushFlag;

    @Schema(description = "预期提醒(false-关闭、true-开启)")
    private Boolean overduePushFlag;

    @Schema(description = "个性化(false-关闭、true-开启)")
    private Boolean individualizationPushFlag;

    @Schema(description = "用药推送(false-关闭、true-开启)")
    private Boolean medicalPushFlag;

    @Schema(description = "音频地址")
    private String musicUrl;

}
