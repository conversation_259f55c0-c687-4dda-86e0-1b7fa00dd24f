package com.ykl.med.user.vo.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 登录或注册响应对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "登录或注册响应对象")
public class LoginOrRegisterRespVO {

    @JsonProperty("access_token")
    @Schema(description = "访问令牌")
    private String accessToken;

    @JsonProperty("token_type")
    @Schema(description = "令牌类型")
    private String tokenType;

    @JsonProperty("refresh_token")
    @Schema(description = "刷新令牌")
    private String refreshToken;

    @JsonProperty("expires_in")
    @Schema(description = "过期时间")
    private Long expiresIn;

    @JsonProperty("scope")
    @Schema(description = "权限范围")
    private String scope;

    @JsonProperty("jti")
    @Schema(description = "jti")
    private String jti;

    @JsonProperty("user_info")
    @Schema(description = "用户信息")
    private UserInfoRespVO userInfo;
}
