package com.ykl.med.user.vo.req;

import java.io.Serializable;
import java.util.Set;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * This class represents the request VO for listing roles.
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "列表角色 Request VO")
public class ListRolesReqVO implements Serializable {

    private static final long serialVersionUID = 3314584091714452934L;

    @Schema(description = "医疗组id", hidden = true)
    private Set<Long> medicalTeamIds;

    @Schema(description = "应用id")
    private Long applicationId;
}
