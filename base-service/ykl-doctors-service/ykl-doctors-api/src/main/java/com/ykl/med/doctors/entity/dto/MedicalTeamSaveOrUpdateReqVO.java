package com.ykl.med.doctors.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 创建医疗组请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "保存或更新医疗组请求VO")
public class MedicalTeamSaveOrUpdateReqVO implements Serializable {

    private static final long serialVersionUID = -119560833679864987L;

    @Schema(description = "组ID, 更新时传, 创建时不传", example = "1")
    private String id;

    @Schema(description = "父医疗组ID", example = "1")
    private String parentId;

    @Schema(description = "医疗组名称", example = "医康链")
    private String name;

    @Schema(description = "显示顺序", example = "1")
    private Integer sort;

    @Schema(description = "负责人ID", example = "1")
    private String leaderUserId;

    @Schema(description = "联系电话", example = "15184387625")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "状态", example = "ENABLE")
    private String status;

    @Schema(description = "是否医疗组", example = "true")
    private Boolean metaTeam;

    @Schema(description = "医院ID", example = "")
    private String hospitalId;
}