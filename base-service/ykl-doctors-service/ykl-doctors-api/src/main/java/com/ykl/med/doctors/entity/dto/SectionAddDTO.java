package com.ykl.med.doctors.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2024-10-8
 */
@Data
@Schema(description = "科室表")
public class SectionAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "科室名称",example = "")
    private String name ;

    @Schema(description = "拼音简码", example = "")
    private String nameShort ;

    @Schema(description = "科室唯一编码", example = "")
    private String value ;

    @Schema(description = "科室类别", example = "")
    private String category ;

    @Schema(description = "专科类别", example = "")
    private String specialCategory ;

    @Schema(description = "平台编码(省平台)", example = "")
    private String platformCode ;

    @Schema(description = "排序值", example = "")
    private String sort ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

}
