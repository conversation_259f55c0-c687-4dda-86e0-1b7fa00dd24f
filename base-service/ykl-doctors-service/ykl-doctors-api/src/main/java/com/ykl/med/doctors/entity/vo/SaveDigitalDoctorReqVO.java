package com.ykl.med.doctors.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
@Data
public class SaveDigitalDoctorReqVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "语音文件地址")
    private String voiceUrl;

    @Schema(description = "语音转文本内容")
    private String voiceText;

    @Schema(description = "状态: true(开启)/false(关闭) ")
    private Boolean status;

    @Schema(description = "数字医生视频")
    private List<DigitalDoctorVideoLibraryReqVO> videoLibrary;

}
