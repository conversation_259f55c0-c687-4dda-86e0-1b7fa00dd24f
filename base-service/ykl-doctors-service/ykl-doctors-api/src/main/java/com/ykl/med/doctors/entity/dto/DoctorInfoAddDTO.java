package com.ykl.med.doctors.entity.dto;


import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2024-10-17
 */
@Data
@Schema(description = "医生表")
public class DoctorInfoAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "姓名", example = "")
    private String name ;

    @Schema(description = "性别", example = "MALE:男, FEMALE:女")
    private String gender ;

    @Schema(description = "生日", example = "")
    private String birthday ;

    @Schema(description = "身份证号", example = "")
    private String idCard ;

    @Schema(description = "手机号", example = "")
    private String mobile ;

    @Schema(description = "邮箱", example = "")
    private String email ;

    @Schema(description = "头像地址", example = "")
    private String avatar ;

    @Schema(description = "医疗组ID", example = "")
    private String medicalTeamId ;

    @Schema(description = "医院ID", example = "")
    private String hospitalId ;

    @Schema(description = "科室ID", example = "")
    private String sectionId ;

    @Schema(description = "HIS系统医生ID(用于绑定)", example = "")
    private String hisDoctorId ;

    @Schema(description = "职称", example = "")
    private String title ;

    @Schema(description = "擅长", example = "")
    private String goodAt ;

    @Schema(description = "简介", example = "")
    private String introduction ;

    @Schema(description = "执业信息", example = "")
    private String practiceInfo ;

    @Schema(description = "医师资格证号", example = "")
    private String medicalCertificateNumber ;

    @Schema(description = "身份证正面照地址", example = "")
    private String frontIdCard ;

    @Schema(description = "身份证反面照地址", example = "")
    private String reverseIdCard ;

    @Schema(description = "执业证地址", example = "")
    private List<String> photoPracticeInfo ;

    @Schema(description = "医师资格证地址", example = "")
    private List<String> photoMedicalCertificateNumber ;

    @Schema(description = "职称证件地址", example = "")
    private List<String> photoTitle ;

    @Schema(description = "签名图片地址", example = "")
    private String photoSign ;

    @Schema(description = "抗菌药物等级(权限)", example = "")
    private String antimicrobialDrugClass ;

    @Schema(description = "门诊医生标志", example = " 0非门诊医生, 1门诊医生")
    private String outpatientDoctorFlag ;

    @Schema(description = "备案标志", example = " 0未备案, 1已备案")
    private String fillingsFlag ;

    @Schema(description = "医责险标志", example = " 0没有, 1有")
    private String medicalInsuranceFlag ;

    @Schema(description = "实名认证标志", example = " 0未实名, 1已实名")
    private String realNameFlag ;

    @Schema(description = "CA签章标志", example = " 0不可签章, 1可签章")
    private String caFlag ;

    @Schema(description = "排序值", example = "")
    private String sort ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

    @Schema(description = "自我介绍(数字医生)", example = "")
    private String selfIntroduction;

    @Schema(description = "是否团队成员；false-不是，true-是", example = "")
    private Boolean teamFlag ;

    @Schema(description = "职务", example = "")
    private String positionTitle;


    /**
     * 其他信息
     * */
    @Schema(description = "用户ID", example = "")
    private String userId;

}
