package com.ykl.med.doctors.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2024-10-17
 */
@Data
@Schema(description = "医生表")
public class DoctorInfoQueryDTO extends CommonDoctorIdVO {

    private Integer pageSize;
    private Integer pageNo;
    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "姓名(用户中的姓名)", example = "")
    private String name ;

    @Schema(description = "身份证号", example = "")
    private String idCard ;

    @Schema(description = "手机号", example = "")
    private String mobile ;

    @Schema(description = "医疗组ID", example = "")
    private String medicalTeamId ;

    @Schema(description = "医院ID", example = "")
    private String hospitalId ;

    @Schema(description = "科室ID", example = "")
    private String sectionId ;

    @Schema(description = "门诊医生标志", example = " 0非门诊医生, 1门诊医生")
    private String outpatientDoctorFlag ;

    @Schema(description = "备案标志", example = " 0未备案, 1已备案")
    private String fillingsFlag ;

    @Schema(description = "医责险标志", example = " 0没有, 1有")
    private String medicalInsuranceFlag ;

    @Schema(description = "实名认证标志", example = " 0未实名, 1已实名")
    private String realNameFlag ;

    @Schema(description = "CA签章标志", example = " 0不可签章, 1可签章")
    private String caFlag ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

    @Schema(description = "排序值; ASC:顺序,DESC:倒序", example = "")
    private String sort ;

    @Schema(description = "是否团队成员；false-不是，true-是", example = "")
    private Boolean teamFlag ;

    @Schema(description = "职务", example = "")
    private String positionTitle;


    /**
     * 其他字段
     * */
    @Schema(description = "id列表", example = "")
    private List<String> ids;
}
