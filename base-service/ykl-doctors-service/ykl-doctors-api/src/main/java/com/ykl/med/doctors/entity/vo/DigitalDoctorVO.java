package com.ykl.med.doctors.entity.vo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
@Data
public class DigitalDoctorVO {

    @Schema(description = "id")
    @Stringify
    private Long id;
    
    @Schema(description = "医生id")
    @Stringify
    private Long doctorId;
    
    @Schema(description = "医生名称")
    private String doctorName;
    
    @Schema(description = "用户名")
    private String username;

    @Schema(description = "头像地址")
    private String avatar ;

    @Schema(description = "医疗组ID")
    @Stringify
    private Long medicalTeamId;
    
    @Schema(description = "医疗组名称")
    private String medicalTeamName;
    
    @Schema(description = "语音文件地址")
    private String voiceUrl;

    @Schema(description = "语音转文本内容")
    private String voiceText;

    @Schema(description = "状态: true(开启)/false(关闭) ")
    private Boolean status;

    @Schema(description = "创建者ID")
    @Stringify
    private Long creatorId;

    @Schema(description = "创建者名称")
    private String creatorName;

    @Schema(description = "创建时间")
    @TimestampConvert
    private LocalDateTime createTime;

    @Schema(description = "最后更新时间")
    @TimestampConvert
    private LocalDateTime updateTime;

    @Schema(description = "医生视频库")
    private List<DigitalDoctorVideoLibraryVO> videoLibrary;


}
