package com.ykl.med.doctors.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 医院表;
 * <AUTHOR> xkli
 * @date : 2024-1-5
 */
@Data
@Schema(description = "医院表")
public class HospitalAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "名称", example = "")
    private String name ;

    @Schema(description = "名称简拼码", example = "")
    private String nameShort ;

    @Schema(description = "编码值", example = "")
    private String value ;

    @Schema(description = "医院等级", example = "")
    private String level ;

    @Schema(description = "排序值", example = "")
    private String sort ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

}
