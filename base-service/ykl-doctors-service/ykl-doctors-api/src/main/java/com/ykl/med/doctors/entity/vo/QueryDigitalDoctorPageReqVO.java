package com.ykl.med.doctors.entity.vo;

import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
@Data
public class QueryDigitalDoctorPageReqVO extends PageParam {

    @Schema(description = "医生姓名")
    private String doctorName;

    @Schema(description = "是否有视频库（true-有，false-没有）")
    private Boolean hasVideoLibrary;

}
