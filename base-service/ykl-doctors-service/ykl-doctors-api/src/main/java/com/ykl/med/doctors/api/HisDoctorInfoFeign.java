package com.ykl.med.doctors.api;

import com.ykl.med.doctors.entity.vo.HisDoctorInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(name = "ykl-doctors-service",path = "/hisDoctorInfo")
public interface HisDoctorInfoFeign {
    @PostMapping("/getAll")
    @Operation(summary = "获取全部的his医生信息")
    List<HisDoctorInfoVO> getAll();
}
