package com.ykl.med.doctors.entity.vo;

import com.ykl.med.framework.common.enums.VideoOrientationEnum;
import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
@Data
public class DigitalDoctorVideoLibraryVO {
    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "关联t_digital_doctor表的主键")
    @Stringify
    private Long digitalDoctorId;

    @Schema(description = "医生id")
    @Stringify
    private Long doctorId;

    @Schema(description = "数字医生名称")
    private String name;

    @Schema(description = "视频分辨率宽（像素）")
    private Integer width;

    @Schema(description = "视频分辨率高（像素）")
    private Integer height;

    @Schema(description = "横竖屏: portrait(竖屏)/landscape(横屏)")
    private VideoOrientationEnum orientation;

    @Schema(description = "视频文件地址")
    private String videoUrl;

    @Schema(description = "视频时长（秒）")
    private Integer videoTime;

    @Schema(description = "视频缩略图地址")
    private String videoThumbnailUrl;
}
