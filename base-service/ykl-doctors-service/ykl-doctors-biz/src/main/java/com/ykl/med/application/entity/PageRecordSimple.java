package com.ykl.med.application.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 简单翻页数据结构
 * */
@Data
public class PageRecordSimple<T> implements Serializable {
    private long total;
    private List<T> list;

    public void setList(List<T> li) {
        if ( li != null ) {
            this.list = li;
        } else {
            this.list = new ArrayList<>();
        }
    }

}
