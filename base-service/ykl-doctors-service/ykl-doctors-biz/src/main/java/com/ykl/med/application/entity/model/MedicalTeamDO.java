package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR> xkli
 * @date : 2024-10-11
 */
@Data
@Schema(description = "")
@TableName("t_medical_team")
public class MedicalTeamDO {

    @Schema(description = "", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long id ;

    @Schema(description = "", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,WHERE:WL_MATCH:%:%")
    private String name ;

    @Schema(description = "", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private Long parentId ;

    @Schema(description = "", example = "")
    @TableLogic("NOTNULL:NN_POST")
    private Integer sort ;

    @Schema(description = "", example = "")
    @TableId
    private Long leaderUserId ;

    @Schema(description = "", example = "")
    private String phone ;

    @Schema(description = "", example = "")
    private String email ;

    @Schema(description = "", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,FILLED:SGV_INSERT:ENABLE")
    private String status ;

    @Schema(description = "", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private Byte metaTeam ;

    @Schema(description = "医院", example = "")
    @TableId
    private Long hospitalId ;

}
