package com.ykl.med.application.mapper;

import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.ykl.med.application.entity.*;
import com.ykl.med.config.GlobalVariable;
import com.ykl.med.config.SimpleException;
import com.ykl.med.enums.LogicValue;
import com.ykl.med.util.GsonUtil;
import com.ykl.med.util.PublicUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class ASqlExecServiceImpl implements ASqlExecService {
    /**
     * 数据库驱动器
     * */
    @Resource
    private DBDriverMapper dbDriverMapper;

    /**
     * sql语句生成工厂
     * */
    @Resource
    private MysqlFactory mysqlFactory;

    /**
     * 事务管理器
     * */
    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * Sql执行：只与值对象列表,翻页对象有关系
     * 可以传入多个值对象一起执行(支持SQL事务)
     * */
    @Override
    public ResponseModel batchSqlExec(HttpRequestData httpRequestData) throws SimpleException, IllegalAccessException {

        // 直接返回ok
        if ( httpRequestData == null || httpRequestData.getValueObjectList() == null || httpRequestData.getValueObjectList().size() == 0 ){
            return new ResponseModel();
        }

        /**
         * 初始化事务
         * */
        TransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);

        ResponseModel responseModel = new ResponseModel();
        for ( ValueObject valueObject : httpRequestData.getValueObjectList() ){
            responseModel = this.sqlExec(valueObject, httpRequestData.getPageRecord());
            // 值对象执行失败: 返回错误
            if ( responseModel.getCode() != 0 ){
                /**
                 * 回滚事务
                 * */
                transactionManager.rollback(status);
                return responseModel;
            }
        }

        /**
         * 提交事务
         * */
        transactionManager.commit(status);

        return responseModel;
    }

    /**
     * 控制sql语句的生成, 执行, 事务等
     * */
    @Override
    public ResponseModel sqlExec(ValueObject valueObject, PageRecord page)
            throws SimpleException, IllegalAccessException {

        if ( valueObject == null ){
            return null;
        }

        ResponseModel responseModel = new ResponseModel();

        // 执行SQL: 失败则回滚
        ResponseModel responseResult = sqlExecMapper(mysqlFactory.crudToSql(valueObject, page), page);
        if ( responseResult == null ){
            responseResult = new ResponseModel();
            if ( valueObject.getSourceMethod().equals("GET") ){
                if ( GlobalVariable.resultFlag ){ // 完整结果
                    PageRecord pageRecord = new PageRecord();
                    pageRecord.setRecords(new ArrayList<>());
                    responseResult.setData(pageRecord);
                } else { // 简单结果
                    // 返回简单分页数据
                    PageRecordSimple<LinkedHashMap<String, Object>> pageRecordSimple = new PageRecordSimple<>();
                    pageRecordSimple.setTotal(0);
                    pageRecordSimple.setList(new ArrayList<>());
                    responseResult.setData(pageRecordSimple);
                }
            }
        }
        if ( responseResult.getCode() != 0 ){
            responseModel.failResponse(responseResult.getMsg());
            return responseModel;
        }

        // 如果是GET查询子值对象内容, 然后组装
        if ( valueObject.getSourceMethod().equals("GET") ){

            // 查询结果字段名称
            String resultFieldName = "list";

            // 拿到结果列表
            List<Map<String, Object>> records = getFieldValueInList( responseResult.getData(), resultFieldName);
            if ( records == null ){
                records = new ArrayList<>();
            }

            // 有记录, 且有子对象
            if ( records.size() != 0 && valueObject.getSubValueObject().size() != 0 ) {

                // 循环查找
                for (Map<String, Object> obj : records) {
                    // 将obj赋值给值对象
                    objectToVObject(valueObject, obj);

                    /**
                     * 填充值对象下的关联子对象
                     * */
                    filedRelationField(valueObject);

                    // 执行对象下的关联子对象
                    for (Map.Entry<String, List<ValueObject>> vObjMap : valueObject.getSubValueObject().entrySet()) {
                        if (vObjMap.getValue().size() > 0) {
                            ResponseModel responseModel1 = sqlExecMapper(mysqlFactory.crudToSql(vObjMap.getValue().get(0), null), null);
                            if ( responseModel1 == null ){
                                continue;
                            }
                            if (responseModel1.getCode() != 0) {
                                responseModel.failResponse(responseModel1.getMsg());
                                return responseModel;
                            }

                            List<Map<String, Object>> objMapList = getFieldValueInList(responseModel1.getData(), resultFieldName);
                            if ( objMapList != null ) {
                                obj.put(vObjMap.getKey(), objMapList);
                            }
                        }
                    }
                }
            }

            // 执行完成: 将records的数据重新装配
            Field field = null;
            try {
                field = responseResult.getData().getClass().getDeclaredField(resultFieldName);
            } catch (Exception ignored){}
            if ( field != null ){
                field.setAccessible(true);
                field.set(responseResult.getData(), records);
            }

        } else { // 其他子项操作

            /**
             * 先删除旧子对象:
             * */
            if ( valueObject.getSourceMethod().equals("PUT") || valueObject.getSourceMethod().equals("DELETE") ) {
                for (Map.Entry<String, List<ValueObject>> subVObjMap : valueObject.getSubValueObject().entrySet()) {
                    // 子对象有值对象
                    if ( subVObjMap.getValue().size() != 0 ){

                        // 如果是"真"更新,且子对象没有传入值时,不删除
                        if ( valueObject.getSourceMethod().equals("PUT") && !subVObjMap.getValue().get(0).isExistValue() ){
                            continue;
                        }

                        // 取出第一个值对象
                        ValueObject valueObject1 = subVObjMap.getValue().get(0);
                        // 表名称
                        String tableName = valueObject1.getTableName();
                        System.out.print(tableName+"\n");

                        // 获取当前操作方法与id数据对象
                        String curMethod = valueObject1.getMethod();
                        List<Object> objects = valueObject1.getVObj().get("id").getObj();
                        // 设置当前操作为删除与情况id数据对象
                        valueObject1.setMethod("DELETE");
                        valueObject1.getVObj().get("id").setObj(new ArrayList<>());
                        // 执行删除操作
                        ResponseModel responseModel1 = sqlExecMapper(mysqlFactory.crudToSql(valueObject1, null), null);
                        if ( responseModel1 != null && responseModel1.getCode() != 0) {
                            responseModel.failResponse(responseModel1.getMsg());
                            return responseModel;
                        }
                        // 还原操作方法与id数据对象
                        valueObject1.setMethod(curMethod);
                        valueObject1.getVObj().get("id").setObj(objects);
                    }
                }
            }

            /**
             * 插入新值对象数据
             * */
            if ( valueObject.getSourceMethod().equals("POST") || valueObject.getSourceMethod().equals("PUT") ) {
                for (Map.Entry<String, List<ValueObject>> subVObjMap : valueObject.getSubValueObject().entrySet()) {
                    for (ValueObject valueObject1 : subVObjMap.getValue()) {
                        // 没有从前端传值: 跳过
                        if (!valueObject1.isExistValue()) {
                            continue;
                        }

                        // 获取当前操作方法
                        String curMethod = valueObject1.getMethod();
                        valueObject1.setMethod("POST");
                        ResponseModel responseModel1 = sqlExecMapper(mysqlFactory.crudToSql(valueObject1, null), null);
                        if ( responseModel1 != null && responseModel1.getCode() != 0) {
                            responseModel.failResponse(responseModel1.getMsg());
                            return responseModel;
                        }
                        valueObject1.setMethod(curMethod);
                    }
                }
            }

            // 执行完成: 不用处理
        }

        /**
         * 结果赋值
         * */
        responseModel.setData(responseResult.getData());

        return responseModel;
    }

    /**
     * 控制sql语句的生成, 执行, 事务等
     * */
    @Override
    public ResponseModel sqlListExec(List<String> stringList, PageRecord page) {

        ResponseModel responseModel = new ResponseModel();

        /**
         * 循环执行sql
         * */
        // 执行sql序号
        int sqlNumber = 0;
        for ( String sqlStr : stringList ) {
            // 执行SQL: 失败则回滚
            ResponseModel responseResult = sqlExecMapper(sqlStr, page);
            if (responseResult.getCode() != 0) {
                responseModel.failResponse(responseResult.getMsg());
                return responseModel;
            }

            responseModel.putData(String.valueOf(sqlNumber), responseResult.getData());
        }

        return responseModel;
    }

    /**
     * 执行sql语句
     * */
    @Override
    public ResponseModel sqlExecMapper(String sqlStr, PageRecord page){

        if ( sqlStr == null ){
            return null;
        }

        ResponseModel responseModel = new ResponseModel();

        PageRecord pageRecord = processPageRecordObject(page);

        // 返回统计结果
        List<LinkedHashMap<String, Object>> countResult = null;
        // SQL执行结果
        List<LinkedHashMap<String, Object>> result;

        // 去掉两头空格
        sqlStr = sqlStr.trim();

        // 是查询SQL: 查询条数
        if ( sqlStr.substring(0, Math.min(sqlStr.length(), 6)).toUpperCase().equals("SELECT") ){
            int limitLength = sqlStr.lastIndexOf("Limit");
            if ( limitLength == -1 ){
                limitLength = sqlStr.length();
            }
            String fromString = sqlStr.substring(sqlStr.lastIndexOf("FROM"), limitLength).trim();
            int orderLength = fromString.lastIndexOf("ORDER BY");
            if ( orderLength == -1 ){
                orderLength =fromString.length();
            }
            String fromStringOutOfOrder = fromString.substring(0, orderLength).trim();
            String selectCountSql = "SELECT COUNT(*) " + fromStringOutOfOrder;

            // 判断是否为分组查询: 重装sql获取分组查询的总数
            if ( selectCountSql.lastIndexOf("GROUP BY") >= 0 ){
                selectCountSql = "SELECT COUNT(*) FROM ("+selectCountSql+") t";
            }

            System.out.print(PublicUtil.DateToString(new Date()) + "\t" + selectCountSql + "\n");
            // 执行
            try {
                countResult = dbDriverMapper.execSql(selectCountSql);
            } catch (Exception e){
                responseModel.failResponse(e.getMessage());
                return responseModel;
            }

            // 获取翻页信息
            if ( (countResult != null) &&  (countResult.size() > 0) ){
                for (Map.Entry<String, Object> map : countResult.get(0).entrySet()){
                    if ( !map.getKey().contains("COUNT") ){
                        continue;
                    }

                    pageRecord.setTotalCount(Long.decode(map.getValue().toString()));

                    // 处理翻页信息
                    long pageAll = pageRecord.getTotalCount() / pageRecord.getPageSize();
                    if ( (pageRecord.getTotalCount() % pageRecord.getPageSize()) != 0 ){
                        pageAll++;
                    }

                    pageRecord.setTotalPage(pageAll);
                }
            }
        }

        try {
            System.out.print(PublicUtil.DateToString(new Date()) + "\t" + sqlStr + "\n");
            result = dbDriverMapper.execSql(sqlStr);
        } catch (Exception e){
            responseModel.failResponse(e.getMessage());
            return responseModel;
        }

        // 执行结果处理
        processExecResults(result);

        // 设置结果
        if ( countResult != null ){ // 是查询, 增加翻页信息
            if ( GlobalVariable.resultFlag ){ // 完整结果
                pageRecord.setRecords(result);
                responseModel.setData(pageRecord);
            } else { // 简单结果
                // 返回简单分页数据
                PageRecordSimple<LinkedHashMap<String, Object>> pageRecordSimple = new PageRecordSimple<>();
                pageRecordSimple.setTotal(pageRecord.getTotalCount());
                pageRecordSimple.setList(result);
                responseModel.setData(pageRecordSimple);
            }

        } else {
            responseModel.setData(result);
        }

        return responseModel;
    }

    /**
     * 对象赋值给值对象
     * */
    public void objectToVObject(ValueObject valueObject, Map<String, Object> DObj){

        if ( valueObject == null || DObj == null ){
            return;
        }

        Map<String, ObjectAttribute> VObj = valueObject.getVObj();

        for ( Map.Entry<String, ObjectAttribute> m : VObj.entrySet() ){
            // 清除之前的值对象数据
            m.getValue().getObj().clear();
            // 获取传入对象值
            Object DObj1 = null;
            if ( DObj.containsKey(m.getValue().getKeyName()) ){
                DObj1 = DObj.get(m.getValue().getKeyName());
            }

            // 不为空, 赋值给值对象
            if ( DObj1 != null ){
                if (DObj1.getClass().getSimpleName().equals("String")) { // 对象是字符串,切分后循环加入到值对象
                    String[] valArr = DObj1.toString().split(",");
                    for (String str : valArr) {
                        // 不为空,赋值
                        if (StringUtils.isNotBlank(str)) {
                            m.getValue().getObj().add(str);
                            valueObject.setExistValue(true);
                        }
                    }
                } else { // 直接整体加入: 肯能是对象, 列表, 数字, 日期等
                    m.getValue().getObj().add(DObj1);
                    valueObject.setExistValue(true);
                }
            }
        }

    }

    /**
     * 填充值对象相关字段: 一对多
     * */
    public void filedRelationField(ValueObject VObj){

        if ( VObj == null ){
            return;
        }

        // 值对象实体
        Map<String, ObjectAttribute> objectAttributeMap = VObj.getVObj();

        // 将值对象中有表对应关系的字段赋值给子对象: 一对多
        for ( Map.Entry<String, ObjectAttribute> vObjMap : objectAttributeMap.entrySet() ){
            // 找到表的映射逻辑配置
            if ( vObjMap.getValue().getLogicActValueMap().containsKey(LogicValue.TR_O2M)
                    && vObjMap.getValue().getLogicActValueMap().get(LogicValue.TR_O2M).size() >= 2
            ){
                // 循环获取配置的表->字段的对应关系
                for ( int b = 0; b < vObjMap.getValue().getLogicActValueMap().get(LogicValue.TR_O2M).size(); )
                {
                    // 对象名称
                    String objectName = vObjMap.getValue().getLogicActValueMap().get(LogicValue.TR_O2M).get(b++);
                    // 字段名称
                    String filedName = vObjMap.getValue().getLogicActValueMap().get(LogicValue.TR_O2M).get(b++);

                    // 找到存在的子值对象
                    if (VObj.getSubValueObject().containsKey(objectName) && (VObj.getSubValueObject().get(objectName) != null))
                    {
                        for (int i = 0; i < VObj.getSubValueObject().get(objectName).size(); i++) {
                            // 找到对象中的字段: 清空->赋值
                            if (VObj.getSubValueObject().get(objectName).get(i).getVObj() != null
                                    && VObj.getSubValueObject().get(objectName).get(i).getVObj().containsKey(filedName)
                            ) {
                                VObj.getSubValueObject().get(objectName).get(i).getVObj().get(filedName).getObj().clear();
                                VObj.getSubValueObject().get(objectName).get(i).getVObj().get(filedName).getObj()
                                        .addAll(vObjMap.getValue().getObj());
                            }
                        }
                    }
                }
            }
        }

        if ( VObj.getSubValueObject() == null || VObj.getSubValueObject().size() == 0 ){
            return;
        }

        // 递归处理关联字段赋值
        Map<String, List<ValueObject>> subValueObject = VObj.getSubValueObject();
        for ( Map.Entry<String, List<ValueObject>> subVObjMap : subValueObject.entrySet() ){
            for ( ValueObject valueObject : subVObjMap.getValue() ){
                filedRelationField(valueObject);
            }
        }

    }

    /**
     * 处理翻页对象,确保其正确性
     * */
    private PageRecord processPageRecordObject(PageRecord page) {
        if ( page == null ){
            page = new PageRecord();
        }

        // 判断pageRecord是否正确
        if ( page.getPageNo() < 1 ){
            page.setPageNo(1);
        }
        if( page.getPageSize() < 1 ){
            page.setPageSize(100);
        }

        // 不能超过1w条
        if ( page.getPageSize() > 10000 ){
            page.setPageSize(10000);
        }

        // 设置返回结果集为[]
        if ( page.getList() == null ){
            page.setRecords(new ArrayList<>());
        }

        return page;
    }

    /**
     * SQL执行结果处理
     * */
    private void processExecResults(List<LinkedHashMap<String, Object>> result) {
        if ( result == null ){
            return;
        }

        // 删除列表中的null
        result.removeIf(Objects::isNull);

        // 处理结果中的LocalDateTime字段
        for (LinkedHashMap<String, Object> stringObjectLinkedHashMap : result) {
            for (Map.Entry<String, Object> objectEntry : stringObjectLinkedHashMap.entrySet()) {
                // 字段值的字符串
                String requestBodyString = objectEntry.getValue().toString();
                // 类型为时间: 转时间戳
                if (objectEntry.getValue() instanceof LocalDateTime) {
                    // 转成时间戳
                    stringObjectLinkedHashMap.put(objectEntry.getKey(), PublicUtil.LocalDateTimeToDate((LocalDateTime) objectEntry.getValue()).getTime());
                    // 转成String格式
                    //stringObjectLinkedHashMap.put(objectEntry.getKey(), PublicUtil.DateToString(PublicUtil.LocalDateTimeToDate((LocalDateTime) objectEntry.getValue())));
                }
                // 值可能是json格式的字符串: 转对象或列表
                else if ( requestBodyString.startsWith("{") && requestBodyString.endsWith("}")
                        || requestBodyString.startsWith("[") && requestBodyString.endsWith("]")
                ) {
                    Map<String, Object> requestBody = new HashMap<>();
                    List<Map<String, Object>> requestBodyList = new ArrayList<>();
                    List<String> requestList = new ArrayList<>();
                    try {
                        if (requestBodyString.startsWith("[{") && requestBodyString.endsWith("}]")) {
                            if ( !requestBodyString.equals("[{}]") ){
                                requestBodyList = GsonUtil.jsonToListMaps(requestBodyString);
                            } else {
                                stringObjectLinkedHashMap.put(objectEntry.getKey(), requestBodyList);
                            }
                        } else if ( requestBodyString.startsWith("[") && requestBodyString.endsWith("]") ){
                            if ( !requestBodyString.equals("[]") ) {
                                Gson gson = new Gson();
                                requestList = gson.fromJson(requestBodyString, new TypeToken<List<String>>() {}.getType());
                            } else {
                                stringObjectLinkedHashMap.put(objectEntry.getKey(), requestList);
                            }
                        } else if (requestBodyString.startsWith("{") && requestBodyString.endsWith("}")) {
                            if ( !requestBodyString.equals("{}") ) {
                                requestBody = GsonUtil.jsonToMaps(requestBodyString);
                            } else {
                                stringObjectLinkedHashMap.put(objectEntry.getKey(), requestBody);
                            }
                        }
                    } catch (Exception ignored){ }

                    if ( requestBodyList.size() != 0 ){
                        stringObjectLinkedHashMap.put(objectEntry.getKey(), requestBodyList);
                    } else if ( requestBody.size() != 0 ){
                        stringObjectLinkedHashMap.put(objectEntry.getKey(), requestBody);
                    } else if ( requestList.size() != 0 ){
                        stringObjectLinkedHashMap.put(objectEntry.getKey(), requestList);
                    }
                }
            }
        }
    }

    /**
     * 得到对象中字段的对象列表
     * */
    private List<Map<String, Object>> getFieldValueInList(Object obj, String fieldName) throws IllegalAccessException {
        if ( obj == null || fieldName == null || fieldName.isEmpty() ){
            return null;
        }

        Field field = null;
        try {
            field = obj.getClass().getDeclaredField(fieldName);
        } catch (Exception ignored){ }

        if ( field == null ){
            return null;
        }

        // 子值对象下的结果
        field.setAccessible(true);

        // 是List
        if ( field.get(obj) instanceof List ){
            return PublicUtil.parseObject(field.get(obj), new TypeReference<List<Map<String, Object>>>() {});
        }

        return null;
    }

}
