package com.ykl.med.application.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class HttpRequestData {
    String method = null;
    PageRecord pageRecord = null;
    // 传入数据对象,包括: query, requestBody对象字段
    Map<String, Object> inputParamMap = null;
    // 批量传入数据对象
    List<Map<String, Object>> dataObject = null;
    // 批量传入数据值对象
    List<ValueObject> valueObjectList = null;
    // 处理值对象时, 必填参数错误时, 赋值
    ResponseModel responseModel = null;
}
