package com.ykl.med.doctors.controller;

import com.ykl.med.doctors.api.DigitalDoctorFeign;
import com.ykl.med.doctors.entity.vo.*;
import com.ykl.med.doctors.service.DigitalDoctorService;
import com.ykl.med.framework.common.pojo.PageResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
@RestController
@RequestMapping("/digitalDoctor")
public class DigitalDoctorController implements DigitalDoctorFeign {

    @Resource
    private DigitalDoctorService digitalDoctorService;

    @Override
    @PostMapping("/page")
    public PageResult<DigitalDoctorVO> page(@RequestBody QueryDigitalDoctorPageReqVO reqVO) {
        return digitalDoctorService.page(reqVO);
    }

    @Override
    @PostMapping("/generateDigitalVideoPage")
    public PageResult<GenerateDigitalVideoVO> generateDigitalVideoPage(@Valid @RequestBody QueryGenerateDigitalVideoPageReqVO reqVO) {
        return digitalDoctorService.generateDigitalVideoPage(reqVO);
    }


    @Override
    @PostMapping("/list")
    public List<DigitalDoctorVO> list(@RequestBody QueryDigitalDoctorReqVO reqVO) {
        return digitalDoctorService.list(reqVO);
    }

    @Override
    @PostMapping("/generateDigitalVideoList")
    public List<GenerateDigitalVideoVO> generateDigitalVideoList(@RequestBody QueryGenerateDigitalVideoReqVO reqVO) {
        return digitalDoctorService.generateDigitalVideoList(reqVO);
    }

    @Override
    @PostMapping("/details")
    public DigitalDoctorVO details(@RequestParam(name = "id") Long id) {
        return digitalDoctorService.details(id);
    }

    @Override
    @PostMapping("/add")
    public void add(@Valid @RequestBody AddDigitalDoctorReqVO reqVO) {
        digitalDoctorService.add(reqVO);
    }

    @Override
    @PostMapping("/save")
    public void save(@Valid @RequestBody SaveDigitalDoctorReqVO reqVO) {
        digitalDoctorService.save(reqVO);
    }


    @Override
    @PostMapping("/generateDigitalVideoLastOne")
    public GenerateDigitalVideoVO generateDigitalVideoLastOne(@Valid @RequestBody QueryGenerateDigitalVideoLastOneReqVO reqVO) {
        return digitalDoctorService.generateDigitalVideoLastOne(reqVO);
    }

    @Override
    @PostMapping("/generateDigitalVideoDetails")
    public GenerateDigitalVideoVO generateDigitalVideoDetails(@RequestParam(name = "id")Long id) {
        return digitalDoctorService.generateDigitalVideoDetails(id);
    }

    @Override
    @PostMapping("/generateVideo")
    public void generateVideo(@Valid @RequestBody GenerateVideoReqVO reqVO) {
        digitalDoctorService.generateVideo(reqVO);
    }

    @Override
    @PostMapping("/generateVideoStatus")
    public void generateVideoStatus(@Valid @RequestBody GenerateVideoStatusReqVO reqVO) {
        digitalDoctorService.generateVideoStatus(reqVO);
    }

    @Override
    @PostMapping("/generateVideoEnable")
    public void generateVideoEnable(@Valid @RequestBody GenerateVideoEnableReqVO reqVO) {
        digitalDoctorService.generateVideoEnable(reqVO);
    }


    @Override
    @PostMapping("/generateVideoDelete")
    public void generateVideoDelete(@RequestParam(name = "id") Long id) {
        digitalDoctorService.generateVideoDelete(id);
    }

    @Override
    @PostMapping("/createDigitalDoctorWork")
    public void createDigitalDoctorWork(@RequestBody GenerateDigitalVideoVO reqVO) {
        digitalDoctorService.createDigitalDoctorWork(reqVO);
    }


    @Override
    @PostMapping("/timeOut")
    public void timeOut() {
        digitalDoctorService.timeOut();
    }

}
