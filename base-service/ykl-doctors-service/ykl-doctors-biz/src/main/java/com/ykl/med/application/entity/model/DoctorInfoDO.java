package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR> xkli
 * @date : 2024-10-17
 */
@Data
@Schema(description = "医生表")
@TableName("t_doctor_info")
public class DoctorInfoDO {

    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long id ;

    @Schema(description = "医生名", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,WHERE:WL_MATCH:%:%")
    private String name ;

    @Schema(description = "性别", example = " MALE:男, FEMALE:女")
    @TableLogic("NOTNULL:NN_POST")
    private String gender ;

    @Schema(description = "生日", example = "")
    private String birthday ;

    @Schema(description = "身份证号", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,WHERE:WL_MATCH:%:%")
    private String idCard ;

    @Schema(description = "手机号", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,WHERE:WL_MATCH:%:%")
    private String mobile ;

    @Schema(description = "邮箱", example = "")
    private String email ;

    @Schema(description = "头像地址", example = "")
    private String avatar ;

    @Schema(description = "医疗组ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private Long medicalTeamId ;

    @Schema(description = "医院ID", example = "")
    @TableId
    private Long hospitalId ;

    @Schema(description = "科室ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private Long sectionId ;

    @Schema(description = "HIS系统医生ID(用于绑定)", example = "")
    @TableId
    private Long hisDoctorId ;

    @Schema(description = "职称", example = "")
    private String title ;

    @Schema(description = "擅长", example = "")
    private String goodAt ;

    @Schema(description = "简介", example = "")
    private String introduction ;

    @Schema(description = "执业信息", example = "")
    private String practiceInfo ;

    @Schema(description = "医师资格证号", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private String medicalCertificateNumber ;

    @Schema(description = "身份证正面照地址", example = "")
    private String frontIdCard ;

    @Schema(description = "身份证反面照地址", example = "")
    private String reverseIdCard ;

    @Schema(description = "执业证地址", example = "")
    private Object photoPracticeInfo ;

    @Schema(description = "医师资格证地址", example = "")
    private Object photoMedicalCertificateNumber ;

    @Schema(description = "职称证件地址", example = "")
    private Object photoTitle ;

    @Schema(description = "签名图片地址", example = "")
    private String photoSign ;

    @Schema(description = "抗菌药物等级(权限)", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private String antimicrobialDrugClass ;

    @Schema(description = "门诊医生标志", example = " 0非门诊医生, 1门诊医生")
    @TableId
    private Byte outpatientDoctorFlag ;

    @Schema(description = "备案标志", example = " 0未备案, 1已备案")
    @TableId
    private Byte fillingsFlag ;

    @Schema(description = "医责险标志", example = " 0没有, 1有")
    @TableId
    private Byte medicalInsuranceFlag ;

    @Schema(description = "实名认证标志", example = " 0未实名, 1已实名")
    @TableId
    private Byte realNameFlag ;

    @Schema(description = "CA签章标志", example = " 0不可签章, 1可签章")
    @TableId
    private Byte caFlag ;

    @Schema(description = "排序值", example = "")
    @TableLogic("FILLED:SGV_SORT")
    private Integer sort ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT:ENABLE")
    private String status ;

    @Schema(description = "删除标志；0未删，1已删", example = "")
    @TableId
    @TableLogic("FILLED:SGV_DELETE:1,FILLED:SGV_WHERE:0,FIELD_PROC:FP_GET_HIDE")
    private Byte deleteFlag ;

    @Schema(description = "hisId", example = "")
    private Long hisId ;

    @Schema(description = "自我介绍(数字医生)", example = "")
    private String selfIntroduction;

    @Schema(description = "是否团队成员；false-不是，true-是", example = "")
    private Byte teamFlag ;

    @Schema(description = "职务", example = "")
    private String positionTitle;

}
