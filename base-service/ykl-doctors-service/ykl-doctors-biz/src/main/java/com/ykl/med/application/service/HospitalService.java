package com.ykl.med.application.service;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.ykl.med.application.entity.HttpRequestData;
import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.application.entity.model.HospitalDO;
import com.ykl.med.application.mapper.ASqlExecService;
import com.ykl.med.application.repository.MapperRepository;
import com.ykl.med.config.SimpleException;
import com.ykl.med.enums.ResponseCode;

import java.text.ParseException;
import java.util.HashMap;

public class HospitalService implements IService {
    @Override
    public String serviceName() {
        return "hospital";
    }

    /**
     * 单表增删改查
     * */
    public ResponseModel crud(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, NoSuchFieldException, ParseException {
        /**
         * 加工值对象: 对象 + 传入对象 ( 第一个参数表示要转换的值对象结构 )
         * */
        MapperRepository mapperRepository = new MapperRepository();
        HttpRequestData httpRequestData = mapperRepository.inputParamsMap(new HospitalDO(), requestObj); // 获取值对象
        ResponseModel responseModel = mapperRepository.batchCheckValueObjectField(httpRequestData); // 判断必填参数
        if ( responseModel.getCode() != 0 ){
            return responseModel;
        }

        /**
         * 获取SQL执行服务, 并执行值对象
         * */
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        return aSqlExecService.sqlExec(httpRequestData.getValueObjectList().get(0),httpRequestData.getPageRecord());
    }
}
