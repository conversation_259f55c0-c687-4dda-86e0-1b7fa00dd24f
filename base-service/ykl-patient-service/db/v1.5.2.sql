CREATE TABLE `ykl_patient`.`t_patient_device` (
    `id` bigint AUTO_INCREMENT  COMMENT '唯一标识',
    `device_type` VARCHAR(100) NOT NULL COMMENT '设备类型',
    `patient_id` bigint NOT NULL COMMENT '患者id',
    `phone_device_no` VARCHAR(255) NOT NULL COMMENT '手机设备编号',
    `device_no` VARCHAR(255) NOT NULL COMMENT '设备编号',
    `name` VARCHAR(255) NOT NULL COMMENT '设备名称',
    `manufacturer_name`  VARCHAR(255) NOT NULL COMMENT '厂商',
    `spec`  VARCHAR(255) NOT NULL COMMENT '规格',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志；0未删，1已删',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_patient_id` (`patient_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='患者-设备管理';
