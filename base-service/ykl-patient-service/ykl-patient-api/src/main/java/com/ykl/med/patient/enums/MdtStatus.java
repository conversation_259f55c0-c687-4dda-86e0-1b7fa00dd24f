package com.ykl.med.patient.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "MDT会诊状态枚举,可用值:WAIT(待接收),RECEIVED(会诊中),END(已结束),CANCEL(已撤回)")
public enum MdtStatus {
    WAIT(1, "待接收"),
    RECEIVED(2, "会诊中"),
    END(3, "已结束"),
    CANCEL(4, "已撤回");
    @EnumValue
    private final int code;
    private final String desc;
}
