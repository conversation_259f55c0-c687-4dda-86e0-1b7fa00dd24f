package com.ykl.med.patient.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * MDT会诊的紧急程度
 */
@Getter
@AllArgsConstructor
@Schema(description = "MDT会诊的紧急程度，可选值：NORMAL(1, \"平\"), URGENT(2, \"急\")")
public enum MdtLevel {
    NORMAL(1, "平"),
    URGENT(2, "急");
    @EnumValue
    private final int code;
    private final String desc;
}
