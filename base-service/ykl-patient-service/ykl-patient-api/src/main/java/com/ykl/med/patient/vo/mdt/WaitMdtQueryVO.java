package com.ykl.med.patient.vo.mdt;

import com.ykl.med.framework.common.interfaces.AutoBuildMedicalTeamId;
import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

@Data
@Schema(description = "MDT等待会诊列表查询对象")
public class WaitMdtQueryVO extends PageParam implements AutoBuildUserId, AutoBuildMedicalTeamId {
    @Schema(description = "当前登录用户id", defaultValue = "1",hidden = true)
    private Long currentUserId;
    @Schema(description = "当前登录用户所在医生团队id", defaultValue = "1",hidden = true)
    private Long currentMedicalTeamId;
    @Schema(description = "当前登录用户所在医生团队id集合", defaultValue = "1",hidden = true)
    private Set<Long> currentMedicalTeamIds;
}
