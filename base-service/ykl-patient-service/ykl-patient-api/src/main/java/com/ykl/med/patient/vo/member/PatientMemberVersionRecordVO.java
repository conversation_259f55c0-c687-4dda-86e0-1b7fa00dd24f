package com.ykl.med.patient.vo.member;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "会员版本变更记录")
public class PatientMemberVersionRecordVO {
    @Schema(description = "患者id")
    @Stringify
    private Long patientId;

    @Schema(description = "医疗组Id")
    @Stringify
    private Long medicalTeamId;

    @Schema(description = "会员版本id")
    @Stringify
    private Long memberVersionId;

    @Schema(description = "购买时长，毫秒级")
    @Stringify
    private Long addTime;

    @Schema(description = "订单号")
    private String outTradeNo;

    @Schema(description = "本次购买前的到期时间")
    @TimestampConvert
    private LocalDateTime startExpireTime;

    @Schema(description = "本次购买后的到期时间")
    @TimestampConvert
    private LocalDateTime endExpireTime;

    @Schema(description = "是否绑定")
    private Boolean isBind;
}
