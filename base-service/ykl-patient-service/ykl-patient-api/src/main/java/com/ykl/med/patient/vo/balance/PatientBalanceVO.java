package com.ykl.med.patient.vo.balance;

import com.ykl.med.framework.common.enums.MemberServicePackageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "患者余额对象")
public class PatientBalanceVO {
    @Schema(description = "患者ID", example = "123456")
    private Long patientId;

    @Schema(description = "服务包类型", example = "EXPERT_ADVICE")
    private MemberServicePackageType servicePackageType;

    @Schema(description = "服务包id，如果是线上视频门诊，就是挂号类型id，如果是通用的，就是0", example = "123456")
    private Long servicePackageId = 0L;

    @Schema(description = "服务包名称", example = "高级专家")
    private String servicePackageName;

    @Schema(description = "余额", example = "500")
    private Integer balance;
}
