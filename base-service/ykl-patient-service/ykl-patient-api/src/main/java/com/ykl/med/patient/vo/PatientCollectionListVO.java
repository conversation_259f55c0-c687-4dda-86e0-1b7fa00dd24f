package com.ykl.med.patient.vo;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.patient.enums.PatientCollectionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "患者收藏")
public class PatientCollectionListVO {
    @Schema(description = "患者收藏类型")
    private PatientCollectionType type;

    @Schema(description = "外部ID", example = "1a2b3c4d")
    private String outBizId;

    @Schema(description = "内容", example = "内容")
    private String content;

    @TimestampConvert
    @Schema(description = "创建时间", example = "2021-01-01 00:00:00")
    private LocalDateTime createTime;
}
