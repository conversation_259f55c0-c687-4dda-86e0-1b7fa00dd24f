package com.ykl.med.patient.vo;

import com.ykl.med.framework.common.json.DateStringConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@Schema(description = "患者信息更改请求")
public class PatientUpdateReqVO {
    @Schema(description = "添加者用户id", defaultValue = "1", hidden = true)
    @NotNull(message = "添加者用户id不能为空")
    private Long userId;

    @Schema(description = "患者id", defaultValue = "1", nullable = true)
    private Long patientId;

    @Schema(description = "患者姓名", defaultValue = "脏东西", nullable = true, minLength = 1, maxLength = 64)
    @NotBlank(message = "患者姓名不能为空")
    @Size(max = 64, message = "患者姓名长度不能超过64")
    @Size(min = 1, message = "患者姓名长度不能小于1")
    private String name;

    @Schema(description = "证件号", defaultValue = "523441234324324", maxLength = 64)
    //@Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$", message = "证件号格式不正确")
    @Pattern(regexp = "^(|^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx])$", message = "证件号格式不正确")
    private String licenseNumber;

    @Schema(description = "证件类型", defaultValue = "idcard")
    //@NotBlank(message = "证件类型不能为空")
    private String licenseType;

    @Schema(description = "性别", defaultValue = "male")
    @NotBlank(message = "性别不能为空")
    private String sex;

    @Schema(description = "生日(毫秒级时间戳)", defaultValue = "1618113920000")
    @DateStringConvert
    private LocalDate birthday;

    @Schema(description = "病种", defaultValue = "病种")
//    @NotBlank(message = "病种不能为空")
    private String diseases;

    @Schema(description = "病种阶段", defaultValue = "病种阶段")
    private String stage;

    @Schema(description = "民族", defaultValue = "汉族")
    //@NotBlank(message = "民族不能为空")
    private String nationality;

    @Schema(description = "婚姻状况", defaultValue = "已婚已育")
    //@NotBlank(message = "婚姻状况不能为空")
    private String maritalStatus;

    @Schema(description = "联系电话", defaultValue = "18120912312")
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    private String contactPhone;

    @Schema(description = "身高(cm)", defaultValue = "165.1")
    @NotNull(message = "身高不能为空")
    @Min(value = 0, message = "身高不能小于0")
    @Max(value = 300, message = "身高不能大于300")
    private BigDecimal height;

    @Schema(description = "体重(kg)", defaultValue = "65")
     private BigDecimal weight;

    @Schema(description = "省", defaultValue = "四川")
    //@NotBlank(message = "省不能为空")
    private String province;
    @Schema(description = "市", defaultValue = "成都")
    //@NotBlank(message = "市不能为空")
    private String city;
    @Schema(description = "区", defaultValue = "高新西区")
    private String district;

    @Schema(description = "联系地址", defaultValue = "成都高新西区")
    //@NotBlank(message = "联系地址不能为空")
    private String contactAddress;

    @Schema(description = "职业", defaultValue = "棒球手")
    private String profession;

    @Schema(description = "当前劳动等级", defaultValue = "普通")
    private String laborCapacity;

    @Schema(description = "运动偏好", defaultValue = "棒球")
    private List<String> exercisePreferences;

    @Schema(description = "该手机号与患者关系", defaultValue = "女儿", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "该手机号与患者关系不能为空")
    private String relation;

    @Schema(description = "血型", defaultValue = "A")
    private String bloodType;



    @Schema(description = "添加者用户手机号", defaultValue = "1", hidden = true)
    @NotNull(message = "添加者用户手机号")
    private String userPhone;

    @Schema(description = "居住状态", defaultValue = "居住状态")
    private String livingStatus;
}
