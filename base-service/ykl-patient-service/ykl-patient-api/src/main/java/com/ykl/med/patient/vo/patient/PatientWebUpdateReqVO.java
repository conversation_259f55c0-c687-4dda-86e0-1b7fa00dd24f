package com.ykl.med.patient.vo.patient;

import com.ykl.med.framework.common.json.DateStringConvert;
import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@Schema(description = "患者web端修修改信息请求对象")
public class PatientWebUpdateReqVO {
    @Stringify
    @Schema(description = "患者码", defaultValue = "100043", example = "100050")
    private Long patientId;

    @Schema(description = "名字", defaultValue = "患者名字", example = "张三")
    @NotBlank(message = "患者姓名不能为空")
    @Size(max = 64, message = "患者姓名长度不能超过64")
    @Size(min = 1, message = "患者姓名长度不能小于1")
    private String name;

    @Schema(description = "许可证号", example = "**********")
    //@Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$", message = "证件号格式不正确")
    @Pattern(regexp = "^(|^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx])$", message = "证件号格式不正确")
    private String licenseNumber;

    @Schema(description = "许可证类型", example = "类型A")
    //@NotBlank(message = "证件类型不能为空")
    private String licenseType;

    @Schema(description = "性别", defaultValue = "male", example = "female")
    @NotBlank(message = "性别不能为空")
    private String sex;

    @DateStringConvert
    @Schema(description = "生日", example = "1632928061000")
    @NotNull(message = "生日不能为空")
    private LocalDate birthday;

    @Schema(description = "民族", example = "汉族")
    //@NotBlank(message = "民族不能为空")
    private String nationality;

    @Schema(description = "婚姻状况", example = "已婚")
    //@NotBlank(message = "婚姻状况不能为空")
    private String maritalStatus;

    @Schema(description = "联系电话", example = "12345678910")
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    private String contactPhone;

    @Schema(description = "身高 (cm)", example = "170")
    @NotNull(message = "身高不能为空")
    @Min(value = 0, message = "身高不能小于0")
    @Max(value = 300, message = "身高不能大于300")
    private BigDecimal height;

    @Schema(description = "体重 (kg)", example = "65")
//    @NotNull(message = "体重不能为空")
//    @Min(value = 0, message = "体重不能小于0")
//    @Max(value = 1000, message = "体重不能大于1000")
    private BigDecimal weight;

    @Schema(description = "省", defaultValue = "四川")
    //@NotBlank(message = "省不能为空")
    private String province;
    @Schema(description = "市", defaultValue = "成都")
    //@NotBlank(message = "市不能为空")
    private String city;
    @Schema(description = "区", defaultValue = "高新西区")
    private String district;

    @Schema(description = "联系地址", example = "北京市朝阳区")
    //@NotBlank(message = "联系地址不能为空")
    private String contactAddress;

    @Schema(description = "职业", example = "程序员")
    private String profession;

    @Schema(description = "当前劳动等级", example = "高水平")
    private String laborCapacity;

    @Schema(description = "运动偏好", example = "足球")
    private List<String> exercisePreferences;

    @Schema(description = "血型", defaultValue = "A")
    private String bloodType;

    @Schema(description = "居住状态", defaultValue = "居住状态")
    private String livingStatus;
}
