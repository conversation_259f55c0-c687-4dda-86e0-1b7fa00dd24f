package com.ykl.med.patient.vo.mdt;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "MDT会诊改状态请求对象")
public class MdtEndReqVO extends CommonDoctorIdVO {
    @Schema(description = "mdt记录id", example = "1",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "mdt记录id不能为空")
    private Long mdtRecordId;

    @Schema(description = "建议", example = "这是一个示例建议")
    private String suggestion;
}
