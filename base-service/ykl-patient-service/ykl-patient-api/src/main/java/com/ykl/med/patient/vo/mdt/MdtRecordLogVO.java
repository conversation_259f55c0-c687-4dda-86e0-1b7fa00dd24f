package com.ykl.med.patient.vo.mdt;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "MDT会诊的患者信息对象")
public class MdtRecordLogVO {
    @Schema(description = "会诊意见", defaultValue = "xxxx")
    private String suggestion;
    @Stringify
    @Schema(description = "接受会诊的医生id", defaultValue = "1")
    private Long receiveUserId;
    @Schema(description = "接受会诊的医生姓名", defaultValue = "张三")
    private String receiveUserName;
    @TimestampConvert
    @Schema(description = "建议时间", defaultValue = "xxxxx")
    private LocalDateTime updateTime;
}
