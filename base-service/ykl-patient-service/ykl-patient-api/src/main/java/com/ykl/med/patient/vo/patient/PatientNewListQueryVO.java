package com.ykl.med.patient.vo.patient;

import com.ykl.med.framework.common.interfaces.AutoBuildMedicalTeamId;
import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

@Data
@Schema(description = "新加入列表的患者信息查询请求")
public class PatientNewListQueryVO extends PageParam implements AutoBuildMedicalTeamId, AutoBuildUserId {
    @Schema(description = "用户id", defaultValue = "1", hidden = true)
    private Long currentUserId;

    @Schema(description = "医生ID", defaultValue = "1", hidden = true)
    private Long currentMedicalTeamId;

    @Schema(description = "医疗团队ID", hidden = true)
    private Set<Long> currentMedicalTeamIds;

    @Schema(description = "是否按照患者加入时间升序排,默认升序")
    private Boolean memberVersionTimeAsc = true;
}
