package com.ykl.med.patient.vo.member;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "会员版本变更记录详细信息")
public class PatientMemberVersionRecordDetailVO extends PatientMemberVersionRecordVO {
    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "支付时间")
    @TimestampConvert
    private LocalDateTime payTime;

    @Schema(description = "是否过期")
    private Boolean isExpire;


    @Schema(description = "医疗组名称")
    private String medicalTeamName;
}
