package com.ykl.med.patient.vo;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.DateStringConvert;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.patient.enums.PatientStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "患者信息")
public class PatientVO {
    @Schema(description = "患者状态")
    private PatientStatus status;

    @Schema(description = "会员状态")
    private CommonStatusEnum memberStatus;

    @Schema(description = "医疗团队ID，从第一次开通会员后就一直存在，表示归属于哪个医疗组管,会随着用户变更会员而变更,但是不会消失")
    @Stringify
    private Long medicalTeamId;

    @Schema(description = "会员申请时间", defaultValue = "2021-04-01 12:00:00")
    @TimestampConvert
    private LocalDateTime memberVersionTime;

    @Schema(description = "绑定的医生Id（主治医师），从第一次绑定后就存在，解绑后剔除")
    @Stringify
    private Long bindDoctorId;

    @Stringify
    @Schema(description = "患者会员码", defaultValue = "1")
    private Long patientId;

    @Schema(description = "患者姓名", defaultValue = "脏东西")
    private String name;

    @Schema(description = "姓名简码", defaultValue = "zdx")
    private String nameCode;

    @Schema(description = "证件号", defaultValue = "523441234324324")
    private String licenseNumber;

    @Schema(description = "证件类型", defaultValue = "idcard")
    private String licenseType;

    @Schema(description = "性别", defaultValue = "male")
    private String sex;

    @DateStringConvert
    @Schema(description = "生日", defaultValue = "1618113920000")
    private LocalDate birthday;

    @Schema(description = "民族", defaultValue = "汉族")
    private String nationality;

    @Schema(description = "婚姻状况", defaultValue = "已婚已育")
    private String maritalStatus;

    @Schema(description = "联系电话", defaultValue = "18120912312")
    private String contactPhone;

    @Schema(description = "身高(cm)", defaultValue = "165.1")
    private BigDecimal height;

    @Schema(description = "省", defaultValue = "四川")
    private String province;
    @Schema(description = "市", defaultValue = "成都")
    private String city;
    @Schema(description = "区", defaultValue = "高新西区")
    private String district;

    @Schema(description = "联系地址", defaultValue = "成都高新西区")
    private String contactAddress;

    @Schema(description = "职业", defaultValue = "棒球手")
    private String profession;

    @Schema(description = "当前劳动等级", defaultValue = "普通")
    private String laborCapacity;

    @Schema(description = "运动偏好", defaultValue = "棒球")
    private List<String> exercisePreferences;

    @Schema(description = "年纪", defaultValue = "年纪")
    private Integer age;

    @Schema(description = "血型", defaultValue = "A")
    private String bloodType;

    @Schema(description = "居住状态", defaultValue = "居住状态")
    private String livingStatus;
}
