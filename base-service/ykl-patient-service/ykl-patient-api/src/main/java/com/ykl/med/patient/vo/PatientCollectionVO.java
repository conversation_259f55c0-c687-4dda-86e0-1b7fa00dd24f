package com.ykl.med.patient.vo;

import com.ykl.med.framework.common.interfaces.AutoBuildPatientId;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.patient.enums.PatientCollectionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "患者收藏")
public class PatientCollectionVO implements AutoBuildPatientId {

    @Schema(description = "患者ID", example = "**********", hidden = true)
    @Stringify
    @NotNull(message = "患者ID不能为空")
    private Long currentPatientId;

    @Schema(description = "患者收藏类型")
    @NotNull(message = "患者收藏类型不能为空")
    private PatientCollectionType type;

    @Schema(description = "外部ID", example = "1a2b3c4d")
    @NotNull(message = "外部ID不能为空")
    private String outBizId;
}
