package com.ykl.med.patient.api;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.patient.vo.PatientUserDetailVO;
import com.ykl.med.patient.vo.patient.PatientDoctorQueryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * 这个FeignClient接口用于管理医患之间的关系.
 */
@FeignClient(name = "ykl-patient-service", path = "patientDoctor")
public interface PatientDoctorFeign {

    /**
     * 绑定医生和病人的关系.
     *
     * @param doctorId  医生的ID
     * @param patientId 病人的ID
     */
    @PostMapping("/patientDoctorBind")
    void patientDoctorBind(@RequestParam(value = "doctorId") Long doctorId,
                           @RequestParam(value = "patientId") Long patientId);

    /**
     * 解除医生和病人的关系.
     *
     * @param doctorId  医生的ID
     * @param patientId 病人的ID
     */
    @PostMapping("/patientDoctorUnBind")
    void patientDoctorUnBind(@RequestParam(value = "doctorId") Long doctorId, @RequestParam(value = "patientId") Long patientId);

    /**
     * 解除医生与他所有病人的关系.
     *
     * @param doctorId 医生的ID
     */
    @PostMapping("/doctorUnBind")
    void doctorUnBind(@RequestParam(value = "doctorId") Long doctorId);

    /**
     * 获取一个医生的所有病人的详细信息.
     *
     * @param queryVO 包含了获取数据所需要的信息
     * @return 所有匹配的病人的信息
     */
    @PostMapping("/getPatientUserByDoctor")
    PageResult<PatientUserDetailVO> getPatientUserByDoctor(@RequestBody @Valid PatientDoctorQueryVO queryVO);

    /**
     * 检查医生和病人是否有医患关系.
     *
     * @param patientId        患者的ID
     * @param commonDoctorIdVO 包含医生ID的对象
     * @return 如果医生和病人有关系则返回true, 否则返回false
     */
    @PostMapping("/checkPatientDoctorPermissions")
    Boolean checkPatientDoctorPermissions(@RequestParam(value = "patientId") Long patientId,
                                          @RequestParam(value = "needMember") Boolean needMember,
                                          @RequestBody CommonDoctorIdVO commonDoctorIdVO);

    /**
     * 获取病人的医生的ID.
     *
     * @param patientId 病人的ID
     * @return 医生的ID
     */
    @PostMapping("/getDoctorIdByPatientId")
    Long getDoctorIdByPatientId(@RequestParam(value = "patientId") Long patientId);


    /**
     * 获取医生绑定的患者个数
     */
    @PostMapping("/getPatientCountByDoctorId")
    Long getPatientCountByDoctorId(@RequestParam(value = "doctorId") Long doctorId);
}

