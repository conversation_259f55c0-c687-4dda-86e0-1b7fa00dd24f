package com.ykl.med.patient.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "时间周期类型, FOREVER(永久), DAILY(每日), MONTHLY(每月), YEAR(每年)")
public enum TimePeriodType {
    FOREVER(1, "永久"),
    DAILY(2, "每日"),
    MONTHLY(3, "每月"),
    YEAR(4, "每年");
    @EnumValue
    private final int code;
    private final String desc;
}
