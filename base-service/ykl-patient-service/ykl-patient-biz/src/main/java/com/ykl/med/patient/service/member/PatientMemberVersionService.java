package com.ykl.med.patient.service.member;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.patient.vo.member.*;
import com.ykl.med.patient.vo.patient.PatientWaitBindVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface PatientMemberVersionService {


    Map<Long,List<MemberVersionVO>> getMemberVersionMapByPatientIds(List<Long> patientIds);

    void addPatientMemberVersion(PatientMemberVersionReqVO reqVO);

    void changePatientMemberVersionBind(Long patientId, Long medicalTeamId);

    PatientMemberVersionVO getMemberVersionByPatient(Long patientId);

    PatientMemberVersionSimpleVO getLastMemberVersionByPatient(Long patientId);

    PatientMemberVersionVO getMemberVersionByUserId(Long userId);

    /**
     * 获取患者等待绑定页面信息
     *
     * @param patientId 患者ID
     * @return 患者等待绑定页面信息
     */
    PatientWaitBindVO getPatientWaitBindInfo(Long patientId);


    PageResult<PatientMemberVersionVO> getPatientByMemberVersionId(Long memberVersionId, Integer page, Integer pageSize);

    List<Long> getMedicalTeamIds(Long patientId);

    /**
     * 获取患者会员版本变更记录列表
     *
     * @param queryVO 查询条件
     * @return 患者会员版本变更记录列表
     */
    PageResult<PatientMemberVersionRecordVO> getPatientMemberVersionRecord(PatientMemberVersionRecordQueryVO queryVO);
}
