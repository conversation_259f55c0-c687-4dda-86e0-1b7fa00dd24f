package com.ykl.med.patient.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.patient.enums.PatientCollectionType;
import lombok.Data;

@TableName("t_patient_collection")
@Data
public class PatientCollectionDO extends BaseDO {

    private Long patientId;

    private PatientCollectionType type;

    /**
     * 外部id
     */
    private String outBizId;
}
