package com.ykl.med.patient.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.patient.vo.member.MemberServicePackageVO;
import lombok.Data;

import java.util.List;

@TableName(value = "t_member_version", autoResultMap = true)
@Data
public class MemberVersionDO extends BaseDO {
    private String name;
    private Integer weights;

    private String purviewList;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<MemberServicePackageVO> packageList;

    private CommonStatusEnum status;
}
