package com.ykl.med.patient.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.MemberServicePackageType;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

@TableName("t_patient_balance")
@Data
public class PatientBalanceDO extends BaseDO {
    private Long patientId;
    private MemberServicePackageType servicePackageType;
    /**
     * 服务包id，如果是线上视频门诊，就是挂号类型id，如果是通用的，就是0
     * 如果是专家咨询就是医疗组id
     */
    private Long servicePackageId;
    private Integer balance;
}
