package com.ykl.med.patient.service.doctor;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.patient.vo.PatientUserDetailVO;
import com.ykl.med.patient.vo.patient.PatientDoctorQueryVO;

public interface PatientDoctorService {
    /**
     * 医患绑定
     * @param doctorId 医生id
     * @param patientId 患者id
     */
    void patientDoctorBind(Long doctorId, Long patientId);

    /**
     * 医患解绑
     * @param doctorId 医生id
     * @param patientId 患者id
     */
    void patientDoctorUnBind(Long doctorId, Long patientId);

    void doctorUnBind(Long doctorId);

    PageResult<PatientUserDetailVO> getPatientUserByDoctor(PatientDoctorQueryVO queryVO);

    Boolean checkPatientDoctorPermissions(CommonDoctorIdVO commonDoctorIdVO, Long patientId,Boolean needMember);

    Long getDoctorIdByPatientId(Long patientId);

    /**
     * 获取医生绑定的患者个数
     */
    Long getPatientCountByDoctorId(Long doctorId);
}
