package com.ykl.med.patient.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.type.EncryptTypeHandler;
import lombok.Data;

@TableName(value = "t_patient_user", autoResultMap = true)
@Data
public class PatientUserDO extends BaseDO {
    private Long patientId;
    private Long userId;
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String phone;
    private String relation;
}
