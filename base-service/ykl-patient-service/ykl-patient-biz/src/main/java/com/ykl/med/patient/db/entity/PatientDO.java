package com.ykl.med.patient.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.EncryptTypeHandler;
import com.ykl.med.framework.mybatis.core.type.StringListTypeHandler;
import com.ykl.med.patient.enums.PatientStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@TableName(value = "t_patient", autoResultMap = true)
@Data
public class PatientDO extends BaseDO {

    /**
     * 状态
     */
    private PatientStatus status = PatientStatus.WAIT_BIND;
    /**
     * 会员状态
     */
    private CommonStatusEnum memberStatus = CommonStatusEnum.DISABLE;

    /**
     * 医疗团队ID，从第一次开通会员后就一直存在，表示归属于哪个医疗组管
     * 会随着用户变更会员而变更,但是不会消失
     */
    private Long medicalTeamId;
    /**
     * 会员申请时间，从第一次开通会员后就一直存在
     */
    private LocalDateTime memberVersionTime;
    /**
     * 绑定的医生Id（主治医师），从第一次绑定后就存在，解绑后剔除
     */
    private Long bindDoctorId;

    /**
     * 姓名
     */
    private String name;
    /**
     * 姓名简码
     */
    private String nameCode;

    /**
     * 证件号
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String licenseNumber;
    /**
     * 证件类型
     */
    private String licenseType;
    /**
     * 性别
     */
    private String sex;
    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 民族
     */
    private String nationality;
    /**
     * 婚姻状况
     */
    private String maritalStatus;
    /**
     * 联系电话
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String contactPhone;
    /**
     * 身高(cm)
     */
    private BigDecimal height;

    /**
     * 省市区
     */
    private String province;
    private String city;
    private String district;

    /**
     * 联系地址
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String contactAddress;
    /**
     * 职业
     */
    private String profession;
    /**
     * 当前劳动等级
     */
    private String laborCapacity;

    /**
     * 运动偏好
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> exercisePreferences;

    /**
     * 血型
     */
    private String bloodType;


    /**
     * 居住状态
     */
    private String livingStatus;
}
