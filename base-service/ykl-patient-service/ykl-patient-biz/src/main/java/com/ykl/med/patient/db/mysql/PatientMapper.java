package com.ykl.med.patient.db.mysql;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.type.EncryptTypeHandler;
import com.ykl.med.patient.db.entity.PatientDO;
import com.ykl.med.patient.db.entity.PatientMemberVersionDO;
import com.ykl.med.patient.db.entity.otherdb.BasicReportDO;
import com.ykl.med.patient.enums.PatientStatus;
import com.ykl.med.patient.vo.patient.PatientAdminListQueryVO;
import com.ykl.med.patient.vo.patient.PatientListQueryVO;
import com.ykl.med.patient.vo.patient.PatientNewListQueryVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

@Mapper
public interface PatientMapper extends MPJBaseMapper<PatientDO> {
    default Page<PatientDO> queryPatientList(PatientListQueryVO reqVO) {
        Long doctorId = reqVO.getOnlyMe() ? reqVO.getCurrentUserId() : null;
        MPJLambdaWrapper<PatientDO> wrapper = JoinWrappers.lambda(PatientDO.class)
                .selectAll(PatientDO.class)
                .leftJoin(BasicReportDO.class, BasicReportDO::getPatientId, PatientDO::getId)
                .eq(doctorId != null, PatientDO::getBindDoctorId, doctorId)
                .eq(reqVO.getMedicalTeamId() != null, PatientDO::getMedicalTeamId, reqVO.getMedicalTeamId())
                .and(StringUtils.isNotEmpty(reqVO.getWord()), wrapper1 -> wrapper1
                        .like(PatientDO::getName, reqVO.getWord())
                        .or()
                        .eq(PatientDO::getNameCode, reqVO.getWord())
                        .or()
                        .eq(PatientDO::getId, reqVO.getWord())
                        .or()
                        .eq(PatientDO::getContactPhone, EncryptTypeHandler.encrypt(reqVO.getWord())))
                .eq(StringUtils.isNoneEmpty(reqVO.getStage()), BasicReportDO::getStage, reqVO.getStage())
                .eq(reqVO.getDiseasesId() != null, BasicReportDO::getDiseaseId, reqVO.getDiseasesId())
                .eq(StringUtils.isNoneEmpty(reqVO.getPeriod()), BasicReportDO::getClinicalStaging, reqVO.getPeriod())
                .eq(reqVO.getStatus() != null, PatientDO::getStatus, reqVO.getStatus())
                .eq(StringUtils.isNoneEmpty(reqVO.getDiagnosisDoctor()), BasicReportDO::getDiagnosisDoctor,
                        reqVO.getDiagnosisDoctor())
                .gt(reqVO.getStartTime() != null, PatientDO::getMemberVersionTime, reqVO.getStartTime())
                .lt(reqVO.getEndTime() != null, PatientDO::getMemberVersionTime, reqVO.getEndTime())
                .eq(PatientDO::getMemberStatus, CommonStatusEnum.ENABLE)
                .groupBy(PatientDO::getId);
        if (reqVO.getTimeDesc()) {
            wrapper.orderByDesc(PatientDO::getMemberVersionTime);
        } else {
            wrapper.orderByAsc(PatientDO::getMemberVersionTime);
        }
        return this.selectJoinPage(new Page<>(reqVO.getPageNo(), reqVO.getPageSize()), PatientDO.class, wrapper);
    }

    default Page<PatientDO> queryAdminPage(PatientAdminListQueryVO reqVO) {
        MPJLambdaWrapper<PatientDO> wrapper = JoinWrappers.lambda(PatientDO.class)
                .selectAll(PatientDO.class)
                .leftJoin(PatientMemberVersionDO.class, PatientMemberVersionDO::getPatientId, PatientDO::getId)
                .leftJoin(BasicReportDO.class, BasicReportDO::getPatientId, PatientDO::getId)
                .eq(reqVO.getDoctorId() != null, PatientDO::getBindDoctorId, reqVO.getDoctorId())
                .eq(reqVO.getMedicalTeamId() != null, PatientDO::getMedicalTeamId, reqVO.getMedicalTeamId())
                .and(StringUtils.isNotEmpty(reqVO.getWord()), wrapper1 -> wrapper1
                        .like(PatientDO::getName, reqVO.getWord())
                        .or()
                        .eq(PatientDO::getNameCode, reqVO.getWord())
                        .or()
                        .eq(PatientDO::getId, reqVO.getWord())
                        .or()
                        .eq(PatientDO::getContactPhone, EncryptTypeHandler.encrypt(reqVO.getWord())))
                .eq(StringUtils.isNoneEmpty(reqVO.getStage()), BasicReportDO::getStage, reqVO.getStage())
                .eq(reqVO.getDiseasesId() != null, BasicReportDO::getDiseaseId, reqVO.getDiseasesId())
                .eq(StringUtils.isNoneEmpty(reqVO.getPeriod()), BasicReportDO::getClinicalStaging, reqVO.getPeriod())
                .eq(reqVO.getStatus() != null, PatientDO::getStatus, reqVO.getStatus())
                .gt(reqVO.getMemberStartTime() != null, PatientDO::getMemberVersionTime, reqVO.getMemberStartTime())
                .lt(reqVO.getMemberEndTime() != null, PatientDO::getMemberVersionTime, reqVO.getMemberEndTime())
                .gt(reqVO.getRegisterStartTime() != null, PatientDO::getCreateTime, reqVO.getRegisterStartTime())
                .lt(reqVO.getRegisterEndTime() != null, PatientDO::getCreateTime, reqVO.getRegisterEndTime());
        if (reqVO.getMemberVersionId() != null) {
            if (reqVO.getMemberVersionId() == 0L) {
                wrapper.having("MAX(expire_time) < {0} OR MAX(expire_time) IS NULL", LocalDateTime.now());
            } else {
                wrapper.eq(PatientMemberVersionDO::getMemberVersionId, reqVO.getMemberVersionId());
                wrapper.gt(PatientMemberVersionDO::getExpireTime, LocalDateTime.now());
            }
        }
        wrapper.groupBy(PatientDO::getId);
        if (reqVO.getMemberTimeDesc()) {
            wrapper.orderByDesc(PatientDO::getMemberVersionTime);
        } else {
            wrapper.orderByAsc(PatientDO::getMemberVersionTime);
        }

        if (reqVO.getRegisterTimeDesc()) {
            wrapper.orderByDesc(PatientDO::getCreateTime);
        } else {
            wrapper.orderByAsc(PatientDO::getCreateTime);
        }
        return this.selectJoinPage(new Page<>(reqVO.getPageNo(), reqVO.getPageSize()), PatientDO.class, wrapper);
    }


    default Page<PatientDO> queryPatientNewList(PatientNewListQueryVO queryVO) {
        // 查询会员没过期，且状态为待绑定、待制定、待数据的患者
        LambdaQueryWrapper<PatientDO> wrapper = buildNewListWrapper(queryVO);
        if (queryVO.getMemberVersionTimeAsc()) {
            wrapper.orderByAsc(PatientDO::getMemberVersionTime);
        } else {
            wrapper.orderByDesc(PatientDO::getMemberVersionTime);
        }
        return this.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), wrapper);
    }

    default Long queryPatientNewListCount(PatientNewListQueryVO queryVO) {
        LambdaQueryWrapper<PatientDO> wrapper = buildNewListWrapper(queryVO);
        return this.selectCount(wrapper);
    }

    default LambdaQueryWrapper<PatientDO> buildNewListWrapper(PatientNewListQueryVO queryVO) {
        return new LambdaQueryWrapper<>(PatientDO.class)
                .and(wrapper1 -> wrapper1
                        .or(wrapper2 -> wrapper2.in(PatientDO::getMedicalTeamId, queryVO.getCurrentMedicalTeamIds())
                                .eq(PatientDO::getStatus, PatientStatus.WAIT_BIND))
                        .or(wrapper2 -> wrapper2.eq(PatientDO::getBindDoctorId, queryVO.getCurrentUserId())
                                .in(PatientDO::getStatus,
                                        Lists.newArrayList(PatientStatus.WAIT_DATA, PatientStatus.WAIT_PLAN))))
                .eq(PatientDO::getMemberStatus, CommonStatusEnum.ENABLE);
    }
}
