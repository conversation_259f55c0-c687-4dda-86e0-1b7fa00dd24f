package com.ykl.med.patient.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.patient.api.MemberVersionFeign;
import com.ykl.med.patient.service.member.MemberVersionService;
import com.ykl.med.patient.service.member.PatientMemberVersionService;
import com.ykl.med.patient.service.patient.PatientService;
import com.ykl.med.patient.vo.member.*;
import com.ykl.med.patient.vo.patient.PatientWaitBindVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "会员服务")
@RestController
@RequestMapping("/member")
@Validated
public class MemberVersionController implements MemberVersionFeign {
    @Resource
    private PatientMemberVersionService patientMemberVersionService;
    @Resource
    private MemberVersionService memberVersionService;
    @Resource
    private PatientService patientService;


    @PostMapping("/saveOrUpdate")
    @Override
    public void saveOrUpdate(@RequestBody @Valid MemberVersionReqVO vo) {
        memberVersionService.saveOrUpdate(vo);
    }

    @PostMapping("/getAllMemberVersion")
    @Override
    public List<MemberVersionVO> getAllMemberVersion() {
        return memberVersionService.getAllMemberVersion();
    }

    @PostMapping("/getMemberVersionById")
    @Override
    public MemberVersionVO getMemberVersionById(@RequestParam(value = "id") Long id) {
        return memberVersionService.getMemberVersionByIdRedis(id);
    }

    @PostMapping("/addPatientMemberVersion")
    @Override
    public void addPatientMemberVersion(@RequestBody @Valid PatientMemberVersionReqVO reqVO) {
        patientMemberVersionService.addPatientMemberVersion(reqVO);
    }

    @PostMapping("/changePatientMemberVersionBind")
    @Override
    public void changePatientMemberVersionBind(@RequestParam(value = "patientId") Long patientId, @RequestParam(value = "medicalTeamId") Long medicalTeamId) {
        patientMemberVersionService.changePatientMemberVersionBind(patientId, medicalTeamId);
    }

    @PostMapping("/getMemberVersionByPatient")
    @Override
    public PatientMemberVersionVO getMemberVersionByPatient(@RequestParam(value = "patientId") Long patientId) {
        return patientMemberVersionService.getMemberVersionByPatient(patientId);
    }

    @PostMapping("/getMemberVersionByUserId")
    @Override
    public PatientMemberVersionVO getMemberVersionByUserId(@RequestParam(value = "userId") Long userId) {
        return patientMemberVersionService.getMemberVersionByUserId(userId);
    }

    @PostMapping("/getPatientWaitBindInfo")
    @Override
    public PatientWaitBindVO getPatientWaitBindInfo(@RequestParam(value = "patientId") Long patientId) {
        return patientMemberVersionService.getPatientWaitBindInfo(patientId);
    }


    @PostMapping("/getPatientByMemberVersionId")
    @Override
    public PageResult<PatientMemberVersionVO> getPatientByMemberVersionId(@RequestParam(value = "memberVersionId") Long memberVersionId,
                                                                          @RequestParam(value = "page") Integer page,
                                                                          @RequestParam(value = "pageSize") Integer pageSize) {
        return patientMemberVersionService.getPatientByMemberVersionId(memberVersionId, page, pageSize);
    }

    @PostMapping("/getMedicalTeamIds")
    @Override
    public List<Long> getMedicalTeamIds(@RequestParam(value = "patientId") Long patientId) {
        return patientMemberVersionService.getMedicalTeamIds(patientId);
    }

    @PostMapping("/getLastMemberVersionByPatient")
    @Override
    public PatientMemberVersionSimpleVO getLastMemberVersionByPatient(@RequestParam(value = "patientId") Long patientId) {
        return patientMemberVersionService.getLastMemberVersionByPatient(patientId);
    }

    @PostMapping("/getPatientMemberVersionRecord")
    @Override
    @Operation(summary = "获取会员版本变更记录")
    public PageResult<PatientMemberVersionRecordVO> getPatientMemberVersionRecord(@RequestBody PatientMemberVersionRecordQueryVO queryVO) {
        return patientMemberVersionService.getPatientMemberVersionRecord(queryVO);
    }


    @PostMapping("/getMemberVersionMapByPatientIds")
    @Override
    @Operation(summary = "获取患者会员版本")
    public Map<Long, List<MemberVersionVO>> getMemberVersionMapByPatientIds(@RequestBody List<Long> patientIds) {
        return patientMemberVersionService.getMemberVersionMapByPatientIds(patientIds);
    }

}
