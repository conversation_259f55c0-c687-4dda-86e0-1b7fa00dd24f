package com.ykl.med.patient.service.patient;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.patient.db.entity.PatientCollectionDO;
import com.ykl.med.patient.db.mysql.PatientCollectionMapper;
import com.ykl.med.patient.enums.PatientCollectionType;
import com.ykl.med.patient.vo.PatientCollectionListVO;
import com.ykl.med.patient.vo.PatientCollectionQueryVO;
import com.ykl.med.patient.vo.PatientCollectionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
public class PatientCollectionServiceImpl extends ServiceImpl<PatientCollectionMapper, PatientCollectionDO> implements PatientCollectionService {
    /**
     * 添加患者收藏
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPatientCollection(PatientCollectionVO patientCollectionVO) {
        log.info("添加患者收藏,{}", JSON.toJSONString(patientCollectionVO));
        PatientCollectionDO patientCollectionDO = this.getByPatientIdAndOutBizId(patientCollectionVO);
        if (patientCollectionDO == null) {
            patientCollectionDO = CopyPropertiesUtil.normalCopyProperties(patientCollectionVO, PatientCollectionDO.class);
            patientCollectionDO.setPatientId(patientCollectionVO.getCurrentPatientId());
            this.save(patientCollectionDO);
        }
    }

    /**
     * 删除患者收藏
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePatientCollection(PatientCollectionVO patientCollectionVO) {
        PatientCollectionDO patientCollectionDO = this.getByPatientIdAndOutBizId(patientCollectionVO);
        this.removeById(patientCollectionDO);
    }

    @Override
    public Boolean checkPatientCollection(PatientCollectionVO patientCollectionVO) {
        PatientCollectionDO patientCollectionDO = this.getByPatientIdAndOutBizId(patientCollectionVO);
        return patientCollectionDO != null;
    }

    @Override
    public Long countCollection(String outBizId, PatientCollectionType type) {
        LambdaQueryWrapper<PatientCollectionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatientCollectionDO::getOutBizId, outBizId);
        queryWrapper.eq(PatientCollectionDO::getType, type);
        return this.count(queryWrapper);
    }

    /**
     * 查询患者收藏
     */
    @Override
    public PageResult<PatientCollectionListVO> getPatientCollection(PatientCollectionQueryVO patientCollectionVO) {
        LambdaQueryWrapper<PatientCollectionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatientCollectionDO::getPatientId, patientCollectionVO.getCurrentPatientId());
        queryWrapper.eq(PatientCollectionDO::getType, patientCollectionVO.getType());
        Page<PatientCollectionDO> patientCollectionDOPage = super.page(new Page<>(patientCollectionVO.getPageNo(), patientCollectionVO.getPageSize()), queryWrapper);
        if (patientCollectionDOPage.getRecords().isEmpty()) {
            return new PageResult<>(null, patientCollectionDOPage.getTotal());
        }
        List<PatientCollectionListVO> list = CopyPropertiesUtil.normalCopyProperties(patientCollectionDOPage.getRecords(), PatientCollectionListVO.class);
        return new PageResult<>(list, patientCollectionDOPage.getTotal());
    }


    public PatientCollectionDO getByPatientIdAndOutBizId(PatientCollectionVO patientCollectionVO) {
        LambdaQueryWrapper<PatientCollectionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatientCollectionDO::getPatientId, patientCollectionVO.getCurrentPatientId());
        queryWrapper.eq(PatientCollectionDO::getType, patientCollectionVO.getType());
        queryWrapper.eq(PatientCollectionDO::getOutBizId, patientCollectionVO.getOutBizId());
        return this.getOne(queryWrapper);
    }
}
