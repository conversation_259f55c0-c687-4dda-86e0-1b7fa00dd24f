package com.ykl.med.patient.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.LongListTypeHandler;
import com.ykl.med.patient.enums.MdtLevel;
import com.ykl.med.patient.enums.MdtStatus;
import com.ykl.med.patient.enums.MdtType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@TableName(value = "t_mdt_record", autoResultMap = true)
@Data
public class MdtRecordDO extends BaseDO {
    private Long patientId;

    private Long reqDoctorId;
    private MdtStatus status;
    private MdtType type;
    private MdtLevel level;

    private String purpose;
    private String medicalHistory;
    private LocalDateTime reqTime;

    /**
     * 评价
     */
    private String evaluation;

    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> medicalTeamIds = new ArrayList<>();

    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> userIds = new ArrayList<>();
}
