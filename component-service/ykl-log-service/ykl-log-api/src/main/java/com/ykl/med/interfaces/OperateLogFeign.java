package com.ykl.med.interfaces;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.vo.OperateLogAddVO;
import com.ykl.med.vo.OperateLogListVO;
import com.ykl.med.vo.OperateLogQueryVO;
import com.ykl.med.vo.OperateLogVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-log-service", path = "/ykl-log-service")
public interface OperateLogFeign {

    @PostMapping("/saveOperateLog")
    void saveOperateLog(@RequestBody OperateLogAddVO operateLogAddVO);

    @PostMapping("/setOperateLogResult")
    void setOperateLogResult(@RequestParam(value = "id") Long id,
                             @RequestParam(value = "resultAsJson") String resultAsJson);

    @PostMapping("/queryOperateLog")
    PageResult<OperateLogListVO> queryOperateLog(@RequestBody OperateLogQueryVO queryVO);

    @PostMapping("/queryOperateLogById")
    OperateLogVO queryOperateLogById(@RequestParam(value = "id") Long id);
}
