package com.ykl.med.util;

import com.alibaba.fastjson.JSONObject;

public class OperateLogUtil {
    public static final String OPERATE_LOG_ID_HEADER= "operateLogId";
    public static String getEventContent(String eventContent, String content) {
        // 如果eventContent包含了{},说明有占位符
        if (eventContent.contains("{")) {
            JSONObject contentJson = JSONObject.parseObject(content);
            // 使用循环来处理所有的占位符
            while (eventContent.contains("{") && eventContent.contains("}")) {
                // 获取占位符中的字段名
                String fieldName = eventContent.substring(eventContent.indexOf("{") + 1, eventContent.indexOf("}"));
                // 从请求对象中获取对应的字段值
                String fieldValue = contentJson.getString(fieldName);
                // 如果字段值为null，可以选择保留占位符或者替换为空字符串
                if (fieldValue == null) {
                    fieldValue = "未知";
                }
                // 替换占位符为实际的字段值
                eventContent = eventContent.replace("{" + fieldName + "}", fieldValue);
            }
        }
        // 设置最终的eventContent
        return eventContent;
    }
}
