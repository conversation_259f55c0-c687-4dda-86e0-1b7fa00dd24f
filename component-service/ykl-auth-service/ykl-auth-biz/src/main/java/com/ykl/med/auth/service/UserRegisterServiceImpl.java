package com.ykl.med.auth.service;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.ykl.med.auth.constants.UserConstants;
import com.ykl.med.auth.entity.UserDO;
import com.ykl.med.auth.enums.UserType;
import com.ykl.med.auth.mapper.UserMapper;
import com.ykl.med.auth.vo.RegisterReqVO;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.enums.CommonStatusEnum;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户注册服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserRegisterServiceImpl implements UserRegisterService {

    private final UserMapper userMapper;

    private final StringRedisTemplate stringRedisTemplate;

    private final IdServiceImpl idService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(RegisterReqVO reqVO) {
        log.info("用户注册请求: {}", JSON.toJSONString(reqVO));
        String mobileSysSha256 = DigestUtil.sha256Hex(reqVO.getMobile() + reqVO.getUserType());
        UserDO userDO = new UserDO()
                .setUsername(mobileSysSha256)
                .setPassword(UUID.randomUUID().toString())
                .setMobile(reqVO.getMobile())
                .setRegisterIp(reqVO.getIp())
                .setLastLoginIp(reqVO.getIp())
                .setLastLoginTime(LocalDateTime.now())
                .setStatus(CommonStatusEnum.ENABLE)
                .setUserType(UserType.valueOf(reqVO.getUserType()));
        userDO.setId(idService.nextId());
        userMapper.insert(userDO);
        String key = UserConstants.REGISTER_REDIS_KEY_PREFIX + userDO.getMobile() + ":" + userDO.getUserType();
        // 设置一个过期时间为一分钟的key,主要用于注册后立即登录
        stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(userDO), 1, TimeUnit.MINUTES);
        return true;
    }
}
