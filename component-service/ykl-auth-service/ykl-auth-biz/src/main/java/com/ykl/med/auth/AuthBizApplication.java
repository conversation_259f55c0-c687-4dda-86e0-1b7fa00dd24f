package com.ykl.med.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;


/**
 * 认证中心启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.ykl.med"})
@ComponentScan(basePackages = {"com.ykl.med"})
@EnableCaching
public class AuthBizApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthBizApplication.class, args);
    }
}
