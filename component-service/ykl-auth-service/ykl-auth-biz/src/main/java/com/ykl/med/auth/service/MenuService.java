package com.ykl.med.auth.service;

import com.ykl.med.auth.vo.MenuListVO;
import com.ykl.med.auth.vo.req.*;
import com.ykl.med.auth.vo.resp.AuthenticatedMenusRespVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MenuService {

    /**
     * 查询应用下的所有菜单
     *
     * @param reqVO 应用ID
     * @return 菜单列表
     */
    List<MenuListVO> list(MenuListReqVO reqVO);


    /**
     * 根据应用ID和权限集查询经过身份验证的菜单
     *
     * @param reqVO 应用ID和权限集
     * @return 菜单树
     */
    AuthenticatedMenusRespVO listAuthenticatedMenus(ListAuthenticatedMenuReqVO reqVO);

    /**
     * 根据应用ID和权限集查询经过身份验证的菜单
     *
     * @param reqVO 应用ID和权限集
     * @return 菜单非树形结构
     */
    List<MenuListVO> listAuthenticatedMenusNonTree(ListAuthenticatedMenuNonTreeReqVO reqVO);

    /**
     * 新增菜单
     *
     * @param reqVO 菜单信息
     */
    void saveOrUpdate(MenuSaveOrUpdateReqVO reqVO);

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     */
    void deleteMenu(Long id);

    /**
     * 删除菜单及其下属所有子菜单
     *
     * @param id 菜单ID
     */
    void deleteMenuAndChildren(Long id);

    /**
     * 查询指定角色的有权限的菜单id集合
     *
     * @param reqVO 请求对象
     */
    List<String> listMenuIds(MenuIdsListReqVO reqVO);

}
