package com.ykl.med.auth.controller;

import com.ykl.med.auth.api.MenuFeign;
import com.ykl.med.auth.service.MenuService;
import com.ykl.med.auth.vo.MenuListVO;
import com.ykl.med.auth.vo.req.*;
import com.ykl.med.auth.vo.resp.AuthenticatedMenusRespVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Menu Controller
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/oauth/menu")
public class MenuController implements MenuFeign {

    private final MenuService menuService;

    /**
     * HTTP Post Request to list all authenticated menus
     *
     * @param reqVO The request object
     * @return List of all authenticated menus
     */
    @PostMapping("/listAuthenticatedMenus")
    public AuthenticatedMenusRespVO listAuthenticatedMenus(@Valid @RequestBody ListAuthenticatedMenuReqVO reqVO) {
        return menuService.listAuthenticatedMenus(reqVO);
    }

    @PostMapping("/list")
    public List<MenuListVO> list(@Valid @RequestBody MenuListReqVO reqVO) {
        return menuService.list(reqVO);
    }

    @PostMapping("/listAuthenticatedMenusNonTree")
    public List<MenuListVO> listAuthenticatedMenusNonTree(@Valid @RequestBody ListAuthenticatedMenuNonTreeReqVO reqVO) {
        return menuService.listAuthenticatedMenusNonTree(reqVO);
    }

    /**
     * HTTP POST Request to add a new menu
     *
     * @param menuSaveOrUpdateReqVO The new menu to be added
     */
    @Override
    @PostMapping("/saveOrUpdate")
    public void saveOrUpdate(@RequestBody MenuSaveOrUpdateReqVO menuSaveOrUpdateReqVO) {
        menuService.saveOrUpdate(menuSaveOrUpdateReqVO);
    }

    @Override
    @PostMapping("/delete")
    public void deleteMenu(@RequestParam Long id) {
        menuService.deleteMenu(id);
    }

    @Override
    @PostMapping("/deleteMenuAndChildren")
    public void deleteMenuAndChildren(@RequestParam Long id) {
        menuService.deleteMenuAndChildren(id);
    }

    @Override
    @PostMapping("/listMenuIds")
    public List<String> listMenuIds(@RequestBody MenuIdsListReqVO reqVO) {
        return menuService.listMenuIds(reqVO);
    }
}