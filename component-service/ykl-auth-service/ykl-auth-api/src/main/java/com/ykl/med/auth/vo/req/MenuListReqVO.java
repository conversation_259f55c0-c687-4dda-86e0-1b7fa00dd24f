package com.ykl.med.auth.vo.req;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 菜单列表请求对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "菜单列表请求对象")
public class MenuListReqVO implements Serializable {

    private static final long serialVersionUID = 6381133348997347177L;

    @Schema(description = "应用ID")
    @NotNull(message = "应用ID不能为空")
    private Long applicationId;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单状态")
    private CommonStatusEnum status;
}
