package com.ykl.med.auth.vo.req;

import com.ykl.med.auth.enums.MenuType;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "新增菜单的请求对象")
public class MenuSaveOrUpdateReqVO implements Serializable {

    private static final long serialVersionUID = -5759787589963295021L;

    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "所属应用id", example = "1")
    private Long applicationId;

    @Schema(description = "菜单名称", example = "主菜单")
    private String name;

    @Schema(description = "权限标识", example = "system:admin:add")
    private String permission;

    @Schema(description = "菜单类型", example = "MENU")
    private MenuType type;

    @Schema(description = "显示顺序", example = "1")
    private Integer sort;

    @Schema(description = "父菜单ID", example = "0")
    private Long parentId;

    @Schema(description = "路由地址", example = "/home")
    private String path;

    @Schema(description = "菜单图标", example = "home-icon")
    private String icon;

    @Schema(description = "组件路径", example = "home")
    private String component;

    @Schema(description = "组件名", example = "home")
    private String componentName;

    @Schema(description = "状态", example = "ENABLE")
    private CommonStatusEnum status;

    @Schema(description = "是否可见", example = "true")
    private Boolean visible;

    @Schema(description = "是否缓存", example = "true")
    private Boolean keepAlive;

    @Schema(description = "是否总是显示", example = "true")
    private Boolean alwaysShow;
}
