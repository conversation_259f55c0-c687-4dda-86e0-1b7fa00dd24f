package com.ykl.med.livekit;

import com.alibaba.fastjson.JSON;
import com.ykl.med.livekit.config.LiveKitProperties;
import com.ykl.med.livekit.config.LoadBalancer;
import com.ykl.med.livekit.service.LivekitService;
import com.ykl.med.livekit.vo.req.RoomCreateReqVO;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.order.api.OrderFeign;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.user.api.UserFeign;
import io.livekit.server.EgressServiceClient;
import livekit.LivekitEgress;
import okhttp3.ResponseBody;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class LivekitTest {

    @Mock
    private UserFeign userFeign;
    @Mock
    private OrderFeign orderFeign;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private LivekitService livekitService;
    @Resource
    private PushSocketFeign pushSocketFeign;
    @Resource
    private LoadBalancer loadBalancer;

    @Before
    public void init() {
        // userFeign应该无论查询什么user的时候都返回一个fakeUser, orderFeign也是需要返回一个fakeOrder
//        Mockito.when(userFeign.getById(1L)).thenReturn(
//                new UserAuthorityVO()
//                        .setId(1L)
//                        .setName("王医生")
//        );
//        Mockito.when(userFeign.getById(2L)).thenReturn(
//                new UserAuthorityVO()
//                        .setId(2L)
//                        .setUsername("李患者")
//        );
//        Mockito.when(orderFeign.getOrderDetailByOrderCode(Mockito.anyString())).thenReturn(new OrderVO()
//                .setUserId(2L)
//        );
//        livekitService = new LivekitServiceImpl(livekitConfig, userFeign, orderFeign, pushSocketFeign, stringRedisTemplate);
    }

    @Test
    public void testCreateRoom() {
        RoomCreateRespVO room = livekitService.createRoom(new RoomCreateReqVO()
                .setDoctorId(1L)
                .setBizId("VC20240130000006")
        );
        System.out.println(JSON.toJSONString(room, true));
    }

    @Test
    public void testGetRoomInfo() {
        RoomCreateRespVO roomInfo = livekitService.getRoomInfoByBizId("VC20240130000006");
        System.out.println(JSON.toJSONString(roomInfo, true));
    }


    @Test
    public void testStartRecord() {
        LiveKitProperties choose = loadBalancer.choose();
        EgressServiceClient egressServiceClient = EgressServiceClient.create(
                choose.getHost(),
                choose.getApiKey(),
                choose.getSecret()
        );
        LivekitEgress.EncodedFileOutput output = LivekitEgress.EncodedFileOutput.newBuilder()
                .setFileType(LivekitEgress.EncodedFileType.MP4)
                .setS3(LivekitEgress.S3Upload.newBuilder()
                        .setEndpoint("http://***************:9000")
                        .setAccessKey("5mz6U2uMx03s2sjMJrlH")
                        .setSecret("9PY3Ny84sDVlUOXYSuscxTppgUMe2P5olHb3dhld")
                        .setBucket("ykl")
                        .build())
                .build();
        Call<LivekitEgress.EgressInfo> egressInfoCall = egressServiceClient.startRoomCompositeEgress(
                "1",
                output
        );

        try {
            Response<LivekitEgress.EgressInfo> execute = egressInfoCall.execute();
            System.out.println(JSON.toJSONString(execute));
            if (!execute.isSuccessful()) {
                try (ResponseBody errorBody = execute.errorBody()) {
                    if (errorBody != null) {
                        System.out.println(errorBody.string());
                    }
                }
            }
            LivekitEgress.EgressInfo egressInfo = execute.body();
            System.out.println(JSON.toJSONString(egressInfo));
        } catch (IOException ignore) {
        }
    }

    @Test
    public void testStopRecord() {
        LiveKitProperties choose = loadBalancer.choose();
        EgressServiceClient egressServiceClient = EgressServiceClient.create(
                choose.getHost(),
                choose.getApiKey(),
                choose.getSecret()
        );
        Call<LivekitEgress.EgressInfo> egressInfoCall = egressServiceClient.stopEgress(
                "EG_YftKUXC9ku5Z");

        try {
            Response<LivekitEgress.EgressInfo> execute = egressInfoCall.execute();
            System.out.println(JSON.toJSONString(execute));
            if (!execute.isSuccessful()) {
                try (ResponseBody errorBody = execute.errorBody()) {
                    if (errorBody != null) {
                        System.out.println(errorBody.string());
                    }
                }
            }
            LivekitEgress.EgressInfo egressInfo = execute.body();
            System.out.println(JSON.toJSONString(egressInfo));
            // handle engress info
        } catch (IOException ignore) {
        }
    }


    @Test
    public void testLoadBalancer() {
        // 获取当前配置的服务器列表
        List<LiveKitProperties> servers = loadBalancer.getServerList();

        // 使用 Map 记录每个 dataId 的命中次数
        Map<String, Integer> countMap = new HashMap<>();
        servers.forEach(server -> countMap.put(server.getDataId(), 0));

        // 循环 10 万次
        int iterations = 100_000;
        for (int i = 0; i < iterations; i++) {
            LiveKitProperties selected = loadBalancer.choose();
            String dataId = selected.getDataId();
            countMap.compute(dataId, (k, v) -> v == null ? 1 : v + 1);
        }

        // 输出统计结果并验证分布情况
        System.out.println("负载均衡统计结果：");
        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
            String dataId = entry.getKey();
            int count = entry.getValue();
            double percentage = count * 100.0 / iterations;
            System.out.printf("dataId: %s, 次数: %d, 占比: %.2f%%\n", dataId, count, percentage);
        }
    }


    @Test
    public void testGetRoomUserList() {
        System.out.println(livekitService.getRoomUserCount("781225722019188736"));
    }


}
