package com.ykl.med.livekit.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * AccessToken
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "livekit")
public class LiveKitServerConfig {

    private List<LiveKitProperties> server;

}
