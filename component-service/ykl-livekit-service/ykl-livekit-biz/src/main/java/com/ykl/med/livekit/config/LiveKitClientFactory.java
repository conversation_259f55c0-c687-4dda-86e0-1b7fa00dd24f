package com.ykl.med.livekit.config;

import io.livekit.server.EgressServiceClient;
import io.livekit.server.RoomServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/17
 */
@Component
public class LiveKitClientFactory {

    public LiveKitClientContext create(LiveKitProperties properties) {
        RoomServiceClient roomServiceClient = RoomServiceClient.create(
                properties.getHost(),
                properties.getApiKey(),
                properties.getSecret()
        );
        EgressServiceClient egressServiceClient = EgressServiceClient.create(
                properties.getHost(),
                properties.getApiKey(),
                properties.getSecret()
        );
        return new LiveKitClientContext.Builder()
                .server(properties)
                .roomServiceClient(roomServiceClient)
                .egressServiceClient(egressServiceClient)
                .build();
    }

    public LiveKitClientContext createFromCache(String json) {
        return LiveKitClientContext.fromJson(json);
    }

}
