package com.ykl.med.livekit.service;

import com.ykl.med.livekit.vo.req.RoomConsultCreateReqVO;
import com.ykl.med.livekit.vo.req.RoomCreateReqVO;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;

/**
 * LivekitService
 *
 * <AUTHOR>
 */
public interface LivekitService {

    /**
     * 使用给定参数创建一个房间。
     *
     * @param reqVO 包含创建房间所需信息的 RoomCreateReqVO 对象
     * @return 表示创建的房间的 RoomCreateRespVO 对象列表
     */
    RoomCreateRespVO createRoom(RoomCreateReqVO reqVO);

    /**
     * 创建问诊视频房间。
     *
     * @param reqVO 包含创建房间所需信息的 RoomConsultCreateReqVO 对象
     * @return 表示创建的房间的 RoomCreateRespVO 对象列表
     */
    RoomCreateRespVO createRoomConsult(RoomConsultCreateReqVO reqVO);

    /**
     * 检索房间信息。
     *
     * @param orderCode 房间的订单代码
     * @param userId    用户 ID
     * @return 表示房间信息的 RoomCreateRespVO 对象
     */
    RoomCreateRespVO getRoomInfo(String orderCode, Long userId);

    /**
     * 检索房间信息。
     *
     * @param bizId  房间的订单代码
     * @param userId 用户 ID
     * @return 表示房间信息的 RoomCreateRespVO 对象
     */
    RoomCreateRespVO getRoomInfoByBizId(Long bizId, Long userId);


    /**
     * 检索房间信息。
     *
     * @param userId 用户 ID
     * @return 表示房间信息的 RoomCreateRespVO 对象
     */
    RoomCreateRespVO getRoomInfoByUserId(Long userId);

    /**
     * 删除房间。
     *
     * @param orderCode 订单编码
     */
    void deleteRoom(String orderCode);


    /**
     * 删除房间。
     *
     * @param bizId    订单编码
     * @param userId   用户 ID
     */

    RoomCreateRespVO deleteRoomBizId(Long bizId, Long userId);
}
