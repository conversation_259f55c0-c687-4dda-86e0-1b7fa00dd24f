package com.ykl.med.livekit.service;

import com.ykl.med.livekit.vo.req.RoomCreateReqVO;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.user.vo.UserAuthorityVO;

import java.util.List;

/**
 * LivekitService
 *
 * <AUTHOR>
 */
public interface LivekitService {

    /**
     * 使用给定参数创建一个房间。
     *
     * @param reqVO 包含创建房间所需信息的 RoomCreateReqVO 对象
     * @return 表示创建的房间的 RoomCreateRespVO 对象列表
     */
    RoomCreateRespVO createRoom(RoomCreateReqVO reqVO);


    /**
     * 检索房间信息。
     *
     * @param bizId 业务id
     * @return 表示房间信息的 RoomCreateRespVO 对象
     */
    RoomCreateRespVO getRoomInfoByBizId(String bizId);


    /**
     * 检索房间信息。
     *
     * @param userId 用户 ID
     * @return 表示房间信息的 RoomCreateRespVO 对象
     */
    RoomCreateRespVO getRoomInfoByUserId(Long userId);

    /**
     * 删除房间。
     *
     * @param bizId 业务id
     */
    void deleteRoom(String bizId);


    Integer getRoomUserCount(String bizId);
}
