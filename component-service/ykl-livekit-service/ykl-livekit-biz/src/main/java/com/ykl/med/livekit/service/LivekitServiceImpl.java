package com.ykl.med.livekit.service;

import com.alibaba.fastjson.JSON;
import com.ykl.med.framework.common.enums.ConsultChatTypeEnum;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.livekit.config.LiveKitClientContext;
import com.ykl.med.livekit.config.LiveKitClientFactory;
import com.ykl.med.livekit.config.LiveKitProperties;
import com.ykl.med.livekit.config.LoadBalancer;
import com.ykl.med.livekit.enums.LivekitErrorCode;
import com.ykl.med.livekit.vo.req.RoomCreateReqVO;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.user.vo.UserAuthorityVO;
import io.livekit.server.*;
import livekit.LivekitEgress;
import livekit.LivekitModels;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * LivekitServiceImpl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LivekitServiceImpl implements LivekitService {


    private final StringRedisTemplate redisTemplate;
    private final LoadBalancer loadBalancer;
    private final LiveKitClientFactory clientFactory;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final String LIVEKIT_DIRECTORY = "livekit/";
    private static final String ROOM_NAME_SEPARATOR = "_";
    private static final String VIDEO_SUFFIX = ".mp4";
    private static final String LIVEKIT_ROOM_PREFIX = "livekit:room:";
    private static final String LIVEKIT_USER_PREFIX = "livekit:user:";
    private static final String LIVEKIT_CONTEXT_PREFIX = "livekit:client:";
    private static final int ROOM_DURATION_SECONDS = 60 * 60 * 2;
    private static final LivekitEgress.EncodedFileType DEFAULT_FILE_TYPE = LivekitEgress.EncodedFileType.MP4;

    @Override
    @SneakyThrows
    public RoomCreateRespVO createRoom(RoomCreateReqVO reqVO) {
        // 拉取配置
        LiveKitProperties choose = loadBalancer.choose();
        // 创建客户端上下文
        LiveKitClientContext context = clientFactory.create(choose);
        // 创建房间名称
        String roomName = generateRoomName(reqVO.getDoctorName());
        try {
            Call<LivekitModels.Room> call = context.getRoomServiceClient().createRoom(roomName, ROOM_DURATION_SECONDS);
            Response<LivekitModels.Room> response = call.execute();
            if (!response.isSuccessful() || response.body() == null) {
                throw new ServiceException(LivekitErrorCode.CREATE_ROOM_FAILED);
            }

            log.info("Livekit创建房间成功: room:{}", roomName);

            UserAuthorityVO doctor = new UserAuthorityVO()
                    .setId(reqVO.getDoctorId())
                    .setName(reqVO.getDoctorName());
            UserAuthorityVO patient = new UserAuthorityVO()
                    .setId(reqVO.getPatientUserId())
                    .setName(reqVO.getPatientUserName());

            // 生成用户token信息
            List<RoomCreateRespVO> roomCreateResponses = generateTokensForUsers(choose, doctor, patient, roomName, reqVO.getBizId(), reqVO.getChatType());

            // 保存房间创建信息到redis中
            saveRoomCreateResponsesInRedis(context, reqVO.getBizId(), roomCreateResponses);

            // 开启录制
            startRoomRecording(roomName, context.getEgressServiceClient());

            return roomCreateResponses.stream().findFirst().orElse(null);
        } catch (IOException e) {
            throw new ServiceException(LivekitErrorCode.CREATE_ROOM_FAILED);
        }
    }


    private String generateRoomName(String room) {
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        return timestamp + ROOM_NAME_SEPARATOR + room;
    }

    private void startRoomRecording(String roomName, EgressServiceClient egressServiceClient) throws IOException {
        String uniqueFilepath = LIVEKIT_DIRECTORY + roomName + VIDEO_SUFFIX;
        LivekitEgress.EncodedFileOutput output = LivekitEgress.EncodedFileOutput.newBuilder()
                .setFileType(DEFAULT_FILE_TYPE)
                .setFilepath(uniqueFilepath)
                .build();

        Call<LivekitEgress.EgressInfo> egressInfoCall = egressServiceClient.startRoomCompositeEgress(roomName, output);
        Response<LivekitEgress.EgressInfo> execute = egressInfoCall.execute();
        if (!execute.isSuccessful() || execute.body() == null) {
            throw new ServiceException(LivekitErrorCode.CREATE_ROOM_FAILED);
        }
        log.info("Room recording started: {}", roomName);
    }


    private List<RoomCreateRespVO> generateTokensForUsers(LiveKitProperties choose, UserAuthorityVO doctor, UserAuthorityVO patient,
                                                          String roomName, String bizId, ConsultChatTypeEnum chatType) {
        return Arrays.stream(new UserAuthorityVO[]{doctor, patient})
                .map(user -> {
                    AccessToken token = new AccessToken(choose.getApiKey(), choose.getSecret());
                    token.setName(user.getName());
                    token.setIdentity(String.valueOf(user.getId()));
                    token.setMetadata(user.getRemark());
                    token.addGrants(new RoomJoin(true), new RoomName(roomName), new RoomRecord(true));
                    return new RoomCreateRespVO()
                            .setBizId(bizId)
                            .setToken(token.toJwt())
                            .setWsUrl(choose.getWsUrl())
                            .setUserId(user.getId())
                            .setDoctorId(doctor.getId())
                            .setDoctorName(doctor.getName())
                            .setDoctorAvatar(doctor.getAvatar())
                            .setPatientUserId(patient.getId())
                            .setPatientUserName(patient.getName())
                            .setPatientUserAvatar(patient.getAvatar())
                            .setRoomName(roomName)
                            .setVideoUrl(LIVEKIT_DIRECTORY + roomName + VIDEO_SUFFIX)
                            .setChatType(chatType != null ? chatType : ConsultChatTypeEnum.VIDEO_CHAT);
                })
                .collect(Collectors.toList());
    }

    private void saveRoomCreateResponsesInRedis(LiveKitClientContext context, String biz, List<RoomCreateRespVO> roomCreateResponses) {
        // 缓存LiveKit连接上下文
        redisTemplate.opsForValue().set(LIVEKIT_CONTEXT_PREFIX + biz, context.toJson(), ROOM_DURATION_SECONDS, TimeUnit.SECONDS);
        // 缓存房间信息
        redisTemplate.opsForValue().set(LIVEKIT_ROOM_PREFIX + biz, JSON.toJSONString(roomCreateResponses.get(0)), ROOM_DURATION_SECONDS, TimeUnit.SECONDS);
        // 缓存用户信息
        roomCreateResponses.forEach(e -> {
            String roomRespJson = JSON.toJSONString(e);
            redisTemplate.opsForValue().set(LIVEKIT_USER_PREFIX + e.getUserId(), roomRespJson, ROOM_DURATION_SECONDS, TimeUnit.SECONDS);
        });
    }

    private void deleteRoomCreateResponsesInRedis(String biz, List<Long> userIds) {
        // LiveKit连接上下文
        redisTemplate.delete(LIVEKIT_CONTEXT_PREFIX + biz);
        // 房间信息
        redisTemplate.delete(LIVEKIT_ROOM_PREFIX + biz);
        // 用户信息
        userIds.forEach(e -> redisTemplate.delete(LIVEKIT_USER_PREFIX + e));
    }

    private LiveKitClientContext getContext(String bizId) {
        String context = redisTemplate.opsForValue().get(LIVEKIT_CONTEXT_PREFIX + bizId);
        return StringUtils.isEmpty(context) ? null : JSON.parseObject(context, LiveKitClientContext.class);
    }


    @Override
    public RoomCreateRespVO getRoomInfoByBizId(String bizId) {
        String roomInfo = redisTemplate.opsForValue().get(LIVEKIT_ROOM_PREFIX + bizId);
        return StringUtils.isEmpty(roomInfo) ? null : JSON.parseObject(roomInfo, RoomCreateRespVO.class);
    }

    @Override
    public RoomCreateRespVO getRoomInfoByUserId(Long userId) {
        String roomInfo = redisTemplate.opsForValue().get(LIVEKIT_USER_PREFIX + userId);
        return StringUtils.isEmpty(roomInfo) ? null : JSON.parseObject(roomInfo, RoomCreateRespVO.class);
    }


    @Override
    public void deleteRoom(String bizId) {
        RoomCreateRespVO roomInfo = this.getRoomInfoByBizId(bizId);
        AssertUtils.notNull(roomInfo, LivekitErrorCode.ROOM_IS_NULL);

        LiveKitClientContext context = getContext(bizId);
        AssertUtils.notNull(context, LivekitErrorCode.DELETE_ROOM_FAILED);
        try {
            Call<Void> call = context.getRoomServiceClient().deleteRoom(roomInfo.getRoomName());
            Response<Void> execute = call.execute();
            if (!execute.isSuccessful()) {
                throw new ServiceException(LivekitErrorCode.DELETE_ROOM_FAILED);
            }
            // 删除缓存
            this.deleteRoomCreateResponsesInRedis(bizId, Arrays.asList(roomInfo.getDoctorId(), roomInfo.getPatientUserId()));
        } catch (IOException e) {
            throw new ServiceException(LivekitErrorCode.DELETE_ROOM_FAILED);
        }
    }

    @Override
    public Integer getRoomUserCount(String bizId) {
        RoomCreateRespVO roomInfo = this.getRoomInfoByBizId(bizId);
        AssertUtils.notNull(roomInfo, LivekitErrorCode.ROOM_IS_NULL);

        LiveKitClientContext context = getContext(bizId);
        AssertUtils.notNull(context, LivekitErrorCode.DELETE_ROOM_FAILED);
        try {
            Call<List<LivekitModels.ParticipantInfo>> call = context.getRoomServiceClient().listParticipants(roomInfo.getRoomName());
            Response<List<LivekitModels.ParticipantInfo>> execute = call.execute();
            if (!execute.isSuccessful()) {
                throw new ServiceException(LivekitErrorCode.DELETE_ROOM_FAILED);
            }
            List<LivekitModels.ParticipantInfo> body = execute.body();
            if (body == null) {
                log.warn("获取房间成员列表失败，响应体为空，bizId={}", bizId);
                return 0;
            }
            // 过滤出真实用户（recorder == false）
            List<LivekitModels.ParticipantInfo> realUsers = body.stream()
                    .filter(participant -> !participant.getPermission().getRecorder())
                    .collect(Collectors.toList());
            log.info("当前房间用户：{}", JSON.toJSONString(realUsers));
            return realUsers.size();
        } catch (IOException e) {
            throw new ServiceException(LivekitErrorCode.DELETE_ROOM_FAILED);
        }
    }

}
