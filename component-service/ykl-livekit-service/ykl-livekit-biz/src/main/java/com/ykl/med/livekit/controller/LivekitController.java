package com.ykl.med.livekit.controller;

import com.ykl.med.livekit.feign.LivekitFeign;
import com.ykl.med.livekit.service.LivekitService;
import com.ykl.med.livekit.vo.req.RoomCreateReqVO;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class LivekitController implements LivekitFeign {

    private final LivekitService livekitService;

    @Override
    @PostMapping("/livekit/createRoom")
    public RoomCreateRespVO createRoom(@RequestBody RoomCreateReqVO reqVO) {
        return livekitService.createRoom(reqVO);
    }


    @Override
    @PostMapping("/livekit/getRoomInfoByBizId")
    public RoomCreateRespVO getRoomInfoByBizId(@RequestParam("bizId") String bizId) {
        return livekitService.getRoomInfoByBizId(bizId);
    }

    @Override
    @PostMapping("/livekit/getRoomInfoByUserId")
    public RoomCreateRespVO getRoomInfoByUserId(@RequestParam("userId") Long userId) {
        return livekitService.getRoomInfoByUserId(userId);
    }

    @Override
    @PostMapping("/livekit/deleteRoom")
    public void deleteRoom(@RequestParam("bizId") String bizId) {
        livekitService.deleteRoom(bizId);
    }


    @Override
    @PostMapping("/livekit/getRoomUserCount")
    public Integer getRoomUserCount(@RequestParam("bizId") String bizId) {
        return livekitService.getRoomUserCount(bizId);
    }


}
