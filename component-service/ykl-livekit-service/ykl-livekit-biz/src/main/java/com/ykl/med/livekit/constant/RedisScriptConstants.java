package com.ykl.med.livekit.constant;

import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;

/**
 * <AUTHOR>
 * @since 2025/6/17
 */
public final class RedisScriptConstants {

    private RedisScriptConstants() {
    }

    /**
     * 原子自增并设置过期时间的 Lua 脚本
     * KEYS[1] - key 名称
     * ARGV[1] - ttl 时间（秒）
     */
    public static final RedisScript<Long> INCR_WITH_EXPIRE;

    static {
        String script = "local key = KEYS[1]\n" +
                "local ttl = tonumber(ARGV[1])\n" +
                "local count = redis.call('INCR', key)\n" +
                "if tonumber(count) == 1 then\n" +
                "    redis.call('EXPIRE', key, ttl)\n" +
                "end\n" +
                "return count";
        INCR_WITH_EXPIRE = new DefaultRedisScript<>(script, Long.class);
    }



}
