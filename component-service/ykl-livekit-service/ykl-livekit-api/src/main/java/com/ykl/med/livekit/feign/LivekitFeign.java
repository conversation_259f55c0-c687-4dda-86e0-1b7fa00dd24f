package com.ykl.med.livekit.feign;

import com.ykl.med.livekit.vo.req.RoomCreateReqVO;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * LivekitFeign
 *
 * <AUTHOR>
 */
@FeignClient(name = "ykl-livekit-service")
public interface LivekitFeign {
    @PostMapping("/livekit/createRoom")
    RoomCreateRespVO createRoom(@RequestBody RoomCreateReqVO reqVO);

    @PostMapping("/livekit/getRoomInfoByBizId")
    RoomCreateRespVO getRoomInfoByBizId(@RequestParam("bizId") String bizId);

    @PostMapping("/livekit/getRoomInfoByUserId")
    RoomCreateRespVO getRoomInfoByUserId(@RequestParam("userId") Long userId);

    @PostMapping("/livekit/deleteRoom")
    void deleteRoom(@RequestParam("bizId") String bizId);

    @PostMapping("/livekit/getRoomUserCount")
    Integer getRoomUserCount(@RequestParam("bizId") String bizId);
}
