package com.ykl.med.livekit.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.checkerframework.checker.units.qual.N;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * RoomCreateReqVO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "RoomCreateReqVO", description = "创建房间请求VO")
public class RoomCreateReqVO implements Serializable {

    private static final long serialVersionUID = 4411463018009078831L;

    @NotNull(message = "医生id 不能为空")
    @Schema(description = "医生id")
    private Long doctorId;

    @NotBlank(message = "订单号 不能为空")
    @Schema(description = "订单号")
    private String orderCode;

    @NotBlank(message = "医生名称 不能为空")
    @Schema(description = "医生名称")
    private String doctorName;

    @NotNull(message = "用户ID 不能为空")
    @Schema(description = "用户ID")
    private Long userId;

    @NotBlank(message = "用户名称 不能为空")
    @Schema(description = "用户名称")
    private String userName;

}
