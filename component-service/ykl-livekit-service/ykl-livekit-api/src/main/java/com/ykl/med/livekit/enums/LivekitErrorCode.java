package com.ykl.med.livekit.enums;

import com.ykl.med.framework.common.exception.ErrorCode;

public interface LivekitErrorCode {

    ErrorCode CREATE_ROOM_FAILED = new ErrorCode(250001, "创建房间失败");

    ErrorCode DELETE_ROOM_FAILED = new ErrorCode(250002, "删除房间失败");

    ErrorCode ONLY_DOCTOR_CAN_DELETE_ROOM = new ErrorCode(250003, "只有医生才能删除房间");

    ErrorCode PARAMS_FAILED = new ErrorCode(250004, "传入参数错误");

    ErrorCode ROOM_IS_NULL = new ErrorCode(250005, "房间信息不存在");
}
