package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.masterdata.enums.CommonConfigType;
import lombok.Data;

@TableName("t_common_config")
@Data
public class CommonConfigDO extends BaseDO {
    private String configKey;
    private String configValue;
    private CommonConfigType type;
    private String remark;
    private CommonStatusEnum status;

    private String configName;
    private String defaultValue;
    private String modelName;
}
