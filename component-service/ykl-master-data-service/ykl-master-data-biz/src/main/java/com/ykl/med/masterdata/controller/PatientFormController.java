package com.ykl.med.masterdata.controller;

import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.service.form.PatientFormSearchService;
import com.ykl.med.masterdata.service.form.PatientFormService;
import com.ykl.med.masterdata.vo.form.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "患者量表")
@RestController
@RequestMapping("/patientForm")
@Validated
public class PatientFormController implements PatientFormFeign {
    @Resource
    private PatientFormService patientFormService;
    @Resource
    private PatientFormSearchService patientFormSearchService;

    @PostMapping("/add")
    @Override
    public PatientFormVO add(@RequestBody PatientFormAddVO patientFormAddVO) {
        return patientFormService.add(patientFormAddVO);
    }

    @PostMapping("/batchAdd")
    @Override
    public List<PatientFormVO> batchAdd(@RequestBody BatchPatientFormAddVO batchPatientFormAddVO) {
        return patientFormService.batchAdd(batchPatientFormAddVO);
    }

    @PostMapping("/send")
    @Override
    public void send(@RequestParam(value = "id") Long id) {
        patientFormService.send(id);
    }

    @PostMapping("/write")
    @Override
    public void write(@RequestBody PatientFormWriteReqVO reqVO) {
        patientFormService.write(reqVO);
    }

    @PostMapping("/draft")
    @Override
    public void draft(@RequestBody PatientFormWriteReqVO reqVO) {
        patientFormService.draft(reqVO);
    }

    @PostMapping("/bizChange")
    @Override
    public void bizChange(@RequestBody PatientFormBizChangeReqVO reqVO) {
        patientFormService.bizChange(reqVO);
    }

    @PostMapping("/afterWriteStatus")
    @Override
    public void afterWriteStatus(@RequestParam(value = "id") Long id) {
        patientFormService.afterWriteStatus(id);
    }

    @PostMapping("/delete")
    @Override
    public void delete(@RequestBody PatientFormDeleteReqVO reqVO) {
        patientFormService.delete(reqVO);
    }

    @PostMapping("/query")
    @Override
    public List<PatientFormVO> query(@RequestBody PatientFormQueryVO queryVO) {
        return patientFormSearchService.query(queryVO);
    }

    @PostMapping("/getById")
    @Override
    public PatientFormVO getById(@RequestParam(value = "id") Long id) {
        return patientFormService.getById(id);
    }

    @PostMapping("/getDetailById")
    @Override
    public PatientFormDetailVO getDetailById(@RequestParam(value = "id") Long id) {
        return patientFormSearchService.getDetailById(id);
    }

    @PostMapping("/getByIds")
    @Override
    public List<PatientFormVO> getByIds(@RequestBody List<Long> ids) {
        return patientFormService.getByIds(ids);
    }

    @PostMapping("/getPatientIdByType")
    @Override
    public List<Long> getPatientIdByType(@RequestParam(value = "type") PatientFormBizType type) {
        return patientFormSearchService.getPatientIdByType(type);
    }
}
