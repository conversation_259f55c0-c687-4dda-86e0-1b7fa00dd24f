package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * 字典类型表
 */
@TableName("t_dict_type")
@Data
public class DictTypeDO extends BaseDO {
    /**
     * 字典名称
     */
    private String name;
    /**
     * 字典类型
     */
    private String type;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private CommonStatusEnum status;
    /**
     * 备注
     */
    private String remark;

}
