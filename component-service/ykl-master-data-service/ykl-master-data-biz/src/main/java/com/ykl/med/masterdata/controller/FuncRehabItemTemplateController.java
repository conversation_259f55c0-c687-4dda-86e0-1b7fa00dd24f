package com.ykl.med.masterdata.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.api.FuncRehabItemTemplateFeign;
import com.ykl.med.masterdata.service.FuncRehabItemService;
import com.ykl.med.masterdata.vo.req.FuncRehabItemPageReqVO;
import com.ykl.med.masterdata.vo.req.FuncRehabItemSaveOrUpdateReqVO;
import com.ykl.med.masterdata.vo.req.FuncRehabItemToggleStatusReqVO;
import com.ykl.med.masterdata.vo.resp.FuncRehabItemTemplateDetailRespVO;
import com.ykl.med.masterdata.vo.resp.FuncRehabPageRespVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/funcRehabItemTemplate")
public class FuncRehabItemTemplateController implements FuncRehabItemTemplateFeign {

    private final FuncRehabItemService funcRehabItemService;

    @Override
    @RequestMapping("/page")
    public PageResult<FuncRehabPageRespVO> page(@RequestBody FuncRehabItemPageReqVO reqVO) {
        return funcRehabItemService.page(reqVO);
    }

    @Override
    @RequestMapping("/toggleStatus")
    public void toggleStatus(@RequestBody FuncRehabItemToggleStatusReqVO reqVO) {
        funcRehabItemService.toggleStatus(reqVO);
    }

    @Override
    @RequestMapping("/saveOrUpdate")
    public void saveOrUpdate(FuncRehabItemSaveOrUpdateReqVO reqVO) {
        funcRehabItemService.saveOrUpdate(reqVO);
    }

    @Override
    @RequestMapping("/detail")
    public FuncRehabItemTemplateDetailRespVO detail(@RequestParam("id") Long id) {
        return funcRehabItemService.getById(id);
    }

    @Override
    @RequestMapping("/list")
    public List<FuncRehabItemTemplateDetailRespVO> list(@RequestParam("ids") Collection<Long> ids) {
        return funcRehabItemService.list(ids);
    }
}
