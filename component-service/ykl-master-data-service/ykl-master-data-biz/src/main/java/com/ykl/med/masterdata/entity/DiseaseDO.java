package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.base.utils.CustomizeLevelInfo;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.util.List;

@Data
@TableName(value = "t_disease", autoResultMap = true)
public class DiseaseDO extends BaseDO {
    /**
     * 系统名称
     */
    private String systemName;
    /**
     * 系统编码
     */
    private String systemCode;
    /**
     * 标准名称
     */
    private String standardName;
    /**
     * 拼音简码
     */
    private String pinyinCode;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     */
    private CommonStatusEnum status;

    /**
     * ICD编码
     */
    private String icdCode;

    /**
     * 疾病分类(字典，人体系统)
     */
    private String type;

    /**
     * 是否自定义等级
     */
    private Boolean customizeLevel;

    /**
     * 自定等级信息
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<CustomizeLevelInfo> customizeLevelInfos;

    /**
     * 三级的程度描述
     */
    private String remarkLevelOne;
    private String remarkLevelTwo;
    private String remarkLevelThree;
}
