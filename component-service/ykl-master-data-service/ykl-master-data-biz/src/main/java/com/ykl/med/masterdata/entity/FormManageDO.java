package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.masterdata.enums.FromManageTypeEnum;
import lombok.Data;

@Data
@TableName("t_form_manage")
public class FormManageDO extends BaseDO {

    /*** 表单名称 */
    private String name;
    /*** 表单类型 */
    private FromManageTypeEnum type;
    /*** 描述 */
    private String remark;
    /*** 表单地址 */
    private String url;
    /*** 状态(false-停用/true-启用) */
    private Boolean status;
    /*** 删除标志(0/false-未删除,1/true-已删除) */
    private Boolean deleteFlag;
}
