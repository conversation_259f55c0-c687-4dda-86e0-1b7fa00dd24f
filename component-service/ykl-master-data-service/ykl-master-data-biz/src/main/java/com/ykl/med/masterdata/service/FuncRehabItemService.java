package com.ykl.med.masterdata.service;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.vo.req.FuncRehabItemSaveOrUpdateReqVO;
import com.ykl.med.masterdata.vo.req.FuncRehabItemPageReqVO;
import com.ykl.med.masterdata.vo.req.FuncRehabItemToggleStatusReqVO;
import com.ykl.med.masterdata.vo.resp.FuncRehabItemTemplateDetailRespVO;
import com.ykl.med.masterdata.vo.resp.FuncRehabPageRespVO;

import java.util.Collection;
import java.util.List;

/**
 * 功能康复模板方案项目Service
 *
 * <AUTHOR>
 */
public interface FuncRehabItemService {

    /**
     * 保存或更新功能康复模板方案项目
     *
     * @param reqVO 功能康复模板方案项目
     */
    void saveOrUpdate(FuncRehabItemSaveOrUpdateReqVO reqVO);

    /**
     * 根据功能康复模板id查询功能康复模板方案项目
     *
     * @param reqVO 功能康复模板id
     * @return 功能康复模板方案项目
     */
    PageResult<FuncRehabPageRespVO> page(FuncRehabItemPageReqVO reqVO);

    /**
     * 根据id查询功能康复模板方案项目
     *
     * @param id id
     * @return 功能康复模板方案项目
     */
    FuncRehabItemTemplateDetailRespVO getById(Long id);

    /**
     * 切换状态
     *
     * @param reqVO 请求VO
     */
    void toggleStatus(FuncRehabItemToggleStatusReqVO reqVO);

    /**
     * 根据id集合查询所有的模板
     *
     * @param ids id集合
     * @return 训练项目详情列表
     */
    List<FuncRehabItemTemplateDetailRespVO> list(Collection<Long> ids);
}