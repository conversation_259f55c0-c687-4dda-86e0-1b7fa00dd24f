package com.ykl.med.masterdata.service.form;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.constants.topic.PatientFormTopic;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.trx.TransactionalAfterCommitExecutor;
import com.ykl.med.masterdata.constants.MasterDataErrorCode;
import com.ykl.med.masterdata.entity.PatientFormDO;
import com.ykl.med.masterdata.mapper.PatientFormMapper;
import com.ykl.med.masterdata.service.FormManageService;
import com.ykl.med.masterdata.vo.FormManageVO;
import com.ykl.med.masterdata.vo.form.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class PatientFormService extends ServiceImpl<PatientFormMapper, PatientFormDO> {
    @Resource
    private PatientFormMapper patientFormMapper;
    @Resource
    private FormManageService formManageService;
    @Resource
    private IdServiceImpl idService;
    @Resource
    private RocketMQTemplate rocketMqTemplate;

    @Transactional(rollbackFor = Exception.class)
    public PatientFormVO add(PatientFormAddVO patientFormAddVO) {
        log.info("PatientFormService.add,{}", JSON.toJSONString(patientFormAddVO));
        FormManageVO formManageVO = formManageService.detail(patientFormAddVO.getFormId());
        AssertUtils.notNull(formManageVO, MasterDataErrorCode.FORM_IS_NULL);
        PatientFormDO patientFormDO = PatientFormDO.buildFromAddVO(patientFormAddVO, formManageVO);
        patientFormDO.buildShaCode();
        try {
            patientFormDO.setId(idService.nextId());
            patientFormMapper.insert(patientFormDO);
        } catch (DuplicateKeyException e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("uk_sha_code_version")) {
                //已经发送过量表了，不做处理，直接返回结果
                return CopyPropertiesUtil.normalCopyProperties(patientFormMapper.getOne(patientFormDO.getShaCode(), patientFormDO.getType()), PatientFormVO.class);
            }
            throw e;
        }
        PatientFormVO patientFormVO = CopyPropertiesUtil.normalCopyProperties(patientFormDO, PatientFormVO.class);
        TransactionalAfterCommitExecutor.executeAfterCommit(() -> rocketMqTemplate.syncSend(PatientFormTopic.TOPIC, patientFormVO));
        return patientFormVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<PatientFormVO> batchAdd(BatchPatientFormAddVO batchPatientFormAddVO) {
        log.info("PatientFormService.batchAdd,{}", JSON.toJSONString(batchPatientFormAddVO));
        List<PatientFormVO> result = new ArrayList<>();
        for (Long formId : batchPatientFormAddVO.getFormIds()) {
            PatientFormAddVO patientFormAddVO = CopyPropertiesUtil.normalCopyProperties(batchPatientFormAddVO, PatientFormAddVO.class);
            patientFormAddVO.setFormId(formId);
            PatientFormVO patientFormVO = this.add(patientFormAddVO);
            result.add(patientFormVO);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void send(Long id) {
        log.info("PatientFormService.send,{}", id);
        PatientFormDO patientFormDO = patientFormMapper.selectById(id);
        AssertUtils.notNull(patientFormDO, MasterDataErrorCode.PATIENT_FORM_IS_NULL);
        patientFormDO.setSendStatus(true);
        patientFormDO.setSendTime(LocalDateTime.now());
        patientFormMapper.updateById(patientFormDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void write(PatientFormWriteReqVO reqVO) {
        log.info("PatientFormService.write,{}", JSON.toJSONString(reqVO));
        PatientFormDO patientFormDO = patientFormMapper.selectById(reqVO.getId());
        AssertUtils.notNull(patientFormDO, MasterDataErrorCode.PATIENT_FORM_IS_NULL);
        patientFormDO.setWriteStatus(true);
        patientFormDO.setWriteTime(LocalDateTime.now());
        patientFormDO.setResult(reqVO.getResult());
        patientFormDO.setScore(reqVO.getScore());
        patientFormDO.setContent(reqVO.getContent());
        patientFormMapper.updateById(patientFormDO);
    }

    /**
     * 保存草稿
     */
    @Transactional(rollbackFor = Exception.class)
    public void draft(PatientFormWriteReqVO reqVO) {
        log.info("PatientFormService.draft,{}", JSON.toJSONString(reqVO));
        PatientFormDO patientFormDO = patientFormMapper.selectById(reqVO.getId());
        AssertUtils.notNull(patientFormDO, MasterDataErrorCode.PATIENT_FORM_IS_NULL);
        patientFormDO.setResult(reqVO.getResult());
        patientFormDO.setScore(reqVO.getScore());
        patientFormDO.setContent(reqVO.getContent());
        patientFormMapper.updateById(patientFormDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void afterWriteStatus(Long id) {
        log.info("PatientFormService.afterWriteStatus,{}", id);
        PatientFormDO patientFormDO = patientFormMapper.selectById(id);
        AssertUtils.notNull(patientFormDO, MasterDataErrorCode.PATIENT_FORM_IS_NULL);
        patientFormDO.setAfterWriteStatus(true);
        patientFormMapper.updateById(patientFormDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void bizChange(PatientFormBizChangeReqVO reqVO) {
        log.info("PatientFormService.bizChange,{}", JSON.toJSONString(reqVO));
        LambdaQueryWrapper<PatientFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(reqVO.getPatientId() != null, PatientFormDO::getPatientId, reqVO.getPatientId());
        queryWrapper.eq(reqVO.getType() != null, PatientFormDO::getType, reqVO.getType());
        queryWrapper.eq(reqVO.getBeforeBizId() != null, PatientFormDO::getBizId, reqVO.getAfterBizId());
        queryWrapper.eq(reqVO.getId() != null, PatientFormDO::getId, reqVO.getId());
        List<PatientFormDO> patientFormDOList = patientFormMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(patientFormDOList)) {
            return;
        }
        patientFormDOList.forEach(patientFormDO -> patientFormDO.setBizId(reqVO.getAfterBizId()));
        patientFormMapper.updateBatch(patientFormDOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(PatientFormDeleteReqVO reqVO) {
        log.info("PatientFormService.delete,{}", JSON.toJSONString(reqVO));
        if (reqVO.getId() == null && reqVO.getPatientId() == null && reqVO.getType() == null && reqVO.getBizId() == null) {
            return;
        }
        LambdaQueryWrapper<PatientFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(reqVO.getPatientId() != null, PatientFormDO::getPatientId, reqVO.getPatientId());
        queryWrapper.eq(reqVO.getType() != null, PatientFormDO::getType, reqVO.getType());
        queryWrapper.eq(reqVO.getBizId() != null, PatientFormDO::getBizId, reqVO.getBizId());
        queryWrapper.eq(reqVO.getId() != null, PatientFormDO::getId, reqVO.getId());
        List<PatientFormDO> patientFormDOList = patientFormMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(patientFormDOList)) {
            return;
        }
        patientFormDOList.forEach(patientFormDO -> patientFormDO.setVersion(patientFormDO.getId()));
        patientFormMapper.updateBatch(patientFormDOList);
    }

    public PatientFormVO getById(Long id) {
        PatientFormDO patientFormDO = patientFormMapper.selectById(id);
        return CopyPropertiesUtil.normalCopyProperties(patientFormDO, PatientFormVO.class);
    }

    public List<PatientFormVO> getByIds(List<Long> ids) {
        List<PatientFormDO> patientFormDOList = patientFormMapper.selectBatchIds(ids);
        return CopyPropertiesUtil.normalCopyProperties(patientFormDOList, PatientFormVO.class);
    }
}