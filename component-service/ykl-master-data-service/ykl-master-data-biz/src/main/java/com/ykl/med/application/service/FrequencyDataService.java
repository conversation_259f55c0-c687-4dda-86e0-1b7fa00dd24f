package com.ykl.med.application.service;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.ykl.med.application.entity.*;
import com.ykl.med.application.entity.model.FrequencyData;
import com.ykl.med.application.interfaces.IService;
import com.ykl.med.application.repository.MapperRepository;
import com.ykl.med.config.SimpleException;
import com.ykl.med.enums.ResponseCode;

import java.text.ParseException;
import java.util.HashMap;

public class FrequencyDataService implements IService {
    @Override
    public String serviceName() {
        return "frequencyData";
    }

    @Override
    public ResponseModel crud(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, NoSuchFieldException, ParseException {

        FrequencyData SObj = new FrequencyData();

        ResponseModel responseModel = new ResponseModel();

        MapperRepository mapperRepository = new MapperRepository();

        // 获取传入数据Map
        HttpRequestData httpRequestData = mapperRepository.inputParamsMap(SObj, requestObj);
        // 判断必填参数
        SimpleException simpleException = mapperRepository.checkValueObjectField(
                httpRequestData.getValueObjectList().get(0),httpRequestData.getMethod(), true);
        if( simpleException.getCode() != 0 ){
            return responseModel.response(ResponseCode.INVALID_ARGUMENT, simpleException.getMessage());
        }

        /**
         * 执行sql: 生成sql, 执行, 返回结果
         * */
        // 获取SQL执行服务: 获取失败
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return responseModel.response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        // 执行SQL
        for ( ValueObject valueObject : httpRequestData.getValueObjectList() ){
            return aSqlExecService.sqlExec(valueObject, httpRequestData.getPageRecord());
        }

        return responseModel;
    }
}
