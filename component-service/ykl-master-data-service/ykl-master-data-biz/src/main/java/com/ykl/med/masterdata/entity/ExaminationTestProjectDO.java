package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.masterdata.enums.ExaminationTestCategory;
import com.ykl.med.masterdata.vo.UnitVO;
import lombok.Data;

import java.util.List;

/**
 * 检查检验项目
 */
@Data
@TableName(value = "t_examination_test_project", autoResultMap = true)
public class ExaminationTestProjectDO extends BaseDO {

    private static final long serialVersionUID = -8285258548036676714L;

    /**
     * 名称
     */
    private String name;
    /**
     * 类别
     */
    private ExaminationTestCategory category;

    /**
     * 别称(List<string>)
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> alias;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 项目类型
     */
    private String type;

    /**
     * 输入方式
     */
    private String inputMethod;

    /**
     * 单位
     */
    private String unit;

    /**
     * 状态
     */
    private CommonStatusEnum status = CommonStatusEnum.ENABLE;

    /**
     * 是否日常监测项目
     */
    private Boolean dailyMonitor;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<UnitVO> otherUnit;
}
