package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 食物表;
 * <AUTHOR> xkli
 * @date : 2023-11-9
 */
@Data
@Schema(description = "食物表")
@TableName("t_food")
public class Food implements Serializable,Cloneable{

    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long id ;

    @Schema(description = "名称", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,WHERE:WL_MATCH:%:%")
    private String name ;

    @Schema(description = "名称简拼码", example = "")
    @TableId
    @TableLogic("WHERE:WL_MATCH:%:%")
    private String nameShort ;

    @Schema(description = "类型（如：主食，蔬菜，水果等）【字典】", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private String type ;

    @Schema(description = "每份数量", example = "")
    @TableLogic("NOTNULL:NN_POST")
    private Short spec ;

    @Schema(description = "每份数量单位（如：g）", example = "")
    @TableLogic("NOTNULL:NN_POST")
    private String specUnit ;

    @Schema(description = "媒体文件（图片文件，JSON数组）", example = "")
    private Object mediaFiles ;

    @Schema(description = "水（单位：g）", example = "")
    private Double water ;

    @Schema(description = "热量（kcal）", example = "")
    private Double kilocalorie ;

    @Schema(description = "蛋白质（g）", example = "")
    private Double protein ;

    @Schema(description = "脂肪（g）", example = "")
    private Double fat ;

    @Schema(description = "碳水化合物（g）", example = "")
    private Double carbohydrate ;

    @Schema(description = "膳食纤维（g）", example = "")
    private Double dietaryFiber ;

    @Schema(description = "胆固醇（mg）", example = "")
    private Double cholesterol ;

    @Schema(description = "灰分（g）", example = "")
    private Double ash ;

    @Schema(description = "维生素-A（μg）", example = "")
    private Double vitaminA ;

    @Schema(description = "胡萝卜素（μg）", example = "")
    private Double vitaminBc ;

    @Schema(description = "视黄醛（μg）", example = "")
    private Double retinol ;

    @Schema(description = "硫胺素（mg）", example = "")
    private Double thiamine ;

    @Schema(description = "核黄素（mg）", example = "")
    private Double riboflavin ;

    @Schema(description = "烟酸（mg）", example = "")
    private Double niacin ;

    @Schema(description = "维生素C（mg）", example = "")
    private Double vitaminC ;

    @Schema(description = "维生素E（mg）", example = "")
    private Double vitaminE ;

    @Schema(description = "矿物质-钙（mg）", example = "")
    private Double mineralCa ;

    @Schema(description = "矿物质-磷（mg）", example = "")
    private Double mineralP ;

    @Schema(description = "矿物质-钾（mg）", example = "")
    private Double mineralK ;

    @Schema(description = "矿物质-钠（mg）", example = "")
    private Double mineralNa ;

    @Schema(description = "矿物质-镁（mg）", example = "")
    private Double mineralMg ;

    @Schema(description = "矿物质-铁（mg）", example = "")
    private Double mineralFe ;

    @Schema(description = "矿物质-锌（mg）", example = "")
    private Double mineralZn ;

    @Schema(description = "矿物质-硒（mg）", example = "")
    private Double mineralSe ;

    @Schema(description = "矿物质-铜（μg）", example = "")
    private Double mineralCu ;

    @Schema(description = "矿物质-锰（mg）", example = "")
    private Double mineralMn ;

    @Schema(description = "矿物质-碘（单位：μg）", example = "")
    private Double mineralI ;

    @Schema(description = "异亮氨酸（mg）", example = "")
    private Double isoleucine ;

    @Schema(description = "亮氨酸（mg）", example = "")
    private Double leucine ;

    @Schema(description = "赖氨酸（mg）", example = "")
    private Double lysine ;

    @Schema(description = "蛋氨酸（mg）", example = "")
    private Double methionine ;

    @Schema(description = "胱氨酸（mg）", example = "")
    private Double cystine ;

    @Schema(description = "苯丙氨酸（mg）", example = "")
    private Double phenylalanine ;

    @Schema(description = "酪氨酸（mg）", example = "")
    private Double tyrosine ;

    @Schema(description = "苏氨酸（mg）", example = "")
    private Double threonine ;

    @Schema(description = "色氨酸（mg）", example = "")
    private Double tryptophan ;

    @Schema(description = "缬氨酸（mg）", example = "")
    private Double valine ;

    @Schema(description = "精氨酸（mg）", example = "")
    private Double arginine ;

    @Schema(description = "组氨酸(mg)", example = "")
    private Double histidine ;

    @Schema(description = "丙氨酸(mg)", example = "")
    private Double alanine ;

    @Schema(description = "天冬氨酸(mg)", example = "")
    private Double aspartate ;

    @Schema(description = "谷氨酸(mg)", example = "")
    private Double glutamate ;

    @Schema(description = "甘氨酸(mg)", example = "")
    private Double glycine ;

    @Schema(description = "脯氨酸(mg)", example = "")
    private Double proline ;

    @Schema(description = "丝氨酸(mg)", example = "")
    private Double serine ;

    @Schema(description = "脂肪酸（g）", example = "")
    private Double fattyAcid ;

    @Schema(description = "饱和脂肪酸（g）", example = "")
    private Double saturatedFattyAcids ;

    @Schema(description = "单不饱和脂肪酸（g）", example = "")
    private Double monounsaturatedFattyAcids ;

    @Schema(description = "多不饱和脂肪酸（g）", example = "")
    private Double polyunsaturatedFattyAcid ;

    @Schema(description = "叶酸（μg）", example = "")
    private Double vitaminFa ;

    @Schema(description = "甜菜碱(mg)", example = "")
    private Double betaine ;

    @Schema(description = "游离胆碱(mg)", example = "")
    private Double freeCholine ;

    @Schema(description = "甘油磷酸胆碱(mg)", example = "")
    private Double gpc ;

    @Schema(description = "磷酸胆碱(mg)", example = "")
    private Double cholinePhosphate ;

    @Schema(description = "卵磷脂(mg)", example = "")
    private Double lecithin ;

    @Schema(description = "神经鞘磷脂(mg)", example = "")
    private Double sphingomyelin ;

    @Schema(description = "总胆碱(mg)", example = "")
    private Double totalCholine ;

    @Schema(description = "鸟嘌呤(mg)", example = "")
    private Double guanine ;

    @Schema(description = "腺嘌呤(mg)", example = "")
    private Double adenine ;

    @Schema(description = "次黄嘌呤(mg)", example = "")
    private Double hypoxanthine ;

    @Schema(description = "黄嘌呤(mg)", example = "")
    private Double xanthine ;

    @Schema(description = "总嘌呤含量(mg)", example = "")
    private Double totalPurineContent ;

    @Schema(description = "胆碱(mg)", example = "")
    private Double choline ;

    @Schema(description = "生物素（ug）", example = "")
    private Double biotin ;

    @Schema(description = "泛酸(mg)", example = "")
    private Double pantothenicAcid ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT:ENABLE")
    private String status ;

    @Schema(description = "删除标志；0未删，1已删", example = "")
    @TableId
    @TableLogic("FILLED:SGV_DELETE:1,FILLED:SGV_WHERE:0,FIELD_PROC:FP_GET_HIDE")
    private Byte deleteFlag ;

    @Schema(description = "名称删除联合主键", example = "")
    @TableLogic("FILLED:SGV_UPDATE:deleteFlag=1,FIELD_PROC:FP_GET_HIDE")
    private Long nameDuk ;

}