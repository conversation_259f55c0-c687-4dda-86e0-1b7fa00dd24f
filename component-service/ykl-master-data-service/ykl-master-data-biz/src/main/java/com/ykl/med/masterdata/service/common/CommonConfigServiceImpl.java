package com.ykl.med.masterdata.service.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.constants.ConfigErrorCodeConstants;
import com.ykl.med.masterdata.entity.CommonConfigDO;
import com.ykl.med.masterdata.enums.CommonConfigType;
import com.ykl.med.masterdata.mapper.CommonConfigMapper;
import com.ykl.med.masterdata.vo.common.CommonConfigPageReqVO;
import com.ykl.med.masterdata.vo.common.CommonConfigSimpleVO;
import com.ykl.med.masterdata.vo.common.CommonConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class CommonConfigServiceImpl extends ServiceImpl<CommonConfigMapper, CommonConfigDO> implements CommonConfigService {
    @Resource
    private CommonConfigMapper commonConfigMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    private static final String COMMON_CONFIG_KEY_PREFIX = "common_config:";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateCommonConfig(CommonConfigVO vo) {
        CommonConfigDO entity;
        if (vo.getId() != null) {
            entity = commonConfigMapper.selectById(vo.getId());
            AssertUtils.notNull(entity, ConfigErrorCodeConstants.CONFIG_NOT_EXISTS);
            if (!entity.getConfigKey().equals(vo.getConfigKey())) {
                entity = commonConfigMapper.selectByConfigKey(vo.getConfigKey());
                AssertUtils.isNull(entity, ConfigErrorCodeConstants.CONFIG_KEY_EXISTS);
            }
        } else {
            entity = commonConfigMapper.selectByConfigKey(vo.getConfigKey());
        }
        if (entity != null) {
            commonConfigMapper.deleteById(entity);
        }
        entity = new CommonConfigDO();
        BeanUtils.copyProperties(vo, entity);
        entity.setId(null);
        this.save(entity);
        //stringRedisTemplate.opsForValue().set(COMMON_CONFIG_KEY_PREFIX + vo.getConfigKey(), entity.getConfigValue(), 1, TimeUnit.HOURS);
    }

    @Override
    public List<CommonConfigSimpleVO> getCommonConfigKeysByMaxId(Long maxId, CommonConfigType type) {
        LambdaQueryWrapper<CommonConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.gt(CommonConfigDO::getId, maxId);
        queryWrapper.in(CommonConfigDO::getType, Lists.newArrayList(type, CommonConfigType.ALL));
        queryWrapper.eq(CommonConfigDO::getStatus, CommonStatusEnum.ENABLE);
        List<CommonConfigDO> list = commonConfigMapper.selectList(queryWrapper);
        List<CommonConfigSimpleVO> vos = new ArrayList<>();
        for (CommonConfigDO entity : list) {
            CommonConfigSimpleVO vo = new CommonConfigSimpleVO();
            vo.setConfigKey(entity.getConfigKey());
            vo.setConfigValue(entity.getConfigValue());
            vos.add(vo);
        }
        return vos;
    }

    @Override
    public List<CommonConfigSimpleVO> getCommonConfigListByKey(List<String> keys) {
        List<CommonConfigSimpleVO> vos = new ArrayList<>();
        for (String key : keys) {
            CommonConfigSimpleVO vo = new CommonConfigSimpleVO();
            vo.setConfigKey(key);
            String value = getCommonConfigValueByKey(key);
            vo.setConfigValue(convertValue(value));
            vos.add(vo);
        }
        return vos;
    }


    @Override
    public JSONObject getCommonConfigValueJsonByKey(String key) {
        return JSONObject.parseObject(getCommonConfigValueByKey(key));
    }


    @Override
    public String getCommonConfigValueByKey(String key) {
//        String commonConfigStr = stringRedisTemplate.opsForValue().get(COMMON_CONFIG_KEY_PREFIX + key);
//        if (StringUtils.isNotBlank(commonConfigStr)) {
//            return commonConfigStr;
//        }
        CommonConfigDO entity = commonConfigMapper.selectByConfigKey(key);
        AssertUtils.notNull(entity, ConfigErrorCodeConstants.CONFIG_NOT_EXISTS);
        //stringRedisTemplate.opsForValue().set(COMMON_CONFIG_KEY_PREFIX + key, entity.getConfigValue(), 1, TimeUnit.HOURS);
        return entity.getConfigValue();
    }

    @Override
    public PageResult<CommonConfigVO> getCommonConfigPage(CommonConfigPageReqVO reqVO) {
        PageResult<CommonConfigDO> dos = commonConfigMapper.selectPage(reqVO);
        List<CommonConfigVO> vos = CopyPropertiesUtil.normalCopyProperties(dos.getList(), CommonConfigVO.class);
        return new PageResult<>(vos, dos.getTotal());
    }

    private Object convertValue(String value) {
        try {
            return JSONObject.parseObject(value);
        } catch (JSONException e) {
            try {
                return JSONArray.parseArray(value);
            } catch (JSONException e1) {
                log.debug(" getCommonConfigListByKey  value:{} is not json", value);
                return value;
            }
        }
    }
}
