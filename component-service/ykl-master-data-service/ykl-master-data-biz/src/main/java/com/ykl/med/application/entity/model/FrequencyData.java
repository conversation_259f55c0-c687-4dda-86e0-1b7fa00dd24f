package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 使用频率表;
 * <AUTHOR> xkli
 * @date : 2023-11-13
 */
@Data
@Schema(description = "使用频率表")
@TableName("t_frequency")
public class FrequencyData implements Serializable,Cloneable{

    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long id ;

    @Schema(description = "名称", example = "")
    @TableId
    @TableLogic("WHERE:WL_MATCH:%:%")
    private String name ;

    @Schema(description = "名称简码（如：q2h表示2小时1次）", example = "")
    @TableId
    @TableLogic("WHERE:WL_MATCH:%:%")
    private String nameShort ;

    @Schema(description = "频次数目", example = "")
    private Short quantity ;

    @Schema(description = "频次数目单位（如：1H, 2H, 1D, 1W）", example = "")
    private String quantityUnit ;

    @Schema(description = "频次时间（如：08:00,12:00,18:00）", example = "")
    private String times ;

    @Schema(description = "业务范围(json)", example = "")
    @TableId
    @TableLogic("WHERE:WL_MATCH:%:%")
    private Object bizScope ;

    @Schema(description = "排序字段", example = "")
    @TableLogic("FILLED:SGV_SORT")
    private String sort ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT:ENABLE")
    private String status ;

    @Schema(description = "删除标志；0未删，1已删", example = "")
    @TableId
    @TableLogic("FILLED:SGV_DELETE:1,FILLED:SGV_WHERE:0,FIELD_PROC:FP_GET_HIDE")
    private Byte deleteFlag ;

    @Schema(description = "名称删除联合主键", example = "")
    @TableLogic("FILLED:SGV_UPDATE:deleteFlag=1,FIELD_PROC:FP_GET_HIDE")
    private Long nameDuk ;

    @Schema(description = "名称简码联合主键", example = "")
    @TableLogic("FILLED:SGV_UPDATE:deleteFlag=1,FIELD_PROC:FP_GET_HIDE")
    private Long nameShortDuk ;

    @Schema(description = "hisId", example = "")
    private Long hisId ;

}
