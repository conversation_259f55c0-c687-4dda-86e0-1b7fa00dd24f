package com.ykl.med.masterdata.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.service.dict.DictDataService;
import com.ykl.med.masterdata.vo.dict.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "管理后台 - 字典数据")
@RestController
@RequestMapping("/system/dict-data")
@Validated
public class DictDataController implements DictDataFeign {

    @Resource
    private DictDataService dictDataService;

    @Override
    @PostMapping("/create")
    @Operation(summary = "新增字典数据")
    public Long createDictData(@Valid @RequestBody DictDataCreateReqVO reqVO) {
        return dictDataService.createDictData(reqVO);
    }

    @Override
    @PostMapping("/update")
    @Operation(summary = "修改字典数据")
    public Boolean updateDictData(@Valid @RequestBody DictDataUpdateReqVO reqVO) {
        dictDataService.updateDictData(reqVO);
        return Boolean.TRUE;
    }

    @Override
    @PostMapping("/getDictDataByValues")
    public List<DictDataRespVO> getDictDataByValues(@RequestBody List<String> values) {
        return dictDataService.getDictDataByValues(values);
    }

    @Override
    @PostMapping("/getDictDataByValue")
    public DictDataRespVO getDictDataByValue(@RequestParam(value = "value") String value) {
        return dictDataService.getDictDataByValue(value);
    }

    @Override
    @PostMapping("/listByMaxId")
    public List<DictDataRespVO> listByMaxId(@RequestParam(value = "maxId") Long maxId, @RequestParam(value = "size") Integer size) {
        return dictDataService.listByMaxId(maxId, size);
    }

    @Override
    @PostMapping("/list-all-simple")
    @Operation(summary = "获得全部字典数据列表", description = "一般用于管理后台缓存字典数据在本地")
    public List<DictDataSimpleRespVO> getSimpleDictDataList() {
        return dictDataService.getDictDataList();
    }

    @Override
    @PostMapping("/page")
    @Operation(summary = "/获得字典类型的分页列表")
    public PageResult<DictDataRespVO> getDictTypePage(@RequestBody DictDataPageReqVO reqVO) {
        return dictDataService.getDictDataPage(reqVO);
    }

    @Override
    @PostMapping("/getDictDataByTypeAndLabel")
    public DictDataRespVO getDictDataByTypeAndLabel(@RequestParam(value = "type") String type, @RequestParam(value = "label") String label) {
        return dictDataService.getDictDataByTypeAndLabel(type, label);
    }


    @Override
    @PostMapping(value = "/get")
    @Operation(summary = "/查询字典数据详细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public DictDataRespVO getDictData(@RequestParam("id") Long id) {
        return dictDataService.getDictData(id);
    }


    @Override
    @PostMapping("/getDictByType")
    @Operation(summary = "app根据类型查询字典")
    public List<DictAppVO> getDictByType(@RequestBody DictAppReqVO vo) {
        return dictDataService.getDictByType(vo.getDictTypes());
    }


    @Override
    @PostMapping("/batchSave")
    public List<DictDataExcelReqVO> batchSave(@RequestBody @Valid List<DictDataExcelReqVO> reqVOS) {
        return dictDataService.upload(reqVOS);
    }


    @Override
    @GetMapping(value = "/getDictHisByKey")
    public DictHisVO getDictHisByKey(@RequestParam("dictKey") String dictKey) {
        return dictDataService.getDictHisByKey(dictKey);
    }


    @Override
    @PostMapping(value = "/getDictHisByKeys")
    public List<DictHisVO> getDictHisByKeys(@Valid @RequestBody QueryDictHisReqVO param) {
        return dictDataService.getDictHisByKeys(param.getDictKeys());
    }

    @Override
    @PostMapping(value = "/getDictHisMap")
    public Map<String, DictHisVO> getDictHisMap(@Valid @RequestBody QueryDictHisReqVO param) {
        return dictDataService.getDictHisMap(param.getDictKeys());
    }

}
