package com.ykl.med.masterdata.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.framework.mybatis.core.mapper.BaseMapperX;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.masterdata.entity.PatientFormDO;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.vo.form.PatientFormQueryVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface PatientFormMapper extends BaseMapperX<PatientFormDO> {
    @Select("select distinct patient_id from t_patient_form where type = #{type} and version = 1")
    List<Long> getPatientIdByType(PatientFormBizType type);

    default PatientFormDO getOne(String shaCode, PatientFormBizType type) {
        return selectOne(new LambdaQueryWrapperX<PatientFormDO>()
                .eq(PatientFormDO::getShaCode, shaCode)
                .eq(PatientFormDO::getVersion, 1L)
                .eq(PatientFormDO::getType, type));
    }

    default List<PatientFormDO> selectList(PatientFormQueryVO queryVO) {
        return selectList(new LambdaQueryWrapperX<PatientFormDO>()
                .eqIfPresent(PatientFormDO::getPatientId, queryVO.getPatientId())
                .eqIfPresent(PatientFormDO::getType, queryVO.getType())
                .eq(PatientFormDO::getVersion, 1L)
                .eqIfPresent(PatientFormDO::getBizId, queryVO.getBizId())
                .inIfPresent(PatientFormDO::getBizId, queryVO.getBizIds())
                .eqIfPresent(PatientFormDO::getFormId, queryVO.getFormId())
                .eqIfPresent(PatientFormDO::getAfterWriteStatus, queryVO.getAfterWriteStatus())
                .eqIfPresent(PatientFormDO::getWriteStatus, queryVO.getWriteStatus())
                .orderByDesc(PatientFormDO::getCreateTime)
        );
    }

    default List<PatientFormDO> selectListSimple(PatientFormQueryVO queryVO) {
        LambdaQueryWrapperX<PatientFormDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<PatientFormDO>();
        lambdaQueryWrapperX.select(PatientFormDO.class, info -> !info.getColumn().equals("content"));
        lambdaQueryWrapperX.eqIfPresent(PatientFormDO::getPatientId, queryVO.getPatientId())
                .eqIfPresent(PatientFormDO::getType, queryVO.getType())
                .eq(PatientFormDO::getVersion, 1L)
                .eqIfPresent(PatientFormDO::getBizId, queryVO.getBizId())
                .inIfPresent(PatientFormDO::getBizId, queryVO.getBizIds())
                .eqIfPresent(PatientFormDO::getFormId, queryVO.getFormId())
                .eqIfPresent(PatientFormDO::getAfterWriteStatus, queryVO.getAfterWriteStatus())
                .eqIfPresent(PatientFormDO::getWriteStatus, queryVO.getWriteStatus())
                .orderByDesc(PatientFormDO::getCreateTime);
        return selectList(lambdaQueryWrapperX);
    }

    default PatientFormDO getLast(Long patientId, PatientFormBizType type, Long fromId) {
        return selectOne(new LambdaQueryWrapperX<PatientFormDO>()
                .eq(PatientFormDO::getPatientId, patientId)
                .eq(PatientFormDO::getType, type)
                .eq(PatientFormDO::getFormId, fromId)
                .eq(PatientFormDO::getVersion, 1L)
                .isNotNull(PatientFormDO::getContent)
                .orderByDesc(PatientFormDO::getCreateTime)
                .last("limit 1")
        );
    }

    default List<PatientFormDO> queryWrittenSimpleByPatientId(Long patientId) {
        LambdaQueryWrapper<PatientFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PatientFormDO.class, info -> !info.getColumn().equals("content"));
        queryWrapper.eq(PatientFormDO::getPatientId, patientId);
        queryWrapper.eq(PatientFormDO::getWriteStatus, true);
        queryWrapper.eq(PatientFormDO::getVersion, 1L);
        return selectList(queryWrapper);
    }

    default Long countWrittenByPatientIdAndType(Long patientId, PatientFormBizType type) {
        LambdaQueryWrapper<PatientFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatientFormDO::getPatientId, patientId);
        queryWrapper.eq(PatientFormDO::getType, type);
        queryWrapper.eq(PatientFormDO::getWriteStatus, true);
        queryWrapper.eq(PatientFormDO::getVersion, 1L);
        return selectCount(queryWrapper);
    }
}
