package com.ykl.med.masterdata.controller;

import com.ykl.med.masterdata.api.RecipeExcelFeign;
import com.ykl.med.masterdata.service.RecipeExcelService;
import com.ykl.med.masterdata.vo.req.recipe.ImportRecipeDishParam;
import com.ykl.med.masterdata.vo.req.recipe.ImportRecipeParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <AUTHOR>
 * @since 2024/7/10
 */
@RestController
@RequestMapping("/recipe/excel")
public class RecipeExcelController implements RecipeExcelFeign {

    @Resource
    private RecipeExcelService recipeExcelService;

    @Override
    @PostMapping("/importRecipe")
    public void importRecipe(@Valid @RequestBody ImportRecipeParam param) {
        recipeExcelService.importRecipe(param);
    }

    @Override
    @PostMapping("/importRecipeDish")
    public void importRecipeDish(@Valid @RequestBody ImportRecipeDishParam param) {
        recipeExcelService.importRecipeDish(param);
    }


}
