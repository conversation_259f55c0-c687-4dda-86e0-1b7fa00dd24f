package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * 字典数据表
 */
@TableName("t_dict_data")
@Data
public class DictDataDO extends BaseDO {

    /**
     * 字典排序
     */
    private Integer sort;
    /**
     * 字典标签
     */
    private String label;
    /**
     * 字典值
     */
    private String value;
    /**
     * 字典类型
     * <p>
     * 冗余 {@link DictDataDO#getDictType()}
     */
    private String dictType;
    /**
     * 状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private CommonStatusEnum status;
    /**
     * 颜色类型
     * <p>
     * 对应到 element-ui 为 default、primary、success、info、warning、danger
     */
    private String colorType;
    /**
     * css 样式
     */
    private String cssClass;
    /**
     * 备注
     */
    private String remark;
}
