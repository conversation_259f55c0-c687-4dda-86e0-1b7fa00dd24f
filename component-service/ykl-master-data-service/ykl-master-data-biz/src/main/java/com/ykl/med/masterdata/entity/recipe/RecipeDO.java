package com.ykl.med.masterdata.entity.recipe;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.math.BigDecimal;


/**
 *  食谱
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@TableName(value = "t_recipe")
public class RecipeDO extends BaseDO {

    /** 食谱名称 */
    private String recipeName;
    /** 食谱名称-后台展示 */
    private String recipeNameBackstage;
    /** 适用人群,字典 */
    private String scopePeople;
    /** 适用季度,字典 */
    private String scopeSeason;
    /** 适用地区,字典*/
    private String scopeRegion;
    /** 图片 */
    private String images;
    /** 备注 */
    private String remark;
    /** 出处 */
    private String source;
    /** 状态(false-关闭/true-开启) */
    private Boolean state;

    /** 总热量（kcal） */
    private BigDecimal kilocalorie;
    /** 总蛋白质（g） */
    private BigDecimal protein;
    /** 总脂肪（g） */
    private BigDecimal fat;
    /** 总碳水化合物（g） */
    private BigDecimal carbohydrate;
    /** 蛋白质百分比 */
    private Integer proteinRatio;
    /** 脂肪百分比 */
    private Integer fatRatio;
    /** 碳水化合物百分比 */
    private Integer carbohydrateRatio;
    /** 油（g） */
    private BigDecimal oil;
    /** 盐（g） */
    private String salt;

    /** 删除标志（false-未删除/true-已删除） */
    private Boolean deleteFlag;

}
