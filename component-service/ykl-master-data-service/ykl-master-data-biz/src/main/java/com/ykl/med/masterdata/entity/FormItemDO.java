package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.masterdata.enums.FormItemType;
import lombok.Data;

/**
 * 表单项数据对象类
 *
 * <AUTHOR>
 */
@Data
@TableName("t_form_item")
public class FormItemDO extends BaseDO {

    private static final long serialVersionUID = 7679384188958377376L;

    /**
     * 子表单ID
     */
    private Long subFormId;

    /**
     * 表单项类型
     */
    private FormItemType type;

    /**
     * JSON字符串
     */
    private String json;
}