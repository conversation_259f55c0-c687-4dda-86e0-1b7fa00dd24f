package com.ykl.med.masterdata.service.treatment;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.constants.MasterDataErrorCode;
import com.ykl.med.masterdata.entity.TreatmentSubTypeExaminationTestDO;
import com.ykl.med.masterdata.mapper.TreatmentSubTypeExaminationTestMapper;
import com.ykl.med.masterdata.vo.ExaminationTestProjectVO;
import com.ykl.med.masterdata.vo.TreatmentSubTypeExaminationTestVO;
import com.ykl.med.masterdata.vo.req.TreatmentSubTypeExaminationTestQueryVO;
import com.ykl.med.masterdata.vo.req.TreatmentSubTypeExaminationTestReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TreatmentSubTypeExaminationTestServiceImpl
        extends ServiceImpl<TreatmentSubTypeExaminationTestMapper, TreatmentSubTypeExaminationTestDO>
        implements TreatmentSubTypeExaminationTestService {

    @Resource
    private ExaminationTestProjectService examinationTestProjectService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(TreatmentSubTypeExaminationTestReqVO reqVO) {
        log.info("TreatmentSubTypeExaminationTestServiceImpl.saveOrUpdate reqVO:{}", JSON.toJSONString(reqVO));
        TreatmentSubTypeExaminationTestDO treatmentSubTypeExaminationTestDO;
        if (reqVO.getId() != null) {
            treatmentSubTypeExaminationTestDO = super.getById(reqVO.getId());
            AssertUtils.notNull(treatmentSubTypeExaminationTestDO, GlobalErrorCodeConstants.BAD_REQUEST);
        } else {
            treatmentSubTypeExaminationTestDO = new TreatmentSubTypeExaminationTestDO();
        }
        BeanUtils.copyProperties(reqVO, treatmentSubTypeExaminationTestDO);
        try {
            super.saveOrUpdate(treatmentSubTypeExaminationTestDO);
        } catch (DuplicateKeyException e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("uq_name")) {
                throw new ServiceException(MasterDataErrorCode.TREATMENT_SUBTYPE_EXAMINATION_TEST_NAME_EXIST);
            }
            throw e;
        }
    }

    @Override
    public void updateStatusByIds(List<Long> ids, CommonStatusEnum status) {
        log.info("TreatmentSubTypeExaminationTestServiceImpl.updateStatusByIds ids:{},status:{}", ids, status);
        List<TreatmentSubTypeExaminationTestDO> treatmentSubTypeExaminationTestDOS = super.listByIds(ids);
        AssertUtils.notEmpty(treatmentSubTypeExaminationTestDOS, GlobalErrorCodeConstants.BAD_REQUEST);
        treatmentSubTypeExaminationTestDOS.forEach(treatmentSubTypeExaminationTestDO -> {
            treatmentSubTypeExaminationTestDO.setStatus(status);
        });
        super.updateBatchById(treatmentSubTypeExaminationTestDOS);
    }

    /**
     * 根据Id修改状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusById(Long id, CommonStatusEnum status) {
        log.info("TreatmentSubTypeExaminationTestServiceImpl.updateStatusById id:{},status:{}", id, status);
        TreatmentSubTypeExaminationTestDO treatmentProjectDO = super.getById(id);
        AssertUtils.notNull(treatmentProjectDO, GlobalErrorCodeConstants.BAD_REQUEST);
        treatmentProjectDO.setStatus(status);
        super.updateById(treatmentProjectDO);
    }

    @Override
    public TreatmentSubTypeExaminationTestVO getBySubType(String subType) {
        LambdaQueryWrapper<TreatmentSubTypeExaminationTestDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TreatmentSubTypeExaminationTestDO::getSubType, subType);
        TreatmentSubTypeExaminationTestDO treatmentProjectDo = super.getOne(queryWrapper);
        if (treatmentProjectDo == null) {
            return null;
        }
        TreatmentSubTypeExaminationTestVO treatmentSubTypeExaminationTestVO = new TreatmentSubTypeExaminationTestVO();
        BeanUtils.copyProperties(treatmentProjectDo, treatmentSubTypeExaminationTestVO);
        return treatmentSubTypeExaminationTestVO;
    }

    @Override
    public TreatmentSubTypeExaminationTestVO getById(Long id) {
        TreatmentSubTypeExaminationTestDO treatmentProjectDo = super.getById(id);
        if (treatmentProjectDo == null) {
            return null;
        }
        return this.build(Collections.singletonList(treatmentProjectDo)).get(0);
    }

    /**
     * 更加搜索条件搜索列表
     */
    @Override
    public List<TreatmentSubTypeExaminationTestVO> query(TreatmentSubTypeExaminationTestQueryVO queryVO) {
        LambdaQueryWrapper<TreatmentSubTypeExaminationTestDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StringUtils.isNotEmpty(queryVO.getSubType()), TreatmentSubTypeExaminationTestDO::getSubType,
                        queryVO.getSubType())
                .eq(queryVO.getStatus() != null, TreatmentSubTypeExaminationTestDO::getStatus, queryVO.getStatus())
                .ne(!Objects.equals(queryVO.getIncludeDailyMonitor(), true),
                        TreatmentSubTypeExaminationTestDO::getDailyMonitor, true)
                .orderByAsc(TreatmentSubTypeExaminationTestDO::getSort);
        List<TreatmentSubTypeExaminationTestDO> treatmentProjectDoList = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(treatmentProjectDoList)) {
            return new ArrayList<>();
        }
        return build(treatmentProjectDoList);
    }

    @Override
    public PageResult<TreatmentSubTypeExaminationTestVO> queryPage(TreatmentSubTypeExaminationTestQueryVO queryVO) {
        LambdaQueryWrapper<TreatmentSubTypeExaminationTestDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(queryVO.getSubType()), TreatmentSubTypeExaminationTestDO::getSubType, queryVO.getSubType())
                .eq(queryVO.getStatus() != null, TreatmentSubTypeExaminationTestDO::getStatus, queryVO.getStatus())
                .ne(!Objects.equals(queryVO.getIncludeDailyMonitor(), true), TreatmentSubTypeExaminationTestDO::getDailyMonitor, true)
                .orderByAsc(TreatmentSubTypeExaminationTestDO::getSort);
        if (queryVO.getHasExaminationTestIds() != null) {
            if (queryVO.getHasExaminationTestIds()) {
                queryWrapper.and(w -> w.isNotNull(TreatmentSubTypeExaminationTestDO::getExaminationTestIds)
                        .or()
                        .ne(TreatmentSubTypeExaminationTestDO::getExaminationTestIds, "[]"));
            } else {
                queryWrapper.and(w -> w.isNull(TreatmentSubTypeExaminationTestDO::getExaminationTestIds)
                        .or()
                        .eq(TreatmentSubTypeExaminationTestDO::getExaminationTestIds, "[]"));
            }
        }
        Page<TreatmentSubTypeExaminationTestDO> subTypeExaminationTestDOPage = super.page(
                new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), queryWrapper);
        if (subTypeExaminationTestDOPage == null
                || CollectionUtils.isEmpty(subTypeExaminationTestDOPage.getRecords())) {
            return PageResult.empty();
        }
        return new PageResult<>(build(subTypeExaminationTestDOPage.getRecords()),
                subTypeExaminationTestDOPage.getTotal());
    }

    private List<TreatmentSubTypeExaminationTestVO> build(
            List<TreatmentSubTypeExaminationTestDO> treatmentProjectDoList) {
        List<Long> testIdList = treatmentProjectDoList.stream()
                .map(TreatmentSubTypeExaminationTestDO::getExaminationTestIds)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        Map<Long, ExaminationTestProjectVO> examinationTestProjectVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(testIdList)) {
            List<ExaminationTestProjectVO> examinationTestProjectVOS = examinationTestProjectService
                    .getByIds(testIdList);
            examinationTestProjectVOMap = examinationTestProjectVOS.stream().collect(Collectors
                    .toMap(ExaminationTestProjectVO::getId, examinationTestProjectVO -> examinationTestProjectVO));
        }
        List<TreatmentSubTypeExaminationTestVO> treatmentSubTypeExaminationTestVOList = new ArrayList<>();
        for (TreatmentSubTypeExaminationTestDO treatmentProjectDO : treatmentProjectDoList) {
            TreatmentSubTypeExaminationTestVO treatmentSubTypeExaminationTestVO = new TreatmentSubTypeExaminationTestVO();
            BeanUtils.copyProperties(treatmentProjectDO, treatmentSubTypeExaminationTestVO);
            for (Long testId : treatmentProjectDO.getExaminationTestIds()) {
                if (examinationTestProjectVOMap.containsKey(testId)) {
                    treatmentSubTypeExaminationTestVO.getExaminationTests()
                            .add(examinationTestProjectVOMap.get(testId));
                }
            }
            treatmentSubTypeExaminationTestVO.getExaminationTests().sort(Comparator.comparing(ExaminationTestProjectVO::getSort));
            treatmentSubTypeExaminationTestVOList.add(treatmentSubTypeExaminationTestVO);
        }
        return treatmentSubTypeExaminationTestVOList;
    }

    @Override
    public List<ExaminationTestProjectVO> getExaminationTestBySubType(String subType) {
        LambdaQueryWrapper<TreatmentSubTypeExaminationTestDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TreatmentSubTypeExaminationTestDO::getSubType, subType);
        queryWrapper.eq(TreatmentSubTypeExaminationTestDO::getStatus, CommonStatusEnum.ENABLE);
        List<TreatmentSubTypeExaminationTestDO> treatmentProjectDoList = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(treatmentProjectDoList)) {
            return new ArrayList<>();
        }
        List<Long> testIdList = treatmentProjectDoList.stream()
                .map(TreatmentSubTypeExaminationTestDO::getExaminationTestIds)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(testIdList)) {
            return new ArrayList<>();
        }
        return examinationTestProjectService.getByIds(testIdList);
    }

    @Override
    public List<TreatmentSubTypeExaminationTestVO> getByIds(Collection<Long> ids) {
        List<TreatmentSubTypeExaminationTestDO> treatmentProjectDoList = super.listByIds(ids);
        if (CollectionUtils.isEmpty(treatmentProjectDoList)) {
            return new ArrayList<>();
        }
        return build(treatmentProjectDoList);
    }

}
