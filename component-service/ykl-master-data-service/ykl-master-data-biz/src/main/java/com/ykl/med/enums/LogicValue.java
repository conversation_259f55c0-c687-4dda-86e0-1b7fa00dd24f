package com.ykl.med.enums;

import lombok.Getter;

/**
 * 用于对象字段注解: @TableLogic
 * 示例：@TableLogic("FILLED:SGV_INSERT:1,FILLED:SGV_DELETE:0,WHERE:WL_RANGE:>=:<,NOTNULL:NN_GET:pageNum@&是必填参数")
 * 说明：多个定义采用","分割,字段之间用":"分割, 如果字段中有","时，用"#!"代替,有":"时,用"@&"代替
 * */
@Getter
public enum LogicValue {
    INIT("默认值",0),
    // 字段类型
    WHERE("条件字段",1),
    TABLE("表字段",2),
    FILLED("自动补充字段值",3),
    NOTNULL("字段空值判断",4),
    FIELD_PROC("字段处理标志",5),
    // 操作类型
    INSERT("插入操作",6),
    UPDATE("更新操作",7),
    SELECT("查询操作",8),
    DELETE("删除操作",9),
    // 条件字段(WHERE)的匹配方式
    WL_MATCH("模糊匹配",201),
    WL_RANGE("条件范围匹配",208),
    // 表字段(TABLE)的对应关系
    TR_O2O("一对一",101),
    TR_O2M("一对多",102),
    TR_M2O("多对一",103),
    TR_M2M("多对多",104),
    // 自动填充字段值(FILLED)的方式
    SGV_INSERT("插入时字段值自动生成",301),
    SGV_UPDATE("更新时字段值自动生成", 302),
    SGV_WHERE("作为条件时,没有输入时,自动填充默认值", 303),
    SGV_DELETE("删除操作时,设置标志代替删除", 304),
    SGV_SORT("查找时使用的默认排序方式",305),
    SGV_GROUP("查询时使用的分组字段", 306),
    // 空值判断字段(NOTNULL)判断方式
    NN_GET("方法为GET时,判断空值", 401),
    NN_POST("方法为POST时,判断空值", 402),
    NN_PUT("方法为PUT时,判断空值", 403),
    NN_DELETE("方法为DELETE时,判断空值", 404),
    // 字段处理(FIELD_PROC)类型
    FP_GET_HIDE("方法为GET时,隐藏该字段",501),
    FP_PUT_INVALID("更新时忽略传入值", 502),
    FP_PUT_WHERE("更新时有传入值,作为条件使用",503)
    ;

    LogicValue(String name, int value) {
        this.name = name;
        this.value = value;
    }

    private final String name;
    private final int value;
}
