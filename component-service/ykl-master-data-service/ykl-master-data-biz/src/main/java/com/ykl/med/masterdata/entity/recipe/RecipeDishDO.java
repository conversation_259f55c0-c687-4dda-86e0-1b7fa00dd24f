package com.ykl.med.masterdata.entity.recipe;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Builder;
import lombok.Data;

/**
 * 食谱-菜品
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@TableName(value = "t_recipe_dish")
public class RecipeDishDO extends BaseDO {

    /** 食谱id */
    private Long recipeId;
    /** 菜品id */
    private Long dishId;
    /** 菜品名称 */
    private String dishName;
    /** 菜品名称-后台 */
    private String dishNameBackstage;
    /** 适用范围,字典*/
    private String scope;
    /** 删除标志（false-未删除/true-已删除） */
    private Boolean deleteFlag;

}

