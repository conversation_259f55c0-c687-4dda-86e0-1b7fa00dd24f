package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * 指导与建议互斥条件
 *
 * <AUTHOR>
 */
@Data
@TableName("t_care_advice_mutex")
public class CareAdviceMutexDO extends BaseDO {

    private static final long serialVersionUID = 4158119193714046229L;

    /**
     * 指导与建议id
     */
    private Long careAdviceId;

    /**
     * 互斥的指导与建议id
     */
    private Long mutexCareAdviceId;

    /**
     * 排序
     */
    private Integer sort;
}
