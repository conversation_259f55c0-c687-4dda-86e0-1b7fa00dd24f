package com.ykl.med.masterdata.service.dict;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.constants.ConfigErrorCodeConstants;
import com.ykl.med.masterdata.entity.DictDataDO;
import com.ykl.med.masterdata.entity.DictHisDO;
import com.ykl.med.masterdata.mapper.DictDataMapper;
import com.ykl.med.masterdata.mapper.DictHisMapper;
import com.ykl.med.masterdata.vo.dict.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 字典数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DictDataServiceImpl extends ServiceImpl<DictDataMapper, DictDataDO> implements DictDataService {

    @Resource
    private DictTypeService dictTypeService;
    @Resource
    private DictDataMapper dictDataMapper;
    @Resource
    private DictHisMapper dictHisMapper;


    @Override
    public List<DictDataSimpleRespVO> getDictDataList() {
        List<DictDataDO> list = dictDataMapper.selectList();
        list.sort(Comparator.comparing(DictDataDO::getDictType)
                .thenComparingInt(DictDataDO::getSort));
        return CopyPropertiesUtil.normalCopyProperties(list, DictDataSimpleRespVO.class);
    }

    @Override
    public PageResult<DictDataRespVO> getDictDataPage(DictDataPageReqVO reqVO) {
        PageResult<DictDataDO> dictDataPage = dictDataMapper.selectPage(reqVO);
        return new PageResult<>(CopyPropertiesUtil.normalCopyProperties(dictDataPage.getList(), DictDataRespVO.class), dictDataPage.getTotal());
    }

    @Override
    public DictDataRespVO getDictDataByTypeAndLabel(String type, String label) {
        DictDataDO dictData = dictDataMapper.selectByDictTypeAndLabel(type, label);
        return CopyPropertiesUtil.normalCopyProperties(dictData, DictDataRespVO.class);
    }

    @Override
    public DictDataRespVO getDictData(Long id) {
        return CopyPropertiesUtil.normalCopyProperties(dictDataMapper.selectById(id), DictDataRespVO.class);
    }

    @Override
    public List<DictDataRespVO> getDictDataByValues(List<String> values) {
        LambdaQueryWrapper<DictDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DictDataDO::getValue, values);
        List<DictDataDO> list = dictDataMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(list, DictDataRespVO.class);
    }

    @Override
    public DictDataRespVO getDictDataByValue(String value) {
        LambdaQueryWrapper<DictDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictDataDO::getValue, value);
        DictDataDO dictDataDO = dictDataMapper.selectOne(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(dictDataDO, DictDataRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createDictData(DictDataCreateReqVO reqVO) {
        // 校验正确性
        validateDictDataForCreateOrUpdate(null, reqVO.getValue(), reqVO.getDictType(), reqVO.getLabel());

        // 插入字典类型
        DictDataDO dictData = CopyPropertiesUtil.normalCopyProperties(reqVO, DictDataDO.class);
        dictDataMapper.insert(dictData);
        return dictData.getId();
    }

    @Override
    public List<DictDataRespVO> listByMaxId(Long maxId, Integer size) {
        LambdaQueryWrapper<DictDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.gt(DictDataDO::getId, maxId);
        queryWrapper.orderByAsc(DictDataDO::getId);
        queryWrapper.last("limit " + size);
        List<DictDataDO> list = dictDataMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(list, DictDataRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDictData(DictDataUpdateReqVO reqVO) {
        // 校验正确性
        validateDictDataForCreateOrUpdate(reqVO.getId(), reqVO.getValue(), reqVO.getDictType(), reqVO.getLabel());
        DictDataDO dictData = dictDataMapper.selectById(reqVO.getId());
        // 删除旧数据
        dictDataMapper.deleteById(dictData);
        // 插入字典类型（为了生成更大的Id）
        DictDataDO dictDataNew = CopyPropertiesUtil.normalCopyProperties(dictData, DictDataDO.class);
        BeanUtils.copyProperties(reqVO, dictDataNew, "id", "value", "dictType");
        dictDataNew.setId(null);
        dictDataMapper.insert(dictDataNew);
    }

    @Override
    public long countByDictType(String dictType) {
        return dictDataMapper.selectCountByDictType(dictType);
    }

    @Override
    public List<DictAppVO> getDictByType(List<String> types) {
        List<DictDataDO> dos;
        if (CollectionUtil.isEmpty(types)) {
            dos = dictDataMapper.selectList();
        } else {
            dos = dictDataMapper.selectByDictType(types);
        }
        if (CollectionUtil.isEmpty(dos)) {
            return null;
        }
        Map<String, DictAppVO> map = new HashMap<>();
        for (DictDataDO dictDataDO : dos) {
            DictDataAppVO dictDataAppVO = CopyPropertiesUtil.normalCopyProperties(dictDataDO, DictDataAppVO.class);
            if (map.containsKey(dictDataDO.getDictType())) {
                map.get(dictDataDO.getDictType()).getDataList().add(dictDataAppVO);
            } else {
                DictAppVO dictAppVO = new DictAppVO();
                dictAppVO.setDictType(dictDataDO.getDictType());
                dictAppVO.setDataList(Lists.newArrayList(dictDataAppVO));
                map.put(dictDataDO.getDictType(), dictAppVO);
            }
        }
        return Lists.newArrayList(map.values());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DictDataExcelReqVO> upload(List<DictDataExcelReqVO> reqVOS) {
        List<DictDataExcelReqVO> fail = new ArrayList<>();
        List<DictDataDO> toSaveOrUpdate = new ArrayList<>();

        Set<String> distinctCheck = new HashSet<>();
        for (DictDataExcelReqVO vo : reqVOS) {
            // 尝试将值和标签都加入distinctCheck，如果无法加入则说明重复。
            if (!distinctCheck.add(vo.getDictType() + vo.getValue())
                    || !distinctCheck.add(vo.getDictType() + vo.getLabel())) {
                vo.setRemark("字典值或标签重复");
                fail.add(vo);
                continue;
            }
            validateDictTypeExists(vo.getDictType());
            DictDataDO dictData = convertVoToDictDataDO(vo);
            toSaveOrUpdate.add(dictData);
        }
        // 把 saveOrUpdateBatch 放在循环外，聚合所有需要保存或更新的数据批量更新，优化数据库查询性能
        if (!toSaveOrUpdate.isEmpty()) {
            saveOrUpdateBatch(toSaveOrUpdate);
        }
        return fail;
    }

    /**
     * 将 VO 对象转为 DO 对象的简单方法（实际代码中可能需要更复杂的转换过程或Error Handling）
     */
    private DictDataDO convertVoToDictDataDO(DictDataExcelReqVO vo) {
        DictDataDO dict = dictDataMapper.selectByValue(vo.getValue());
        if (dict == null) {
            dict = dictDataMapper.selectByDictTypeAndLabel(vo.getDictType(), vo.getLabel());
            if (dict == null) {
                dict = new DictDataDO();
                dict.setStatus(CommonStatusEnum.ENABLE);
            }
        } else if (
                !Objects.equals(
                        dict.getId(),
                        dictDataMapper.selectByDictTypeAndLabel(vo.getDictType(), vo.getLabel()).getId()
                )
        ) {
            throw new IllegalStateException("字典标签和值重复");
        }

        dict.setDictType(vo.getDictType());
        dict.setLabel(vo.getLabel());
        dict.setValue(vo.getValue());
        dict.setSort(vo.getSort());
        dict.setRemark(vo.getRemark());
        return dict;
    }

    private void validateDictDataForCreateOrUpdate(Long id, String value, String dictType, String label) {
        // 校验自己存在
        validateDictDataExists(id);
        // 校验字典类型有效
        validateDictTypeExists(dictType);
        // 校验字典数据的值的唯一性
        validateDictDataValueUnique(id, value);
        validateDictDataLabelUnique(id, dictType, label);
    }

    public void validateDictDataValueUnique(Long id, String value) {
        DictDataDO dictData = dictDataMapper.selectByValue(value);
        if (dictData == null) {
            return;
        }
        AssertUtils.isTrue(id != null && dictData.getId().equals(id), ConfigErrorCodeConstants.DICT_DATA_VALUE_DUPLICATE);
    }

    public void validateDictDataLabelUnique(Long id, String dictType, String label) {
        DictDataDO dictData = dictDataMapper.selectByDictTypeAndLabel(dictType, label);
        if (dictData == null) {
            return;
        }
        AssertUtils.isTrue(id != null && dictData.getId().equals(id), ConfigErrorCodeConstants.DICT_DATA_VALUE_DUPLICATE);
    }


    public void validateDictDataExists(Long id) {
        if (id == null) {
            return;
        }
        DictDataDO dictData = dictDataMapper.selectById(id);
        AssertUtils.notNull(dictData, ConfigErrorCodeConstants.DICT_DATA_NOT_EXISTS);
    }

    public void validateDictTypeExists(String type) {
        DictTypeRespVO dictType = dictTypeService.getDictTypeByType(type);
        AssertUtils.notNull(dictType, ConfigErrorCodeConstants.DICT_TYPE_NOT_EXISTS);
        AssertUtils.isTrue(CommonStatusEnum.ENABLE == dictType.getStatus(), ConfigErrorCodeConstants.DICT_TYPE_NOT_ENABLE);
    }

    @Override
    public DictHisVO getDictHisByKey(String dictKay) {
        List<DictHisVO> dictHisByKeys = getDictHisByKeys(Collections.singletonList(dictKay));
        if (CollectionUtil.isEmpty(dictHisByKeys)) {
            return null;
        }
        return dictHisByKeys.get(0);
    }

    @Override
    public List<DictHisVO> getDictHisByKeys(List<String> dictKeys) {
        LambdaQueryWrapper<DictHisDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(dictKeys)) {
            queryWrapper.in(DictHisDO::getDictKey, dictKeys);
        }
        queryWrapper.orderByAsc(DictHisDO::getId);
        List<DictHisDO> list = dictHisMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return CopyPropertiesUtil.normalCopyProperties(list, DictHisVO.class);
    }

    @Override
    public Map<String, DictHisVO> getDictHisMap(List<String> dictKeys) {
        List<DictHisVO> dictHisByKeys = getDictHisByKeys(dictKeys);
        if (CollectionUtil.isEmpty(dictHisByKeys)) {
            return null;
        }
        return dictHisByKeys.stream().collect(Collectors.toMap(DictHisVO::getDictKey, Function.identity()));
    }
}
