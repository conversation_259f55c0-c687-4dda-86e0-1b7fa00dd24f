package com.ykl.med.masterdata.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.enums.DictHisTypeEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * 字典数据表
 */
@TableName("t_dict_his")
@Data
public class DictHisDO{
    /**
     * id
     */
    private Long id;
    /**
     * 类型,性别、用法、计量单位、婚姻状况
     */
    private DictHisTypeEnum type;
    /**
     * 字典值
     */
    private String dictKey;
    /**
     * hisId
     */
    private String hisId;
    /**
     * his名称
     */
    private String hisName;

}
