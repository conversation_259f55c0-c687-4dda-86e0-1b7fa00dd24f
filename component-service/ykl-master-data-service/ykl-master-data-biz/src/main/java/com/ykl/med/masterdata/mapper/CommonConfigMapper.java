package com.ykl.med.masterdata.mapper;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.mybatis.core.mapper.BaseMapperX;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.masterdata.entity.CommonConfigDO;
import com.ykl.med.masterdata.vo.common.CommonConfigPageReqVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CommonConfigMapper extends BaseMapperX<CommonConfigDO> {
    default CommonConfigDO selectByConfigKey(String configKey) {
        return selectOne(CommonConfigDO::getConfigKey, configKey);
    }

    default PageResult<CommonConfigDO> selectPage(CommonConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CommonConfigDO>()
                .eqIfPresent(CommonConfigDO::getType, reqVO.getType())
                .eqIfPresent(CommonConfigDO::getStatus, reqVO.getStatus()).orderByDesc(CommonConfigDO::getUpdateTime)
                .and(StringUtils.isNotEmpty(reqVO.getConfigKey()), wrapper1 -> wrapper1
                        .like(CommonConfigDO::getConfigKey, reqVO.getConfigKey())
                        .or()
                        .like(CommonConfigDO::getConfigName, reqVO.getConfigKey())))
                ;
    }
}
