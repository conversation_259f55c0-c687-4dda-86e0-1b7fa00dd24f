package com.ykl.med.masterdata.service.form;

import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.constants.MasterDataErrorCode;
import com.ykl.med.masterdata.entity.PatientFormDO;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.mapper.PatientFormMapper;
import com.ykl.med.masterdata.vo.form.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class PatientFormSearchService {
    @Resource
    private PatientFormMapper patientFormMapper;

    /**
     * 获取患者已填写的数据
     * @param patientId 患者Id
     * @return
     */
    public List<PatientFormWrittenSimpleVO> queryWrittenSimpleByPatientId(Long patientId) {
        List<PatientFormDO> patientFormDOList = patientFormMapper.queryWrittenSimpleByPatientId(patientId);
        return CopyPropertiesUtil.normalCopyProperties(patientFormDOList, PatientFormWrittenSimpleVO.class);
    }

    public Long countWrittenByPatientIdAndType(Long patientId, PatientFormBizType type) {
        return patientFormMapper.countWrittenByPatientIdAndType(patientId, type);
    }

    public List<PatientFormVO> query(PatientFormQueryVO queryVO) {
        //todo content太大了，有些业务实际不需要
        List<PatientFormDO> patientFormDOList = patientFormMapper.selectList(queryVO);
        return CopyPropertiesUtil.normalCopyProperties(patientFormDOList, PatientFormVO.class);
    }

    public List<PatientFormSimpleVO> querySimple(PatientFormQueryVO queryVO) {
        List<PatientFormDO> patientFormDOList = patientFormMapper.selectListSimple(queryVO);
        return CopyPropertiesUtil.normalCopyProperties(patientFormDOList, PatientFormSimpleVO.class);
    }

    public PatientFormDetailVO getDetailById(Long id) {
        PatientFormDO patientFormDO = patientFormMapper.selectById(id);
        AssertUtils.notNull(patientFormDO, MasterDataErrorCode.PATIENT_FORM_IS_NULL);
        PatientFormDO last = patientFormMapper.getLast(patientFormDO.getPatientId(), patientFormDO.getType(), patientFormDO.getFormId());
        PatientFormDetailVO detail = CopyPropertiesUtil.normalCopyProperties(patientFormDO, PatientFormDetailVO.class);
        if (last != null) {
            detail.setLastContent(last.getContent());
        }
        return detail;
    }

    public List<Long> getPatientIdByType(PatientFormBizType type) {
        return patientFormMapper.getPatientIdByType(type);
    }
}
