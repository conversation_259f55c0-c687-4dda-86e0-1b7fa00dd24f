package com.ykl.med.masterdata.vo.req.recipe;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@Schema(description = "菜品列表查询入参")
public class DishQueryReqVO implements Serializable {

    @Schema(description = "菜品名称")
    private String name;

    @Schema(description = "状态(false-关闭/true-开启)")
    private Boolean state;

    @Schema(description = "适用范围,字典")
    private String scope;

    @NotNull(message = "pageNo can not null")
    @Schema(description = "分页参数", example = "默认1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageNo = 1;

    @NotNull(message = "pageSize can not null")
    @Schema(description = "分页参数", example = "默认10", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageSize = 10;

}
