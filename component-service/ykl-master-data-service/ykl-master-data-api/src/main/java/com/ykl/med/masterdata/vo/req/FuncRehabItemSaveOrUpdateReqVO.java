package com.ykl.med.masterdata.vo.req;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.masterdata.enums.MediaType;
import com.ykl.med.masterdata.vo.FuncRehabItemDeviceDataVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(name = "FuncRehabItemSaveOrUpdateReqVO", description = "功能康复训练项目保存或更新请求VO")
public class FuncRehabItemSaveOrUpdateReqVO implements Serializable {

    private static final long serialVersionUID = -45606921496229933L;

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "呼吸功能训练")
    @NotBlank(message = "项目名称不能为空")
    private String name;

    @Schema(description = "拼音简码", requiredMode = Schema.RequiredMode.REQUIRED, example = "hxgnxl")
    @NotBlank(message = "拼音简码不能为空")
    private String code;

    @Schema(description = "训练方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "深呼吸")
    @NotBlank(message = "训练方式不能为空")
    private String method;

    @Schema(description = "训练目的", example = "提高呼吸能力")
    private String objective;

    @Schema(description = "操作方法", example = "操作方法")
    private String operationMethod;

    @Schema(description = "训练频次", requiredMode = Schema.RequiredMode.REQUIRED, example = "频率表id")
    @NotNull(message = "训练频次不能为空")
    private Long frequencyId;

    @Schema(description = "最大频次", example = "频率表id")
    private Long maxFrequencyId;

    @Schema(description = "单次数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "单次数量不能为空")
    private Integer actionsPerSession;

    @Schema(description = "单次组数", example = "3")
    private Integer setsPerSession;

    @Schema(description = "每组数量", example = "10")
    private Integer actionsPerSet;

    @Schema(description = "每组间隔时间(分钟)", example = "5")
    private Integer restTimePerSet;

    @Schema(description = "单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "次")
    @NotBlank(message = "单位不能为空")
    private String unit;

    @Schema(description = "周总时长", example = "150")
    private Integer totalDurationPerWeek;

    @Schema(description = "辅助康复器械", example = "呼吸器")
    private String equipment;

    @Schema(description = "是否需要护理人员辅助", example = "true")
    private Boolean needAssist;

    @Schema(description = "开始阶段", example = "字典值")
    private String startStage;

    @Schema(description = "阶段后几天", example = "3")
    private Integer startDays;

    @Schema(description = "注意事项", example = "注意事项")
    private String attention;

    @Schema(description = "媒体文件类型", example = "IMAGE")
    private MediaType mediaType;

    @Schema(description = "媒体文件", example = "文件路径")
    private List<String> mediaFiles;

    @Schema(description = "封面文件（JSON数组）", example = "")
    private List<String> coverFiles ;

    @Schema(description = "备注", example = "备注")
    private String remark;

    @Schema(description = "参考文献", example = "参考文献")
    private String reference;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "ENABLE")
    @NotNull(message = "状态不能为空")
    private CommonStatusEnum status;

    @Schema(description = "智能设备")
    private List<FuncRehabItemDeviceDataVO> deviceData;

}