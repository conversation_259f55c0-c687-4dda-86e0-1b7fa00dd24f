package com.ykl.med.masterdata.constants;

import com.ykl.med.framework.common.exception.ErrorCode;

/**
 * 基础数据错误码
 *
 * <AUTHOR>
 */
public interface MasterDataErrorCode {

    ErrorCode ACCESS_DENIED = new ErrorCode(210001, "没有操作权限");
    ErrorCode FORM_NOT_EXIST = new ErrorCode(210002, "表单不存在");

    ErrorCode TREATMENT_PROJECT_NAME_EXIST = new ErrorCode(210003, "诊疗项目名称已存在");

    ErrorCode EXAMINATION_TEST_PROJECT_NAME_EXIST = new ErrorCode(210004, "检查检验项目名称已存在");


    ErrorCode TREATMENT_SUBTYPE_EXAMINATION_TEST_NAME_EXIST = new ErrorCode(210005, "检查检验子类名称已存在");

    ErrorCode SPORT_TRAIN_NAME_EXIST = new ErrorCode(210006, "训练方式名称已存在");

    ErrorCode SPORT_PROJECT_NAME_EXIST = new ErrorCode(210007, "运动项目名称已存在");

    ErrorCode DATA_DUPLICATED = new ErrorCode(210008, "数据重复");
    ErrorCode ILLEGAL_PARAMETER = new ErrorCode(210009, "非法参数");
    ErrorCode ILLEGAL_PARAMETER_IMAGE_SIZE = new ErrorCode(210010, "图片最多九张");
    ErrorCode ILLEGAL_PARAMETER_VIDEO_SIZE = new ErrorCode(210011, "视频最多一个");
    ErrorCode DISH_IS_NULL = new ErrorCode(210012, "食品数据不存在");

    ErrorCode RECIPE_IS_NULL = new ErrorCode(210013, "食谱数据不存在");

    ErrorCode PATIENT_FORM_IS_NULL = new ErrorCode(210014, "患者量表不存在");
    ErrorCode THE_ATTACHMENT_ALREADY_EXISTS = new ErrorCode(210015, "附件已存在");
    ErrorCode THE_ATTACHMENT_NOT_EXISTS = new ErrorCode(210016, "附件不存在");
    ErrorCode FORM_IS_NULL = new ErrorCode(210017, "量表不存在");
}
