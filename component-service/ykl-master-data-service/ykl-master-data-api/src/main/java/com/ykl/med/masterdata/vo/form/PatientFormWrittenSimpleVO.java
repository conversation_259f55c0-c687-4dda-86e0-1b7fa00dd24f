package com.ykl.med.masterdata.vo.form;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "患者已填写量表记录-简易")
public class PatientFormWrittenSimpleVO {
    @Schema(description = "业务类型")
    private PatientFormBizType type;
    @Schema(description = "量表名称", example = "XXXX")
    private String formName;
    @Schema(description = "量表结果", example = "XXXX")
    private String result;
    @Schema(description = "分数", example = "1")
    private Integer score;
    @TimestampConvert
    @Schema(description = "填写时间", example = "2023-01-01 00:00:00")
    private LocalDateTime writeTime;
}
