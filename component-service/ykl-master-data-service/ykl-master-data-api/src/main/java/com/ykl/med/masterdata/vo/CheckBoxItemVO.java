package com.ykl.med.masterdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 复选框组选项VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "CheckBoxOptionVO", description = "复选框组选项VO")
public class CheckBoxItemVO implements Serializable {
    private static final long serialVersionUID = -2991471600926096806L;

    @Schema(description = "选项")
    private String label;

    @Schema(description = "值")
    private String value;

    @Schema(description = "分值")
    private Integer score;

    @Schema(description = "排序")
    private Integer sort;
}
