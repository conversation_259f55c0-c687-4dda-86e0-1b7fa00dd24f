package com.ykl.med.masterdata.vo.req;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.masterdata.enums.ExaminationTestCategory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 检查检验项目
 */
@Data
@Schema(description = "检查检验项目查询视图对象")
public class ExaminationTestProjectQueryVO extends PageParam {

    @Schema(description = "ids", example = "示例名称")
    private List<Long> ids;

    @Schema(description = "名称", example = "示例名称")
    private String name;

    @Schema(description = "类别")
    private ExaminationTestCategory category;

    @Schema(description = "状态", example = "ENABLE")
    private CommonStatusEnum status;

    @Schema(description = "是否包含日常监测", example = "false", hidden = true)
    private Boolean includeDailyMonitor;
}