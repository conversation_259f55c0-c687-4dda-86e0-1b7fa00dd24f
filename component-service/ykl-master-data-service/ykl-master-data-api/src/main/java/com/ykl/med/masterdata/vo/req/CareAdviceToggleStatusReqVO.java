package com.ykl.med.masterdata.vo.req;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 指导与建议状态切换请求VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "CareAdviceToggleStatusReqVO", description = "指导与建议状态切换请求VO")
public class CareAdviceToggleStatusReqVO implements Serializable {

    private static final long serialVersionUID = 7470217952907539148L;

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id集合不能为空")
    private List<Long> ids;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private CommonStatusEnum status;
}
