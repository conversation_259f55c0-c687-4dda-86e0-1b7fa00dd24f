package com.ykl.med.masterdata.vo.form;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@Schema(description = "患者量表记录")
public class PatientFormVO {
    @Schema(description = "主键id", example = "1")
    @Stringify
    private Long id;

    @Schema(description = "创建时间", example = "2023-01-01 00:00:00")
    @TimestampConvert
    private LocalDateTime createTime;

    @Schema(description = "患者id", example = "1234567")
    @Stringify
    private Long patientId;

    @Schema(description = "业务类型")
    private PatientFormBizType type;
    @Schema(description = "业务id", example = "1")
    @Stringify
    private Long bizId;
    @Schema(description = "shaCode", example = "argartrtwqe123123")
    private String shaCode;
    @Schema(description = "量表id", example = "1")
    @Stringify
    private Long formId;
    @Schema(description = "量表名称", example = "XXXX")
    private String formName;
    @Schema(description = "量表备注", example = "XXXX")
    private String formRemark;
    @Schema(description = "量表url", example = "XXXX")
    private String url;
    @Schema(description = "量表内容", example = "XXXX")
    private String content;
    @Schema(description = "量表结果", example = "XXXX")
    private String result;
    @Schema(description = "分数", example = "1")
    private Integer score;
    @Schema(description = "是否已经发送给患者", example = "true")
    private Boolean sendStatus;

    @TimestampConvert
    @Schema(description = "发送时间", example = "2023-01-01 00:00:00")
    private LocalDateTime sendTime;
    @Schema(description = "是否已经填写", example = "true")
    private Boolean writeStatus;

    @TimestampConvert
    @Schema(description = "填写时间", example = "2023-01-01 00:00:00")
    private LocalDateTime writeTime;
}
