package com.ykl.med.masterdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "单位")
public class UnitVO implements Serializable {

    private static final long serialVersionUID = -7168421585914497712L;

    @Schema(description = "单位", example = "ss")
    private String unit;

    @Schema(description = "与标准单位换算比", example = "12")
    private BigDecimal standardUnitRatio;
}
