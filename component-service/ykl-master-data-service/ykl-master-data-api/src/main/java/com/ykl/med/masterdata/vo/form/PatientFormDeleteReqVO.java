package com.ykl.med.masterdata.vo.form;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "患者表单删除请求对象")
public class PatientFormDeleteReqVO {
    @Stringify
    @Schema(description = "患者表单id", example = "1")
    private Long id;

    @Schema(description = "患者id", example = "1234567")
    @Stringify
    private Long patientId;

    @Schema(description = "业务类型")
    @NotNull(message = "业务类型不能为空")
    private PatientFormBizType type;

    @Schema(description = "业务id", example = "1")
    @Stringify
    private Long bizId;
}
