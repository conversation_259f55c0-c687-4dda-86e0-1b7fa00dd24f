package com.ykl.med.masterdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 文本输入框VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "TextInputVO", description = "文本输入框VO")
public class TextInputVO extends BaseFormItemVO implements Serializable {

    private static final long serialVersionUID = -6357397153471938360L;

    @Schema(description = "最小长度")
    private Integer minLength;

    @Schema(description = "最大长度")
    private Integer maxLength;

}
