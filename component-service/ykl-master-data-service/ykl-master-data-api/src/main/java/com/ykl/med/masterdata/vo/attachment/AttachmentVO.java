package com.ykl.med.masterdata.vo.attachment;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.masterdata.enums.AttachmentBizType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "附件")
public class AttachmentVO {
    @Schema(description = "主键")
    @Stringify
    private Long id;
    @Schema(description = "创建时间")
    @TimestampConvert
    private LocalDateTime createTime;
    @Schema(description = "业务类型")
    private AttachmentBizType bizType;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "路径")
    private String path;
    @Schema(description = "后缀")
    private String suffix;
    @Schema(description = "状态")
    private CommonStatusEnum status;
}
