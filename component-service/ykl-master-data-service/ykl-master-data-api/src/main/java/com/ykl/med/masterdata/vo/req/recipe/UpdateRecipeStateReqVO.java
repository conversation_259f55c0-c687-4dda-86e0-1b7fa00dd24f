package com.ykl.med.masterdata.vo.req.recipe;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Data
@Schema(name = "UpdateRecipeStateReqVO", description = "更新食谱状态入参")
public class UpdateRecipeStateReqVO implements Serializable {

    @NotNull(message = "ids can not null")
    @Schema(description = "食谱唯一id", example = "11011", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> ids;

    @NotNull(message = "state can not null")
    @Schema(description = "状态(false-关闭/true-开启)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean state;
}
