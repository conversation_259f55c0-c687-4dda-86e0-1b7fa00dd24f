package com.ykl.med.masterdata.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExaminationTestCategory {
    ELECTROCARDIOGRAM(1, "心电图项目"),
    LUNG_FUNCTION(2, "肺功能项目"),
    DNA_TEST(3, "基因检测项目"),
    LABORATORY_TEST(4, "检验项目"),
    ;
    @EnumValue
    private final int code;
    private final String desc;

    public static ExaminationTestCategory getEnumByDesc(String desc) {
        for (ExaminationTestCategory examinationTestCategory : ExaminationTestCategory.values()) {
            if (examinationTestCategory.getDesc().equals(desc)) {
                return examinationTestCategory;
            }
        }
        return null;
    }
}
