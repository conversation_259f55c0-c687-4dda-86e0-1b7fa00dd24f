package com.ykl.med.masterdata.entiry.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class FrequencyParamDTO {

    @Schema(description = "唯一ID")
    private String id;

    // 系统默认频次ID
    @Schema(description = "系统默认频次ID")
    private String frequencyId;

    @Schema(description = "频率类型:每秒,每分,每日,每周,每月,间隔,临时一次")
    private String frequencyType;

    @Schema(description = "频次起始时间")
    private List<String> frequencyStartTime;

    @Schema(description = "固定日期")
    private String frequencyFixDate;

    @Schema(description = "时间间隔: 数字")
    private String frequencyIntervalTime;

    @Schema(description = "时间间隔单位: 分,时,日,月,周")
    private String frequencyIntervalTimeUnit;

    @Schema(description = "执行时间列表，格式：2 08:00:00,08:00:00,10:00")
    private List<String> frequencyExecTimeList;

    @Schema(description = "生成执行时间的开始时间")
    private LocalDateTime startTime;

    @Schema(description = "生成执行时间的结束时间")
    private LocalDateTime endTime;
}
