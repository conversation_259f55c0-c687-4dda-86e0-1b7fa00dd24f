package com.ykl.med.masterdata.vo;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.masterdata.enums.MediaType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "运动训练视图模型")
public class SportTrainVO implements Serializable {

    private static final long serialVersionUID = 196346418019094349L;

    @Stringify
    @Schema(description = "唯一标识", example = "1")
    private Long id;

    @Schema(description = "名称", example = "运动训练")
    private String name;

    @Schema(description = "操作方式", example = "操作方式示例")
    private String operationMethod;

    @Schema(description = "封面图片")
    private List<String> coverImg;

    @Schema(description = "文件类型", example = "VIDEO")
    private MediaType mediaType;

    @Schema(description = "媒体文件")
    private List<String> mediaFile;

    @Schema(description = "备注", example = "这是一个备注示例")
    private String remark;

    @Schema(description = "状态，默认为ENABLE", example = "ENABLE")
    private CommonStatusEnum status;
}