package com.ykl.med.masterdata.api;

import com.ykl.med.framework.common.pojo.IdListReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.vo.FormManageVO;
import com.ykl.med.masterdata.vo.req.FormManageListReqVO;
import com.ykl.med.masterdata.vo.req.FormManageSaveReqVO;
import com.ykl.med.masterdata.vo.req.FormManageUpdateStatusReqVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/15
 */
@FeignClient(name = "ykl-master-data-service", path = "/ykl-master-data-service/form/manage")
public interface FormManageFeign {
    @PostMapping("/page")
    PageResult<FormManageVO> page(@Valid @RequestBody FormManageListReqVO reqVO);

    @PostMapping("/list")
    List<FormManageVO> list(@Valid @RequestBody FormManageListReqVO reqVO);

    @PostMapping("/detail")
    FormManageVO detail(@RequestParam("id") Long id);

    @PostMapping("/listDetail")
    List<FormManageVO> listDetail(@RequestBody Collection<Long> ids);

    @PostMapping("/updateStatus")
    void updateStatus(@Valid @RequestBody FormManageUpdateStatusReqVO reqVO);

    @PostMapping("/save")
    void save(@Valid @RequestBody FormManageSaveReqVO reqVO);

    @PostMapping("/delete")
    void delete(@Valid @RequestBody IdListReqVO reqVO);
}
