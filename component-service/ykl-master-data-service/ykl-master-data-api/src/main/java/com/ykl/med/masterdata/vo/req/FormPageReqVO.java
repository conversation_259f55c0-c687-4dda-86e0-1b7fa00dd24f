package com.ykl.med.masterdata.vo.req;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.masterdata.enums.FormType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 表单列表请求VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "FormListReqVO", description = "表单列表请求VO")
public class FormPageReqVO extends PageParam {

    private static final long serialVersionUID = 1824148129189512277L;

    @Schema(description = "表单名称")
    private String label;

    @Schema(description = "拼音简码")
    private String code;

    @Schema(description = "表单分类, 功能康复: FUNC_REHAB, 运动康复: SPORT_REHAB, 心理康复: PSYCHO_REHAB")
    private String category;

    @Schema(description = "表单类型")
    private FormType type;

    @Schema(description = "是否线上", example = "true")
    private Boolean online;

    @Schema(description = "是否必填", example = "true")
    private Boolean required;

    @Schema(description = "状态", example = "ENABLE")
    private CommonStatusEnum status;

}
