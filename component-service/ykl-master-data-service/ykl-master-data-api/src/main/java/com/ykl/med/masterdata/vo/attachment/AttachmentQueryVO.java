package com.ykl.med.masterdata.vo.attachment;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.masterdata.enums.AttachmentBizType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "附件查询请求")
public class AttachmentQueryVO extends PageParam {
    @Schema(description = "业务类型数组")
    private List<AttachmentBizType> bizTypes;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "名称数组")
    private List<String> names;

    @Schema(description = "状态")
    private CommonStatusEnum status;
}
