package com.ykl.med.masterdata.vo.version;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "应用版本查询对象")
public class AppVersionQueryVO extends PageParam {
    @Schema(description = "版本", example = "1.0.1")
    private String version;

    @Schema(description = "应用id", example = "1")
    @NotNull(message = "应用id不能为空")
    @Stringify
    private Long appId;
}
