package com.ykl.med.masterdata.entiry.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 食物表;
 * <AUTHOR> xkli
 * @date : 2023-11-30
 */
@Data
@Schema(description = "食物表")
public class FoodAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "名称", example = "")
    private String name ;

    @Schema(description = "名称简拼码", example = "")
    private String nameShort ;

    @Schema(description = "类型（如：主食，蔬菜，水果等）【字典】", example = "")
    private String type ;

    @Schema(description = "每份数量", example = "")
    private String spec ;

    @Schema(description = "每份数量单位（如：g）", example = "")
    private String specUnit ;

    @Schema(description = "媒体文件（图片文件，JSON数组）", example = "")
    private List<String> mediaFiles ;

    @Schema(description = "水（单位：g）", example = "")
    private String water ;

    @Schema(description = "热量（kcal）", example = "")
    private String kilocalorie ;

    @Schema(description = "蛋白质（g）", example = "")
    private String protein ;

    @Schema(description = "脂肪（g）", example = "")
    private String fat ;

    @Schema(description = "碳水化合物（g）", example = "")
    private String carbohydrate ;

    @Schema(description = "膳食纤维（g）", example = "")
    private String dietaryFiber ;

    @Schema(description = "胆固醇（mg）", example = "")
    private String cholesterol ;

    @Schema(description = "灰分（g）", example = "")
    private String ash ;

    @Schema(description = "维生素-A（μg）", example = "")
    private String vitaminA ;

    @Schema(description = "胡萝卜素（μg）", example = "")
    private String vitaminBc ;

    @Schema(description = "视黄醛（μg）", example = "")
    private String retinol ;

    @Schema(description = "硫胺素（mg）", example = "")
    private String thiamine ;

    @Schema(description = "核黄素（mg）", example = "")
    private String riboflavin ;

    @Schema(description = "烟酸（mg）", example = "")
    private String niacin ;

    @Schema(description = "维生素C（mg）", example = "")
    private String vitaminC ;

    @Schema(description = "维生素E（mg）", example = "")
    private String vitaminE ;

    @Schema(description = "矿物质-钙（mg）", example = "")
    private String mineralCa ;

    @Schema(description = "矿物质-磷（mg）", example = "")
    private String mineralP ;

    @Schema(description = "矿物质-钾（mg）", example = "")
    private String mineralK ;

    @Schema(description = "矿物质-钠（mg）", example = "")
    private String mineralNa ;

    @Schema(description = "矿物质-镁（mg）", example = "")
    private String mineralMg ;

    @Schema(description = "矿物质-铁（mg）", example = "")
    private String mineralFe ;

    @Schema(description = "矿物质-锌（mg）", example = "")
    private String mineralZn ;

    @Schema(description = "矿物质-硒（mg）", example = "")
    private String mineralSe ;

    @Schema(description = "矿物质-铜（μg）", example = "")
    private String mineralCu ;

    @Schema(description = "矿物质-锰（mg）", example = "")
    private String mineralMn ;

    @Schema(description = "矿物质-碘（单位：μg）", example = "")
    private String mineralI ;

    @Schema(description = "异亮氨酸（mg）", example = "")
    private String isoleucine ;

    @Schema(description = "亮氨酸（mg）", example = "")
    private String leucine ;

    @Schema(description = "赖氨酸（mg）", example = "")
    private String lysine ;

    @Schema(description = "蛋氨酸（mg）", example = "")
    private String methionine ;

    @Schema(description = "胱氨酸（mg）", example = "")
    private String cystine ;

    @Schema(description = "苯丙氨酸（mg）", example = "")
    private String phenylalanine ;

    @Schema(description = "酪氨酸（mg）", example = "")
    private String tyrosine ;

    @Schema(description = "苏氨酸（mg）", example = "")
    private String threonine ;

    @Schema(description = "色氨酸（mg）", example = "")
    private String tryptophan ;

    @Schema(description = "缬氨酸（mg）", example = "")
    private String valine ;

    @Schema(description = "精氨酸（mg）", example = "")
    private String arginine ;

    @Schema(description = "组氨酸(mg)", example = "")
    private String histidine ;

    @Schema(description = "丙氨酸(mg)", example = "")
    private String alanine ;

    @Schema(description = "天冬氨酸(mg)", example = "")
    private String aspartate ;

    @Schema(description = "谷氨酸(mg)", example = "")
    private String glutamate ;

    @Schema(description = "甘氨酸(mg)", example = "")
    private String glycine ;

    @Schema(description = "脯氨酸(mg)", example = "")
    private String proline ;

    @Schema(description = "丝氨酸(mg)", example = "")
    private String serine ;

    @Schema(description = "脂肪酸（g）", example = "")
    private String fattyAcid ;

    @Schema(description = "饱和脂肪酸（g）", example = "")
    private String saturatedFattyAcids ;

    @Schema(description = "单不饱和脂肪酸（g）", example = "")
    private String monounsaturatedFattyAcids ;

    @Schema(description = "多不饱和脂肪酸（g）", example = "")
    private String polyunsaturatedFattyAcid ;

    @Schema(description = "叶酸（μg）", example = "")
    private String vitaminFa ;

    @Schema(description = "甜菜碱(mg)", example = "")
    private String betaine ;

    @Schema(description = "游离胆碱(mg)", example = "")
    private String freeCholine ;

    @Schema(description = "甘油磷酸胆碱(mg)", example = "")
    private String gpc ;

    @Schema(description = "磷酸胆碱(mg)", example = "")
    private String cholinePhosphate ;

    @Schema(description = "卵磷脂(mg)", example = "")
    private String lecithin ;

    @Schema(description = "神经鞘磷脂(mg)", example = "")
    private String sphingomyelin ;

    @Schema(description = "总胆碱(mg)", example = "")
    private String totalCholine ;

    @Schema(description = "鸟嘌呤(mg)", example = "")
    private String guanine ;

    @Schema(description = "腺嘌呤(mg)", example = "")
    private String adenine ;

    @Schema(description = "次黄嘌呤(mg)", example = "")
    private String hypoxanthine ;

    @Schema(description = "黄嘌呤(mg)", example = "")
    private String xanthine ;

    @Schema(description = "总嘌呤含量(mg)", example = "")
    private String totalPurineContent ;

    @Schema(description = "胆碱(mg)", example = "")
    private String choline ;

    @Schema(description = "生物素（ug）", example = "")
    private String biotin ;

    @Schema(description = "泛酸(mg)", example = "")
    private String pantothenicAcid ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

}
