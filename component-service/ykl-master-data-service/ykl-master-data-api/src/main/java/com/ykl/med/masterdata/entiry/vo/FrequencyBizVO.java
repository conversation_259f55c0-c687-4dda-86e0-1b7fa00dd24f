package com.ykl.med.masterdata.entiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> xkli
 * @date : 2024-8-24
 */
@Data
@Schema(description = "频次业务数据表")
public class FrequencyBizVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "频次ID",example = "")
    private String frequencyId ;

    @Schema(description = "频次名称",example = "")
    private String frequencyName ;

    @Schema(description = "频次时间(如: 08:00,12:00,18:00)",example = "")
    private String times ;

    /**
     * 其他字段
     * */
    @Schema(description = "生成的频次名称",example = "")
    String frequencyGenName;

    @Schema(description = "频率类型: 每分,每时,每日,每周,每月,间隔,临时一次",example = "")
    String frequencyType;

    @Schema(description = "频次起始时间: unix时间戳(毫秒)",example = "")
    List<String> frequencyStartTime;

    @Schema(description = "固定日期: unix时间戳(毫秒)",example = "")
    String frequencyFixDate;

    @Schema(description = "时间间隔: 数字",example = "")
    String frequencyIntervalTime;

    @Schema(description = "时间间隔单位: 分,时,日,月,周",example = "")
    String frequencyIntervalTimeUnit;

    @Schema(description = "执行时间列表，格式：2 08:00:00,08:00:00,10:00",example = "")
    List<String> frequencyExecTimeList;

    @Schema(description = "计划执行时间列表, 格式: 2000-01-01 08:00:00",example = "")
    public List<String> frequencyGenExecTimeList = new ArrayList<>();

    @Schema(description = "计划执行时间列表, 格式: 2000-01-01 08:00:00",example = "")
    public Map<String, String> frequencyGenExecTimeMap = new HashMap<>();

    @Schema(description = "当前时间之后的执行时间列表",example = "")
    public List<String> nextGenExecTimeList = new ArrayList<>();

    @Schema(description = "频率配置数据错误信息",example = "")
    String frequencyError;
}
