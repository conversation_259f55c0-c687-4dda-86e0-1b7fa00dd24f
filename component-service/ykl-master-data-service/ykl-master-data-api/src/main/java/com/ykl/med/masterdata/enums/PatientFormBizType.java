package com.ykl.med.masterdata.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "应用类型,可选值:(FOLLOWUP_INSPECT-检查随访计划、FOLLOWUP_TRACK-跟踪随访计划、PSYCHOLOGY-心理康复方案、NUTRITION-营养康复方案)")
public enum PatientFormBizType {
    FOLLOWUP_INSPECT("检查随访计划"),
    FOLLOWUP_TRACK("跟踪随访计划"),
    PSYCHOLOGY("心理康复方案"),
    NUTRITION("营养康复方案"),
    SPORT("运动康复方案"),
    FUNC("功能康复方案"),
    CONSULT("问诊"),
    ;


    private final String desc;
}
