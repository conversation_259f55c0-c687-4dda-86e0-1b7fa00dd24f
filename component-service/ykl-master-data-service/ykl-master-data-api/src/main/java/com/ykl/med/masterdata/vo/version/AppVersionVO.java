package com.ykl.med.masterdata.vo.version;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.masterdata.enums.AppVersionFileType;
import com.ykl.med.masterdata.enums.AppVersionUpdateCategory;
import com.ykl.med.masterdata.enums.AppVersionUpdateType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AppVersionVO {
    @Schema(description = "ID", example = "1")
    private Long id;

    @Schema(description = "应用类型", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long appId;

    @Schema(description = "版本号", example = "1.0.0")
    private String version;

    @Schema(description = "版本描述", example = "修改了一些bug")
    private String versionDesc;

    @Schema(description = "下载链接", example = "http://www.baidu.com")
    private String downloadUrl;

    @Schema(description = "状态", example = "ENABLE")
    private CommonStatusEnum status;

    @Schema(description = "创建时间", example = "16839423412")
    @TimestampConvert
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "16839423412")
    @TimestampConvert
    private LocalDateTime updateTime;

    @Schema(description = "更新类型", example = "SILENT")
    private AppVersionUpdateType updateType;

    @Schema(description = "更新类型", example = "FULL_UPDATE", requiredMode = Schema.RequiredMode.REQUIRED)
    private AppVersionUpdateCategory updateCategory;

    @Schema(description = "文件类型", example = "INSTALLATION_PACKAGE", requiredMode = Schema.RequiredMode.REQUIRED)
    private AppVersionFileType fileType;

    @TimestampConvert
    @Schema(description = "发布时间", example = "159002344444", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime publishTime;
}
