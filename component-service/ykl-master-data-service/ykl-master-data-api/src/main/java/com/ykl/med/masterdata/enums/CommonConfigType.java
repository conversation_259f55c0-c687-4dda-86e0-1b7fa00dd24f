package com.ykl.med.masterdata.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "常见配置类型,APP:患者端,WEB:医护端,ALL:全部系统,BACK:后端")
public enum CommonConfigType {
    APP(1, "患者端"),
    WEB(2, "医护端"),
    ALL(3, "全部系统"),
    BACK(4, "后端");
    /**
     * 编码
     */
    @EnumValue
    private final int code;

    /**
     * 描述
     */
    private final String desc;

}
