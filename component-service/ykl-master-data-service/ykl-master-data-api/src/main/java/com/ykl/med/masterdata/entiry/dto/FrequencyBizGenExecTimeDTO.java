package com.ykl.med.masterdata.entiry.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2024-8-24
 */
@Data
@Schema(description = "频次业务生成执行时间对象")
public class FrequencyBizGenExecTimeDTO {

    @Schema(description = "频次业务ID", example = "")
    String id;

    @Schema(description = "频次起始时间,格式:2000-01-01 14:23:00", example = "")
    String frequencyStartTime;

    @Schema(description = "固定日期, 此值为空时, 使用频次起始时间作为固定日期",example = "")
    String frequencyFixDate;

    @Schema(description = "生成执行计划的开始时间,格式:2000-01-01 14:23:00", example = "")
    String startTime;

    @Schema(description = "生成执行计划的结束时间,格式:2000-01-01 14:23:00", example = "")
    String endTime;
}
