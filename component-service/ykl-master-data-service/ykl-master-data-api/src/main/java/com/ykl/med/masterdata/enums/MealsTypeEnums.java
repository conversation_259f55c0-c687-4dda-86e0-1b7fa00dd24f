package com.ykl.med.masterdata.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Getter
@AllArgsConstructor
@Schema(enumAsRef = true, description = "每日三餐类型, DINNER_CLASS_BREAKFAST(早餐)、MORNING_PLUS_FOOD(早加餐)、DINNER_CLASS_LUNCH(午餐)、DINNER_CLASS_LUNCH_ADDITIONAL(午加餐)、DINNER_CLASS_DINNER(晚餐)")
public enum MealsTypeEnums {

    DINNER_CLASS_BREAKFAST(1, "早餐"),
    DINNER_CLASS_BREAKFAST_ADDITIONAL(2, "早加餐"),
    DINNER_CLASS_LUNCH(3, "午餐"),
    DINNER_CLASS_LUNCH_ADDITIONAL(4, "午加餐"),
    DINNER_CLASS_DINNER(5, "晚餐");

    private final Integer type;

    private final String desc;

    private static final Map<Integer, MealsTypeEnums> BUSINESS_CODE_MAP = new HashMap<>();
    private static final Map<String, MealsTypeEnums> BUSINESS_NAMA_MAP = new HashMap<>();

    static {
        for (MealsTypeEnums mealsTypeEnums : MealsTypeEnums.values()) {
            BUSINESS_CODE_MAP.put(mealsTypeEnums.getType(), mealsTypeEnums);
            BUSINESS_NAMA_MAP.put(mealsTypeEnums.name(), mealsTypeEnums);
        }
    }

    /**
     * 根据编码获取 MealsTypeEnums
     *
     * @param type 类型
     * @return MealsTypeEnums
     */
    public static MealsTypeEnums getByCode(Integer type) {
        return BUSINESS_CODE_MAP.get(type);
    }

    /**
     * 根据枚举获取 MealsTypeEnums
     *
     * @param name 枚举
     * @return MealsTypeEnums
     */
    public static MealsTypeEnums getByName(String name) {
        return BUSINESS_NAMA_MAP.get(name);
    }


}
