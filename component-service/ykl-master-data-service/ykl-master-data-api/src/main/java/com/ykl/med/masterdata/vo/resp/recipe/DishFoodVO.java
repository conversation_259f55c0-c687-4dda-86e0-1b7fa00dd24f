package com.ykl.med.masterdata.vo.resp.recipe;

import com.ykl.med.base.number.NumberUnitExchange;
import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Data
@Schema(description = "食物简介")
public class DishFoodVO {

    @Schema(description = "食品id", example = "100111")
    @Stringify
    private Long dishId;

    @Schema(description = "食物id", example = "100111")
    @Stringify
    private Long foodId;

    @Schema(description = "食物名称", example = "土豆")
    private String foodName;

    @Schema(description = "数量", example = "100")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal amount;

    @Schema(description = "单位（如：g）", example = "g")
    private String specUnit;

    @Schema(description = "热量（kcal）", example = "600.0")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal kilocalorie;

    @Schema(description = "蛋白质（g）", example = "26.4")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal protein;

    @Schema(description = "脂肪（g）", example = "80.0")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal fat;

    @Schema(description = "碳水化合物（g）", example = "100.0")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal carbohydrate;


}
