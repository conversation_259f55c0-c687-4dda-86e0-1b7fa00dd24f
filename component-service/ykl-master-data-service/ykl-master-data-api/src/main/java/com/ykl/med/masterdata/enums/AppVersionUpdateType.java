package com.ykl.med.masterdata.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "APP版本更新类型，可选值：SILENT(静默更新),RECOMMENDED(推荐更新),MANDATORY(强制更新)")
public enum AppVersionUpdateType {
    SILENT(1, "静默更新"),
    RECOMMENDED(5, "推荐更新"),
    MANDATORY(10, "强制更新"),
    ;

    private final Integer code;
    private final String desc;
}
