package com.ykl.med.masterdata.vo.req;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.masterdata.enums.TreatmentProjectType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 诊疗项目
 */
@Schema(description = "诊疗项目查询")
@Data
public class TreatmentProjectQueryVO extends PageParam {
    @Schema(description = "词语", example = "示例词语")
    private String word;


    @Schema(description = "诊疗类型", example = "示例类型")
    private TreatmentProjectType type;


    @Schema(description = "状态", example = "ENABLE", defaultValue = "ENABLE")
    private CommonStatusEnum status;

    @Schema(description = "子类型", example = "示例子类型")
    private String subType;

}