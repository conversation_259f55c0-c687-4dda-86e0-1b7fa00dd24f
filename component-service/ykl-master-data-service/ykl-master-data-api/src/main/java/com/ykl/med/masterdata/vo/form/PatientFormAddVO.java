package com.ykl.med.masterdata.vo.form;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "患者表单添加对象")
public class PatientFormAddVO {
    @Schema(description = "患者id", example = "1234567")
    @Stringify
    //@NotNull(message = "患者id不能为空")
    private Long patientId;

    @Schema(description = "业务类型")
    @NotNull(message = "业务类型不能为空")
    private PatientFormBizType type;

    @Stringify
    @Schema(description = "业务id", example = "1")
    @NotNull(message = "业务id不能为空")
    private Long bizId;
    /**
     * 用于各个业务标记唯一的，比如症状为当天同一个患者只有一个相同的量表
     * shaCode =sha1(日期+患者id+量表id)
     * 不传会走默认规则，Type,PatientId,BizId,FormId
     */
    @Schema(description = "shaCode", example = "argartrtwqe123123")
    private String shaCode;

    @Stringify
    @Schema(description = "量表id", example = "1")
    @NotNull(message = "量表id不能为空")
    private Long formId;

    @Schema(description = "是否已经发送给患者", example = "true")
    private Boolean sendStatus = false;
}
