package com.ykl.med.masterdata.vo.req.recipe;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@Schema(name = "RecipeQueryReqVO", description = "菜谱列表请求入参")
public class RecipeQueryReqVO implements Serializable {

    @Schema(description = "菜品名称")
    private String name;

    @Schema(description = "状态(false-关闭/true-开启)")
    private Boolean state;

    @Schema(description = "适用人群,字典")
    private String scopePeople;

    @Schema(description = "适用季度,字典")
    private String scopeSeason;

    @Schema(description = "适用地区,字典")
    private String scopeRegion;

    @NotNull(message = "pageNo can not null")
    @Schema(description = "分页参数", example = "默认1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageNo = 1;

    @NotNull(message = "pageNo can not null")
    @Schema(description = "分页参数", example = "默认10", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageSize = 10;

}
