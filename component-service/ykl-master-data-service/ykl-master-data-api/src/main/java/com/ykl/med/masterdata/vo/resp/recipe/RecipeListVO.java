package com.ykl.med.masterdata.vo.resp.recipe;

import com.ykl.med.base.number.NumberUnitExchange;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Data
@Schema(description = "食谱列表")
public class RecipeListVO implements Serializable {

    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "食谱名称")
    private String recipeName;

    @Schema(description = "食谱名称-后台展示")
    private String recipeNameBackstage;

    @Schema(description = "适用人群,字典")
    private String scopePeople;

    @Schema(description = "适用季度,字典")
    private String scopeSeason;

    @Schema(description = "适用地区,字典")
    private String scopeRegion;

    @Schema(description = "图片")
    private String images;

    @Schema(description = "状态(false-关闭/true-开启)")
    private Boolean state;

    @Schema(description = "总热量（kcal）")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal kilocalorie;

    @Schema(description = "总蛋白质（g）")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal protein;

    @Schema(description = "总脂肪（g）")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal fat;

    @Schema(description = "总碳水化合物（g）")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal carbohydrate;

    @Schema(description = "蛋白质百分比（g）")
    private Integer proteinRatio;

    @Schema(description = "脂肪百分比（g）")
    private Integer fatRatio;

    @Schema(description = "碳水化合物百分比（g）")
    private Integer carbohydrateRatio;

    @Schema(description = "油（g）")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal oil;

    @Schema(description = "盐（g）")
    private String salt;

    @Schema(description = "早餐")
    private List<String> morningFood;

    @Schema(description = "早加餐")
    private List<String> morningPlusFood;

    @Schema(description = "午餐")
    private List<String> noonFood;

    @Schema(description = "午加餐")
    private List<String> noonPlusFood;

    @Schema(description = "晚餐")
    private List<String> dinnerFood;

    @Schema(description = "更新时间")
    @TimestampConvert
    private LocalDateTime updateTime;

    @Schema(description = "创建时间")
    @TimestampConvert
    private LocalDateTime createTime;

}
