package com.ykl.med.masterdata.api;

import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.CommonResult;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.entiry.dto.FrequencyBizAddDTO;
import com.ykl.med.masterdata.entiry.dto.FrequencyBizQueryDTO;
import com.ykl.med.masterdata.entiry.dto.FrequencyParamDTO;
import com.ykl.med.masterdata.entiry.dto.FrequencySetTimeDTO;
import com.ykl.med.masterdata.entiry.vo.FrequencyBizVO;
import com.ykl.med.masterdata.entiry.vo.FrequencyDataVO;
import com.ykl.med.masterdata.entiry.vo.FrequencyUtilVO;
import com.ykl.med.masterdata.vo.FrequencyDataScopeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "ykl-master-data-service", path = "/ykl-master-data-service")
public interface FrequencyDataFeign {
    /**
     * 使用频率管理增删改查
     * @param httpMethod : http请求的方法: GET/POST/PUT/DELETE
     * @param obj : http请求对象
     * */
    @PostMapping("/api/frequencyData/crud")
    PageResult<FrequencyDataVO> crud(@RequestParam(value = "httpMethod") String httpMethod, @RequestBody Object obj);

    /**
     * 频次适用范围
     * */
    @PostMapping("/aggregation/frequencyData/scope")
    PageResult<FrequencyDataScopeVO> frequencyDataScope();

    /**
     * 查询业务频次数据
     * */
    @PostMapping("/aggregation/frequencyData/queryBizData")
    PageResult<FrequencyBizVO> queryBizData(@RequestBody FrequencyBizQueryDTO requestBody);

    /**
     * 增加业务频次数据
     * */
    @PostMapping("/aggregation/frequencyData/addBizData")
    CommonResult<FrequencyBizVO> addBizData(@RequestBody FrequencyBizAddDTO requestBody);

    /**
     * 判断频次次数限制
     * */
    @PostMapping("/aggregation/frequencyData/checkFrequencyTimesLimit")
    ServiceException checkFrequencyTimesLimit(@RequestParam(value = "setTimeString") String setTimeString);

    /**
     * 解析频次配置字符串
     * */
    @PostMapping("/aggregation/frequencyData/splitFrequencySetTime")
    PageResult<FrequencyUtilVO> splitFrequencySetTime(@RequestBody List<FrequencySetTimeDTO> requestBody);

    /**
     * 生成频次配置字符串
     * */
    @PostMapping("/aggregation/frequencyData/genFrequencySetTime")
    PageResult<FrequencyUtilVO> genFrequencySetTime(@RequestBody List<FrequencyParamDTO> frequencyParamDTOList);
}
