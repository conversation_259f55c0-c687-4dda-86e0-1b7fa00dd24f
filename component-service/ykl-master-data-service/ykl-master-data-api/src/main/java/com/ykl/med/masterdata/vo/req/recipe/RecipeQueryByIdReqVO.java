package com.ykl.med.masterdata.vo.req.recipe;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@Schema(name = "RecipeQueryReqVO", description = "菜谱详情请求入参")
public class RecipeQueryByIdReqVO implements Serializable {

    @Schema(description = "唯一id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;


    public RecipeQueryByIdReqVO() {
    }


    public RecipeQueryByIdReqVO(Long id) {
        this.id = id;
    }


}
