package com.ykl.med.masterdata.vo.req;

import com.ykl.med.masterdata.enums.FromManageTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025/4/15
 */
@Data
public class FormManageSaveReqVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "表单名称")
    @NotBlank(message = "表单名称 不能为空")
    private String name;

    @Schema(description = "表单类型")
    @NotNull(message = "表单类型 不能为空")
    private FromManageTypeEnum type;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "表单地址")
    @NotBlank(message = "表单地址 不能为空")
    private String url;

    @Schema(description = "状态(false-停用/true-启用)")
    @NotNull(message = "状态 不能为空")
    private Boolean status;

}
