package com.ykl.med.masterdata.entiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class FrequencyUtilVO {

    @Schema(description = "唯一ID")
    private String id;

    @Schema(description = "频次配置字符串")
    private String setTime;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "频率类型:每秒,每分,每日,每周,每月,间隔,临时一次")
    private String frequencyType;

    @Schema(description = "频次起始时间")
    private List<String> frequencyStartTime;

    @Schema(description = "固定日期")
    private String frequencyFixDate;

    @Schema(description = "时间间隔: 数字")
    private String frequencyIntervalTime;

    @Schema(description = "时间间隔单位: 分,时,日,月,周")
    private String frequencyIntervalTimeUnit;

    @Schema(description = "执行时间列表，格式：2 08:00:00,08:00:00,10:00")
    private List<String> frequencyExecTimeList;

    @Schema(description = "生成执行计划列表")
    private List<LocalDateTime> genExecTimeList = new ArrayList<>();

    @Schema(description = "频率配置数据错误信息",example = "")
    private String frequencyError;
}
