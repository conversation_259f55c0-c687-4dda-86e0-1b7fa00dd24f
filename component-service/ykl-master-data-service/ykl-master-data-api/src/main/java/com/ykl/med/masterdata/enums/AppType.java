package com.ykl.med.masterdata.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "应用类型,可选值:(PC_WEB:电脑网页,WECHAT_APPLET:微信小程序,MOBILE_WEB:移动网页,TABLET_APP_ANDROID:平板APP_安卓,TABLET_APP_IOS:平板APP_IOS,MOBILE_APP_ANDROID:手机APP_安卓,MOBILE_APP_IOS:手机APP_IOS)")
public enum AppType {
    PC_WEB("电脑网页"),
    WECHAT_APPLET("微信小程序"),
    MOBILE_WEB("移动网页"),
    TABLET_APP_ANDROID("平板APP_安卓"),
    TABLET_APP_IOS("平板APP_IOS"),
    MOBILE_APP_ANDROID("手机APP_安卓"),
    MOBILE_APP_IOS("手机APP_IOS");

    private final String desc;
}
