package com.ykl.med.masterdata.api.treatment;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.vo.ExaminationTestProjectVO;
import com.ykl.med.masterdata.vo.TreatmentSubTypeExaminationTestVO;
import com.ykl.med.masterdata.vo.req.TreatmentSubTypeExaminationTestQueryVO;
import com.ykl.med.masterdata.vo.req.TreatmentSubTypeExaminationTestReqVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 检验子类feign
 *
 * <AUTHOR>
 */
@FeignClient(name = "ykl-master-data-service", path = "/ykl-master-data-service/treatmentSubTypeExaminationTest")
public interface TreatmentSubTypeExaminationTestFeign {

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存或者更新治疗子类型检测数据")
    void saveOrUpdate(@RequestBody @Valid TreatmentSubTypeExaminationTestReqVO reqVO);

    @PostMapping("/updateStatusByIds")
    @Operation(summary = "根据 ids 更新状态")
    void updateStatusByIds(@RequestBody List<Long> ids, @RequestParam(value = "status") CommonStatusEnum status);

    @PostMapping("/updateStatusById")
    @Operation(summary = "根据 id 更新状态")
    void updateStatusById(@RequestParam(value = "id") Long id, @RequestParam(value = "status") CommonStatusEnum status);

    @PostMapping("/getBySubType")
    @Operation(summary = "根据子类型获取检测项目")
    TreatmentSubTypeExaminationTestVO getBySubType(@RequestParam(value = "subType") String subType);

    @PostMapping("/getById")
    @Operation(summary = "根据ID获取治疗子类型检测")
    TreatmentSubTypeExaminationTestVO getById(@RequestParam(value = "id") Long id);

    @PostMapping("/getByIds")
    @Operation(summary = "根据IDs获取治疗子类型检测")
    List<TreatmentSubTypeExaminationTestVO> getByIds(@RequestBody Collection<Long> ids);

    @PostMapping("/query")
    @Operation(summary = "查询治疗子类型检测")
    List<TreatmentSubTypeExaminationTestVO> query(@RequestBody TreatmentSubTypeExaminationTestQueryVO queryVO);

    @PostMapping("/queryPage")
    @Operation(summary = "分页查询治疗子类型检测")
    PageResult<TreatmentSubTypeExaminationTestVO> queryPage(@RequestBody TreatmentSubTypeExaminationTestQueryVO queryVO);

    @PostMapping("/getExaminationTestBySubType")
    @Operation(summary = "根据子类型获取检测项目")
    List<ExaminationTestProjectVO> getExaminationTestBySubType(@RequestParam(value = "subType") String subType);
}
