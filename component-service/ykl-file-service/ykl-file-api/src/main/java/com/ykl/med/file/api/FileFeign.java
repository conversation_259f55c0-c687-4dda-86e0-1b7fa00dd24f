package com.ykl.med.file.api;

import com.ykl.med.file.enums.FileBizType;
import com.ykl.med.file.vo.FileListVO;
import com.ykl.med.file.vo.FileQueryVO;
import com.ykl.med.file.vo.FileUploadRespVO;
import com.ykl.med.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
@FeignClient("ykl-file-service")
public interface FileFeign {

    @PostMapping(value = "/file/uploadSingle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    FileUploadRespVO uploadSingle(@RequestParam("bucket") String bucket,
                                  @RequestParam("path") String path,
                                  @RequestPart("file") MultipartFile file,
                                  @RequestParam(value = "fileBizType", required = false) FileBizType fileBizType,
                                  @RequestParam(value = "remark", required = false) String remark);

    @GetMapping(value = "/file/getFileInfo")
    FileUploadRespVO getFileInfo(@RequestParam("bucket") String bucket, @RequestParam("path") String path);

    @GetMapping(value = "/file/getFileInfoList")
    List<FileUploadRespVO> getFileInfoList(@RequestParam("bucket") String bucket,
                                           @RequestParam("paths") List<String> paths);

    @GetMapping(value = "/file/deleteFiles")
    void deleteFiles(@RequestParam("bucket") String bucket, @RequestParam("paths") List<String> paths);

    @PostMapping(value = "/file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    List<FileUploadRespVO> upload(@NonNull @RequestParam("bucket") String bucket,
                                  @NonNull @RequestParam("path") String path,
                                  @RequestPart("files") MultipartFile[] files,
                                  @RequestParam(value = "fileBizType", required = false) FileBizType fileBizType);

    /**
     * 重新上传一次mino
     */
    @PostMapping(value = "/file/moveFile")
    FileUploadRespVO moveFile(@RequestParam("bucket") String bucket,
                              @RequestParam("path") String path,
                              @RequestParam("fullFileName") String fullFileName,
                              @RequestParam(value = "fileBizType", required = false) FileBizType fileBizType,
                              @RequestParam(value = "deleteOldFile") Boolean deleteOldFile);

    @PostMapping(value = "videoCompression")
    @Operation(summary = "视频压缩")
    FileUploadRespVO videoCompression(@RequestParam("bucket") String bucket,
                                      @RequestParam("videoPath") String videoPath,
                                      @RequestParam("height") Integer height,
                                      @RequestParam(value = "fileBizType", required = false) FileBizType fileBizType);

    @PostMapping(value = "thumbImage")
    @Operation(summary = "图片压缩成缩略图")
    FileUploadRespVO thumbImage(@RequestParam("bucket") String bucket,
                                @RequestParam("fullFileName") String fullFileName,
                                @RequestParam("thumbWidth") Integer thumbWidth,
                                @RequestParam("thumbHeight") Integer thumbHeight,
                                @RequestParam("fileBizType") FileBizType fileBizType);

    @PostMapping(value = "queryPage")
    @Operation(summary = "文件查询")
    PageResult<FileListVO> queryPage(@RequestBody FileQueryVO fileQueryVO);
}
