package com.ykl.med.file.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "文件业务类型")
public enum FileBizType {
    USER_AVATAR("用户头像"),
    DICOM("第三方工单的dicom"),
    EDU_COVER("文章封面图"),
    EDU_VIDEO("文章章节视频"),
    EDU_AUDIO("文章章节音频"),
    MANUAL_PRODUCTION("人工制作"),
    OTHER("其他");

    private final String desc;
}
