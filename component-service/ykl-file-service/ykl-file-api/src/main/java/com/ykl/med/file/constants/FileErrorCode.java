package com.ykl.med.file.constants;

import com.ykl.med.framework.common.exception.ErrorCode;

/**
 * <AUTHOR>
 */
public interface FileErrorCode {
    ErrorCode FILE_UPLOAD_REPEAT = new ErrorCode(180001, "请勿上传重复文件");
    ErrorCode FILE_UPLOAD_NAME_EMPTY = new ErrorCode(180002, "文件名称不能为空");

    ErrorCode FILE_NOT_SUPPORT = new ErrorCode(180003, "文件类型不支持");


    ErrorCode COMPRESS_ERROR = new ErrorCode(180004, "压缩失败");

    ErrorCode LIMIT_ERROR = new ErrorCode(180005, "限流");
}
