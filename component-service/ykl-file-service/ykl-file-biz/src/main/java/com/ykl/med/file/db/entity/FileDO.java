package com.ykl.med.file.db.entity;

import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.file.enums.FileBizType;
import com.ykl.med.file.enums.FileSubType;
import com.ykl.med.file.vo.FileExtra;
import com.ykl.med.file.vo.FileUploadRespVO;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.NoNullFastjsonTypeHandler;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Objects;

@TableName(value = "t_file", autoResultMap = true)
@Data
public class FileDO extends BaseDO {
    private String bucket;
    private String path;
    private String fileName;
    private FileBizType fileBizType;
    private FileSubType fileSubType;
    private Long fileSize;
    private Boolean deleted = false;
    @TableField(typeHandler = NoNullFastjsonTypeHandler.class)
    private FileExtra extra;
    private String remark;

    public String getFullFileName() {
        return path + "/" + fileName;
    }

    public void buildPath(String pathReq, String forceFileName) {
        if (StringUtils.isNotEmpty(forceFileName)) {
            path = pathReq;
        } else {
            path = pathReq + "/" + fileBizType;
        }
    }

    @SneakyThrows
    public void buildFileName(String forceFileName, MultipartFile file) {
        if (StringUtils.isEmpty(forceFileName)) {
            String hash;
            try (InputStream inputStream = file.getInputStream()) {
                hash = DigestUtil.sha256Hex(inputStream);
            }
            String suffix = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".")).toLowerCase();
            if (fileBizType == FileBizType.OTHER) {
                fileName = hash + suffix;
            } else {
                //这些类型都需要单独建文件夹，后续会压缩或者做不同尺寸的图,文件夹名称就是文件hash
                path = path + "/" + hash;
                //取文件后缀
                fileName = "source" + suffix;
            }
        } else {
            fileName = forceFileName;
        }
    }

    public FileUploadRespVO buildFileUploadRespVO() {
        FileUploadRespVO fileUploadRespVO = new FileUploadRespVO();
        fileUploadRespVO.setPath(getFullFileName());
        fileUploadRespVO.setName(fileName);
        fileUploadRespVO.setSize(fileSize);
        fileUploadRespVO.setFileType(fileSubType.getFileType());
        fileUploadRespVO.setWidth(extra.getWidth());
        fileUploadRespVO.setHeight(extra.getHeight());
        return fileUploadRespVO;
    }
}
