package com.ykl.med.file.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

public class CustomMultipartFile implements MultipartFile {
    private final File file;
    private final String contentType;

    public CustomMultipartFile(File file) {
        this(file, "application/octet-stream"); // 默认二进制流类型
    }

    public CustomMultipartFile(File file, String contentType) {
        this.file = file;
        this.contentType = contentType;
    }

    @Override
    public String getName() {
        return "file"; // Spring通常期望此为表单字段名
    }

    @Override
    public String getOriginalFilename() {
        return file.getName();
    }

    @Override
    public String getContentType() {
        return contentType;
    }

    @Override
    public boolean isEmpty() {
        return file.length() == 0;
    }

    @Override
    public long getSize() {
        return file.length();
    }

    @Override
    public byte[] getBytes() throws IOException {
        return Files.readAllBytes(file.toPath()); // 仅在必须时使用
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return Files.newInputStream(file.toPath()); // 关键改进：流式读取
    }

    @Override
    public void transferTo(File dest) throws IOException {
        try (InputStream in = getInputStream()) {
            Files.copy(in, dest.toPath(), StandardCopyOption.REPLACE_EXISTING);
        }
    }
}