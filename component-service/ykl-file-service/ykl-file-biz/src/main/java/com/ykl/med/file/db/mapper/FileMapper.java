package com.ykl.med.file.db.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ykl.med.file.db.entity.FileDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface FileMapper extends MPJBaseMapper<FileDO> {
    default FileDO getFile(String bucket, String path, String fileName) {
        LambdaQueryWrapper<FileDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileDO::getBucket, bucket)
                .eq(FileDO::getPath, path)
                .eq(FileDO::getFileName, fileName);
        return selectOne(wrapper);
    }
}