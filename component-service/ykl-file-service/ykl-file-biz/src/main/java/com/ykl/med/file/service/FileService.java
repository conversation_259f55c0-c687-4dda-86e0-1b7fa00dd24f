package com.ykl.med.file.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.ykl.med.file.constants.FileErrorCode;
import com.ykl.med.file.db.entity.FileDO;
import com.ykl.med.file.db.mapper.FileMapper;
import com.ykl.med.file.enums.FileBizType;
import com.ykl.med.file.enums.FileSubType;
import com.ykl.med.file.enums.FileType;
import com.ykl.med.file.util.*;
import com.ykl.med.file.vo.FileExtra;
import com.ykl.med.file.vo.FileListVO;
import com.ykl.med.file.vo.FileQueryVO;
import com.ykl.med.file.vo.FileUploadRespVO;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Service
public class FileService {
    @Resource
    private FileMapper fileMapper;
    private final Lock lockVideoThumb = new ReentrantLock();
    private final Lock lockMove = new ReentrantLock();
    private final Lock lockImageThumb = new ReentrantLock();

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public FileUploadRespVO upload(String bucket,
                                   String path,
                                   String forceFileName,
                                   FileBizType fileBizType,
                                   MultipartFile file,
                                   String remark) {
        //文件类型过滤
        FileSubType fileSubType = FileTypeValidator.getFileType(file);
        AssertUtils.notNull(fileSubType, FileErrorCode.FILE_NOT_SUPPORT);
        FileExtra fileExtra = new FileExtra();
        if (fileSubType.getFileType() == FileType.image) {
            ImageUtil.buildFileExtra(fileExtra, file);
        }
        FileDO fileDO = new FileDO();
        fileDO.setFileBizType(fileBizType == null ? FileBizType.OTHER : fileBizType);
        fileDO.buildPath(path, forceFileName);
        fileDO.buildFileName(forceFileName, file);
        fileDO.setFileSubType(fileSubType);
        fileDO.setBucket(bucket);
        fileDO.setExtra(fileExtra);
        fileDO.setFileSize(file.getSize());
        fileDO.setRemark(remark);
        log.info("上传文件，{}", JSONObject.toJSONString(fileDO));
        if (FileUploader.isFileExists(bucket, fileDO.getFullFileName())) {
            FileDO old = fileMapper.getFile(bucket, fileDO.getPath(), fileDO.getFileName());
            if (old != null) {
                if (StringUtils.isNotEmpty(remark)) {
                    old.setRemark(remark);
                    fileMapper.updateById(old);
                }
            } else {
                fileMapper.insert(fileDO);
            }
            return this.getFileInfo(bucket, fileDO.getFullFileName());
        }
        FileUploader.putFile(bucket, fileDO.getFullFileName(), file, fileSubType);
        fileMapper.insert(fileDO);
        return fileDO.buildFileUploadRespVO();
    }

    public PageResult<FileListVO> queryPage(FileQueryVO fileQueryVO) {
        LambdaQueryWrapper<FileDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(fileQueryVO.getRemark()), FileDO::getRemark, fileQueryVO.getRemark())
                .gt(fileQueryVO.getStartCreateTime() != null, FileDO::getCreateTime, fileQueryVO.getStartCreateTime())
                .lt(fileQueryVO.getEndCreateTime() != null, FileDO::getCreateTime, fileQueryVO.getEndCreateTime())
                .eq(fileQueryVO.getFileBizType() != null, FileDO::getFileBizType, fileQueryVO.getFileBizType())
                .orderByDesc(FileDO::getCreateTime);
        Page<FileDO> page = fileMapper.selectPage(new Page<>(fileQueryVO.getPageNo(), fileQueryVO.getPageSize()), queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }
        List<FileListVO> fileListVOS = new ArrayList<>();
        for (FileDO fileDO : page.getRecords()) {
            FileListVO fileListVO = CopyPropertiesUtil.normalCopyProperties(fileDO, FileListVO.class);
            fileListVO.setFileType(fileListVO.getFileSubType().getFileType());
            fileListVOS.add(fileListVO);
        }
        return new PageResult<>(fileListVOS, page.getTotal());
    }

    public FileUploadRespVO getFileInfo(String bucket, String fullFileName) {
        String path = fullFileName.substring(0, fullFileName.lastIndexOf("/"));
        String fileName = fullFileName.substring(fullFileName.lastIndexOf("/") + 1);
        FileDO fileDO = fileMapper.getFile(bucket, path, fileName);
        if (fileDO != null) {
            return fileDO.buildFileUploadRespVO();
        } else {
            //老数据
            return FileUploader.getFileInfo(bucket, fullFileName);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(String bucket, String fullFileName) {
        String path = fullFileName.substring(0, fullFileName.lastIndexOf("/"));
        String fileName = fullFileName.substring(fullFileName.lastIndexOf("/") + 1);
        FileDO fileDO = fileMapper.getFile(bucket, path, fileName);
        FileUploader.deleteFiles(bucket, Lists.newArrayList(fullFileName));
        if (fileDO != null) {
            fileDO.setDeleted(true);
            fileMapper.updateById(fileDO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFiles(String bucket, List<String> fullFileNames) {
        for (String fullFileName : fullFileNames) {
            deleteFile(bucket, fullFileName);
        }
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public FileUploadRespVO moveFile(String bucket, String path, String fullFileName, FileBizType fileBizType, Boolean deleteOldFile) {
        log.info("移动文件，bucket:{},path:{},fullFileName:{},fileBizType:{},deleteOldFile:{}", bucket, path, fullFileName, fileBizType, deleteOldFile);
        String fileName = fullFileName.substring(fullFileName.lastIndexOf("/") + 1);
        if (!lockMove.tryLock()) {
            throw new ServiceException(FileErrorCode.LIMIT_ERROR);
        }
        try {
            String noSuffixFileName = fileName.substring(0, fileName.lastIndexOf("."));
            String suffix = fullFileName.substring(fullFileName.lastIndexOf(".")).toLowerCase();
            String newFileName = path + "/" + fileBizType + "/" + noSuffixFileName + "/" + "source" + "." + suffix;
            log.info("移动文件，newFileName:{}", newFileName);
            if (FileUploader.isFileExists(bucket, newFileName)) {
                return this.getFileInfo(bucket, newFileName);
            }
            try {
                // 1. 下载到临时文件
                FileUploader.download(bucket, fullFileName, fileName);
                log.info("下载文件完成，fileName:{}", fileName);
                // 2. 直接基于临时文件创建 MultipartFile（不读入内存）
                MultipartFile multipartFile = new CustomMultipartFile(new File(fileName));
                FileUploadRespVO respVO = this.upload(bucket, path, null, fileBizType, multipartFile, null);
                // 3. 清理旧文件
                if (deleteOldFile) {
                    this.deleteFile(bucket, fullFileName);
                }
                return respVO;
            } catch (Exception e) {
                throw new RuntimeException("Move file failed: " + fullFileName, e);
            } finally {
                File file = new File(fileName);
                file.delete();
            }
        } finally {
            lockMove.unlock();
        }


    }

    public List<FileUploadRespVO> getFileInfoList(String bucket, List<String> fullFileNames) {
        List<FileUploadRespVO> fileUploadRespVOList = new ArrayList<>();
        for (String fullFileName : fullFileNames) {
            fileUploadRespVOList.add(getFileInfo(bucket, fullFileName));
        }
        return fileUploadRespVOList;
    }

    @Transactional(rollbackFor = Exception.class)
    public FileUploadRespVO videoCompression(String bucket, String videoFileName, Integer height, FileBizType fileBizType) {
        log.info("视频压缩，bucket:{},videoFileName:{},height:{},fileBizType:{}", bucket, videoFileName, height, fileBizType);
        if (!lockVideoThumb.tryLock()) {
            throw new ServiceException(FileErrorCode.LIMIT_ERROR);
        }
        try {
            //判断是不是mp4
            AssertUtils.isTrue(videoFileName.endsWith(".mp4"), FileErrorCode.FILE_NOT_SUPPORT);
            String path = videoFileName.substring(0, videoFileName.lastIndexOf("/"));
            String updatedFileName = height + ".mp4";

            if (FileUploader.isFileExists(bucket, path + "/" + updatedFileName)) {
                log.info("视频压缩，视频已存在，bucket:{},videoFileName:{},height:{},fileBizType:{}", bucket, videoFileName, height, fileBizType);
                return this.getFileInfo(bucket, path + "/" + updatedFileName);
            }

            String localFileName = videoFileName.substring(videoFileName.lastIndexOf("/") + 1);
            FileUploader.download(bucket, videoFileName, localFileName);
            log.info("视频压缩，下载完成，bucket:{},videoFileName:{},height:{},fileBizType:{}", bucket, videoFileName, height, fileBizType);
            String compressionFilePath;
            try {
                compressionFilePath = VideoCompressionUtil.compressVideo(localFileName, height);
            } finally {
                File file = new File(localFileName);
                file.delete();
            }

            File compressionFile = null;
            try {
                compressionFile = new File(compressionFilePath);
                return this.upload(bucket, path, updatedFileName, fileBizType, new CustomMultipartFile(compressionFile), null);
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                if (compressionFile != null) {
                    compressionFile.delete();
                }
            }
        } finally {
            lockVideoThumb.unlock();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public FileUploadRespVO thumbImage(String bucket, String fullFileName, Integer thumbWidth, Integer thumbHeight, FileBizType fileBizType) {
        log.info("生成缩略图，bucket:{},fullFileName:{},thumbWidth:{},thumbHeight:{},fileBizType:{}", bucket, fullFileName, thumbWidth, thumbHeight, fileBizType);
        if (!lockImageThumb.tryLock()) {
            throw new ServiceException(FileErrorCode.LIMIT_ERROR);
        }
        try {
            String localFileName = fullFileName.substring(fullFileName.lastIndexOf("/") + 1);
            FileUploader.download(bucket, fullFileName, localFileName);
            File inputFile = new File(localFileName);
            //文件类型判断，有些文件上传时后缀是jpg，但是实际是png，所以需要判断一下，使用真实的文件类型
            //图片类型比较特殊，后缀改了也可以读取，所以造成了这种情况
            String fileSuffix;
            FileSubType fileSubType = FileTypeValidator.getFileType(new CustomMultipartFile(inputFile));
            if (fileSubType == null || fileSubType.getFileType() != FileType.image) {
                return null;
            }
            if (fileSubType == FileSubType.PNG) {
                fileSuffix = "png";
            } else if (fileSubType == FileSubType.JPEG) {
                fileSuffix = "jpg";
            } else {
                return null;
            }
            // 调试信息
            String thumbFileName = thumbWidth + "." + fileSuffix;
            File outputFile = new File(thumbFileName);
            try {
                ImageUtil.createThumbnail(inputFile, outputFile, thumbWidth, thumbHeight, fileSuffix);
                return this.upload(bucket, fullFileName.substring(0, fullFileName.lastIndexOf("/")), thumbFileName, fileBizType, new CustomMultipartFile(outputFile), null);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } finally {
                inputFile.delete();
                outputFile.delete();
            }
        } finally {
            lockImageThumb.unlock();
        }
    }

}
