package com.ykl.med.file.component;

import io.minio.MinioClient;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Minio客户端单例
 *
 * <AUTHOR>
 */
@Getter
@Component
public class MinioClientSingleton {

    @Getter
    private static MinioClientSingleton instance;

    private final MinioClient minioClient;

    public MinioClientSingleton(@Value("${ykl.min-io.endpoint}") String domainUrl,
                                @Value("${ykl.min-io.accessKey}") String accessKey,
                                @Value("${ykl.min-io.secretKey}") String secretKey) {
        this.minioClient = MinioClient.builder()
                .endpoint(domainUrl)
                .credentials(accessKey, secretKey)
                .build();
        instance = this;
    }

}