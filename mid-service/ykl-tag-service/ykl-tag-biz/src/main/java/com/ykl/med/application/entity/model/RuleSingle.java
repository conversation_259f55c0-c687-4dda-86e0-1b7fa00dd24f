package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> xkli
 * @date : 2024-6-19
 */
@Data
@Schema(description = "规则单一表")
@TableName("t_rule")
public class RuleSingle {

    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long id ;

    @Schema(description = "规则名称", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,WHERE:WL_MATCH:%:%")
    private String name ;

    @Schema(description = "规则类别【枚举】", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private String bizClass ;

    @Schema(description = "业务数据(json)", example = "")
    private Object bizData ;

    @Schema(description = "内容数据", example = "")
    private String bizContent ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT:ENABLE")
    private String status ;

    @Schema(description = "删除标志；0未删，1已删", example = "")
    @TableId
    @TableLogic("FILLED:SGV_DELETE:1,FILLED:SGV_WHERE:0,FIELD_PROC:FP_GET_HIDE")
    private Byte deleteFlag ;

    @Schema(description = "删除名称联合主键", example = "")
    @TableLogic("FILLED:SGV_UPDATE:deleteFlag=1,FIELD_PROC:FP_GET_HIDE")
    private Long nameDuk ;

}
