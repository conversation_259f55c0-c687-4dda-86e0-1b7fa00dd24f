package com.ykl.med.application.service;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.ykl.med.application.entity.HttpRequestData;
import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.application.entity.ValueObject;
import com.ykl.med.application.entity.model.TagClass;
import com.ykl.med.application.interfaces.IService;
import com.ykl.med.application.repository.MapperRepository;
import com.ykl.med.config.SimpleException;
import com.ykl.med.enums.ResponseCode;

import java.text.ParseException;
import java.util.HashMap;

public class TagClassService implements IService {
    @Override
    public String serviceName() {
        return "tagClass";
    }

    @Override
    public ResponseModel crud(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, ParseException, NoSuchFieldException {

        // 定义值对象
        TagClass SObj = new TagClass();

        // 初始化结果对象
        ResponseModel responseModel = new ResponseModel();

        // 创建值对象仓库: 用于加工值对象
        MapperRepository mapperRepository = new MapperRepository();

        // 加工值对象: 值对象+传入对象
        HttpRequestData httpRequestData = mapperRepository.inputParamsMap(SObj, requestObj);
        // 判断必填参数: 只使用第一个值对象
        SimpleException simpleException = mapperRepository.checkValueObjectField(
                httpRequestData.getValueObjectList().get(0), httpRequestData.getMethod(), true);
        if( simpleException.getCode() != 0 ){
            return responseModel.response(ResponseCode.INVALID_ARGUMENT, simpleException.getMessage());
        }

        /**
         * 获取SQL执行服务
         * */
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return responseModel.response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        /**
         * 执行sql: 生成sql, 执行, 返回结果
         * */
        for ( ValueObject valueObject : httpRequestData.getValueObjectList() ){
            return aSqlExecService.sqlExec(valueObject, httpRequestData.getPageRecord());
        }

        return responseModel;
    }
}
