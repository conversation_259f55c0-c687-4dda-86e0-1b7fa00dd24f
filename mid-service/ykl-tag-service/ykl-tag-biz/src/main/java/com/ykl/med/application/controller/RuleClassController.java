package com.ykl.med.application.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.application.entity.ServiceObject;
import com.ykl.med.application.repository.ServiceRepository;
import com.ykl.med.enums.ResponseCode;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.number.NumberUtils;
import com.ykl.med.tag.entity.dto.*;
import com.ykl.med.tag.entity.enums.RecommendBizCategories;
import com.ykl.med.tag.entity.vo.PatientTagVO;
import com.ykl.med.tag.entity.vo.RuleClassVO;
import com.ykl.med.tag.entity.vo.TagBaseVO;
import com.ykl.med.tag.entity.vo.TagVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import com.ykl.med.util.PublicUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Tag(name = "规则类别管理接口")
@RestController
@RequestMapping("/aggregation/ruleClass")
@Validated
public class RuleClassController {

    @PostMapping("/query")
    @Operation(summary = "规则类别查询接口", description = "")
    public PageResult<RuleClassVO> query(@RequestBody RuleClassQueryDTO requestBody) {

        if (requestBody.getPageNo() == null) {
            requestBody.setPageNo(1);
        }
        if (requestBody.getPageSize() == null) {
            requestBody.setPageSize(20);
        }

        // 初始化返回参数
        PageResult<RuleClassVO> pageResult = new PageResult<>();
        pageResult.setTotal(0L);
        pageResult.setList(new ArrayList<>());

        ServiceObject serviceObject = new ServiceObject();
        serviceObject.setName("ruleClass");
        serviceObject.setMethod("crud");
        serviceObject.setHttpMethod("GET");
        PageResult<RuleClassVO> ruleClassVOPageResult = this.serviceMethodCall(serviceObject, requestBody, new TypeReference<PageResult<RuleClassVO>>() {});
        if ( ruleClassVOPageResult == null || ruleClassVOPageResult.getList() == null || ruleClassVOPageResult.getList().size() == 0 ){
            return pageResult;
        }

        // 业务类别列表
        List<String> bizCategoryList = new ArrayList<>();
        // 赋值业务类别数据
        for ( RuleClassVO ruleClassVO : ruleClassVOPageResult.getList() ){
            if ( ruleClassVO.getBizCategory() == null ){
                continue;
            }

            bizCategoryList.addAll(ruleClassVO.getBizCategory());

            ruleClassVO.setBizCategoryList(new ArrayList<>());
            for ( String str : ruleClassVO.getBizCategory()) {
                RecommendBizCategories recommendBizCategories = RecommendBizCategories.findEnumItem("value", str);
                if ( recommendBizCategories != null ){
                    Map<String, String> stringStringMap = new HashMap<>();
                    stringStringMap.put(str, recommendBizCategories.getLabel());
                    ruleClassVO.getBizCategoryList().add(stringStringMap);
                }
            }
        }

        bizCategoryList.add("GROUP:QUERY"); // 分组

        PatientTagQueryDTO patientTagQueryDTO = new PatientTagQueryDTO();
        patientTagQueryDTO.setBizCategory(String.join(",",bizCategoryList));
        patientTagQueryDTO.setTagId("GROUP:QUERY");
        ServiceObject serviceObject1 = new ServiceObject().setName("patientTag").setMethod("crud").setHttpMethod("GET");
        PageResult<PatientTagVO> patientTagVOPageResult = this.serviceMethodCall(serviceObject1, patientTagQueryDTO, new TypeReference<PageResult<PatientTagVO>>() {});

        // 根据业务类别生成map
        Map<String, List<PatientTagVO>> patientTagVOMap = new HashMap<>();

        /**
         * 获取标签数据
         * */
        // 标签ID map
        Map<String, TagVO> tagVOMap = new HashMap<>();
        if( patientTagVOPageResult != null && patientTagVOPageResult.getList() != null && patientTagVOPageResult.getList().size() != 0 ){

            patientTagVOMap = patientTagVOPageResult.getList().stream().collect(Collectors.groupingBy(PatientTagVO::getBizCategory));

            List<String> tagIdList = patientTagVOPageResult.getList().stream().map(PatientTagVO::getTagId).
                    filter(Objects::nonNull).distinct().collect(Collectors.toList());

            TagQueryDTO tagQueryDTO = new TagQueryDTO();
            tagQueryDTO.setId(String.join(",", tagIdList));
            ServiceObject serviceObject2 = new ServiceObject().setName("tag").setMethod("crud").setHttpMethod("GET");
            PageResult<TagVO> tagVOPageResult = this.serviceMethodCall(serviceObject2, tagQueryDTO, new TypeReference<PageResult<TagVO>>() {});
            if ( tagVOPageResult != null && tagVOPageResult.getList() != null && tagVOPageResult.getList().size() != 0 ){
                tagVOMap = tagVOPageResult.getList().stream().collect(Collectors.toMap(TagVO::getId, Function.identity(),(key1,key2) -> key2));
            }
        }

        // 循环赋值
        for ( RuleClassVO ruleClassVO : ruleClassVOPageResult.getList() ){

            if ( ruleClassVO.getBizCategory() == null ){
                continue;
            }

            ruleClassVO.setBizCategoryTagInfos(new HashMap<>());

            for ( String str : ruleClassVO.getBizCategory() ){
                ruleClassVO.getBizCategoryTagInfos().put(str, new ArrayList<>());
                if ( patientTagVOMap.containsKey(str) ){
                    for ( PatientTagVO patientTagVO : patientTagVOMap.get(str) ){
                        if ( tagVOMap.containsKey(patientTagVO.getTagId()) ){
                            TagBaseVO tagBaseVO = new TagBaseVO();
                            tagBaseVO.setId(tagVOMap.get(patientTagVO.getTagId()).getId());
                            tagBaseVO.setName(tagVOMap.get(patientTagVO.getTagId()).getName());
                            ruleClassVO.getBizCategoryTagInfos().get(str).add(tagBaseVO);
                        }
                    }
                }
            }
        }

        fixUserInfo(ruleClassVOPageResult);

        return ruleClassVOPageResult;
    }

    @PostMapping("/add")
    @Operation(summary = "规则类别添加接口", description = "")
    public void add(@RequestBody RuleClassAddDTO requestBody) {

        if ( StringUtils.isBlank(requestBody.getStatus()) ){
            requestBody.setStatus(null);
        }

        if (StringUtils.isBlank(requestBody.getName())){
            NumberUtils.uuidGeneratorLong18();
        }

        ServiceObject serviceObject = new ServiceObject().setName("ruleClass").setMethod("crud").setHttpMethod("POST");
        this.serviceMethodCall(serviceObject, requestBody, null);
    }

    @PostMapping("/update")
    @Operation(summary = "规则类别更新接口", description = "")
    public void update(@RequestBody RuleClassAddDTO requestBody) {
        ServiceObject serviceObject = new ServiceObject().setName("ruleClass").setMethod("crud").setHttpMethod("PUT");
        this.serviceMethodCall(serviceObject, requestBody, null);
    }

    @PostMapping("/delete")
    @Operation(summary = "规则类别删除接口", description = "")
    public void delete(@RequestBody TagDeleteDTO requestBody) {
        ServiceObject serviceObject = new ServiceObject().setName("ruleClass").setMethod("crud").setHttpMethod("DELETE");
        this.serviceMethodCall(serviceObject, requestBody, null);
    }

    /**
     * 补齐数据操作用户数据信息
     * */
    private void fixUserInfo(PageResult<RuleClassVO> pageResult) {

        // 结果列表为null, 不处理
        if ( ( pageResult.getList() == null ) || pageResult.getList().size() == 0 ){
            return ;
        }

        List<String> createUserIds = pageResult.getList().stream().map(RuleClassVO::getCreateUserId).distinct().collect(Collectors.toList());
        List<String> lastUserIds = pageResult.getList().stream().map(RuleClassVO::getLastUserId).distinct().collect(Collectors.toList());
        createUserIds.addAll(lastUserIds);
        // 去null值
        createUserIds.removeAll(Collections.singleton(null));
        // 去重
        Set<String> set = new HashSet<>(createUserIds);
        List<String> UserIds = new ArrayList<>(set);

        if ( UserIds.size() != 0 ) {
            IdListReqVO idListReqVO = new IdListReqVO();
            idListReqVO.setIdList(new ArrayList<>());

            idListReqVO.setIdList(UserIds.stream().map(Long::valueOf).collect(Collectors.toList()));
            List<UserSimpleVO> userSimpleVOS = userFeign.listByUserIds(idListReqVO);

            Map<Long, UserSimpleVO> userSimpleVOMap = userSimpleVOS.stream().collect(
                    Collectors.toMap(UserSimpleVO::getId, Function.identity(), (key1, key2) -> key2));

            for (RuleClassVO resultVO : pageResult.getList()) {
                if ( resultVO.getCreateUserId() != null ) {
                    Long userId = Long.parseLong(resultVO.getCreateUserId());
                    if (userSimpleVOMap.containsKey(userId)) {
                        resultVO.setCreateUserName(userSimpleVOMap.get(userId).getUsername());
                    }
                }
                if ( resultVO.getLastUserId() != null ) {
                    Long lastUserId = Long.parseLong(resultVO.getLastUserId());
                    if (userSimpleVOMap.containsKey(lastUserId)) {
                        resultVO.setLastUserName(userSimpleVOMap.get(lastUserId).getUsername());
                    }
                }
            }
        }
    }

    @Resource
    private UserFeign userFeign;

    /**
     * 服务仓库对象
     * */
    @Autowired
    ServiceRepository serviceRepository;

    /**
     * 服务方法调用函数
     * */
    private <T> T serviceMethodCall(ServiceObject serviceObject, Object requestBody, TypeReference<T> type){

        Map<String, String> requestQueryMap = new HashMap<>();
        Map<String, Object> paramObj = new HashMap<>();
        paramObj.put("requestMethod", "GET");
        paramObj.put("requestHeader", null);
        paramObj.put("requestQuery", null);
        paramObj.put("requestQueryMap", requestQueryMap);
        paramObj.put("requestBody", null);
        paramObj.put("requestBodyString", null);
        paramObj.put("requestBodyList", null);
        paramObj.put("requestFiles", null);

        requestQueryMap.put("httpMethod", serviceObject.getHttpMethod());
        paramObj.put("requestBody", requestBody);

        ResponseModel responseModel = PublicUtil.parseObject(
                serviceRepository.doService(serviceObject.getName(), serviceObject.getMethod(), new Object[]{paramObj}),
                new TypeReference<ResponseModel>() {});

        if ( responseModel.getCode() == null || responseModel.getCode() != 0  ){
            // 写入重复数据
            if ( responseModel.getMsg().contains("database") && responseModel.getMsg().contains("Duplicate") ){
                responseModel.response(ResponseCode.EXEC_SQL_WRITE_DATA_REPEAT,
                        PublicUtil.getSubString(responseModel.getMsg(),"Duplicate entry '","' for key", ":数据重复"));
            } else {
                responseModel.response(ResponseCode.UNKNOWN_EXCEPTION);
            }
            throw new ServiceException(responseModel.getCode(), responseModel.getMsg());
        }

        // 数据为空: 生成默认值
        if ( responseModel.getData() == null ){
            Map<String, Object> mapObj = new HashMap<>();
            mapObj.put("total",0L);
            mapObj.put("list",new ArrayList<>());
            responseModel.setData(mapObj);
        }

        // 转换结构
        if ( type != null ) {
            return PublicUtil.parseObject(responseModel.getData(), type);
        }

        return null;
    }
}
