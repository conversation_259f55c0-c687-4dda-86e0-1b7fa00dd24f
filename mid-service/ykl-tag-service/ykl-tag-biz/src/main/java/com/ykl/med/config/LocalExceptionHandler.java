package com.ykl.med.config;

import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.enums.ResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/1/20 9:58
 */
@Slf4j
@ControllerAdvice
public class LocalExceptionHandler {
    @ExceptionHandler({MethodArgumentNotValidException.class,
            BindException.class,
            ConstraintViolationException.class,
            DefinitionException.class
    })
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResponseModel handleArgumentInvalidException(Exception exception) {
        BindingResult bindingResult;
        String errorInfo;
        if (exception instanceof MethodArgumentNotValidException) {
            bindingResult = ((MethodArgumentNotValidException) exception).getBindingResult();
            errorInfo = getErrorInfo(bindingResult);
        } else if (exception instanceof ConstraintViolationException) {
            StringBuilder errorInfoBuilder = new StringBuilder();
            Set<ConstraintViolation<?>> constraintViolations = ((ConstraintViolationException) exception).getConstraintViolations();
            constraintViolations.forEach(item -> {
                if (errorInfoBuilder.length() > 0) {
                    errorInfoBuilder.append(",");
                }
                errorInfoBuilder.append(item.getMessage());
            });
            errorInfo = errorInfoBuilder.toString();
        } else if (exception instanceof DefinitionException) {
            errorInfo = ((DefinitionException) exception).getErrorMsg();
        } else {
            bindingResult = ((BindException) exception).getBindingResult();
            errorInfo = getErrorInfo(bindingResult);
        }

        return new ResponseModel().response(ResponseCode.INVALID_ARGUMENT, errorInfo);
    }

    private String getErrorInfo(BindingResult bindingResult) {
        StringBuilder errorInfo = new StringBuilder();
        for (int i = 0; i < bindingResult.getFieldErrors().size(); i++) {
            if (i > 0) {
                errorInfo.append(",");
            }
            FieldError fieldError = bindingResult.getFieldErrors().get(i);
            errorInfo.append(fieldError.getDefaultMessage());
        }
        return errorInfo.toString();
    }


    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseModel handleDefaultException(Exception exception) {
        log.error(exception.toString());
        exception.printStackTrace();
        return new ResponseModel().response(ResponseCode.UNKNOWN_EXCEPTION);
    }
}
