package com.ykl.med.tag.entity.vo;

import com.ykl.med.masterdata.vo.attachment.AttachmentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2024-6-19
 */
@Data
@Schema(description = "推荐项目信息")
public class RecommendItemVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "项目类别",example = "")
    private String itemClass ;

    @Schema(description = "项目类型",example = "")
    private String type ;

    @Schema(description = "名称",example = "")
    private String name ;

    @Schema(description = "项目类别名称",example = "")
    private String categoryName ;

    @Schema(description = "项目类型名称",example = "")
    private String typeName ;

    @Schema(description = "标题",example = "")
    private String title ;

    @Schema(description = "封面图URL",example = "")
    private String cover ;

    @Schema(description = "摘要",example = "")
    private String summary ;

    @Schema(description = "作者",example = "")
    private String author ;

    @Schema(description = "包装规格（20mg*5袋/盒）",example = "")
    private String packageSpec ;

    @Schema(description = "包装规格单位（如：盒）【字典】",example = "")
    private String packageSpecUnit ;

    @Schema(description = "强度/适用范围", example = "1")
    private String intensity;

    @Schema(description = "用法/方式", example = "1")
    private String UsageMethod;

    @Schema(description = "频次ID", example = "1")
    private String frequencyId;

    @Schema(description = "最大频次ID", example = "1")
    private String maxFrequencyId;

    @Schema(description = "单次用量", example = "1")
    private String singleNum;

    @Schema(description = "单次用量单位", example = "1")
    private String singleUnit;

    @Schema(description = "生产厂商ID【实体】",example = "")
    private String producerId ;

    @Schema(description = "生产厂商名称【实体】",example = "")
    private String producerName ;

    @Schema(description = "附件列表", example = "[]")
    private List<AttachmentVO> attachments;

    @Schema(description = "备注",example = "")
    private String remark ;

    @Schema(description = "互斥ID列表",example = "")
    private List<String> mutexIdList ;

}
