package com.ykl.med.tag.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2024-6-20
 */
@Data
@Schema(description = "规则标签返回对象")
public class RuleTagReturnVO {

    @Schema(description = "规则标签关系ID",example = "")
    private String id ;

    @Schema(description = "标签列表",example = "")
    private List<TagAndClassVO> tags ;
}
