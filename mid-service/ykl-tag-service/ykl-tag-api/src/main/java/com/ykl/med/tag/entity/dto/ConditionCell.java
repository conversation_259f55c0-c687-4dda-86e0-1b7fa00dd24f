package com.ykl.med.tag.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: wang <EMAIL>
 * @Description: CompareValues
 * @Date: 2023-12-19 16:27:18
 * @Version: 1.0
 */

@Data
@Schema(description = "规则条件配置的最小单元")
public class ConditionCell {

    @Schema(description = "数据类型:number、col、text、empty",example = "col")
    private String dataType;

    @Schema(description = "运算符:+、-、*、/,无传空字符串",example = "")
    private String operationType;

    @Schema(description = "用户直接输入的值、输入的字段attribute_id",example = "")
    private String value;

    @Schema(description = "当dataType = col,值填充event_id",example = "")
    private String parentValue;

    @Schema(description = "当dataType = col,值填充:true,false",example = "false")
    private Boolean timeFlag;

    @Schema(description = "自动填充,比如cm、kg",example = "")
    private String unit;
}
