package com.ykl.med.tag.api;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.tag.entity.vo.TagVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-tag-service", path = "/ykl-tag-service/api/tag")
public interface TagFeign {
    /**
     * 标签管理增删改查
     * @param httpMethod : http请求的方法: GET/POST/PUT/DELETE
     * @param obj : http请求对象
     * */
    @PostMapping("/crud")
    PageResult<TagVO> crud(@RequestParam(value = "httpMethod") String httpMethod, @RequestBody Object obj);
}
