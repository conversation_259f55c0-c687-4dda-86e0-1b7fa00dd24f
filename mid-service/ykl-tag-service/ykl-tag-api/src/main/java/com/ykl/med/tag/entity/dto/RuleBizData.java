package com.ykl.med.tag.entity.dto;

import com.ykl.med.tag.entity.vo.RecommendItemVO;
import com.ykl.med.tag.entity.vo.TagReturnVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2024-6-20
 */
@Data
@Schema(description = "规则业务数据")
public class RuleBizData {

    @Schema(description = "业务类别", example = "")
    private String bizCategory ;

    @Schema(description = "项目类别", example = "")
    private String itemCategory ;

    @Schema(description = "项目ID列表", example = "")
    private List<String> itemIds ;

    @Schema(description = "标签ID列表", example = "")
    private List<String> tagIds ;

    @Schema(description = "字段列表数组")
    private List<ConditionObject> conditionCells;

    @Schema(description = "内容说明", example = "")
    private String content ;

    /**
     * 其他字段数据
     * */
    @Schema(description = "项目数据")
    private List<RecommendItemVO> itemInfos;

    @Schema(description = "标签数据")
    private List<TagReturnVO> tagInfos;

    @Schema(description = "返回结果总数")
    private Long total;
}
