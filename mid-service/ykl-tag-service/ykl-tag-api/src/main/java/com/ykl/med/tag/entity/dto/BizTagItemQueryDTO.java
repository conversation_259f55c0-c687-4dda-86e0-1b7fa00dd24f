package com.ykl.med.tag.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2024-6-24
 */
@Data
@Schema(description = "业务标签查询对象")
public class BizTagItemQueryDTO {

    private Integer pageSize;
    private Integer pageNo;

    @Schema(description = "业务类别: value值", example = "")
    private String bizCategory ;

    @Schema(description = "标签ID", example = "")
    private List<String> tagIds ;

    @Schema(description = "项目ID列表", example = "")
    private List<String> itemIds ;

}
