package com.ykl.med.tag.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "自定义变量对象")
public class Variable {

    @Schema(description = "定义的对象的key",example = "x")
    private String key;

    @Schema(description = "最小单元对象")
    private List<ConditionCell> values;

    @Schema(description = "自动生成")
    private String value;
}
