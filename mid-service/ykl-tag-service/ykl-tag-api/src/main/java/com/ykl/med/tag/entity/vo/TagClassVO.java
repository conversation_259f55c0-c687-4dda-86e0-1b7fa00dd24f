package com.ykl.med.tag.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import java.util.Date;

/**
 * 标签类别表;
 * <AUTHOR> xkli
 * @date : 2023-12-7
 */
@Data
@Schema(description = "标签类别表")
public class TagClassVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "名称",example = "")
    private String name ;

    @Schema(description = "备注",example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】",example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】",example = "")
    private String lastUserId ;

    @Schema(description = "创建时间",example = "")
    private String createTime ;

    @Schema(description = "更新时间",example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE",example = "")
    private String status ;

    @Schema(description = "创建者名称",example = "")
    private String createUserName ;

    @Schema(description = "最后一次修改者名称",example = "")
    private String lastUserName ;

}
