package com.ykl.med.product.db.entity;

import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.MemberServicePackageType;
import com.ykl.med.framework.common.enums.ProductType;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.NoNullFastjsonTypeHandler;
import com.ykl.med.product.vo.SkuAttributeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

@TableName(value = "t_sku", autoResultMap = true)
@Data
public class SkuDO extends BaseDO {
    /**
     * 特定字段组合的sha1值
     * 用于判断是否重复
     */
    private String shaCode;
    /**
     * sku名称
     */
    private String name;
    /**
     * sku名称简码
     */
    private String nameCode;
    /**
     * 商品Id
     */
    private Long productId;
    /**
     * 零售价
     */
    private Integer retailPrice;
    /**
     * 原价
     */
    private Integer originalPrice;
    /**
     * 会员价
     */
    private Integer memberPrice;
    /**
     * 划线形式展示原价
     */
    private Boolean lineOriginalPrice;

    /**
     * 上下架状态
     */
    private Boolean onSale;

    /**
     * 规格数据(json)
     */
    @TableField(typeHandler = NoNullFastjsonTypeHandler.class)
    private SkuAttributeVO skuAttribute;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 外部业务Id
     * type=MEMBER_VERSION：outBizId= 医疗组id
     * type=PATIENT_EDUCATION_COURSES：无
     * type=SERVICE_PACKAGE 并且为视频门诊：outBizId= 挂号类别id
     * type=SERVICE_PACKAGE 并且为专家咨询：outBizId= 医疗组id
     * type=PRESCRIPTION_DRUGS: outBizId= 药房Id
     * type=TREATMENT_PROJECT：outBizId= 医疗组id
     */
    private String outBizId;

    /**
     * 商品图片
     */
    private String skuImg;

    /**
     * 第三方编码
     * type=PRESCRIPTION_DRUGS: thirdPartyCode= 药房药品编码
     */
    private String thirdPartyCode;

    /**
     * 是否限购
     */
    private Boolean limitBuy;

    /**
     * 限购数量
     */
    private Integer limitBuyNum;

    /**
     * hisId
     */
    private Long hisId ;

    /**
     * his药品id
     */
    private Long hisGoodsId ;


    public String buildShaCode(ProductType productType, String productOutBizId) {
        if (productType == ProductType.MEMBER_VERSION || productType == ProductType.SERVICE_PACKAGE) {
            //一个规格只能有一个商品
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(productType.name()).append("_")
                    .append(productOutBizId).append("_")
                    .append(this.outBizId).append("_")
                    .append(this.skuAttribute.getNumber());
            if (Objects.equals(productOutBizId, MemberServicePackageType.ONLINE_VIDEO_CLINIC.name())) {
                stringBuilder.append("_").append(this.name);
            }
            if (this.skuAttribute.getUnit() != null) {
                stringBuilder.append("_").append(this.skuAttribute.getUnit());
            }
            return DigestUtil.sha256Hex(stringBuilder.toString());
        }
        if (productType == ProductType.PRESCRIPTION_DRUG) {
            //处方药，一个药房只能有一个同样的药品
            //todo 少加了productType.name() ， 后面做处方商品增删改查的时候加上
            return DigestUtil.sha256Hex(productOutBizId + "_" + this.outBizId);
        } else if (productType == ProductType.TREATMENT_PROJECT) {
            //诊疗项目 一个医疗组一个服务机构只能有一个同样的诊疗项目
            return DigestUtil.sha256Hex(productType.name() + "_" + productOutBizId + "_" +
                    this.outBizId + this.name + this.skuAttribute.getServiceOrganizationId());
        } else {
            //患教课程，一个患教课程只能有一个商品
            return DigestUtil.sha256Hex(productType.name() + "_" + productOutBizId);
        }
    }
}
