package com.ykl.med.product.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.product.enums.StockChangeType;
import com.ykl.med.product.enums.StockRecordStatus;
import lombok.Data;

import java.time.LocalDateTime;

@TableName("t_stock_record")
@Data
public class StockRecordDO extends BaseDO {
    private String shaCode;
    private Long skuId;
    private StockChangeType type;
    private Integer quantity;
    /**
     * 业务单号
     */
    private String bizNo;
    private String remark;

    private StockRecordStatus status = StockRecordStatus.ACTUAL_DEDUCTION_COMPLETED;

    /**
     * 锁定过期时间
     */
    private LocalDateTime lockExpireTime;
}
