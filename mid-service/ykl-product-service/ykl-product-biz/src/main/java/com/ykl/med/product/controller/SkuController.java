package com.ykl.med.product.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.product.feign.SkuFeign;
import com.ykl.med.product.service.SkuService;
import com.ykl.med.product.vo.req.SkuChangeSaleReqVO;
import com.ykl.med.product.vo.req.SkuQueryVO;
import com.ykl.med.product.vo.resp.PrescriptionDrugSkuDetailVO;
import com.ykl.med.product.vo.resp.SkuDetailVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "商品SKU服务")
@RestController
@RequestMapping("/sku")
@Validated
public class SkuController implements SkuFeign {
    @Resource
    private SkuService skuService;

    @Override
    @PostMapping("/batchChangeSale")
    public void batchChangeSale(@RequestBody SkuChangeSaleReqVO reqVO) {
        skuService.batchChangeSale(reqVO.getSkuIds(), reqVO.getOnSale());
    }

    @Override
    @PostMapping("/queryPrescriptionDrugSkuDetail")
    public PageResult<PrescriptionDrugSkuDetailVO> queryPrescriptionDrugSkuDetail(@RequestBody SkuQueryVO queryVO) {
        return skuService.queryPrescriptionDrugSkuDetail(queryVO);
    }

    @Override
    @PostMapping("/querySkuDetail")
    public PageResult<SkuDetailVO> querySkuDetail(@RequestBody SkuQueryVO queryVO) {
        return skuService.querySkuDetail(queryVO);
    }
}
