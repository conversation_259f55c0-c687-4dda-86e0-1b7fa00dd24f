package com.ykl.med.product.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.product.db.entity.ProductTreatmentProjectTypeDO;
import com.ykl.med.product.db.mysql.ProductTreatmentProjectTypeMapper;
import com.ykl.med.product.vo.treatment.ProductTreatmentProjectTypeVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProductTreatmentProjectTypeService extends ServiceImpl<ProductTreatmentProjectTypeMapper, ProductTreatmentProjectTypeDO> {
    @Resource
    private ProductTreatmentProjectTypeMapper productTreatmentProjectTypeMapper;
    @Resource
    private IdServiceImpl idService;

    @Transactional(rollbackFor = Exception.class)
    public Long save(String name) {
        ProductTreatmentProjectTypeDO productTreatmentProjectTypeDO = new ProductTreatmentProjectTypeDO();
        productTreatmentProjectTypeDO.setName(name);
        productTreatmentProjectTypeDO.setId(idService.nextId());
        productTreatmentProjectTypeMapper.insert(productTreatmentProjectTypeDO);
        return productTreatmentProjectTypeDO.getId();
    }

    public String getName(Long id) {
        ProductTreatmentProjectTypeDO productTreatmentProjectTypeDO = productTreatmentProjectTypeMapper.selectById(id);
        return productTreatmentProjectTypeDO.getName();
    }

    public Map<Long, String> getNameMap(List<Long> ids) {
        return productTreatmentProjectTypeMapper.selectBatchIds(ids).stream()
                .collect(Collectors.toMap(ProductTreatmentProjectTypeDO::getId, ProductTreatmentProjectTypeDO::getName));
    }

    public List<ProductTreatmentProjectTypeVO> getAll() {
        List<ProductTreatmentProjectTypeDO> productTreatmentProjectTypeDOList = productTreatmentProjectTypeMapper.selectList();
        return CopyPropertiesUtil.normalCopyProperties(productTreatmentProjectTypeDOList, ProductTreatmentProjectTypeVO.class);
    }
}
