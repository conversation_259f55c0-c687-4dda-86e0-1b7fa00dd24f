package com.ykl.med.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.enums.ProductType;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.product.constants.ProductErrorCodeConstants;
import com.ykl.med.product.db.entity.ProductDO;
import com.ykl.med.product.db.entity.SkuDO;
import com.ykl.med.product.db.entity.StockDO;
import com.ykl.med.product.db.mysql.ProductMapper;
import com.ykl.med.product.service.ProductService;
import com.ykl.med.product.service.ServiceOrganizationService;
import com.ykl.med.product.service.SkuService;
import com.ykl.med.product.service.StockService;
import com.ykl.med.product.utils.SkuConvert;
import com.ykl.med.product.vo.SkuAttributeVO;
import com.ykl.med.product.vo.req.ProductQueryVO;
import com.ykl.med.product.vo.req.ProductReqVO;
import com.ykl.med.product.vo.req.SkuReqVO;
import com.ykl.med.product.vo.resp.*;
import com.ykl.med.product.vo.treatment.ServiceOrganizationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductServiceImpl extends ServiceImpl<ProductMapper, ProductDO> implements ProductService {
    @Resource
    private ProductMapper productMapper;
    @Resource
    private SkuService skuService;
    @Resource
    private StockService stockService;
    @Resource
    private ServiceOrganizationService serviceOrganizationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateProduct(ProductReqVO productReqVO) {
        log.info("saveOrUpdateProduct reqVO:{}", JSON.toJSONString(productReqVO));
        ProductDO productDO;
        if (productReqVO.getId() != null) {
            productDO = productMapper.selectById(productReqVO.getId());
            AssertUtils.notNull(productDO, ProductErrorCodeConstants.PRODUCT_NOT_EXISTS);
            productDO.setDescription(productReqVO.getDescription());
        } else {
            productDO = new ProductDO();
            productDO.setCreatorId(productReqVO.getCreatorId());
        }
        BeanUtils.copyProperties(productReqVO, productDO, "creatorId");
        productDO.setShaCode(productDO.buildShaCode());
        try {
            super.saveOrUpdate(productDO);
        } catch (DuplicateKeyException e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("uq_sha_code")) {
                throw new ServiceException(ProductErrorCodeConstants.PRODUCT_DUPLICATE);
            }
            throw e;
        }

        if (CollectionUtils.isEmpty(productReqVO.getSkuList())) {
            return;
        }
        List<Long> skuIds = productReqVO.getSkuList().stream().map(SkuReqVO::getId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, SkuDO> oldSkuMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<SkuDO> skuDOList = skuService.selectBySkuIds(skuIds);
            oldSkuMap = skuDOList.stream().collect(Collectors.toMap(SkuDO::getId, skuDO -> skuDO));
        }

        List<SkuDO> saveSkuDOList = new ArrayList<>();
        List<StockDO> stockDOList = new ArrayList<>();
        Map<String, StockDO> skuStockMap = new HashMap<>();
        for (SkuReqVO skuReqVO : productReqVO.getSkuList()) {
            SkuDO skuDO = oldSkuMap.get(skuReqVO.getId());
            boolean isNew = false;
            if (skuDO == null) {
                skuDO = new SkuDO();
                skuDO.setProductId(productDO.getId());
                isNew = true;

            }
            BeanUtils.copyProperties(skuReqVO, skuDO, "id");
            skuDO.setShaCode(skuDO.buildShaCode(productDO.getType(), productDO.getOutBizId()));
            saveSkuDOList.add(skuDO);
            if (isNew) {
                skuStockMap.put(skuDO.getShaCode(), new StockDO().setStock(skuReqVO.getStock() == null ? Integer.valueOf(0) : skuReqVO.getStock()));
            }
        }
        skuService.saveOrUpdateBatch(saveSkuDOList);
        for (SkuDO skuDO : saveSkuDOList) {
            //这里把sku的插入和库存插入强绑定到一个事务下
            StockDO stockDO = skuStockMap.get(skuDO.getShaCode());
            if (stockDO == null) {
                continue;
            }
            stockDO.setSkuId(skuDO.getId());
            stockDOList.add(stockDO);
        }
        stockService.saveOrUpdateBatch(stockDOList);
    }

    @Override
    public ProductDetailVO getProductDetailById(Long productId) {
        ProductDO productDO = productMapper.selectById(productId);
        if (productDO == null) {
            return null;
        }
        ProductDetailVO productDetailVO = new ProductDetailVO();
        BeanUtils.copyProperties(productDO, productDetailVO);
        List<SkuDO> skuDOList = skuService.selectByProductId(productId);
        if (CollectionUtils.isEmpty(skuDOList)) {
            return productDetailVO;
        }

        List<Long> skuIds = skuDOList.stream().map(SkuDO::getId).collect(Collectors.toList());
        Map<Long, StockVO> skuStockMap = this.getStockMap(skuIds);

        List<SkuDetailVO> skuDetailVOList = new ArrayList<>();
        for (SkuDO skuDO : skuDOList) {
            SkuDetailVO skuDetailVO = new SkuDetailVO();
            BeanUtils.copyProperties(skuDO, skuDetailVO);
            skuDetailVO.setSkuAttribute(CopyPropertiesUtil.normalCopyProperties(skuDO.getSkuAttribute(), SkuAttributeDetailVO.class));
            skuDetailVO.setStock(skuStockMap.getOrDefault(skuDO.getId(), new StockVO()).getStock());
            skuDetailVOList.add(skuDetailVO);
        }
        productDetailVO.setSkuList(skuDetailVOList);
        return productDetailVO;
    }


    @Override
    public PageResult<ProductListVO> pageProduct(ProductQueryVO queryVO) {
        PageResult<ProductDO> productDOList = productMapper.selectByQueryVO(queryVO);
        if (CollectionUtils.isEmpty(productDOList.getList())) {
            return PageResult.empty();
        }
        List<Long> productIds = productDOList.getList().stream().map(ProductDO::getId).collect(Collectors.toList());
        List<SkuDO> skuDOList = skuService.selectByProductIdIn(productIds);
        Map<Long, List<SkuDO>> skuMap = new HashMap<>();
        Map<Long, StockVO> skuStockMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuDOList)) {
            skuMap = skuDOList.stream().collect(Collectors.groupingBy(SkuDO::getProductId));
            List<Long> skuIds = skuDOList.stream().map(SkuDO::getId).collect(Collectors.toList());
            skuStockMap = this.getStockMap(skuIds);
        }
        List<ProductListVO> productListVOList = new ArrayList<>();
        for (ProductDO productDO : productDOList.getList()) {
            ProductListVO productListVO = new ProductListVO();
            BeanUtils.copyProperties(productDO, productListVO);
            List<SkuDO> skus = skuMap.getOrDefault(productDO.getId(), Collections.emptyList());
            if (CollectionUtils.isEmpty(skus)) {
                productListVOList.add(productListVO);
                continue;
            }
            Integer totalSales = 0;
            List<SkuListVO> skuListVOList = new ArrayList<>();
            for (SkuDO skuDO : skus) {
                StockVO stockVO = skuStockMap.getOrDefault(skuDO.getId(), new StockVO());
                totalSales += stockVO.getSoldStock();
                SkuListVO skuListVO = CopyPropertiesUtil.normalCopyProperties(skuDO, SkuListVO.class);
                skuListVO.setSkuAttribute(CopyPropertiesUtil.normalCopyProperties(skuDO.getSkuAttribute(), SkuAttributeDetailVO.class));
                skuListVOList.add(skuListVO);
            }
            productListVO.setSkuInfos(skuListVOList);
            productListVO.setTotalSales(totalSales);
            productListVOList.add(productListVO);
        }
        return new PageResult<>(productListVOList, productDOList.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeSale(Long productId, Boolean onSale) {
        log.info("changeSale productId:{},onSale:{}", productId, onSale);
        ProductDO productDO = productMapper.selectById(productId);
        AssertUtils.notNull(productDO, ProductErrorCodeConstants.PRODUCT_NOT_EXISTS);
        List<SkuDO> skuDOList = skuService.selectByProductId(productDO.getId());
        if (!CollectionUtils.isEmpty(skuDOList)) {
            //把skuDOList 的onSale改成onSale
            skuDOList.forEach(skuDO -> skuDO.setOnSale(onSale));
            skuService.saveOrUpdateBatch(skuDOList);
        }
    }

    @Override
    public List<SkuOrderDetailVO> getSkuOrderDetailList(List<Long> skuIds) {
        List<SkuDO> skuDOList = skuService.selectBySkuIds(skuIds);
        if (CollectionUtils.isEmpty(skuDOList)) {
            return new ArrayList<>();
        }
        List<Long> productIds = skuDOList.stream().map(SkuDO::getProductId).collect(Collectors.toList());
        Map<Long, ProductDO> productMap = this.getProductMap(productIds);
        Map<Long, StockVO> stockVOMap = this.getStockMap(skuIds);
        List<Long> serviceOrganizationIds = new ArrayList<>();
        for (SkuDO skuDO : skuDOList) {
            ProductDO product = productMap.get(skuDO.getProductId());
            if (product.getType() == ProductType.TREATMENT_PROJECT) {
                serviceOrganizationIds.add(skuDO.getSkuAttribute().getServiceOrganizationId());
            }
        }
        List<ServiceOrganizationVO> serviceOrganizationVOS = org.apache.commons.collections4.CollectionUtils.isEmpty(serviceOrganizationIds) ? new ArrayList<>() : serviceOrganizationService.list(serviceOrganizationIds);
        return SkuConvert.batchDoToOrderDetailVO(skuDOList, productMap, stockVOMap,serviceOrganizationVOS);
    }


    private Map<Long, ProductDO> getProductMap(List<Long> productIds) {
        List<ProductDO> productDOList = productMapper.selectBatchIds(productIds);
        if (CollectionUtils.isEmpty(productDOList)) {
            return new HashMap<>();
        }
        return productDOList.stream().collect(Collectors.toMap(ProductDO::getId, e -> e));
    }

    private Map<Long, StockVO> getStockMap(List<Long> skuIds) {
        List<StockVO> stockDOList = stockService.getStocks(skuIds);
        if (CollectionUtils.isEmpty(stockDOList)) {
            return new HashMap<>();
        }
        return stockDOList.stream().collect(Collectors.toMap(StockVO::getSkuId, e -> e));
    }
}
