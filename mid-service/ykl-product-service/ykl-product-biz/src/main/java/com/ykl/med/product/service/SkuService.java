package com.ykl.med.product.service;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.product.db.entity.SkuDO;
import com.ykl.med.framework.common.enums.ProductType;
import com.ykl.med.product.vo.req.SkuQueryVO;
import com.ykl.med.product.vo.resp.PrescriptionDrugSkuDetailVO;
import com.ykl.med.product.vo.resp.ProductDetailVO;
import com.ykl.med.product.vo.resp.SkuDetailVO;

import java.util.List;

public interface SkuService {
    List<SkuDO> selectByProductId(Long productId);

    List<SkuDO> selectByProductIdIn(List<Long> productIds);

    List<SkuDO> selectBySkuIds(List<Long> skuIds);

    void saveOrUpdateBatch(List<SkuDO> skuDOList);

    void batchChangeSale(List<Long> skuIds, Boolean onSale);

    List<ProductDetailVO> getBySkuOutBizIdAndType(String skuOutBizId, ProductType type);

    List<ProductDetailVO> getByOutBizIdAndType(String productOutBizId, ProductType type);

    PageResult<PrescriptionDrugSkuDetailVO> queryPrescriptionDrugSkuDetail(SkuQueryVO queryVO);

    PageResult<SkuDetailVO> querySkuDetail(SkuQueryVO queryVO);

    List<String> getExistOutBizIds(List<String> productOutBizId, ProductType type);
}
