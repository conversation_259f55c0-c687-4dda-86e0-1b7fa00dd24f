package com.ykl.med.product.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.product.db.entity.TreatmentOrganizationRelationDO;
import com.ykl.med.product.db.mysql.TreatmentOrganizationRelationMapper;
import com.ykl.med.product.vo.treatment.ServiceOrganizationVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TreatmentOrganizationRelationService extends ServiceImpl<TreatmentOrganizationRelationMapper, TreatmentOrganizationRelationDO> {
    @Resource
    private TreatmentOrganizationRelationMapper treatmentOrganizationRelationMapper;
    @Resource
    private IdServiceImpl idService;
    @Resource
    private ServiceOrganizationService serviceOrganizationService;

    @Transactional(rollbackFor = Exception.class)
    public void addRelation(Long productTreatmentProjectTypeId, Long serviceOrganizationId) {
        LambdaQueryWrapper<TreatmentOrganizationRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TreatmentOrganizationRelationDO::getProductTreatmentProjectTypeId, productTreatmentProjectTypeId);
        queryWrapper.eq(TreatmentOrganizationRelationDO::getServiceOrganizationId, serviceOrganizationId);
        TreatmentOrganizationRelationDO treatmentOrganizationRelationDO = treatmentOrganizationRelationMapper.selectOne(queryWrapper);
        if (treatmentOrganizationRelationDO == null) {
            treatmentOrganizationRelationDO = new TreatmentOrganizationRelationDO();
            treatmentOrganizationRelationDO.setId(idService.nextId());
        } else {
            return;
        }
        treatmentOrganizationRelationDO.setProductTreatmentProjectTypeId(productTreatmentProjectTypeId);
        treatmentOrganizationRelationDO.setServiceOrganizationId(serviceOrganizationId);
        treatmentOrganizationRelationMapper.insert(treatmentOrganizationRelationDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRelation(Long productTreatmentProjectTypeId, Long serviceOrganizationId) {
        LambdaQueryWrapper<TreatmentOrganizationRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TreatmentOrganizationRelationDO::getProductTreatmentProjectTypeId, productTreatmentProjectTypeId);
        queryWrapper.eq(TreatmentOrganizationRelationDO::getServiceOrganizationId, serviceOrganizationId);
        treatmentOrganizationRelationMapper.delete(queryWrapper);
    }

    public List<ServiceOrganizationVO> getServiceOrganizationByProductTreatmentProjectTypeId(Long productTreatmentProjectTypeId) {
        LambdaQueryWrapper<TreatmentOrganizationRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TreatmentOrganizationRelationDO::getProductTreatmentProjectTypeId, productTreatmentProjectTypeId);
        List<TreatmentOrganizationRelationDO> treatmentOrganizationRelationDOS = treatmentOrganizationRelationMapper.selectList(queryWrapper);
        List<Long> serviceOrganizationIds = treatmentOrganizationRelationDOS.stream().map(TreatmentOrganizationRelationDO::getServiceOrganizationId).collect(Collectors.toList());
        return serviceOrganizationService.list(serviceOrganizationIds);
    }


}
