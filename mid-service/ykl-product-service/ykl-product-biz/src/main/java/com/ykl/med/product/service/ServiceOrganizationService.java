package com.ykl.med.product.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.product.db.entity.ServiceOrganizationDO;
import com.ykl.med.product.db.mysql.ServiceOrganizationMapper;
import com.ykl.med.product.vo.treatment.ServiceOrganizationVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ServiceOrganizationService extends ServiceImpl<ServiceOrganizationMapper, ServiceOrganizationDO> {
    @Resource
    private ServiceOrganizationMapper serviceOrganizationMapper;
    @Resource
    private IdServiceImpl idService;

    @Transactional(rollbackFor = Exception.class)
    public Long save(String name) {
        ServiceOrganizationDO serviceOrganizationDO = new ServiceOrganizationDO();
        serviceOrganizationDO.setId(idService.nextId());
        serviceOrganizationDO.setName(name);
        serviceOrganizationMapper.insert(serviceOrganizationDO);
        return serviceOrganizationDO.getId();
    }

    public String getName(Long id) {
        return serviceOrganizationMapper.selectById(id).getName();
    }

    public Map<Long, String> getNameMap(List<Long> ids) {
        return serviceOrganizationMapper.selectBatchIds(ids).stream()
                .collect(Collectors.toMap(ServiceOrganizationDO::getId, ServiceOrganizationDO::getName));
    }

    public List<ServiceOrganizationVO> list(List<Long> ids) {
        List<ServiceOrganizationDO> serviceOrganizationDOList = serviceOrganizationMapper.selectBatchIds(ids);
        return CopyPropertiesUtil.normalCopyProperties(serviceOrganizationDOList, ServiceOrganizationVO.class);
    }
}
