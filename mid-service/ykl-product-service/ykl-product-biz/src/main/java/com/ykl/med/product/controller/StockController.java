package com.ykl.med.product.controller;

import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.product.feign.StockFeign;
import com.ykl.med.product.service.StockService;
import com.ykl.med.product.vo.req.StockChangeReqVO;
import com.ykl.med.product.vo.resp.StockRecordVO;
import com.ykl.med.product.vo.resp.StockVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "库存服务")
@RestController
@RequestMapping("/stock")
@Validated
public class StockController implements StockFeign {
    @Resource
    private StockService stockService;

    @Override
    @PostMapping("/changeStock")
    public void changeStock(@RequestBody @Valid StockChangeReqVO reqVO) {
        stockService.changeStock(reqVO);
    }

    @Override
    @PostMapping("/selectStockRecordOutTime")
    public List<StockRecordVO> selectStockRecordOutTime(@RequestParam(value = "endTime") Long lockExpireTime) {
        return stockService.selectStockRecordOutTime(DateTimeUtils.convertLocalDateTimeOfTimestamp(lockExpireTime));
    }


    @Override
    @PostMapping("/getStocks")
    public List<StockVO> getStocks(@RequestBody List<Long> skuIds) {
        return stockService.getStocks(skuIds);
    }

    @Override
    @PostMapping("/getStock")
    public StockVO getStock(@NotNull @RequestParam(value = "skuId") Long skuId) {
        return stockService.getStock(skuId);
    }

}
