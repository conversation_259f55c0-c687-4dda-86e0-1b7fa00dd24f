package com.ykl.med.product.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.enums.ProductType;
import com.ykl.med.product.feign.ProductFeign;
import com.ykl.med.product.service.ProductService;
import com.ykl.med.product.service.SkuService;
import com.ykl.med.product.vo.req.ProductQueryVO;
import com.ykl.med.product.vo.req.ProductReqVO;
import com.ykl.med.product.vo.resp.ProductDetailVO;
import com.ykl.med.product.vo.resp.ProductListVO;
import com.ykl.med.product.vo.resp.SkuOrderDetailVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@Tag(name = "商品服务")
@RestController
@RequestMapping("/product")
@Validated
public class ProductController implements ProductFeign {
    @Resource
    private ProductService productService;
    @Resource
    private SkuService skuService;

    @Override
    @PostMapping("/saveOrUpdateProduct")
    public void saveOrUpdateProduct(@RequestBody ProductReqVO reqVO) {
        productService.saveOrUpdateProduct(reqVO);
    }

    @Override
    @PostMapping("/getProductDetailById")
    public ProductDetailVO getProductDetailById(@NotNull(message = "商品Id不能为空") @RequestParam(value = "productId") Long productId) {
        return productService.getProductDetailById(productId);
    }

    @Override
    @PostMapping("/pageProduct")
    public PageResult<ProductListVO> pageProduct(@RequestBody ProductQueryVO queryVO) {
        return productService.pageProduct(queryVO);
    }

    @Override
    @PostMapping("/changeSale")
    public void changeSale(@NotNull(message = "商品Id不能为空") @RequestParam(value = "productId") Long productId,
                           @NotNull(message = "上下架不能为空") @RequestParam(value = "onSale") Boolean onSale) {
        productService.changeSale(productId, onSale);
    }


    @Override
    @PostMapping("/getSkuOrderDetailList")
    public List<SkuOrderDetailVO> getSkuOrderDetailList(@RequestBody List<Long> skuIds) {
        return productService.getSkuOrderDetailList(skuIds);
    }


    @Override
    @PostMapping("/getBySkuOutBizIdAndType")
    public List<ProductDetailVO> getBySkuOutBizIdAndType(@RequestParam(value = "skuOutBizId") String skuOutBizId,
                                                              @RequestParam(value = "type") ProductType type) {
        return skuService.getBySkuOutBizIdAndType(skuOutBizId, type);
    }

    @Override
    @PostMapping("/getByOutBizIdAndType")
    public List<ProductDetailVO> getByOutBizIdAndType(@RequestParam(value = "productOutBizId") String productOutBizId,
                                                                   @RequestParam(value = "type") ProductType type) {
        return skuService.getByOutBizIdAndType(productOutBizId, type);
    }


    @Override
    @PostMapping("/getExistOutBizIds")
    public List<String> getExistOutBizIds(@RequestBody List<String> productOutBizId,
                                          @RequestParam(value = "type") ProductType type) {
        return skuService.getExistOutBizIds(productOutBizId, type);
    }

}
