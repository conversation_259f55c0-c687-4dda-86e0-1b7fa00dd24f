package com.ykl.med.product.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Represents a request to change the sale status of multiple products.
 */
@Data
public class SkuChangeSaleReqVO {

    @Schema(description = "sku的ID列表", defaultValue = "[1, 2, 3, 4]",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "sku的ID列表不能为空")
    private List<Long> skuIds;

    @Schema(description = "产品销售状态", defaultValue = "true")
    @NotNull(message = "产品销售状态不能为空")
    private Boolean onSale;
}