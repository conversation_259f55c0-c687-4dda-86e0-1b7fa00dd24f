package com.ykl.med.product.vo.treatment;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "商品诊疗项目类别")
public class ProductTreatmentProjectTypeVO {
    @Schema(description = "商品诊疗项目类别ID", defaultValue = "1")
    @Stringify
    private Long id;

    @Schema(description = "商品诊疗项目类别名称", defaultValue = "1")
    private String name;
}
