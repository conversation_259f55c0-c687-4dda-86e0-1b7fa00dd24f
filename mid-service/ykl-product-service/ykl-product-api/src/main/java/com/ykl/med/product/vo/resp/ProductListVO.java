package com.ykl.med.product.vo.resp;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.enums.ProductType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "商品列表对象")
public class ProductListVO {

    @Stringify
    @Schema(description = "商品ID", defaultValue = "1")
    private Long id;

    @Stringify
    @Schema(description = "创建者ID", defaultValue = "1001", hidden = true)
    private Long creatorId;

    @Schema(description = "创建者名称", defaultValue = "张三")
    private String creatorName;

    @Schema(description = "商品名称", defaultValue = "商品A")
    private String name;

    @Schema(description = "SKU信息列表")
    private List<SkuListVO> skuInfos;

    @Schema(description = "商品类型", defaultValue = "MEMBER_VERSION")
    private ProductType type;

    @Schema(description = "总销售量", defaultValue = "100")
    private Integer totalSales;

    @Schema(description = "总销售金额", defaultValue = "5000")
    private Integer totalSalesAmount;

    @TimestampConvert
    @Schema(description = "创建时间", defaultValue = "12636568756856")
    private LocalDateTime createTime;

    @TimestampConvert
    @Schema(description = "更新时间", defaultValue = "12636568756856")
    private LocalDateTime updateTime;

    private String outBizId;
}
