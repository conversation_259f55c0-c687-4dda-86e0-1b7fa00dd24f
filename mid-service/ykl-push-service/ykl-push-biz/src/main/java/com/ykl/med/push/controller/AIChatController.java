package com.ykl.med.push.controller;

import com.ykl.med.push.api.AIChatFeign;
import com.ykl.med.push.service.ai.AIChatService;
import com.ykl.med.push.vo.ai.AIChatAddVO;
import com.ykl.med.push.vo.ai.AIChatVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "AI会话")
@RestController
@RequestMapping("aiChat")
@Validated
public class AIChatController implements AIChatFeign {
    @Resource
    private AIChatService aiChatService;

    @Override
    @PostMapping("/save")
    public void save(@RequestBody AIChatAddVO addVO) {
        aiChatService.save(addVO);
    }

    @Override
    @PostMapping("/getAIChat")
    public AIChatVO getAIChat(@RequestParam(value = "chatId") String chatId) {
        return aiChatService.getAIChat(chatId);
    }
}
