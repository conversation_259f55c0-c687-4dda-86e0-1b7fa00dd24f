package com.ykl.med.push.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.ykl.med.doctors.api.PharmacistInfoFeign;
import com.ykl.med.doctors.entity.dto.PharmacistInfoQueryDTO;
import com.ykl.med.doctors.entity.vo.PharmacistInfoVO;
import com.ykl.med.framework.common.enums.SendMessageType;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientUserVO;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.push.enums.FormChangeNoticeType;
import com.ykl.med.push.service.message.SocketService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "推送websocket服务")
@RestController
@RequestMapping("/pushSocket")
@Validated
public class PushSocketController implements PushSocketFeign {
    @Resource
    private SocketService socketService;
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private PharmacistInfoFeign pharmacistInfoFeign;

    @Override
    @PostMapping("/sendScanNotice")
    public void sendScanNotice(@RequestParam(value = "toUserId") Long toUserId,
                               @RequestParam(value = "medicalTeamId") Long medicalTeamId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("medicalTeamId", medicalTeamId);
        socketService.sendMessage(toUserId, SendMessageType.SCAN_NOTICE, jsonObject);
    }

    @Override
    @PostMapping("/sendLiveNotice")
    public void sendLiveNotice(@RequestParam(value = "toUserId") Long toUserId,
                               @RequestParam(value = "bizId", required = false) Long bizId,
                               @RequestParam(value = "orderCode", required = false) String orderCode) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bizId", bizId);
        jsonObject.put("orderCode", orderCode);
        socketService.sendMessage(toUserId, SendMessageType.LIVE_NOTICE, jsonObject);
    }

    @Override
    @PostMapping("/sendWarnMessage")
    public void sendWarnMessage(@RequestParam(value = "toUserId") Long toUserId) {
        socketService.sendMessage(toUserId, SendMessageType.WARN_MESSAGE, null);
    }

    @Override
    @PostMapping("/sendReportResultNotice")
    public void sendReportResultNotice(@RequestParam(value = "toUserId") Long toUserId, @RequestBody JSONObject jsonObject) {
        socketService.sendMessage(toUserId, SendMessageType.REPORT_RESULT_NOTICE, jsonObject);
    }


    @Override
    @PostMapping("/sendMemberVersionSuccessNotice")
    public void sendMemberVersionSuccessNotice(@RequestParam(value = "toUserId") Long toUserId) {
        socketService.sendMessage(toUserId, SendMessageType.MEMBER_VERSION_SUCCESS_NOTICE, null);
    }

    @Override
    @PostMapping("/sendRecipeChangeNotification")
    public void sendRecipeChangeNotification(@RequestParam(value = "toPatientId") Long toPatientId) {
        List<PatientUserVO> patientUserList = patientUserFeign.getFamilyByPatientId(toPatientId);
        for (PatientUserVO patientUserVO : patientUserList) {
            socketService.sendMessage(patientUserVO.getUserId(), SendMessageType.RECIPE_CHANGE_NOTIFICATION, null);
        }
    }

    @Override
    @PostMapping("/sendMedicalOrderChangeNotice")
    public void sendMedicalOrderChangeNotice(@RequestParam(value = "toPatientId") Long toPatientId) {
        List<PatientUserVO> patientUserList = patientUserFeign.getFamilyByPatientId(toPatientId);
        for (PatientUserVO patientUserVO : patientUserList) {
            socketService.sendMessage(patientUserVO.getUserId(), SendMessageType.MEDICAL_ORDER_CHANGE_NOTICE, null);
        }
    }


    @Override
    @PostMapping("/sendFormChangeNotice")
    public void sendFormChangeNotice(@RequestParam(value = "toUserId") Long toUserId, @RequestParam(value = "type") FormChangeNoticeType type) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", type);
        socketService.sendMessage(toUserId, SendMessageType.FORM_CHANGE_NOTICE, jsonObject);
    }

    @Override
    @PostMapping("/sendNewPrescriptionNotice")
    public void sendNewPrescriptionNotice(@RequestParam(value = "pharmacyId") Long pharmacyId) {
        PharmacistInfoQueryDTO queryDTO = new PharmacistInfoQueryDTO();
        queryDTO.setPharmacy(Lists.newArrayList(pharmacyId.toString()));
        queryDTO.setPageSize(1000);
        PageResult<PharmacistInfoVO> patientUserList = pharmacistInfoFeign.query(queryDTO);
        if (patientUserList.getTotal() == 0) {
            return;
        }
        for (PharmacistInfoVO pharmacistInfoVO : patientUserList.getList()) {
            if (StringUtils.isEmpty(pharmacistInfoVO.getUserId())) {
                continue;
            }
            socketService.sendMessage(Long.valueOf(pharmacistInfoVO.getUserId()), SendMessageType.NEW_PRESCRIPTION_NOTICE, null);
        }
    }

    @PostMapping("/refreshPageNotice")
    @Override
    public void refreshPageNotice(@RequestParam(value = "toUserId") Long toUserId, @RequestParam(value = "patientId") Long patientId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("patientId", patientId);
        socketService.sendMessage(toUserId, SendMessageType.REFRESH_PAGE, jsonObject);
    }

    @Override
    @PostMapping("/setUserCurrentPage")
    public void setUserCurrentPage(@RequestParam(value = "userId") Long userId) {
        socketService.setUserCurrentPage(userId);
    }

    @Override
    @PostMapping("/delUserCurrentPage")
    public void delUserCurrentPage(@RequestParam(value = "userId") Long userId) {
        socketService.delUserCurrentPage(userId);
    }
}
