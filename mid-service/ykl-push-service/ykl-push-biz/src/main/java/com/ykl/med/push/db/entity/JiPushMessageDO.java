package com.ykl.med.push.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.push.enums.JiPushMessageType;
import com.ykl.med.push.enums.JiPushScopeType;
import lombok.Data;

/**
 * 极光推送消息对象.
 */
@TableName("t_ji_push_message")
@Data
public class JiPushMessageDO extends BaseDO {
    private String requestId;

    /**
     * 患者 ID.
     */
    private Long patientId;

    /**
     * 用户 ID.
     */
    private Long userId;

    /**
     * 别名.
     */
    private String alias;

    /**
     * 类型
     */
    private JiPushMessageType messageType;

    /**
     * 群发范围
     */
    private JiPushScopeType scopeType;

    private String logoUrl;

    /**
     * 消息标题.
     */
    private String title;

    /**
     * 消息内容.
     */
    private String content;

    /**
     * 发送编号.
     */
    private String sendNo;

    /**
     * 消息 ID.
     */
    private String messageId;


    private Boolean success;
    private String errorMsg;
}