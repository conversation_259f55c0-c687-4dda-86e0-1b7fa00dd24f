package com.ykl.med.push.service.jipush;

import cn.jiguang.sdk.api.PushApi;
import cn.jiguang.sdk.bean.push.PushSendParam;
import cn.jiguang.sdk.bean.push.PushSendResult;
import cn.jiguang.sdk.bean.push.audience.Audience;
import cn.jiguang.sdk.bean.push.message.notification.NotificationMessage;
import cn.jiguang.sdk.bean.push.message.notification.ThirdNotificationMessage;
import cn.jiguang.sdk.bean.push.options.Options;
import cn.jiguang.sdk.enums.platform.Platform;
import com.alibaba.fastjson2.JSONObject;
import com.ykl.med.push.config.JiPushConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class JiPushApiService {
    @Resource
    private JiPushConfig jiPushConfig;

    private PushApi pushApi;

    /**
     * 获取 or 创建 PushApi 对象
     * 如果对象非空，直接返回，否则创建一个新的PushApi
     *
     * @return 返回创建的PushApi
     * @throws RuntimeException 如果创建过程中发生了任何异常
     */
    public PushApi getPushApi() {
        if (Objects.nonNull(pushApi)) {
            return pushApi;
        }
        try {
            pushApi = new PushApi.Builder()
                    .setAppKey(jiPushConfig.getAppKey())
                    .setMasterSecret(jiPushConfig.getMasterSecret())
                    .build();
            return pushApi;
        } catch (Exception e) {
            log.error("极光推送服务创建异常", e);
            throw new RuntimeException("极光推送服务创建异常");
        }
    }


    /**
     * 发送单个消息
     * 调用 getPushApi() 来发送消息，并返回结果
     *
     * @param title       消息的标题
     * @param content     消息的内容
     * @param alias       消息的别名
     * @param unReadCount 未读消息的数量
     * @param logoUrl     头像
     * @return 返回发送消息的结果
     * @see PushSendResult
     */
    public PushSendResult sendSingleMessage(String title, String content, String alias, Long unReadCount, String logoUrl, String yklAppJump) {
        log.info("sendSingleMessage title:{},content:{},alias:{},unReadCount:{},logoUrl:{},yklAppJump:{}", title, content, alias, unReadCount, logoUrl, yklAppJump);
        PushSendParam param = new PushSendParam();
        //消息组装
        NotificationMessage notificationMessage = getNotificationMessage(title, content, unReadCount.intValue(), logoUrl, yklAppJump);
        param.setNotification(notificationMessage);

        // 目标人群
        Audience audience = new Audience();
        audience.setAliasList(Collections.singletonList(alias));

        // 指定目标
        param.setAudience(audience);
        //ios需要的设置
        Options options = new Options();
        options.setApnsProduction(true);
        options.setClassification(1);
        HashMap<String, Object> thirdPartyChannel = new HashMap<>();
        HashMap<String, Object> huawei=new HashMap<>();
        huawei.put("importance", "NORMAL");
        huawei.put("category","IM");
        thirdPartyChannel.put("huawei",huawei);
        options.setThirdPartyChannel(thirdPartyChannel);
        param.setOptions(options);
        // 指定平台
        param.setPlatform(Arrays.asList(Platform.android, Platform.ios, Platform.hmos));
        // 回调
        // param.setCallback();

        PushSendResult result = getPushApi().send(param);
        log.info("result:{}", result);
        return result;
    }

    /**
     * 获取通知消息
     * 创建一个 NotificationMessage 对象，设置对应的属性
     *
     * @param title       消息的标题
     * @param content     消息的内容
     * @param unReadCount 未读消息的数量
     * @param logoUrl     头像
     * @return 返回创建的 NotificationMessage
     * @see NotificationMessage
     */
    private NotificationMessage getNotificationMessage(String title, String content, Integer unReadCount, String logoUrl, String yklAppJump) {
        NotificationMessage.Android android = new NotificationMessage.Android();
        android.setAlert(content);
        android.setTitle(title);
        android.setBadgeSetNumber(unReadCount);
        Map<String, Object> extras = android.getExtras() == null ? new HashMap<>() : android.getExtras();
        extras.put("logoUrl", logoUrl);
        //app内跳转地址
        extras.put("ykl_app_jump", yklAppJump);
        android.setExtras(extras);
        NotificationMessage.Android.Intent intent = new NotificationMessage.Android.Intent();
        intent.setUrl("intent:#Intent;action=android.intent.action.MAIN;end");
        android.setIntent(intent);

        NotificationMessage.IOS ios = new NotificationMessage.IOS();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("body", content);
        jsonObject.put("title", title);
        ios.setAlert(jsonObject);
        ios.setBadge(String.valueOf(unReadCount));
        Map<String, Object> extrasIos = ios.getExtras() == null ? new HashMap<>() : ios.getExtras();
        extrasIos.put("logoUrl", logoUrl);
        ios.setExtras(extrasIos);

        NotificationMessage.HMOS hmos = new NotificationMessage.HMOS();
        hmos.setAlert(content);
        hmos.setTitle(title);
        hmos.setBadgeSetNumber(unReadCount);
        hmos.setExtras(extras);
        hmos.setCategory("IM");
        NotificationMessage.Android.Intent intentHmos = new NotificationMessage.Android.Intent();
        intentHmos.setUrl("action.system.home");
        hmos.setIntent(intentHmos);


        NotificationMessage notificationMessage = new NotificationMessage();
        notificationMessage.setAlert(title);
        notificationMessage.setAndroid(android);
        notificationMessage.setIos(ios);
        notificationMessage.setHmos(hmos);
        return notificationMessage;
    }

    private ThirdNotificationMessage getNotificationMessage3rd(String title, String content, Integer unReadCount, String logoUrl, String yklAppJump) {
        ThirdNotificationMessage notificationMessage = new ThirdNotificationMessage();
        notificationMessage.setTitle(title);
        notificationMessage.setContent(content);
        notificationMessage.setBadgeSetNumber(unReadCount);
        Map<String, Object> extras = new HashMap<>();
        extras.put("logoUrl", logoUrl);
        extras.put("ykl_app_jump", yklAppJump);
        notificationMessage.setExtras(extras);
        notificationMessage.setIntent(new ThirdNotificationMessage.Intent());
        return notificationMessage;
    }
}