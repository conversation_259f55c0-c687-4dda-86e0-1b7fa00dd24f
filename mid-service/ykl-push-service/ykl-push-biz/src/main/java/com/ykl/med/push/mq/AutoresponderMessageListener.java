package com.ykl.med.push.mq;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.push.constans.MessageConstants;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.service.message.MessageService;
import com.ykl.med.push.vo.message.AutoresponderMessageVO;
import com.ykl.med.push.vo.message.MessageSendReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class AutoresponderMessageListener {

    @Resource
    private MessageService messageService;

    @KafkaListener(topics = MessageConstants.AUTORESPONDER_MESSAGE_TOPIC, groupId = MessageConstants.AUTORESPONDER_MESSAGE_GROUP)
    public void sendMessage(String message) {
        log.info("AutoresponderMessageTopic 接收到消息：{}", message);
        try {
            AutoresponderMessageVO autoresponderMessageVO = JSONObject.parseObject(message, AutoresponderMessageVO.class);
            MessageSendReqVO messageSendReqVO = new MessageSendReqVO();
            messageSendReqVO.setChatId(autoresponderMessageVO.getChatId());
            messageSendReqVO.setMsg(autoresponderMessageVO.getMessage());
            messageSendReqVO.setCurrentUserId(autoresponderMessageVO.getUserId());
            messageSendReqVO.setType(MessageType.AUTORESPONDER);
            messageSendReqVO.setRequestId(autoresponderMessageVO.getRequestId());
            messageService.sendMsg(messageSendReqVO);
        } catch (Exception e) {
            log.error("AutoresponderMessageTopic 发送消息失败：{}", message, e);
        }
    }
}
