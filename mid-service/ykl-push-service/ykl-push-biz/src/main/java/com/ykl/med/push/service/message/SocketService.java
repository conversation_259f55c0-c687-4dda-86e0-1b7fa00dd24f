package com.ykl.med.push.service.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ykl.med.framework.common.enums.SendMessageType;
import com.ykl.med.framework.common.pojo.SendMessageVO;
import com.ykl.med.framework.common.pojo.SocketSendMessageVO;
import com.ykl.med.push.db.entity.ChatDO;
import com.ykl.med.push.db.mysql.ChatMapper;
import com.ykl.med.push.enums.ChatType;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.service.jipush.JiPushMessageService;
import com.ykl.med.push.utils.SystemUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class SocketService {
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AutoresponderMessageService autoresponderMessageService;
    @Resource
    private JiPushMessageService jiPushMessageService;

    @Resource
    private ChatMapper chatMapper;
    private final static String USER_SOCKET_KEY = "userSocket:";
    /**
     * 用户当前页面
     */
    private final static String USER_CURRENT_PAGE_KEY = "userCurrentPage:";
    private final static String SOCKET_MQ_TOPIC = "socketTopic";


    /**
     * 处理消息
     *
     * @param to          接收者ID
     * @param chatId      聊天ID
     * @param messageId   消息ID
     * @param from        发送者ID
     * @param messageType 消息类型
     */
    public void processMessage(Long to, Long chatId, Long messageId, Long from, MessageType messageType) {
        log.info("processMessage to:{},chatId:{},messageId:{},from:{},messageType:{}", to, chatId, messageId, from, messageType);

        JSONObject data = new JSONObject();
        data.put("chatId", chatId);
        data.put("messageId", messageId);
        data.put("messageType", messageType);
        data.put("from", from);

        SendMessageType sendCommand = SendMessageType.MESSAGE;
        if (isUserOnline(to)) {
            ChatDO chat = chatMapper.selectById(chatId);
            if (chat != null && chat.getType() == ChatType.LIVE) {
                sendCommand = SendMessageType.LIVE_MESSAGE;
            }
//            if (!inPage(to)) {
//                //app 没在应用内，也要极光推送
//                jiPushMessageService.sendJiPushMessageMQ(messageId.toString(), from, to, messageType);
//            }
        } else if (SystemUserUtil.isSystemUser(from) || MessageType.AUTORESPONDER.equals(messageType)) {
            jiPushMessageService.sendJiPushMessageMQ(messageId.toString(), from, to, messageType);
            // 如果是系统消息或者自动回复消息，不用触发自动回复
            return;
        } else {
            jiPushMessageService.sendJiPushMessageMQ(messageId.toString(), from, to, messageType);
            //触发自动回复
            autoresponderMessageService.sendAutoresponderMessage(chatId, from, to, messageId);
        }

        sendMessage(to, sendCommand, data);
    }

    /**
     * 发送消息
     *
     * @param toUserId    接收者用户id
     * @param sendCommand 发送指令类型
     * @param data        要发送的数据
     */
    public void sendMessage(Long toUserId, SendMessageType sendCommand, JSONObject data) {
        log.info("sendMessage toUserId:{},sendCommand:{}", toUserId, sendCommand.name());

        SocketSendMessageVO socketSendMessageVO = SocketSendMessageVO.builder()
                .cmd(sendCommand.getCmd())
                .data(data)
                .build();

        if (isUserOnline(toUserId)) {
            SendMessageVO sendMessageVO = new SendMessageVO();
            sendMessageVO.setUserId(toUserId);
            sendMessageVO.setMessage(JSON.toJSONString(socketSendMessageVO));
            log.info("sendMessage to:{} is online,sendMessageVO:{}", toUserId, JSONObject.toJSONString(sendMessageVO));
            kafkaTemplate.send(SOCKET_MQ_TOPIC, JSONObject.toJSONString(Lists.newArrayList(sendMessageVO)));
        }
    }


    /**
     * 判断用户是否在线
     *
     * @param userId 用户id
     * @return 如果用户在线，返回true，否则返回false
     */
    private boolean isUserOnline(Long userId) {
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(USER_SOCKET_KEY + userId));
    }

    /**
     * 获取用户当前页面
     *
     * @param userId 用户id
     * @return 如果用户在app内，返回true，否则返回false
     */
    public Boolean inPage(Long userId) {
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(USER_CURRENT_PAGE_KEY + userId));
    }


    /**
     * 设置用户当前页面
     *
     * @param userId 用户id
     */
    public void setUserCurrentPage(Long userId) {
        stringRedisTemplate.opsForValue().set(USER_CURRENT_PAGE_KEY + userId, "1");
        stringRedisTemplate.expire(USER_CURRENT_PAGE_KEY + userId, 3, TimeUnit.MINUTES);
    }

    /**
     * 删除用户当前页面
     *
     * @param userId 用户id
     */
    public void delUserCurrentPage(Long userId) {
        stringRedisTemplate.delete(USER_CURRENT_PAGE_KEY + userId);
    }

}
