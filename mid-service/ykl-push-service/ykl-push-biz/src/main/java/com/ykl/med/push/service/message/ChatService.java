package com.ykl.med.push.service.message;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.vo.dict.DictDataRespVO;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientUserVO;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.push.db.entity.ChatDO;
import com.ykl.med.push.db.entity.MessageDO;
import com.ykl.med.push.db.mysql.ChatMapper;
import com.ykl.med.push.enums.ChatType;
import com.ykl.med.push.enums.LiveChatStatus;
import com.ykl.med.push.vo.message.*;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 聊天会话服务
 */
@Service
@Slf4j
public class ChatService {
    @Resource
    private ChatMapper chatMapper;
    @Resource
    private UserChatService userChatService;
    @Resource
    private UserFeign userFeign;
    @Resource
    private MessageService messageService;
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private DictDataFeign dictDataFeign;
    @Resource
    private PatientFeign patientFeign;
    @Resource
    private CommonConfigFeign commonConfigFeign;

    /**
     * 创建聊天会话
     *
     * @param reqVO 创建聊天会话的请求参数
     * @return 聊天会话的ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createChat(ChatCreateReqVO reqVO) {
        log.info("createChat reqVO:{}", JSON.toJSONString(reqVO));
        String shaCode = ChatDO.buildShaCode(reqVO.getCurrentUserId(), reqVO.getTargetId(), reqVO.getChatType(), reqVO.getCode());
        ChatDO chatDO = chatMapper.selectOne(new LambdaQueryWrapper<ChatDO>().eq(ChatDO::getShaCode, shaCode));
        if (Objects.nonNull(chatDO)) {
            if (reqVO.getChatType() == ChatType.LIVE) {
                UserSimpleVO userSimpleVO = userFeign.getByUserId(reqVO.getCurrentUserId());
                ChatDO.buildLiveChat(reqVO, chatDO, userSimpleVO);
                chatMapper.updateById(chatDO);
                userChatService.addChatUser(chatDO.getId(), reqVO.getCurrentUserId());
            }
            return chatDO.getId();
        }
        chatDO = new ChatDO().setType(reqVO.getChatType()).setShaCode(shaCode);
        if (reqVO.getChatType() == ChatType.LIVE) {
            UserSimpleVO userSimpleVO = userFeign.getByUserId(reqVO.getCurrentUserId());
            ChatDO.buildLiveChat(reqVO, chatDO, userSimpleVO);
        } else if (reqVO.getChatType() == ChatType.PERSON) {
            ChatExtra chatExtra = new ChatExtra();
            chatExtra.setPersonUserId1(reqVO.getCurrentUserId());
            chatExtra.setPersonUserId2(reqVO.getTargetId());
            chatDO.setExtra(chatExtra);
        } else if (reqVO.getChatType() == ChatType.SYSTEM) {
            ChatExtra chatExtra = new ChatExtra();
            chatExtra.setSystemUserId(reqVO.getCurrentUserId());
            chatDO.setExtra(chatExtra);
        }
        chatMapper.insert(chatDO);
        if (reqVO.getChatType() == ChatType.SYSTEM) {
            userChatService.addChatUser(chatDO.getId(), reqVO.getTargetId());
        } else {
            userChatService.addChatUser(chatDO.getId(), reqVO.getCurrentUserId());
            if (reqVO.getChatType() == ChatType.PERSON) {
                userChatService.addChatUser(chatDO.getId(), reqVO.getTargetId());
            }
        }
        return chatDO.getId();
    }


    public ChatVO getLiveChat(String liveCode) {
        String shaCode = ChatDO.buildShaCode(null, null, ChatType.LIVE, liveCode);
        ChatDO chatDO = chatMapper.selectOne(new LambdaQueryWrapper<ChatDO>().eq(ChatDO::getShaCode, shaCode));
        return CopyPropertiesUtil.normalCopyProperties(chatDO, ChatVO.class);
    }

    public ChatVO getPersonChat(Long userId1, Long userId2) {
        String shaCode = ChatDO.buildShaCode(userId1, userId2, ChatType.PERSON, null);
        ChatDO chatDO = chatMapper.selectOne(new LambdaQueryWrapper<ChatDO>().eq(ChatDO::getShaCode, shaCode));
        return CopyPropertiesUtil.normalCopyProperties(chatDO, ChatVO.class);
    }

    public List<ChatVO> getPersonChatByPatient(Long patientId, Long doctorId) {
        List<PatientUserVO> patientUserVOS = patientUserFeign.getFamilyByPatientId(patientId);
        List<String> shaCodes = new ArrayList<>();
        for (PatientUserVO patientUserVO : patientUserVOS) {
            String shaCode = ChatDO.buildShaCode(doctorId, patientUserVO.getUserId(), ChatType.PERSON, null);
            shaCodes.add(shaCode);
        }
        List<ChatDO> chatDOS = chatMapper.selectList(new LambdaQueryWrapper<ChatDO>().in(ChatDO::getShaCode, shaCodes));
        return CopyPropertiesUtil.normalCopyProperties(chatDOS, ChatVO.class);
    }


    public List<ChatPatientDoctorListVO> getPatientDoctorList(Long patientId) {
        PatientBaseVO patientBaseVO = patientFeign.getPatientBaseById(patientId);
        List<PatientUserVO> patientUserVOS = patientUserFeign.getFamilyByPatientId(patientId);
        Map<Long, String> userRelationMap = patientUserVOS.stream().collect(Collectors.toMap(PatientUserVO::getUserId, PatientUserVO::getRelation));
        List<Long> userIds = patientUserVOS.stream().map(PatientUserVO::getUserId).collect(Collectors.toList());
        List<UserChatVO> userChatVOS = userChatService.getChatByUserIds(userIds);
        if (CollectionUtils.isEmpty(userChatVOS)) {
            return Collections.emptyList();
        }
        List<Long> chatIds = userChatVOS.stream().map(UserChatVO::getChatId).collect(Collectors.toList());
        List<ChatDO> chatDOS = chatMapper.selectList(new LambdaQueryWrapper<ChatDO>()
                .eq(ChatDO::getType, ChatType.PERSON)
                .in(ChatDO::getId, chatIds));
        if (CollectionUtils.isEmpty(chatDOS)) {
            return Collections.emptyList();
        }
        List<Long> lastMessageIds = chatDOS.stream().map(ChatDO::getLastMessageId).collect(Collectors.toList());
        List<MessageDO> messageDOS = messageService.listByIds(lastMessageIds);
        Map<Long, MessageDO> messageDOMap = messageDOS.stream().collect(Collectors.toMap(MessageDO::getId, messageDO -> messageDO));
        List<Long> totalUserIds = new ArrayList<>();
        for (ChatDO chatDO : chatDOS) {
            ChatExtra chatExtra = chatDO.getExtra();
            totalUserIds.add(chatExtra.getPersonUserId1());
            totalUserIds.add(chatExtra.getPersonUserId2());
        }
        List<UserSimpleVO> userSimpleVOS = userFeign.listByUserIds(new IdListReqVO().setIdList(totalUserIds));
        Map<Long, UserSimpleVO> userMap = userSimpleVOS.stream().collect(Collectors.toMap(UserSimpleVO::getId, userSimpleVO -> userSimpleVO));
        List<ChatPatientDoctorListVO> chatPatientDoctorListVOS = new ArrayList<>();
        for (ChatDO chatDO : chatDOS) {
            ChatPatientDoctorListVO chatPatientDoctorListVO = CopyPropertiesUtil.normalCopyProperties(chatDO, ChatPatientDoctorListVO.class);
            if (userIds.contains(chatDO.getExtra().getPersonUserId1())) {
                chatPatientDoctorListVO.setPatientUserId(chatDO.getExtra().getPersonUserId1());
                chatPatientDoctorListVO.setDoctorUserId(chatDO.getExtra().getPersonUserId2());
            } else {
                chatPatientDoctorListVO.setPatientUserId(chatDO.getExtra().getPersonUserId2());
                chatPatientDoctorListVO.setDoctorUserId(chatDO.getExtra().getPersonUserId1());
            }
            chatPatientDoctorListVO.setPatientName(patientBaseVO.getName());
            chatPatientDoctorListVO.setSex(patientBaseVO.getSex());
            chatPatientDoctorListVO.setDoctorUserName(userMap.get(chatPatientDoctorListVO.getDoctorUserId()).getName());
            chatPatientDoctorListVO.setDoctorUserAvatar(userMap.get(chatPatientDoctorListVO.getDoctorUserId()).getAvatar());
            chatPatientDoctorListVO.setPatientUserAvatar(userMap.get(chatPatientDoctorListVO.getPatientUserId()).getAvatar());
            chatPatientDoctorListVO.setRelation(userRelationMap.get(chatPatientDoctorListVO.getPatientUserId()));
            MessageDO messageDO = messageDOMap.get(chatDO.getLastMessageId());
            chatPatientDoctorListVO.setLastMessageTime(messageDO == null ? null : messageDO.getTime());
            chatPatientDoctorListVOS.add(chatPatientDoctorListVO);
        }
        return chatPatientDoctorListVOS;
    }

    /**
     * 修改直播聊天状态
     *
     * @param liveCode 直播code
     * @param status   直播状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeLiveChatStatus(String liveCode, LiveChatStatus status) {
        log.info("changeLiveChatStatus liveCode:{}, status:{}", liveCode, status);
        String shaCode = DigestUtil.sha256Hex(ChatType.LIVE + "_" + liveCode);
        ChatDO chatDO = chatMapper.selectOne(new LambdaQueryWrapper<ChatDO>().eq(ChatDO::getShaCode, shaCode));
        if (Objects.isNull(chatDO)) {
            log.error("changeLiveChatStatus chat not found, liveCode:{}", liveCode);
            return;
        }
        if (status == LiveChatStatus.VIDEO_NOW) {
            chatDO.getExtra().setLiveStatus(LiveChatStatus.VIDEO_NOW);
        } else if (status == LiveChatStatus.CLOSE) {
            chatDO.getExtra().setLiveStatus(LiveChatStatus.CLOSE);
        } else {
            return;
        }
        chatMapper.updateById(chatDO);

    }

    /**
     * 获取聊天会话信息
     *
     * @param chatId 会话ID
     * @return 聊天会话的详情信息
     */
    public ChatVO getChat(Long chatId) {
        ChatDO chatDO = chatMapper.selectById(chatId);
        return CopyPropertiesUtil.normalCopyProperties(chatDO, ChatVO.class);
    }

    public List<ChatDetailVO> getChatDetail(List<Long> chatIds, Long userId) {
        List<ChatDO> chatDOS = chatMapper.selectList(new LambdaQueryWrapper<ChatDO>().in(ChatDO::getId, chatIds));
        List<Long> otherUserIds = chatDOS.stream()
                .filter(e -> e.getType() == ChatType.PERSON)
                .map(chatDO -> chatDO.getExtra().getOtherUserId(userId))
                .collect(Collectors.toList());
        Map<Long, UserSimpleVO> userMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(otherUserIds)) {
            List<UserSimpleVO> userSimpleVOS = userFeign.listByUserIds(new IdListReqVO().setIdList(otherUserIds));
            userMap = userSimpleVOS.stream().collect(Collectors.toMap(UserSimpleVO::getId, e -> e));
        }
        List<ChatDetailVO> chatDetailVOS = new ArrayList<>();
        JSONObject systemUserJson = commonConfigFeign.getCommonConfigValueJsonByKey("system_user");
        for (ChatDO chatDO : chatDOS) {
            ChatDetailVO chatDetailVO = CopyPropertiesUtil.normalCopyProperties(chatDO, ChatDetailVO.class);
            if (chatDO.getType() == ChatType.PERSON) {
                Long otherUserId = chatDO.getExtra().getOtherUserId(userId);
                UserSimpleVO userSimpleVO = userMap.getOrDefault(otherUserId, new UserSimpleVO());
                chatDetailVO.setName(userSimpleVO.getName());
                chatDetailVO.setLogo(userSimpleVO.getAvatar());
                if (StringUtils.isNotEmpty(userSimpleVO.getMedicalCategory())) {
                    DictDataRespVO dictDataRespVO = dictDataFeign.getDictDataByValue(userSimpleVO.getMedicalCategory());
                    if (Objects.nonNull(dictDataRespVO)) {
                        chatDetailVO.setName(chatDetailVO.getName() + dictDataRespVO.getLabel());
                    }
                }
            } else if (chatDO.getType() == ChatType.SYSTEM) {
                Long systemUserId = chatDO.getExtra().getSystemUserId();
                JSONObject systemUser = systemUserJson.getJSONObject(systemUserId.toString());
                chatDetailVO.setName(systemUser.getString("name"));
                chatDetailVO.setLogo(systemUser.getString("logo"));
            }
            chatDetailVOS.add(chatDetailVO);
        }
        return chatDetailVOS;
    }

    /**
     * 更新聊天会话的最后一条消息
     *
     * @param messageId 消息ID
     * @param chatId    会话ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateLastMessageById(Long messageId, Long chatId) {
        chatMapper.updateLastMessageById(messageId, chatId);
    }
}
