package com.ykl.med.push.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.StringListTypeHandler;
import com.ykl.med.push.enums.UserComplaintReplyType;
import com.ykl.med.push.enums.UserComplaintStatus;
import com.ykl.med.push.enums.UserComplaintType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@TableName(value = "t_user_complaint", autoResultMap = true)
@Data
public class UserComplaintDO extends BaseDO {
    private Long userId;
    private String name;
    private String contactPhone;
    private String content;
    private String reason;
    private UserComplaintType type;
    private Long targetUser;
    private String targetUserName;
    private Integer flowerNum;
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> images;
    private UserComplaintStatus status;

    private Long replyUserId;
    private UserComplaintReplyType replyType;
    private LocalDateTime replyTime;
    private String replyContent;

}
