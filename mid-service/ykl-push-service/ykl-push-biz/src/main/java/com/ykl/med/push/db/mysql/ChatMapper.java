package com.ykl.med.push.db.mysql;

import com.github.yulichang.base.MPJBaseMapper;
import com.ykl.med.push.db.entity.ChatDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface ChatMapper extends MP<PERSON>BaseMapper<ChatDO> {
    @Update("UPDATE t_chat SET last_message_id = #{lastMessageId} WHERE id = #{id} and last_message_id < #{lastMessageId}")
    int updateLastMessageById(@Param("lastMessageId") Long lastMessageId, @Param("id") Long id);
}
