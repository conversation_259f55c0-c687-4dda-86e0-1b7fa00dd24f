package com.ykl.med.push.service.complaint;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.mybatis.core.type.EncryptTypeHandler;
import com.ykl.med.push.db.entity.UserComplaintDO;
import com.ykl.med.push.db.mysql.UserComplaintMapper;
import com.ykl.med.push.enums.UserComplaintStatus;
import com.ykl.med.push.vo.complaint.UserComplaintQueryVO;
import com.ykl.med.push.vo.complaint.UserComplaintReplyVO;
import com.ykl.med.push.vo.complaint.UserComplaintReqVO;
import com.ykl.med.push.vo.complaint.UserComplaintVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户投诉服务类
 */
@Service
@Slf4j
public class UserComplaintService {

    @Resource
    private UserComplaintMapper userComplaintMapper;

    /**
     * 添加用户投诉
     *
     * @param reqVO 投诉请求视图对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void addUserComplaint(UserComplaintReqVO reqVO) {
        log.info("addUserComplaint reqVO:{}", JSON.toJSONString(reqVO));
        UserComplaintDO userComplaintDO = CopyPropertiesUtil.normalCopyProperties(reqVO, UserComplaintDO.class);
        userComplaintDO.setUserId(reqVO.getCurrentUserId());
        userComplaintDO.setStatus(UserComplaintStatus.WAIT_REPLY);
        userComplaintMapper.insert(userComplaintDO);
    }

    /**
     * 回复用户投诉
     *
     * @param reqVO 投诉回复视图对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void replyUserComplaint(UserComplaintReplyVO reqVO) {
        log.info("replyUserComplaint reqVO:{}", JSON.toJSONString(reqVO));
        UserComplaintDO userComplaintDO = userComplaintMapper.selectById(reqVO.getId());
        userComplaintDO.setReplyUserId(reqVO.getCurrentUserId());
        userComplaintDO.setReplyType(reqVO.getReplyType());
        userComplaintDO.setReplyContent(reqVO.getReplyContent());
        userComplaintDO.setReplyTime(reqVO.getReplyTime() == null ? LocalDateTime.now() : reqVO.getReplyTime());
        userComplaintDO.setStatus(UserComplaintStatus.REPLIED);
        userComplaintMapper.updateById(userComplaintDO);
    }

    /**
     * 查询用户投诉
     *
     * @param reqVO 投诉查询视图对象
     * @return 分页的用户投诉列表
     */
    public PageResult<UserComplaintVO> queryUserComplaint(UserComplaintQueryVO reqVO) {
        LambdaQueryWrapper<UserComplaintDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(reqVO.getWord())) {
            queryWrapper.and(e -> e.like(UserComplaintDO::getName, reqVO.getWord())
                    .or().eq(UserComplaintDO::getContactPhone, EncryptTypeHandler.encrypt(reqVO.getWord()))
                    .or().like(UserComplaintDO::getContent, reqVO.getWord()));
        }
        queryWrapper.eq(reqVO.getStatus() != null, UserComplaintDO::getStatus, reqVO.getStatus());
        queryWrapper.eq(reqVO.getUserId() != null, UserComplaintDO::getUserId, reqVO.getUserId());
        queryWrapper.eq(UserComplaintDO::getType, reqVO.getType());
        queryWrapper.orderByDesc(UserComplaintDO::getCreateTime);
        Page<UserComplaintDO> page = userComplaintMapper.selectPage(new Page<>(reqVO.getPageNo(), reqVO.getPageSize()), queryWrapper);
        if (page == null || page.getRecords() == null) {
            return PageResult.empty();
        }
        List<UserComplaintVO> list = CopyPropertiesUtil.normalCopyProperties(page.getRecords(), UserComplaintVO.class);
        return new PageResult<>(list, page.getTotal());
    }
}