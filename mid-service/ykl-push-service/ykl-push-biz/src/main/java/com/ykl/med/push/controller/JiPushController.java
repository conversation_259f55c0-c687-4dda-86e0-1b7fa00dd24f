package com.ykl.med.push.controller;

import com.ykl.med.push.api.JiPushFeign;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.service.jipush.JiPushMessageService;
import com.ykl.med.push.vo.jipush.JiPushMessageReqVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "极光推送服务")
@RestController
@RequestMapping("/jipush")
@Validated
public class JiPushController implements JiPushFeign {
    @Resource
    private JiPushMessageService jiPushMessageService;

    @Override
    @PostMapping("/sendJiPushMessage")
    public void sendJiPushMessage(@RequestBody @Validated(JiPushMessageReqVO.JiPushScopeNotAll.class) JiPushMessageReqVO reqVO) {
        jiPushMessageService.sendJiPushMessage(reqVO);
    }

    @Override
    @PostMapping("/sendJiPushMessageMQ")
    public void sendJiPushMessageMQ(
            @RequestParam(value = "requestId") String requestId,
            @RequestParam(value = "fromUserId") Long fromUserId,
            @RequestParam(value = "toUserId") Long toUserId,
            @RequestParam(value = "messageType") MessageType messageType) {
        jiPushMessageService.sendJiPushMessageMQ(requestId, fromUserId, toUserId, messageType);
    }
}
