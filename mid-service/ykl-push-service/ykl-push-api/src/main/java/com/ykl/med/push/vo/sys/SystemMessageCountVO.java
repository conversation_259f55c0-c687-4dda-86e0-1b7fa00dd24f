package com.ykl.med.push.vo.sys;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "系统消息计数VO")
public class SystemMessageCountVO {
    @Stringify
    @Schema(description = "系统消息计数", example = "123456")
    private Long systemMessageCount;

    @Schema(description = "警告消息计数", example = "123456")
    @Stringify
    private Long warnMessageCount;

    @Schema(description = "总计数", example = "123456")
    @Stringify
    private Long totalCount;
}