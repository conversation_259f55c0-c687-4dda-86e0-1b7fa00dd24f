package com.ykl.med.push.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTaskType {

    SYSTEM_MESSAGE_OVERDUE("系统消息逾期提醒"),//延时
    //废弃了
    NEW_PATIENT("新用户"),
    PATIENT_MEMBER_VERSION_CHANGE("患者会员版本变动"),
    //目前该事件只处理了医生后续调度
    PENDING_WARN("待处理预警"),

    PATIENT_FINISH_SYMPTOM_FORM("完成症状量表"),//{"isPatient":true}
    PATIENT_FINISH_FOLLOW_FORM("完成随访量表"),
    PATIENT_FINISH_FUNCTIONAL_FORM("完成功能康复表单"),
    PATIENT_FINISH_SPORTS_FORM("完成运动康复表单"),
    PATIENT_FINISH_PSYCHOLOGICAL_FORM("完成心理康复表单"),
    PATIENT_FINISH_NUTRITION_FORM("完成营养康复表单"),

    PATIENT_RECORDS_FILE("患者上传病历档案文件"),

    PATIENT_DRUG_CHANGE("药品调整"),
    PATIENT_RECORDS_CHANGE("健康档案调整"),
    PATIENT_INFO_CHANGE("基本信息调整"),
    PATIENT_METRIC_CHANGE("指标调整"),
    PATIENT_SYMPTOM_CHANGE("症状调整"),
    PATIENT_ADVERSE_REACTION_CHANGE("不良反应调整"),

    FOLLOW_START("随访开始"),
    FOLLOW_END("随访结束"),
    FOLLOW_LOST("随访即将失访"),//延时

    FUNCTIONAL_PLAN_EXPIRE("功能方案即将过期"),//延时
    SPORTS_PLAN_EXPIRE("运动方案即将过期"),//延时
    PSYCHOLOGICAL_PLAN_EXPIRE("心理方案即将过期"),//延时
    NUTRITION_PLAN_EXPIRE("营养方案即将过期"),//延时
    SYMPTOM_FORM_NEED_CHANGE("症状量表需要调整"),//延时

    VIDEO_CONSULTATION_START("视频咨询开始"),
    VIDEO_CONSULTATION_START_BIND_DOCTOR("视频咨询开始(给主管医生发的)"),

    MDT_CONSULTATION_WAIT("MDT会诊等待接诊"),
    MDT_CONSULTATION_START("MDT会诊开始"),


    SYMPTOM_FORM_CHANGE("症状量表调整"),

    CONSULT_START("问诊开始"),
    CONSULT_END("问诊结束"),

    MEDICAL_ADVICE_ROLLBACK("创建医嘱回滚"),

    MEDICAL_ORDER_START("用药开始"),
    PRESCRIPTION_REVIEW("处方审核"),
    PRESCRIPTION_REVIEW_PASS("处方审核通过"),

    REPORT_TO_BE_REVIEWED("待审核报告(报告状态变成待审核是触发)"),
    PSYCHO_REHAB_CONSULT_NOTICE("心理预约问诊周期提醒"),//延时


    PSYCHO_REHAB_PLAN_CHANGE("心理康复方案修改事件"),
    FUNC_REHAB_PLAN_CHANGE("功能康复方案修改事件"),
    SPORTS_REHAB_PLAN_CHANGE("运动康复方案修改事件"),
    NUTRITION_REHAB_PLAN_CHANGE("营养康复方案修改事件"),

    PATIENT_DOCTOR_BIND_CHANGE("患者与医生绑定关系变更事件"),

    PSYCHO_REHAB_BASE_PLAN("心理康复基础方案延迟推送"),
    FUNC_REHAB_BASE_PLAN("功能康复基础方案延迟推送"),
    SPORTS_REHAB_BASE_PLAN("运动康复基础方案延迟推送"),
    ;

    private final String desc;
}
