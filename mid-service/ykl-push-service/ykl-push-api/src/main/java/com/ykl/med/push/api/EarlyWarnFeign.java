package com.ykl.med.push.api;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.push.vo.warn.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "ykl-push-service", path = "earlyWarn")
public interface EarlyWarnFeign {

    @PostMapping("/addEarlyWarn")
    @Operation(summary = "添加预警")
    Long addEarlyWarn(@Valid @RequestBody EarlyWarnReqVO reqVO);

    @PostMapping("/send")
    @Operation(summary = "发送预警")
    void send(@Valid @RequestBody EarlyWarnSendReqVO reqVO);

    @PostMapping("/doctorFinish")
    @Operation(summary = "医生完成预警")
    void doctorFinish(@Valid @RequestBody EarlyWarnDoctorFinishReqVO reqVO);

    @PostMapping("/doctorEarlyWarnList")
    @Operation(summary = "医生预警列表")
    PageResult<EarlyWarnDoctorListVO> doctorEarlyWarnList(@Valid @RequestBody EarlyWarnDoctorQueryVO reqVO);

    @PostMapping("/getEarlyWarnDoctorMessagePage")
    @Operation(summary = "医生预警消息列表")
    PageResult<EarlyWarnDoctorMessageVO> getEarlyWarnDoctorMessagePage(@Valid @RequestBody EarlyWarnDoctorMessageQueryVO queryVO);

    @PostMapping("/appEarlyWarnList")
    @Operation(summary = "app预警列表")
    PageResult<EarlyWarnDetailVO> appEarlyWarnList(@Valid @RequestBody EarlyWarnAppQueryVO queryVO);

    @PostMapping("/getEarlyWarnWebById")
    @Operation(summary = "web预警详情")
    EarlyWarnDetailVO getEarlyWarnWebById(@RequestParam("id") Long id);

    @PostMapping("/getEarlyWarnAppTotal")
    @Operation(summary = "app预警总数")
    EarlyWarnAppTotalVO getEarlyWarnAppTotal(@RequestParam("patientId") Long patientId);

    @PostMapping("/getEarlyWarnAppById")
    @Operation(summary = "app预警详情")
    EarlyWarnDetailVO getEarlyWarnAppById(@RequestParam("id") Long id, @RequestParam("patientId") Long patientId);

    @PostMapping("/executedEarlyWarnItem")
    @Operation(summary = "执行预警项")
    void executedEarlyWarnItem(@RequestBody List<Long> itemIds, @RequestParam("patientId") Long patientId);

    @PostMapping("/joinToDo")
    @Operation(summary = "加入待办")
    void joinToDo(@RequestBody List<WarnJoinToDoReqVO> reqVOList, @RequestParam("patientId") Long patientId);

    @PostMapping("/getEarlyWarnCountByDoctor")
    @Operation(summary = "医生预警总数")
    Long getEarlyWarnCountByDoctor(@RequestParam("doctorId") Long doctorId);

    @PostMapping("/getPatientWarnList")
    @Operation(summary = "获取患者预警列表")
    List<PatientWarnVO> getPatientWarnList(@RequestBody List<Long> patientIds);
}