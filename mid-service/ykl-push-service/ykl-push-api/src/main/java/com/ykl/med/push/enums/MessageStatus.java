package com.ykl.med.push.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "消息状态，可选值 NORMAL:正常，RECALL:撤回")
public enum MessageStatus {
    NORMAL(1, "正常"),
    RECALL(2, "撤回"),
    COMPLETED(3, "已完成"),
    EXPIRED(4, "已过期"),
    ;
    /**
     * 编码
     */
    @EnumValue
    private final int code;

    /**
     * 详情
     */
    private final String desc;
}
