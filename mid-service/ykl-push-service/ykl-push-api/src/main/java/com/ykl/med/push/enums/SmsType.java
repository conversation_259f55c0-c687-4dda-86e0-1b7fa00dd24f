package com.ykl.med.push.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "短信类型")
public enum SmsType {
    LOGIN(1, "登录"),
    FAMILY_BIND(2, "家庭成员绑定"),
    CHANGE_PASSWORD(3, "修改密码"),


    VIDEO_CONSULT_BEGIN(10,"视频问诊开始"),
    VIDEO_FOLLOW_UP_TODAY( 11,"今天视频随访"),
    VIDEO_FOLLOW_UP_ADD( 12,"视频随访预约"),

    AUDIO_CONSULT_BEGIN(13,"语音问诊开始"),
    AUDIO_FOLLOW_UP_TODAY( 14,"今天语音随访"),
    AUDIO_FOLLOW_UP_ADD( 15,"语音随访预约");

    /**
     * 编码
     */
    @EnumValue
    private final int code;

    /**
     * 详情
     */
    private final String desc;

}
