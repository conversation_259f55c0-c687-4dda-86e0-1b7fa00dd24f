package com.ykl.med.push.vo.sys;

import com.ykl.med.push.enums.SystemMessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "系统消息查询VO")
public class SystemMessageQueryListVO {
    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "消息类型")
    private SystemMessageType systemMessageType;

    @Schema(description = "业务Id")
    private String bizId;

}
