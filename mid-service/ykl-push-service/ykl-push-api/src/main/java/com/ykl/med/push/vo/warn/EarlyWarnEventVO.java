package com.ykl.med.push.vo.warn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.json.Stringify;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Schema(description="早期预警事件视图对象")
public class EarlyWarnEventVO {
    @Schema(description = "事件ID", example = "1")
    @Stringify
    private Long eventId;

    @Schema(description = "事件名称", example = "事件1")
    private String eventName;

    @Schema(description = "数据ID", example = "1")
    @Stringify
    private Long attributeId;

    @Schema(description = "数据名称", example = "数据1")
    private String attributeName;

    @Schema(description = "数据值", example = "值1")
    private String attributeValue;

    @Schema(description = "数据单位", example = "单位1")
    private String attributeUnit;

    @Schema(description = "事件时间", example = "1616304473000")
    @TimestampConvert
    @NotNull(message = "事件时间不能为空")
    private LocalDateTime eventTime;

    @Schema(description = "扩展信息", example = "扩展信息1")
    private String extraInfo;
}