package com.ykl.med.push.vo.message;

import com.ykl.med.framework.common.pojo.CommonUserIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "创建临时聊天请求")
public class ChatLiveCreateReqVO extends CommonUserIdVO {
    @Schema(description = "房间号", defaultValue = "2")
    @NotNull(message = "房间号不能为空")
    private String code;
}