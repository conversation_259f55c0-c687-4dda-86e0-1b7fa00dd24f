package com.ykl.med.push.vo.message;


import com.ykl.med.push.enums.MessageStatus;
import com.ykl.med.push.enums.MessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "业务消息查询")
@Data
public class MessageStatusChangeVO {
    @Schema(description = "用户ID", example = "**********")
    private List<Long> userId;
    @Schema(description = "业务ID", example = "**********")
    private String bizId;
    @Schema(description = "业务类型", example = "**********", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务类型不能为空")
    private MessageType messageType;

    @Schema(description = "消息状态", example = "**********")
    private MessageStatus messageStatus = MessageStatus.COMPLETED;

    @Schema(description = "是否是患者操作的", example = "**********")
    private Boolean isPatient = false;
}