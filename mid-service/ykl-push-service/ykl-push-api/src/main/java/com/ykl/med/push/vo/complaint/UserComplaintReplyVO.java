package com.ykl.med.push.vo.complaint;

import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.push.enums.UserComplaintReplyType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Schema(description = "用户投诉回复")
public class UserComplaintReplyVO implements AutoBuildUserId {

    @Schema(description = "ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "ID不能为空")
    @Stringify
    private Long id;

    @Schema(description = "当前用户ID", example = "1", hidden = true)
    private Long currentUserId;

    @Schema(description = "回复类型", example = "REPLY", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "回复类型不能为空")
    private UserComplaintReplyType replyType;

    @Schema(description = "回复时间", example = "156883425553")
    @TimestampConvert
    private LocalDateTime replyTime;

    @Schema(description = "回复内容", example = "这是一个回复。")
    private String replyContent;
}