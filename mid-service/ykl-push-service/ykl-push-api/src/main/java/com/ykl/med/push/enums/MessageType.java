package com.ykl.med.push.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "消息类型，可选值:TEXT(文本消息),PIC(图片消息),PIC_TXT(图文消息),RECALL(撤回消息)," +
        "PDF(文件消息),VIDEO(视频消息),AUDIO(音频消息),COMPLETED(已完成),EXPIRED(已过期),FOLLOW_UP_FORM(随访表单)," +
        "SYMPTOM_SCALE(症状量表),FUNCTIONAL_FORM(功能康复表单),SPORTS_FORM(运动康复表单),PSYCHOLOGICAL_FORM(心理康复表单)," +
        "NUTRITION_FORM(营养康复表单),EARLY_WARN(预警干预),DOCTOR_ORDERS(医嘱),PATIENT_EDUCATION(患教文章)," +
        "AUTORESPONDER(自动回复),FOLLOW_TRACK_FORM(跟踪随访表单),FUNCTIONAL_PLAN(功能康复计划),SPORTS_PLAN(运动康复计划)," +
        "PSYCHOLOGICAL_PLAN(心理康复计划),NUTRITION_PLAN(营养康复计划),REPORT(报告识别),REPORT_CONFIRM(报告确认)," +
        "SYSTEM_MESSAGE(系统消息),MEDICAL_HISTORY_FORM(问诊病史填写量表),PRESCRIPTION(处方),MEDICAL_CONSULTATION_SUMMARY(就诊咨询小结)")
public enum MessageType {
    TEXT(1, "聊天"),
    PIC(2, "聊天"),
    PIC_TXT(3, "聊天"),
    RECALL(4, "撤回"),
    PDF(5, "PDF文件"),
    VIDEO(6, "视频"),
    AUDIO(7, "音频"),

    COMPLETED(8, "已完成"),
    EXPIRED(9, "已过期"),

    FOLLOW_UP_FORM(11, "随访表单"),
    SYMPTOM_SCALE(12, "症状量表"),
    FUNCTIONAL_FORM(13, "功能康复表单"),
    SPORTS_FORM(14, "运动康复表单"),
    PSYCHOLOGICAL_FORM(15, "心理康复表单"),
    NUTRITION_FORM(16, "营养康复表单"),
    EARLY_WARN(17, "预警干预"),

    DOCTOR_ORDERS(18, "药品"),

    PATIENT_EDUCATION(20, "患教文章"),
    AUTORESPONDER(21, "自动回复"),

    FOLLOW_TRACK_FORM(22, "跟踪随访表单"),

    FUNCTIONAL_PLAN(23, "功能康复计划"),
    SPORTS_PLAN(24, "运动康复计划"),
    PSYCHOLOGICAL_PLAN(25, "心理康复计划"),
    NUTRITION_PLAN(26, "营养康复计划"),

    REPORT(27, "报告识别"),
    REPORT_CONFIRM(28, "报告确认"),

    //显示在中间
    SYSTEM_MESSAGE(29, "系统消息"),
    MEDICAL_HISTORY_FORM(30, "问诊病史填写量表"),
    PRESCRIPTION(31, "处方"),
    MEDICAL_CONSULTATION_SUMMARY(32, "就诊咨询小结"),
    TREATMENT(33, "诊疗医嘱"),

    PATIENT_REPORT(34, "患者报告"),
    TEXT_AND_LINK(35, "文本消息加超链接"),
    ;
    /**
     * 编码
     */
    @EnumValue
    private final int code;

    /**
     * 详情
     */
    private final String desc;


}
