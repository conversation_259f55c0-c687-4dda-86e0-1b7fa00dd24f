package com.ykl.med.push.vo.jipush;

import com.ykl.med.push.enums.JiPushMessageType;
import com.ykl.med.push.enums.JiPushScopeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class JiPushMessageMqVO {
    @Schema(description = "请求 ID", example = "12345")
    private String requestId;
    @Schema(description = "发送者用户 ID", example = "56789")
    private Long fromUserId;

    @Schema(description = "用户 ID", example = "56789")
    private Long userId;

    @Schema(description = "类型")
    private JiPushMessageType messageType;

    @Schema(description = "消息标题", example = "mytitle")
    private String title;

    @Schema(description = "消息内容", example = "mycontent")
    private String content;

    @Schema(description = "患者 ID", example = "12345")
    private Long patientId;

    @Schema(description = "别名", example = "myalias")
    private String alias;

    @Schema(description = "群发范围")
    private JiPushScopeType scopeType;
}
