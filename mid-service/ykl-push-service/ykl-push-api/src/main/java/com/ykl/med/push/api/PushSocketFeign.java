package com.ykl.med.push.api;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.push.enums.FormChangeNoticeType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Feign客户端，用于进行ykl-push-service服务的操作
 * 该接口定义了与推送通知有关的操作
 */
@FeignClient(name = "ykl-push-service", path = "pushSocket")
public interface PushSocketFeign {

    /**
     * 发送扫描通知
     *
     * @param toUserId      接收通知的用户ID
     * @param medicalTeamId 对应的医疗团队ID
     */
    @PostMapping("/sendScanNotice")
    void sendScanNotice(@RequestParam(value = "toUserId") Long toUserId,
                        @RequestParam(value = "medicalTeamId", required = false) Long medicalTeamId);


    /**
     * 发送在线通知
     *
     * @param toUserId  接收通知的用户ID
     * @param orderCode 相应的订单编号
     */
    @PostMapping("/sendLiveNotice")
    void sendLiveNotice(@RequestParam(value = "toUserId") Long toUserId,
                        @RequestParam(value = "bizId", required = false) Long bizId,
                        @RequestParam(value = "orderCode", required = false) String orderCode);

    /**
     * 发送预警消息
     *
     * @param toUserId 接收通知的用户ID
     */
    @PostMapping("/sendWarnMessage")
    void sendWarnMessage(@RequestParam(value = "toUserId") Long toUserId);

    /**
     * 发送报告识别通知
     *
     * @param toUserId   用户id
     * @param jsonObject 内容
     */
    @PostMapping("/sendReportResultNotice")
    void sendReportResultNotice(@RequestParam(value = "toUserId") Long toUserId, @RequestBody JSONObject jsonObject);

    /**
     * 发送会员版本升级成功通知
     *
     * @param toUserId 用户id
     */
    @PostMapping("/sendMemberVersionSuccessNotice")
    void sendMemberVersionSuccessNotice(@RequestParam(value = "toUserId") Long toUserId);

    @PostMapping("/sendRecipeChangeNotification")
    void sendRecipeChangeNotification(@RequestParam(value = "toPatientId") Long toPatientId);

    @PostMapping("/sendMedicalOrderChangeNotice")
    void sendMedicalOrderChangeNotice(@RequestParam(value = "toPatientId") Long toPatientId);

    /**
     * 发送表单变更通知
     *
     * @param toUserId 用户id
     * @param type     变更类型
     */
    @PostMapping("/sendFormChangeNotice")
    void sendFormChangeNotice(@RequestParam(value = "toUserId") Long toUserId, @RequestParam(value = "type") FormChangeNoticeType type);

    /**
     * 发送新处方通知
     *
     * @param pharmacyId 处方id
     */
    @PostMapping("/sendNewPrescriptionNotice")
    void sendNewPrescriptionNotice(@RequestParam(value = "pharmacyId") Long pharmacyId);

    /**
     * 刷新页面通知
     *
     * @param toUserId  用户id
     * @param patientId 患者id
     */
    @PostMapping("/refreshPageNotice")
    void refreshPageNotice(@RequestParam(value = "toUserId") Long toUserId, @RequestParam(value = "patientId") Long patientId);

    /**
     * 设置用户在应用内
     *
     * @param userId 用户id
     */
    @PostMapping("/setUserCurrentPage")
    void setUserCurrentPage(@RequestParam(value = "userId") Long userId);

    @PostMapping("/delUserCurrentPage")
    void delUserCurrentPage(@RequestParam(value = "userId") Long userId);
}