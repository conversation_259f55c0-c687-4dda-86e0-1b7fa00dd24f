package com.ykl.med.push.vo.todo;

import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "历史待办事项信息列表")
public class ToDoMessageHistoryListVO {

    @Schema(description = "内容")
    private List<String> content;

    @Schema(description = "执行时间", example = "1634736225000")
    @TimestampConvert
    private LocalDateTime executedTime;
}