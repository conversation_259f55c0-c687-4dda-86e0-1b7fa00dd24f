package com.ykl.med.push.vo.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "业务消息基础VO")
public class BizMessageBaseVO {

    @Schema(description = "业务id", defaultValue = "defaultBizId")
    private String bizId;

    @Schema(description = "媒体列表")
    private List<String> mediaIds;

    @Schema(description = "业务名称")
    private String bizName;

    @Schema(description = "业务摘要")
    private String bizSummary;

    @Schema(description = "业务类型")
    private String bizType;

    @Schema(description = "业务作者")
    private String bizAuthor;

    @Schema(description = "子项目")
    private List<BizMessageBaseVO> childrenBiz;

    /**
     * {@link com.ykl.med.framework.common.constants.JumpConstants}
     */
    @Schema(description = "业务跳转")
    private String yklJumpUrl;

    @Schema(description = "超链接")
    private String linkText;
}