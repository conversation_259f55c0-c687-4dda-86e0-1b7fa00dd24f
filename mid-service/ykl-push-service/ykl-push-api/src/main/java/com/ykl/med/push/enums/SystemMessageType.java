package com.ykl.med.push.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum SystemMessageType {
    NEW_PATIENT("患者新加入", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    OVERDUE_NEW_PATIENT("患者新加入逾期提醒", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),

    PENDING_WARN("待处理预警", NoticeType.WARN_NOTICE, NoticeSubType.WARNING_MESSAGE),
    OVERDUE_PENDING_WARN("待处理预警逾期", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),

    PATIENT_FINISH_SYMPTOM_FORM("患者完成症状量表", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_FINISH_FOLLOW_FORM("患者完成随访量表", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_FINISH_FUNCTIONAL_FORM("患者完成功能康复表单", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_FINISH_SPORTS_FORM("患者完成运动康复表单", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_FINISH_PSYCHOLOGICAL_FORM("患者完成心理康复表单", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_FINISH_NUTRITION_FORM("患者完成营养康复表单", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),

    PATIENT_RECORDS_FILE("患者上传病历档案文件", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    OVERDUE_PATIENT_RECORDS_FILE("病历档案逾期", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),

    PATIENT_DRUG_CHANGE("药品调整", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_RECORDS_CHANGE("健康档案调整", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_INFO_CHANGE("基本信息调整", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_METRIC_CHANGE("指标调整", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_SYMPTOM_CHANGE("症状调整", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),
    PATIENT_ADVERSE_REACTION_CHANGE("不良反应调整", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),

    FOLLOW_START("随访开始", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    FOLLOW_END("随访结束", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    OVERDUE_FOLLOW_END("随访结束逾期", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),
    FOLLOW_LOST("随访即将失访", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),

    FUNCTIONAL_PLAN_EXPIRE("功能方案即将过期", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    SPORTS_PLAN_EXPIRE("运动方案即将过期", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    PSYCHOLOGICAL_PLAN_EXPIRE("心理方案即将过期", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    NUTRITION_PLAN_EXPIRE("营养方案即将过期", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    OVERDUE_FUNCTIONAL_PLAN_EXPIRE("功能方案即将过期逾期", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),
    OVERDUE_SPORTS_PLAN_EXPIRE("运动方案即将过期逾期", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),
    OVERDUE_PSYCHOLOGICAL_PLAN_EXPIRE("心理方案即将过期逾期", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),
    OVERDUE_NUTRITION_PLAN_EXPIRE("营养方案即将过期逾期", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),

    SYMPTOM_FORM_NEED_CHANGE("症状量表需要调整", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    OVERDUE_SYMPTOM_FORM_NEED_CHANGE("症状量表需要调整逾期", NoticeType.OVERDUE_TODO, NoticeSubType.TO_DO_LIST),

    VIDEO_CONSULTATION_START("视频咨询开始", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    VIDEO_CONSULTATION_START_BIND_DOCTOR("视频咨询开始(给主管医生发的)", NoticeType.MESSAGE_NOTICE, NoticeSubType.VIDEO_CONSULTATION),

    MDT_CONSULTATION_WAIT("MDT会诊等待接诊", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    MDT_CONSULTATION_START("MDT会诊开始", NoticeType.MESSAGE_NOTICE, NoticeSubType.VIDEO_CONSULTATION),

    PRESCRIPTION_REVIEW("处方审核结果通知", NoticeType.MESSAGE_NOTICE, NoticeSubType.VIDEO_CONSULTATION),

    REPORT_TO_BE_REVIEWED("待审核报告", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),

    COMMON_TO_DO("通用待办", NoticeType.MESSAGE_NOTICE, NoticeSubType.TO_DO_LIST),
    COMMON_PATIENT_OPERATIONS("通用患者操作", NoticeType.MESSAGE_NOTICE, NoticeSubType.PATIENT_OPERATE),

    REHAB_PLAN_ADD("康复方案新增", NoticeType.MESSAGE_NOTICE, NoticeSubType.MESSAGE_NOTICE),
    REHAB_PLAN_CHANGE("康复方案调整", NoticeType.MESSAGE_NOTICE, NoticeSubType.MESSAGE_NOTICE),
    FORM_ADD("表单新增", NoticeType.MESSAGE_NOTICE, NoticeSubType.MESSAGE_NOTICE)
    ;

    private final String desc;
    private final NoticeType noticeType;
    private final NoticeSubType noticeSubType;


    public static List<SystemMessageType> getNoticeSubTypeList(NoticeSubType noticeSubType) {
        if (noticeSubType == null) {
            return null;
        }
        SystemMessageType[] values = SystemMessageType.values();
        return Arrays.stream(values).filter(systemMessageType -> systemMessageType.getNoticeSubType().equals(noticeSubType)).collect(Collectors.toList());
    }


    public static List<SystemMessageType> getNoticeTypeList(NoticeType noticeType) {
        if (noticeType == null) {
            return null;
        }
        SystemMessageType[] values = SystemMessageType.values();
        return Arrays.stream(values).filter(systemMessageType -> systemMessageType.getNoticeType().equals(noticeType)).collect(Collectors.toList());
    }

    private static final Map<SystemMessageType, SystemMessageType> MAP = new HashMap<>();

    static {
        MAP.put(SystemMessageType.OVERDUE_NEW_PATIENT, SystemMessageType.NEW_PATIENT);
        MAP.put(SystemMessageType.OVERDUE_PENDING_WARN, SystemMessageType.PENDING_WARN);
        MAP.put(SystemMessageType.OVERDUE_PATIENT_RECORDS_FILE, SystemMessageType.PATIENT_RECORDS_FILE);
        MAP.put(SystemMessageType.OVERDUE_FUNCTIONAL_PLAN_EXPIRE, SystemMessageType.FUNCTIONAL_PLAN_EXPIRE);
        MAP.put(SystemMessageType.OVERDUE_SPORTS_PLAN_EXPIRE, SystemMessageType.SPORTS_PLAN_EXPIRE);
        MAP.put(SystemMessageType.OVERDUE_PSYCHOLOGICAL_PLAN_EXPIRE, SystemMessageType.PSYCHOLOGICAL_PLAN_EXPIRE);
        MAP.put(SystemMessageType.OVERDUE_NUTRITION_PLAN_EXPIRE, SystemMessageType.NUTRITION_PLAN_EXPIRE);
        MAP.put(SystemMessageType.OVERDUE_SYMPTOM_FORM_NEED_CHANGE, SystemMessageType.SYMPTOM_FORM_NEED_CHANGE);
        MAP.put(SystemMessageType.OVERDUE_FOLLOW_END, SystemMessageType.FOLLOW_END);
    }

    /**
     * 获取逾期(源头类型)
     *
     * @param systemMessageType 系统消息类型
     * @return 逾期源头类型
     */
    public static SystemMessageType getSourceType(SystemMessageType systemMessageType) {
        return MAP.get(systemMessageType);
    }

    /**
     * 获取逾期类型
     *
     * @param systemMessageType 系统消息类型
     * @return 逾期类型
     */
    public static SystemMessageType getOverdueType(SystemMessageType systemMessageType) {
        return MAP.entrySet().stream()
                .filter(entry -> entry.getValue() == systemMessageType)
                .map(Map.Entry::getKey)
                .findAny()
                .orElse(null);
    }


    @Getter
    @AllArgsConstructor
    public enum NoticeType {
        MESSAGE_NOTICE("消息提醒"),
        OVERDUE_TODO("逾期待办"),
        WARN_NOTICE("预警提醒"),
        ;
        private final String desc;
    }


    @Getter
    @AllArgsConstructor
    public enum NoticeSubType {
        TO_DO_LIST("待办事项"),
        WARNING_MESSAGE("预警消息"),
        PATIENT_OPERATE("患者操作"),
        VIDEO_CONSULTATION("门诊提醒"),
        MESSAGE_NOTICE("消息提醒"),
        ;
        private final String desc;
    }
}
