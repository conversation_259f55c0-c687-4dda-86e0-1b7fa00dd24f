package com.ykl.med.push.vo.message;

import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.push.enums.MessageResourceType;
import com.ykl.med.push.enums.MessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "直播消息发送请求对象")
@Data
public class LiveMessageSendReqVO implements AutoBuildUserId {
    @Schema(description = "用户ID", example = "2", hidden = true)
    @NotNull(message = "用户ID不能为空")
    private Long currentUserId;

    @Schema(description = "请求ID(做幂等)", defaultValue = "212312dgasrtq45", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请求ID不能为空")
    private String requestId;

    @Schema(description = "房间号", defaultValue = "2", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "房间号不能为空")
    private String code;

    @Schema(description = "消息类型", defaultValue = "TEXT", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "消息类型不能为空")
    private MessageType type;

    @Schema(description = "消息内容", defaultValue = "这是一条默认消息", requiredMode = Schema.RequiredMode.REQUIRED)
    private String msg;

    @Schema(description = "附加的业务消息")
    private BizMessageBaseVO extra;

    @Schema(description = "是否发送给自己", defaultValue = "false", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean sendMe = true;

    @Schema(description = "消息来源类型", defaultValue = "APP", requiredMode = Schema.RequiredMode.REQUIRED, hidden = true)
    private MessageResourceType resourceType = MessageResourceType.SYSTEM;
}
