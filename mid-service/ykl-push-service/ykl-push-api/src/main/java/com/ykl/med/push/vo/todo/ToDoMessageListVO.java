package com.ykl.med.push.vo.todo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.push.enums.ToDoMessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "待办事项信息列表")
public class ToDoMessageListVO {

    @Stringify
    @Schema(description = "id", example = "123456")
    private Long id;

    @Schema(description = "类型", example = "EARLY_WARN")
    private ToDoMessageType type;

    @Schema(description = "内容", example = "这是一个内容示例")
    private String content;

    @Schema(description = "外部业务id", example = "OUT123456")
    private String outBizId;

    @Schema(description = "逾期", example = "false")
    private Boolean expired;

    @Schema(description = "过期时间", example = "1623749132000")
    @TimestampConvert
    private LocalDateTime expireTime;

    @Schema(description = "干预项目类型", example = "药物")
    private String subType;
}