package com.ykl.med.push.vo.sys;

import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "系统消息读取请求VO")
public class SystemMessageReadReqVO implements AutoBuildUserId {
    @Schema(description = "当前用户ID", defaultValue = "2", hidden = true)
    @NotNull(message = "当前用户ID不能为空")
    private Long currentUserId;

    @Schema(description = "消息ID")
    private Long messageId;

    @Schema(description = "消息ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> messageIds;

    @Schema(description = "全部阅读", defaultValue = "false", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "全部阅读不能为空")
    private Boolean allRead;
}