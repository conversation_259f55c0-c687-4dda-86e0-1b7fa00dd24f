package com.ykl.med.push.vo.warn;

import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.push.enums.EarlyWarnFinishStatus;
import com.ykl.med.framework.common.enums.EarlyWarnLevel;
import com.ykl.med.push.enums.EarlyWarnType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EarlyWarnDoctorQueryVO extends PageParam implements AutoBuildUserId{

    @Schema(description = "病人ID", example = "12345",requiredMode = Schema.RequiredMode.REQUIRED)
    @Stringify
    private Long patientId;

    @Schema(description = "当前用户ID", example = "54321", hidden = true)
    @Stringify
    private Long currentUserId;

    @Schema(description = "预警类型")
    private EarlyWarnType earlyWarnType;


    @Schema(description = "预警等级")
    private EarlyWarnLevel warnLevel;

    @Schema(description = "医生预警处理完成", example = "NO_PROCESSING_REQUIRED")
    private EarlyWarnFinishStatus doctorFinish;
}