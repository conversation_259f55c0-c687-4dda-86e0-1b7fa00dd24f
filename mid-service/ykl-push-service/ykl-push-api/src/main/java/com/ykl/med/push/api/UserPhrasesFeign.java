package com.ykl.med.push.api;

import com.ykl.med.push.vo.message.UserPhrasesReqVO;
import com.ykl.med.push.vo.message.UserPhrasesVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * 用户常用语的Feign客户端定义。
 */
@FeignClient(value = "ykl-push-service", path = "/userPhrases")
public interface UserPhrasesFeign {

    /**
     * 添加用户常用语。
     *
     * @param reqVO 用户常用语请求对象
     */
    @PostMapping("/add")
    void add(@RequestBody @Valid UserPhrasesReqVO reqVO);

    /**
     * 根据用户ID获取用户常用语。
     *
     * @param userId 用户ID
     * @return 返回用户常用语信息对象
     */
    @PostMapping("/getByUserId")
    UserPhrasesVO getByUserId(@RequestParam(value = "userId") Long userId);
}