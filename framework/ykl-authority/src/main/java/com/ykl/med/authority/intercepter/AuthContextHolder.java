package com.ykl.med.authority.intercepter;


import com.alibaba.ttl.TransmittableThreadLocal;
import com.ykl.med.user.vo.UserAuthorityVO;
import lombok.Getter;

/**
 * 认证上下文
 *
 * <AUTHOR>
 */
public class AuthContextHolder {
    private final TransmittableThreadLocal threadLocal = new TransmittableThreadLocal();

    @Getter
    private static final AuthContextHolder instance = new AuthContextHolder();

    private AuthContextHolder() {
    }

    public void setContext(UserAuthorityVO t) {
        this.threadLocal.set(t);
    }

    public UserAuthorityVO getContext() {
        return (UserAuthorityVO) this.threadLocal.get();
    }

    public void clear() {
        this.threadLocal.remove();
    }
}
