package com.ykl.med.linkdoc;

import com.ykl.med.linkdoc.service.LinkDocService;
import com.ykl.med.linkdoc.service.LinkDocServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/12/26
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(LinkDocProperties.class)
public class LinkDocConfiguration {

    @Resource
    private LinkDocProperties linkDocProperties;

    @Bean(name = "LinkDocService")
    public LinkDocService build() {
        log.info("初始化LinkDoc: {}", linkDocProperties);
        return new LinkDocServiceImpl(linkDocProperties);
    }

}
