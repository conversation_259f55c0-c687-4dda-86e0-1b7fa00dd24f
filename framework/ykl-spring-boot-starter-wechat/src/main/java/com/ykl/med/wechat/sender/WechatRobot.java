package com.ykl.med.wechat.sender;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * 微信机器人
 */
@Slf4j
@Data
@SuppressWarnings("UnstableApiUsage")
public class WechatRobot {

    /**
     * 发送超时时间10s
     */
    private static final int TIME_OUT = 10000;
    private static final String ERR_MSG = "errmsg";
    private static final String DEFAULT_ERR_MSG = "ok";
    /**
     * 线程池异步去处理
     */
    private static final ThreadPoolExecutor POOL_EXECUTOR = new ThreadPoolExecutor(1, 2,
            60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(60),
            r -> {
                Thread t = new Thread(r);
                t.setName("ding-push:" + r);
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 微信限流，每分钟20次
     */
    private static final RateLimiter RATE_LIMITER = RateLimiter.create(0.3);

    private String webHook;
    private String serverName;

    public WechatRobot(String webHook,  String serverName) {
        this.webHook = webHook;
        this.serverName = serverName;
    }

    private static String buildReqStr(String content) {
        // 消息内容
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("content", content);
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("msgtype", "text");
        reqMap.put("text", contentMap);
        return JSON.toJSONString(reqMap);
    }

    private static void postJson(String url, String reqStr) {
        try {
            String body = HttpRequest.post(url).body(reqStr).timeout(TIME_OUT).execute().body();
            JSONObject jsonObject = JSON.parseObject(body);
            String resultMsg = jsonObject.getString(ERR_MSG);
            if (!DEFAULT_ERR_MSG.equals(resultMsg)) {
                log.error("微信发送异常：{}", resultMsg);
            }
        } catch (Exception e) {
            log.info("发送微信出现异常，errMsg:{}", e.getMessage());
        }
    }


    public void sendMsg(Exception e) {
        try {
            StackTraceElement realStackTrace = getRealStackTrace(e);
            String realStackTraceString = (realStackTrace != null) ? realStackTrace.toString() : "No stack trace available";
            String sb = serverName + " : \n"
                    + e.getMessage() + ";\n "
                    + realStackTraceString + ";\n "
                    + "ip : " + InetAddress.getLocalHost().getHostName();
            sendMsg(sb);
        } catch (Exception de) {
            log.info("发送微信消息异常,{}", de.getMessage());
        }
    }

    public void sendMsg(String content) {
        if (RATE_LIMITER.tryAcquire()) {
            // 组装请求内容
            String reqStr = buildReqStr(content);
            // 推送消息（http请求）
            POOL_EXECUTOR.submit(() -> postJson(this.webHook, reqStr));
        } else {
            log.info("发送内容:{},限流中...", content);
        }
    }


    public static StackTraceElement getRealStackTrace(Exception ex) {
        if (ex.getStackTrace() == null || ex.getStackTrace().length == 0) {
            return null;
        }
        int i = 0;
        while (ex.getStackTrace()[i].getClassName().contains("com.ykl.med.base.exception.ServiceFeignErrorDecoder")
                || ex.getStackTrace()[i].getClassName().contains("com.ykl.med.framework.common")
                || !ex.getStackTrace()[i].getClassName().contains("com.ykl.med")) {
            i++;
            if (i >= ex.getStackTrace().length) {
                return null;
            }
        }
        return ex.getStackTrace()[i];
    }
}