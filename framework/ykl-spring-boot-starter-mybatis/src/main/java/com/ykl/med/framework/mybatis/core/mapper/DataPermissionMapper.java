package com.ykl.med.framework.mybatis.core.mapper;

import org.apache.ibatis.annotations.*;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Set;

/**
 * 数据权限Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataPermissionMapper {

    @Select({"<script>",
            "select distinct business_id from ${tableName}_data_permission",
            "where all_visible or user_id = #{userId} or medical_team_id in",
            "<foreach item='medicalTeamId' index='index' collection='medicalTeamIds' open='(' separator=',' close=')'>",
            "#{medicalTeamId}",
            "</foreach>",
            "</script>"})
    List<Long> businessIds(@Param("tableName") @NonNull String tableName, @Param("userId") @NonNull Long userId, @Param("medicalTeamIds") @NonNull Set<Long> medicalTeamIds);

    @Delete("delete from ${tableName}_data_permission where business_id = #{businessId}")
    void delete(@Param("tableName") @NonNull String tableName, @Param("businessId") @NonNull Long businessId);

    @Insert("insert into ${tableName}_data_permission (all_visible, user_id, medical_team_id, business_id) values (#{allVisible}, #{userId}, #{medicalTeamId}, #{businessId})")
    void insert(@Param("tableName") @NonNull String tableName, @Param("allVisible") @NonNull Boolean allVisible, @Param("businessId") @NonNull Long businessId, @Param("userId") Long userId, @Param("medicalTeamId") Long medicalTeamId);

}
