package com.ykl.med.framework.trx;

import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 事务后置操作
 */
public class TransactionalAfterCommitExecutor {

    public static void executeAfterCommit(Runnable runnable) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 事务成功提交后执行
                    runnable.run();
                }
            });
        } else {
            // 非事务环境，直接执行
            runnable.run();
        }
    }
}

