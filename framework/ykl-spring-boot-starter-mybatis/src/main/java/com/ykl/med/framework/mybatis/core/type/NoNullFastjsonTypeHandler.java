package com.ykl.med.framework.mybatis.core.type;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@MappedTypes({Object.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class NoNullFastjsonTypeHandler extends AbstractJsonTypeHandler<Object> {
    private static final Logger log = LoggerFactory.getLogger(com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class);
    private final Class<?> type;

    public NoNullFastjsonTypeHandler(Class<?> type) {
        if (log.isTraceEnabled()) {
            log.trace("FastjsonTypeHandler(" + type + ")");
        }

        Assert.notNull(type, "Type argument cannot be null", new Object[0]);
        this.type = type;
    }

    protected Object parse(String json) {
        return JSON.parseObject(json, this.type);
    }

    protected String toJson(Object obj) {
        return JSON.toJSONString(obj);
    }
}