package com.ykl.med.supervise.service;

import com.ykl.med.supervise.SuperviseProperties;
import com.ykl.med.supervise.vo.AccessToken;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.IdentityHashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/11/5
 */
public abstract class AbstractSuperviseService {

    private static final Logger log = LoggerFactory.getLogger(AbstractSuperviseService.class);
    private static final int MAX_FAILED_ATTEMPTS = 5; // 最大失败次数
    private static final long MELT_DOWN_DURATION = TimeUnit.HOURS.toMillis(2); // 熔断时长
    private static final int DELAY_SECOND = 60 * 60;
    private static final int MIN_REFRESH_SECOND = 60 * 10;
    private static final long TIME_OUT = 60 * 60; // 提前1小时
    protected SuperviseProperties properties;
    protected StringRedisTemplate redisTemplate;
    protected static final String GET_ACCESS_TOKEN = "/wjw/third/oauth/getAccessToken";
    protected static final String UPLOAD_CONSULT = "/wjw/upload/uploadConsult";
    protected static final String UPLOAD_FURTHER_CONSULT = "/wjw/upload/uploadFurtherConsult";
    protected static final String UPLOAD_RECIPE = "/wjw/upload/uploadRecipe";
    protected static final String ACCESS_TOKEN_REDIS_KEY = "supervision:platform:sichuan:kay";
    protected static final String ACCESS_TOKEN_LOCK_KEY = "supervision:platform:sichuan:lock";
    private static final String FAILED_ATTEMPTS_KEY = "supervision:token:failed_attempts";
    private static final String LAST_FAILED_TIME_KEY = "supervision:token:last_failed_time";
    private final Boolean manualPaused;


    protected AbstractSuperviseService(SuperviseProperties superviseProperties, StringRedisTemplate stringRedisTemplate) {
        this.properties = superviseProperties;
        this.redisTemplate = stringRedisTemplate;
        this.manualPaused = superviseProperties.getManualPaused() != null ? superviseProperties.getManualPaused() : false;

        // 缓存内过期数据，开启线程自检
        Executors.newSingleThreadScheduledExecutor().scheduleWithFixedDelay(this::accessTokenExpiredCheck, 1, DELAY_SECOND, TimeUnit.SECONDS);
    }

    private String refreshAccessToken() {
        AccessToken token = getNowToken();
        redisTemplate.opsForValue().set(ACCESS_TOKEN_REDIS_KEY, token.getAccessToken(), token.getExpiresIn() - TIME_OUT, TimeUnit.SECONDS);
        return token.getAccessToken();
    }


    private void accessTokenExpiredCheck() {
        try {
            // 手动暂停判断
            if (isManualPaused()) {
                log.info("监管平台：Token刷新已被手动暂停");
                return;
            }

            // 检查 token 是否有效
            Long expire = redisTemplate.getExpire(ACCESS_TOKEN_REDIS_KEY, TimeUnit.SECONDS);
            if (expire != null && expire > MIN_REFRESH_SECOND) {
                return;
            }

            // 检查是否处于熔断期
            if (isInMeltDownPeriod()) {
                log.warn("处于熔断期，暂时跳过 token 刷新");
                return;
            }

            // 获取锁并刷新 token
            String uuid = UUID.randomUUID().toString();
            Boolean absent = redisTemplate.opsForValue().setIfAbsent(ACCESS_TOKEN_LOCK_KEY, uuid, 5, TimeUnit.SECONDS);
            if (absent == null || !absent) {
                return;
            }

            refreshAccessToken(); // 成功刷新 token
            resetFailedAttempts(); // 成功后重置失败次数

        } catch (Exception e) {
            log.error("刷新监管平台Token失败", e);
            incrementFailedAttempts();

            // 可选：发送告警通知
            if (getFailedAttempts() >= MAX_FAILED_ATTEMPTS) {
                log.warn("已达到最大失败次数 {}，暂停刷新 {} 分钟", MAX_FAILED_ATTEMPTS, MELT_DOWN_DURATION / 60_000);
            }
        }
    }

    // 增加失败次数
    private void incrementFailedAttempts() {
        String countStr = redisTemplate.opsForValue().get(FAILED_ATTEMPTS_KEY);
        int count = countStr == null ? 0 : Integer.parseInt(countStr);
        count++;
        redisTemplate.opsForValue().set(FAILED_ATTEMPTS_KEY, String.valueOf(count), MELT_DOWN_DURATION, TimeUnit.MILLISECONDS);
        redisTemplate.opsForValue().set(LAST_FAILED_TIME_KEY, String.valueOf(System.currentTimeMillis()), MELT_DOWN_DURATION, TimeUnit.MILLISECONDS);
    }

    // 获取失败次数
    private int getFailedAttempts() {
        String countStr = redisTemplate.opsForValue().get(FAILED_ATTEMPTS_KEY);
        return countStr == null ? 0 : Integer.parseInt(countStr);
    }

    protected String getUrl(String url) {
        return properties.getUrl() + url;
    }

    protected abstract AccessToken getNowToken();

    protected String getToken() {
        String token = redisTemplate.opsForValue().get(ACCESS_TOKEN_REDIS_KEY);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        // 重新获取
        return refreshAccessToken();
    }

    // 重置失败次数
    private void resetFailedAttempts() {
        redisTemplate.delete(FAILED_ATTEMPTS_KEY);
        redisTemplate.delete(LAST_FAILED_TIME_KEY);
    }

    // 是否处于熔断期
    private boolean isInMeltDownPeriod() {
        String lastFailedTimeStr = redisTemplate.opsForValue().get(LAST_FAILED_TIME_KEY);
        if (StringUtils.isBlank(lastFailedTimeStr)) {
            return false;
        }

        long lastFailedTime = Long.parseLong(lastFailedTimeStr);
        return System.currentTimeMillis() - lastFailedTime < MELT_DOWN_DURATION;
    }

    // 是否手动暂停刷新
    private boolean isManualPaused() {
        return manualPaused;
    }

    protected Map<String, Object> initParams() {
        Map<String, Object> param = new IdentityHashMap<>();
        param.put("accessToken", getToken());
        param.put("clientId", properties.getClientId());
        param.put("orgName", properties.getOrgName());
        param.put("orgCode", properties.getOrgCode());
        param.put("cityId", properties.getCityId());
        return param;
    }


}
