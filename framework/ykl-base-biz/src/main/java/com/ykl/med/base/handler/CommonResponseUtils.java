package com.ykl.med.base.handler;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class CommonResponseUtils {

    public static Object convert(Object body, MethodParameter methodParameter) {
        //string 需要单独处理
        if (String.class.equals(methodParameter.getMethod().getReturnType())) {
            return JSONObject.toJSONString(CommonResult.success(body));
        } else if (Long.class.equals(methodParameter.getMethod().getReturnType())) {
            //long 给他转string，防止精度丢失
            return CommonResult.success(String.valueOf(body));
        } else if (isArrayListLong(methodParameter.getMethod())) {
            //List<Long> 给他转List<string>，防止精度丢失
            return CommonResult.success(arrayListLongChange(body));
        } else {
            return CommonResult.success(body);
        }
    }

    private static List<String> arrayListLongChange(Object body) {
        if (body == null) {
            return null;
        }
        List<Long> list = (List<Long>) body;
        return list.stream().map(String::valueOf).collect(Collectors.toList());
    }

    private static boolean isArrayListLong(Method method) {
        try {
            MethodParameter methodParameter = new MethodParameter(method, -1);
            Type genericReturnType = methodParameter.getGenericParameterType();
            if (genericReturnType instanceof ParameterizedType) {
                ParameterizedType type = (ParameterizedType) genericReturnType;
                Type[] typeArguments = type.getActualTypeArguments();
                for (Type typeArgument : typeArguments) {
                    if (typeArgument.getTypeName().equals("java.lang.Long")) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析异常", e);
        }
        return false;
    }
}
