package com.ykl.med.base.exception;


import com.alibaba.fastjson.JSONObject;
import com.ykl.med.base.constants.ServiceFeignConstants;
import com.ykl.med.base.utils.IOUtils;
import com.ykl.med.framework.common.exception.ErrorCode;
import com.ykl.med.framework.common.exception.ServiceException;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collection;

/**
 * Feign服务异常解析器
 * A调用B,如果B返回的数据结构和A不一致，会抛出异常，这个异常会被这个类捕获，然后解析异常信息，返回给A
 * <AUTHOR>
 */
@Component
@Slf4j
public class ServiceFeignErrorDecoder implements ErrorDecoder {

    private final ErrorDecoder defaultErrorDecoder = new Default();

    @Override
    public Exception decode(String methodKey, Response response) {
        Exception exception = handleErrorResponse(response);
        if (exception != null) {
            return exception;
        }
        return defaultErrorDecoder.decode(methodKey, response);
    }

    private Exception handleErrorResponse(Response response) {
        if (response.status() != HttpServletResponse.SC_INTERNAL_SERVER_ERROR) {
            return null;
        }
        Collection<String> errorCodes = response.headers().get(ServiceFeignConstants.SOA_ERROR_SIGN);
        if (errorCodes == null || errorCodes.isEmpty()) {
            return null;
        }
        try {
            byte[] responseBody = IOUtils.getBytes(response.body().asInputStream());
            JSONObject soaExceptionVO = JSONObject.parseObject(new String(responseBody, StandardCharsets.UTF_8));
            ErrorCode errorCode = new ErrorCode(soaExceptionVO.getInteger("code"), soaExceptionVO.getString("msg"));
            return new ServiceException(errorCode);
        } catch (IOException e) {
            log.error("Error when handling error response", e);
            return null;
        }
    }

}
