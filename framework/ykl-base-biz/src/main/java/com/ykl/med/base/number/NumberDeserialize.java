package com.ykl.med.base.number;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/7/18
 */
public class NumberDeserialize extends JsonDeserializer<BigDecimal> {

    @Override
    public BigDecimal deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        JsonNode jsonNode = jsonParser.getCodec().readTree(jsonParser);
        String value;
        if (jsonNode.isNumber()) {
            value = String.valueOf(jsonNode.asDouble());
        } else {
            value = jsonNode.textValue();
            if (StringUtils.isEmpty(value)) {
                return new BigDecimal("0");
            }
        }
        return new BigDecimal(value);
    }
}
