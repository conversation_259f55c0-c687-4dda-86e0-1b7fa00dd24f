package com.ykl.med.framework.common.util;

import java.io.File;
import java.nio.file.Files;

/**
 * <AUTHOR>
 * @since 2025/4/23
 */
public class TempFileHolder implements AutoCloseable {

    private File file;


    public TempFileHolder(File file) {
        this.file = file;
        this.file.deleteOnExit();
    }

    public File getFile() {
        return file;
    }

    @Override
    public void close() throws Exception {
        if (file != null && file.exists()) {
            Files.delete(file.toPath());
        }
    }
}
