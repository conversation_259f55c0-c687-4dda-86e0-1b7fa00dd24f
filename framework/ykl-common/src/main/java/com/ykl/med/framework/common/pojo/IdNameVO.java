package com.ykl.med.framework.common.pojo;

import java.io.Serializable;

import com.ykl.med.framework.common.json.Stringify;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "IdNameVO", description = "id名称VO")
public class IdNameVO implements Serializable {

    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "名称")
    private String name;

    
    
}
