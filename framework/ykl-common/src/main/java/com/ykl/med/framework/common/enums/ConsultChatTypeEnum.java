package com.ykl.med.framework.common.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Getter
@AllArgsConstructor
@Schema(description = "VIDEO_CHAT-视频、TEXT_CHAT-图文、VOICE_CHAT-语音")
public enum ConsultChatTypeEnum {

    VIDEO_CHAT("视频聊天"),
    TEXT_CHAT("文字聊天"),
    VOICE_CHAT("语音聊天");

    private final String desc;
}
