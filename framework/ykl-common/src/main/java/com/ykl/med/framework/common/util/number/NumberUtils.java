package com.ykl.med.framework.common.util.number;

import cn.hutool.core.util.StrUtil;

/**
 * 数字的工具类，补全 {@link cn.hutool.core.util.NumberUtil} 的功能
 *
 * <AUTHOR>
 */
public class NumberUtils {

    public static Long parseLong(String str) {
        return StrUtil.isNotEmpty(str) ? Long.valueOf(str) : null;
    }

    /**
     * 生成18位数字uuid
     * */
    public static Long uuidGeneratorLong18(){
        return System.currentTimeMillis()*100000L+ System.nanoTime()%100000L;
    }

}
