package com.ykl.med.framework.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.ykl.med.framework.common.pojo.PageResult;
import org.checkerframework.checker.units.qual.K;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class BuildDataUtils {

    /**
     * 构建数据
     *
     * @param dataList          数据列表
     * @param keyExtractor      key提取器
     * @param feignList         feign查询结果
     * @param mapKeyExtractor   map key提取器
     * @param mapValueExtractor map value提取器
     * @param valueSetter       value设置器
     * @return 数据列表
     */
    public static <T, R, K, V> List<T> buildListData(List<T> dataList,
                                                     Function<T, K> keyExtractor,
                                                     List<R> feignList,
                                                     Function<R, K> mapKeyExtractor,
                                                     Function<R, V> mapValueExtractor,
                                                     BiConsumer<T, V> valueSetter) {
        if (CollectionUtil.isEmpty(dataList) || CollectionUtil.isEmpty(feignList)) {
            return dataList;
        }
        Map<K, R> resultMap = feignList.stream().collect(Collectors.toMap(mapKeyExtractor, r -> r));
        for (T t : dataList) {
            K key = keyExtractor.apply(t);
            R r = resultMap.get(key);
            if (r != null) {
                V value = mapValueExtractor.apply(r);
                valueSetter.accept(t, value);
            }
        }
        return dataList;
    }


    /**
     * 构建数据
     *
     * @param dataList          数据列表
     * @param keyExtractor      key提取器
     * @param feignListFunction feign查询方法
     * @param mapKeyExtractor   map key提取器
     * @param mapValueExtractor map value提取器
     * @param valueSetter       value设置器
     * @return 数据列表
     */
    public static <T, R, K, V> List<T> buildListDataFeign(List<T> dataList,
                                                          Function<T, K> keyExtractor,
                                                          Function<List<K>, List<R>> feignListFunction,
                                                          Function<R, K> mapKeyExtractor,
                                                          Function<R, V> mapValueExtractor,
                                                          BiConsumer<T, V> valueSetter) {
        if (CollectionUtil.isEmpty(dataList)) {
            return dataList;
        }
        List<K> keys = dataList.stream().map(keyExtractor).collect(Collectors.toList());
        List<R> resultList = feignListFunction.apply(keys);
        return buildListData(dataList, keyExtractor, resultList, mapKeyExtractor, mapValueExtractor, valueSetter);
    }

    /**
     * 构建数据
     *
     * @param dataList           数据列表
     * @param keyExtractor       key提取器
     * @param feignListFunction  feign查询方法
     * @param mapKeyExtractor    map key提取器
     * @param mapValueExtractor  map value提取器
     * @param valueSetter        value设置器
     * @param mapValueExtractor1 map value提取器1
     * @param valueSetter1       value设置器1
     * @return 数据列表
     */
    public static <T, R, K, V> List<T> buildListDataFeign(List<T> dataList,
                                                          Function<T, K> keyExtractor,
                                                          Function<List<K>, List<R>> feignListFunction,
                                                          Function<R, K> mapKeyExtractor,
                                                          Function<R, V> mapValueExtractor,
                                                          BiConsumer<T, V> valueSetter,
                                                          Function<R, V> mapValueExtractor1,
                                                          BiConsumer<T, V> valueSetter1) {
        if (CollectionUtil.isEmpty(dataList)) {
            return dataList;
        }
        List<K> keys = dataList.stream().map(keyExtractor).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<R> resultList = feignListFunction.apply(keys);
        List<T> list = buildListData(dataList, keyExtractor, resultList, mapKeyExtractor, mapValueExtractor, valueSetter);
        return buildListData(list, keyExtractor, resultList, mapKeyExtractor, mapValueExtractor1, valueSetter1);
    }

    /**
     * 构建分页数据
     *
     * @param pageResult        分页数据
     * @param keyExtractor      key提取器（从分页数据提取）
     * @param feignListFunction feign查询方法
     * @param feignKeyExtractor feign key提取器
     * @param mapValueExtractor map value提取器（从feign查询结果提取）
     * @param valueSetter       value设置器
     * @param <T>               分页数据类型
     * @param <R>               feign返回数据类型
     * @param <K>               key类型
     * @param <V>               value类型
     * @return 分页数据
     */
    public static <T, R, K, V> PageResult<T> buildPageDataFeign(PageResult<T> pageResult,
                                                                Function<T, K> keyExtractor,
                                                                Function<List<K>, List<R>> feignListFunction,
                                                                Function<R, K> feignKeyExtractor,
                                                                Function<R, V> mapValueExtractor,
                                                                BiConsumer<T, V> valueSetter) {
        if (pageResult == null || CollectionUtil.isEmpty(pageResult.getList())) {
            return pageResult;
        }
        List<T> resultDataList = buildListDataFeign(pageResult.getList(), keyExtractor, feignListFunction, feignKeyExtractor, mapValueExtractor, valueSetter);
        pageResult.setList(resultDataList);
        return pageResult;
    }

    /**
     * 构建分页数据
     *
     * @param pageResult         分页数据
     * @param keyExtractor       key提取器（从分页数据提取）
     * @param feignListFunction  feign查询方法
     * @param feignKeyExtractor  feign key提取器
     * @param mapValueExtractor  map value提取器（从feign查询结果提取）
     * @param valueSetter        value设置器
     * @param mapValueExtractor1 map value提取器1
     * @param valueSetter1       value设置器1
     * @return 分页数据
     */
    public static <T, R, K, V> PageResult<T> buildPageDataFeign(PageResult<T> pageResult,
                                                                Function<T, K> keyExtractor,
                                                                Function<List<K>, List<R>> feignListFunction,
                                                                Function<R, K> feignKeyExtractor,
                                                                Function<R, V> mapValueExtractor,
                                                                BiConsumer<T, V> valueSetter,
                                                                Function<R, V> mapValueExtractor1,
                                                                BiConsumer<T, V> valueSetter1) {
        if (pageResult == null || CollectionUtil.isEmpty(pageResult.getList())) {
            return pageResult;
        }
        List<T> resultDataList = buildListDataFeign(pageResult.getList(), keyExtractor, feignListFunction, feignKeyExtractor, mapValueExtractor, valueSetter, mapValueExtractor1, valueSetter1);
        pageResult.setList(resultDataList);
        return pageResult;
    }
}
