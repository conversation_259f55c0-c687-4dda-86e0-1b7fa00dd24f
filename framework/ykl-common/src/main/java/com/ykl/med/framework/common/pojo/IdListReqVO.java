package com.ykl.med.framework.common.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * id列表请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "id列表请求")
public class IdListReqVO implements Serializable {

    private static final long serialVersionUID = 7590817713681540761L;

    @Schema(description = "id列表", example = "[1,2,3]", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> idList;
}
