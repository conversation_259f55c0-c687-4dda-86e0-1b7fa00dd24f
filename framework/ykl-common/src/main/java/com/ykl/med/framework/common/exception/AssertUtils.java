package com.ykl.med.framework.common.exception;

import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;

/**
 * 断言工具,判断是否满足断言,如果不满足,则抛出BusinessException
 *
 * <AUTHOR>
 */
public class AssertUtils {


    public static void isTrue(boolean expression, ErrorCode errorCode) throws ServiceException {
        if (!expression) {
            throw new ServiceException(errorCode);
        }
    }

    public static void isNull(Object object, ErrorCode errorCode) throws ServiceException {
        if (object != null) {
            throw new ServiceException(errorCode);
        }
    }

    public static void notNull(Object object, ErrorCode errorCode) throws ServiceException {
        if (object == null) {
            throw new ServiceException(errorCode);
        }
    }

    public static void hasLength(String text, ErrorCode errorCode) throws ServiceException {
        if (!StringUtils.hasLength(text)) {
            throw new ServiceException(errorCode);
        }
    }

    public static void hasText(String text, ErrorCode errorCode) throws ServiceException {
        if (!StringUtils.hasText(text)) {
            throw new ServiceException(errorCode);
        }
    }

    public static void doesNotContain(String textToSearch, String substring, ErrorCode errorCode) throws ServiceException {
        if (StringUtils.hasLength(textToSearch) && StringUtils.hasLength(substring) &&
                textToSearch.contains(substring)) {
            throw new ServiceException(errorCode);
        }
    }

    public static void notEmpty(Object[] array, ErrorCode errorCode) throws ServiceException {
        if (ObjectUtils.isEmpty(array)) {
            throw new ServiceException(errorCode);
        }
    }

    public static void noNullElements(Object[] array, ErrorCode errorCode) throws ServiceException {
        if (array != null) {
            for (Object element : array) {
                if (element == null) {
                    throw new ServiceException(errorCode);
                }
            }
        }
    }

    public static void notEmpty(Collection<?> collection, ErrorCode errorCode) throws ServiceException {
        if (CollectionUtils.isEmpty(collection)) {
            throw new ServiceException(errorCode);
        }
    }

    public static void notEmpty(Map<?, ?> map, ErrorCode errorCode) throws ServiceException {
        if (CollectionUtils.isEmpty(map)) {
            throw new ServiceException(errorCode);
        }
    }


}
