package com.ykl.med.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 不良反应类别
 * */
@AllArgsConstructor
@Getter
public enum AdverseReactionCategory {

    /**
     * 食物分类
     * */
    SYMPTOM("症状","SYMPTOM","symptom"),
    DISEASES("疾病","DISEASES","diseases"),
    TREATMENT("疾病","TREATMENT","treatment")
    ;

    private final String label;
    private final String value;
    private final String name;

    /**
     * 使用value查找
     * */
    public static AdverseReactionCategory fromValue(String value){
        for ( AdverseReactionCategory enumItem : AdverseReactionCategory.values() ){
            if ( enumItem.value.equals(value) ){
                return enumItem;
            }
        }

        return null;
    }

    /**
     * 使用name查找
     * */
    public static AdverseReactionCategory fromName(String name){
        for ( AdverseReactionCategory enumItem : AdverseReactionCategory.values() ){
            if ( enumItem.name.equals(name) ){
                return enumItem;
            }
        }

        return null;
    }

}
