package com.ykl.med.framework.common.util;

import org.apache.commons.codec.digest.DigestUtils;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES加密 解密
 *
 * <AUTHOR>
 * 2024/9/2
 */
public class AesUtils {
    /**
     * 密钥长度必须为16位
     */

    private static final String AES = "AES";

    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    /**
     * 默认key
     */
    private static final String DEFAULT_KEY = "ZYwQLRuet517tXo6xbqZsMjVtC3Jeedb";

    public static String encrypt(String data) throws Exception {
        return encrypt(data,DEFAULT_KEY);
    }

    /**
     * AES加密字符串
     *
     * @param data 需要被加密的字符串
     * @return base64 密文
     */
    public static String encrypt(String data, String key) throws Exception {
        if (data == null) {
            return null;
        }
        // AES专用密钥
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), AES);
        // 创建密码器
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        byte[] byteContent = data.getBytes(StandardCharsets.UTF_8);
        // 初始化为加密模式的密码器
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        // 加密后的byte数据
        byte[] bytes = cipher.doFinal(byteContent);
        // 转base64
        return Base64.getEncoder().encodeToString(bytes);
    }

    public static String decrypt(String data) throws Exception {
        return decrypt(data,DEFAULT_KEY);
    }
    /**
     * 解密AES加密过的字符串
     *
     * @param data AES加密过过的内容
     * @return 明文
     */
    public static String decrypt(String data, String key) throws Exception {
        if (data == null) {
            return null;
        }

        byte[] decodeArray = Base64.getDecoder().decode(data);
        // AES专用密钥
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), AES);
        // 创建密码器
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        // 初始化为解密模式的密码器
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        // 明文数组
        byte[] bytes = cipher.doFinal(decodeArray);

        return new String(bytes, StandardCharsets.UTF_8);

    }

    public static void main(String[] args) throws Exception {
        String data = "{\"visitId\":\"100001\",\"patientId\":\"1000001\",\"sendTime\":\"2025-01-01 00:00:00\",\"sendUser\":\"李富贵\",\"orderIds\":[100001,100002,100003],\"timestamp\":\"1737446769570\"}";
        System.out.println("加密前===>" + data);

        String encrypt = encrypt(data);
        System.out.println("加密后===>" + encrypt);

        String integritySign = DigestUtils.md5Hex(encrypt + DEFAULT_KEY);
        System.out.println("sign===>" + integritySign);

        String decrypt = decrypt(encrypt);
        System.out.println("解密后===>" + decrypt);

    }

}
