package com.ykl.med.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SendMessageType {
    TOKEN_EXPIRE(999, "token过期了"),
    OPEN(1000, "连接成功"),
    MESSAGE(1001, "新聊天消息"),//{chatId:XXX,messageId:XXX}
    SCAN_NOTICE(1002, "扫码通知"),//{medicalTeamId:XXX}
    LIVE_NOTICE(1003, "live视频通知"),//{bizId:XXX,orderCode:XXX}
    LIVE_MESSAGE(1004, "live视频聊天消息"),//{chatId:XXX,messageId:XXX}
    WARN_MESSAGE(1005, "预警消息"),//莫得内容
    WEB_SYSTEM_MESSAGE(1006, "医护端系统消息"),//莫得内容
    REPORT_RESULT_NOTICE(1007, "报告识别结果通知"),//莫得内容
    FORM_CHANGE_NOTICE(1008, "量表变动通知"),//{type:XXX}
    MEMBER_VERSION_SUCCESS_NOTICE(1009, "会员开通成功通知"),//莫得内容
    TO_DO_NOTICE(1010, "待办通知"),//莫得内容
    RECIPE_CHANGE_NOTIFICATION(1011, "食谱变动通知"),//莫得内容
    MEDICAL_ORDER_CHANGE_NOTICE(1012, "用药改变通知"),//莫得内容
    CHAT_CHANGE(1013, "会话有变动"),//{chatId:XXX}
    NEW_PRESCRIPTION_NOTICE(1014, "新处方通知"),//莫得内容
    ;

    private final Integer cmd;
    private final String desc;
}
