package com.ykl.med.framework.common.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.ykl.med.framework.common.util.date.LocalDateTimeUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static java.lang.String.format;

/**
 * 将DateStringConvert的Date类型数据重新封装为Date
 *
 * <AUTHOR>
 */
public class DateStringConvertAnnotationFieldJsonDeserializer extends JsonDeserializer<Object> implements ContextualDeserializer {

    private final String nullString = String.valueOf((Object) null);

    private JavaType javaType;

    @Override
    public Object deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        if (StringUtils.isBlank(value) || nullString.equals(value)) {
            return null;
        }
        try {
            //将yyyy-mm-dd hh:mm:ss格式的时间转换为date
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (javaType.getRawClass().equals(LocalDateTime.class)) {
                return LocalDateTime.parse(value, formatter);
            } else if (javaType.getRawClass().equals(LocalDate.class)) {
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                return LocalDate.parse(value, formatter);
            } else if (javaType.getRawClass().equals(Date.class)) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return simpleDateFormat.parse(value);
            } else {
                throw new RuntimeException("不支持的类型");
            }
        } catch (NumberFormatException | ParseException e) {
            throw new RuntimeException(format("解析 \"%s\":\"%s\" 出现异常,请检查数据是否正确",
                    p.getParsingContext().getCurrentName(), value));
        }
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext deserializationContext, BeanProperty beanProperty) {
        javaType = deserializationContext.getContextualType() != null
                ? deserializationContext.getContextualType()
                : beanProperty.getMember().getType();
        return this;
    }
}
