package com.ykl.med.framework.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "会员服务包类型, EXPERT_ADVICE(专家咨询), ONLINE_VIDEO_CLINIC(线上视频门诊)")
public enum MemberServicePackageType {

    EXPERT_ADVICE(1, "专家咨询"),
    ONLINE_VIDEO_CLINIC(3, "线上视频门诊");

    @EnumValue
    private final int code;
    private final String desc;
}
