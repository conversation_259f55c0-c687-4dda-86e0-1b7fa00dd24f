package com.ykl.med.framework.common.json;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于将 Long 类型数据在Json传输的时候转换为 String 类型，并在反解析的时候自动转换回 Long 类型，解析出现异常会抛出
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.FIELD})
@JacksonAnnotationsInside
@JsonSerialize(using = StringifyAnnotationFieldJsonSerializer.class)
@JsonDeserialize(using = StringifyAnnotationFieldJsonDeserializer.class)
public @interface Stringify {

}
