package com.ykl.med.framework.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.pojo.PageResult;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpMethod;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class CrudUtilsTest {

    @Test
    public void testGetByField() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Arrays.asList(new Object()), 1L);
        when(feignFunctionMock.apply(anyString(), any())).thenReturn(pageResult);

        Object result = CrudUtils.getByField(feignFunctionMock, "testField", "testName");

        assertNotNull(result);
        verify(feignFunctionMock, times(1))
                .apply(eq(HttpMethod.GET.name()), any(JSON.class));
    }

    @Test
    public void testGetByFieldNullResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Arrays.asList(), 0L);
        when(feignFunctionMock.apply(anyString(), any())).thenReturn(pageResult);

        Object result = CrudUtils.getByField(feignFunctionMock, "testField", "testName");

        assertNull(result);
        verify(feignFunctionMock, times(1))
                .apply(eq(HttpMethod.GET.name()), any(JSON.class));
    }

    @Test
    public void testGetByFieldNullFeignFunction() {
        BiFunction<String, Object, PageResult<Object>> nullFunction = null;

        assertThrows(NullPointerException.class, () -> {
            CrudUtils.getByField(nullFunction, "testField", "testName");
        });
    }

    @Test
    public void testGetListValidResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Arrays.asList(new Object(), new Object()), 2L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        List<Object> result = CrudUtils.getList(feignFunctionMock, "testObject");

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }

    @Test
    public void testGetListEmptyResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Collections.emptyList(), 0L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        List<Object> result = CrudUtils.getList(feignFunctionMock, "testObject");

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }

    @Test
    public void testGetListNullFeignFunction() {
        BiFunction<String, Object, PageResult<Object>> nullFunction = null;

        assertThrows(NullPointerException.class, () -> {
            CrudUtils.getList(nullFunction, "testObject");
        });
    }

    @Test
    public void testGetOneValidValue() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Arrays.asList(new Object()), 1L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any(JSONObject.class))).thenReturn(pageResult);

        JSONObject queryDTO = new JSONObject();
        queryDTO.put("testField", "testName");
        Object result = CrudUtils.getOne(feignFunctionMock, queryDTO);

        assertNotNull(result);
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any(JSONObject.class));
    }
    @Test
    public void testGetOneNullValue() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Collections.emptyList(), 0L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        JSONObject queryDTO = new JSONObject();
        queryDTO.put("testField", "testName");
        Object result = CrudUtils.getOne(feignFunctionMock, queryDTO);

        assertNull(result);
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }



    @Test
    public void testGetListByFieldValidResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Arrays.asList(new Object(), new Object()), 2L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        List<Object> result = CrudUtils.getListByField(feignFunctionMock, "testField", "testName");

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }

    @Test
    public void testGetListByFieldEmptyResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Collections.emptyList(), 0L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        List<Object> result = CrudUtils.getListByField(feignFunctionMock, "testField", "testName");

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }

    @Test
    public void testGetListByFieldNullFeignFunction() {
        BiFunction<String, Object, PageResult<Object>> nullFunction = null;

        assertThrows(NullPointerException.class, () -> {
            CrudUtils.getListByField(nullFunction, "testField", "testName");
        });
    }

    @Test
    public void testGetByIdsLongValidResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Arrays.asList(new Object(), new Object()), 2L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        List<Object> result = CrudUtils.getByIdsLong(feignFunctionMock, Arrays.asList(1L, 2L));

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }

    @Test
    public void testGetByIdsLongEmptyResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Collections.emptyList(), 0L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        List<Object> result = CrudUtils.getByIdsLong(feignFunctionMock, Arrays.asList(1L, 2L));

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }

    @Test
    public void testGetByIdsLongNullFeignFunction() {
        BiFunction<String, Object, PageResult<Object>> nullFunction = null;

        assertThrows(NullPointerException.class, () -> {
            CrudUtils.getByIdsLong(nullFunction, Arrays.asList(1L, 2L));
        });
    }

    @Test
    public void testGetByIdsValidResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Arrays.asList(new Object(), new Object()), 2L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        List<Object> result = CrudUtils.getByIds(feignFunctionMock, Arrays.asList("1", "2"));

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }

    @Test
    public void testGetByIdsEmptyResult() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        PageResult<Object> pageResult = new PageResult<>(Collections.emptyList(), 0L);
        when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

        List<Object> result = CrudUtils.getByIds(feignFunctionMock, Arrays.asList("1", "2"));

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
    }

    @Test
    public void testGetByIdsNullFeignFunction() {
        BiFunction<String, Object, PageResult<Object>> nullFunction = null;

        assertThrows(NullPointerException.class, () -> {
            CrudUtils.getByIds(nullFunction, Arrays.asList("1", "2"));
        });
    }
     @Test
     public void testGetByIdValidResult() {
         BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
         PageResult<Object> pageResult = new PageResult<>(Arrays.asList(new Object()), 1L);
         when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

         Object result = CrudUtils.getById(feignFunctionMock, "1");

         assertNotNull(result);
         verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
     }

     @Test
     public void testGetByIdNullResult() {
         BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
         PageResult<Object> pageResult = new PageResult<>(Collections.emptyList(), 0L);
         when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

         Object result = CrudUtils.getById(feignFunctionMock, "1");

         assertNull(result);
         verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any(JSON.class));
     }

     @Test
     public void testGetByIdNullFunction() {
        BiFunction<String, Object, PageResult<Object>> nullFunction = null;

        assertThrows(NullPointerException.class, () -> {
            CrudUtils.getById(nullFunction, "1");
        });
     }

     @Test
     public void testGetByIdLongNull() {
         BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
         Object result = CrudUtils.getById(feignFunctionMock, (Long) null);

         assertNull(result);
     }
    @Test
    public void testGetByIdEmptyId() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        Object result = CrudUtils.getById(feignFunctionMock, "");
        assertNull(result);
    }

    @Test
    public void testGetOneParseException() {
        BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);
        Object nonSerializableObject = new Object();

        Object result = CrudUtils.getOne(feignFunctionMock, nonSerializableObject);

        assertNull(result, "Expected result to be null given non-serializable input object.");
    }
     @Test
     public void testGetByIdLongValidResult() {
         BiFunction<String, Object, PageResult<Object>> feignFunctionMock = mock(BiFunction.class);

         PageResult<Object> pageResult = new PageResult<>(Arrays.asList(new Object()), 1L);
         when(feignFunctionMock.apply(eq(HttpMethod.GET.name()), any())).thenReturn(pageResult);

         Object result = CrudUtils.getById(feignFunctionMock, 1L);

         assertNotNull(result);
         verify(feignFunctionMock, times(1)).apply(eq(HttpMethod.GET.name()), any());
     }
 }

