package com.ykl.med.his.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/15
 */
@Data
public class HisFastSendOrderVO {

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "就诊ID")
    private Long visitId;

    @Schema(description = "医嘱列表")
    private List<FastSendOrderResp> orderList;

    @Data
    public static class FastSendOrderResp {

        @Schema(description = "医嘱记录ID 后续医嘱关联的唯一ID")
        private Long orderId;

        @Schema(description = "医嘱项目ID")
        private Long orditemId;

        @Schema(description = "医嘱项目名称")
        private String orditemName;

        @Schema(description = "处方ID")
        private Long recipeId;

        @Schema(description = "处方号")
        private String recipeNo;

        @Schema(description = "医嘱金额")
        private BigDecimal amount;
    }

}
