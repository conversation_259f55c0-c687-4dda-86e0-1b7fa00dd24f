package com.ykl.med.his.service;


import com.ykl.med.framework.common.pojo.CommonResult;
import com.ykl.med.his.vo.*;

import java.util.List;

/**
 *  上传监管平台
 * <AUTHOR>
 * @since 2024/11/5
 */
public interface HisService {


    /**
     * 历史就诊记录
     */
    CommonResult<List<HisVisitHistoryVO>> visitHistory(QueryVisitHistoryReq param);


    /**
     * 接诊
     */
    CommonResult<HisQuickVisitVO> quickVisit(QuickVisitReq param);


    /**
     * 提交问诊记录
     */
    CommonResult<String> saveInquiryRecord(SaveInquiryRecordReq param);


    /**
     * 提交医嘱
     */
    CommonResult<HisFastSendOrderVO> fastSendOrder(FastSendOrderReq param);


    /**
     * 撤销医嘱
     */
    CommonResult<String> cancelSend(CancelSendReq param);


    /**
     * 变更医嘱状态并通知药房
     */
    CommonResult<String> paySuccess(PaySuccessReq param);



}
