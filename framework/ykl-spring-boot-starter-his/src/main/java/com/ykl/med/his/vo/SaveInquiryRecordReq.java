package com.ykl.med.his.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/15
 */
@Data
public class SaveInquiryRecordReq {

    @Schema(description = "就诊ID")
    private Long visitId;

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "就诊号")
    private String visitNo;

    @Schema(description = "患者姓名")
    private String name;

    @Schema(description = "患者性别")
    private String gender;

    @Schema(description = "科室ID")
    private Long deptId;

    @Schema(description = "诊断医师ID")
    private Long doctorId;

    @Schema(description = "病史")
    private String bs;

    @Schema(description = "主诉")
    private String zs;

    @Schema(description = "既往史")
    private String jws;

    @Schema(description = "过敏史")
    private String gms;

    @Schema(description = "临床诊断")
    private String lczd;

    @Schema(description = "处理意见")
    private String clyj;

    @Schema(description = "诊断列表")
    private List<HisDiagnoseVO> diagnoseList;

    @Data
    public static class HisDiagnoseVO {
        @Schema(description = "中西医类别 1：西医诊断、2：中医诊断")
        private String diagnosMode;

        @Schema(description = "诊断序号")
        private Integer serialNo;

        @Schema(description = "是否主诊断 1：是、0：否")
        private Boolean isMainDiagnos;

        @Schema(description = "ICD码")
        private String icdCode;

        @Schema(description = "诊断名称")
        private String diseaseName;

        @Schema(description = "诊断状态 1：疑诊、2：确诊")
        private String diagnosStatus;

        @Schema(description = "中医证候 中医诊断时必填")
        private String chineseSyndrome;

        @Schema(description = "中医证候编码 中医诊断时必填")
        private String chineseSyndromeCode;

        @Schema(description = "诊断时间")
        private String diagnosTime;

    }


}
