package com.ykl.med.his.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/15
 */
@Data
public class FastSendOrderReq {

    @Schema(description = "就诊ID")
    private Long visitId;

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "处方笺pdf预览地址")
    private String pdfUrl;

    @Schema(description = "处方号")
    private String recipeNo;

    @Schema(description = "医嘱列表,多个医嘱一起提交会合并成一个处方")
    private List<HisOrderItemVO> orditemList;

    @Data
    public static class HisOrderItemVO {

        @Schema(description = "医嘱项目ID")
        private Long orditemId;

        @Schema(description = "药品ID 药品医嘱时必填")
        private Long goodsId;

        @Schema(description = "用法ID")
        private Long usageId;

        @Schema(description = "频次ID")
        private Long frequencyId;

        @Schema(description = "单次剂量 有频次时必填")
        private BigDecimal everyDose;

        @Schema(description = "单次剂量单位 有单次剂量时必填")
        private String everyDoseUnit;

        @Schema(description = "疗程（天）")
        private Integer daysTreat;

        @Schema(description = "总剂量")
        private BigDecimal totalDose;

        @Schema(description = "参考单价")
        private BigDecimal price;

        @Schema(description = "滴速")
        private String dropSpeed;

        @Schema(description = "是否超量")
        private Boolean isOverstep;

        @Schema(description = "超量说明 超量时必填")
        private String overstepExplain;

        @Schema(description = "用药目的")
        private String purposeDrug;

        @Schema(description = "医师嘱托")
        private String doctorEntrust;

        @Schema(description = "是否加急")
        private Boolean isUrgent;

        @Schema(description = "开单科室ID")
        private Long createDeptId;

        @Schema(description = "执行科室ID 可能为科室、病区、药房")
        private Long execDeptId;

        @Schema(description = "发药数量")
        private Integer dispensingQty;

        @Schema(description = "发药方式（1：药房发药、4：自备药(比如京东大药房,美团之类的)）")
        private String dispensingMode;

        @Schema(description = "是否院外医嘱")
        private Boolean isOutHosp;

        @Schema(description = "就诊ID")
        private Long visitId;

        @Schema(description = "医嘱名称")
        private String orditemName;

        @Schema(description = "医嘱开始时间")
        private String createDate;

        @Schema(description = "给药时机")
        private String gysj;

        @Schema(description = "患者ID")
        private Long patientId;

        @Schema(description = "医嘱数量")
        private BigDecimal qty;
    }

}
