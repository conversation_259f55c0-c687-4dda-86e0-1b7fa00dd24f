package com.ykl.med.dingding.configuration;

import com.ykl.med.dingding.enable.EnableDingDing;
import com.ykl.med.dingding.properties.DingDingProperties;
import com.ykl.med.dingding.sender.DingRobot;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@EnableConfigurationProperties(value = DingDingProperties.class)
@Configuration
@ConditionalOnBean(annotation = EnableDingDing.class)
public class DingDingConfiguration {
    @Value("${spring.application.name:defaultServer}")
    private String serverName;

    @Bean
    public DingRobot buildPushSender(DingDingProperties dingDingProperties) {
        return new DingRobot(dingDingProperties.getWebhook(), dingDingProperties.getSecret(), serverName);
    }
}