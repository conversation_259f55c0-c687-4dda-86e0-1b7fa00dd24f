package com.ykl.med.rocketmq;

import com.alibaba.fastjson.JSON;
import com.ykl.med.framework.common.pojo.BaseMqMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

/**
 * Rocket MQ 发送消息相关接口 实现
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
@Slf4j
public class RocketMQServiceImpl implements RocketMQService {

    private final RocketMQTemplate rocketMqTemplate;

    public RocketMQServiceImpl(RocketMQTemplate rocketMqTemplate) {
        this.rocketMqTemplate = rocketMqTemplate;
    }

    @Override
    public void send(String destination, BaseMqMessage data) {
        if (rocketMqTemplate == null) {
            log.error("RocketMQTemplate 未初始化");
            throw new IllegalStateException("RocketMQTemplate 未正确注入");
        }

        if (StringUtils.isBlank(destination) || data == null) {
            log.warn("发送消息失败，参数为空: destination={}, data={}", destination, JSON.toJSONString(data));
            throw new IllegalArgumentException("destination 和 data 不能为空");
        }

        try {
            // 同步发送
            SendResult sendResult = rocketMqTemplate.syncSend(destination, data);
            // 打印发送结果
            log.info("RocketMQ 消息发送结果: destination={}, status={}, msgId={}, messageId={}",
                    destination, sendResult.getSendStatus(), sendResult.getMsgId(), data.getMessageId());
        } catch (Exception e) {
            log.error("RocketMQ 消息发送失败，destination: {}, data: {}", destination, JSON.toJSONString(data), e);
            throw new RuntimeException("RocketMQ 消息发送失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void sendDelayed(String destination, BaseMqMessage data, int delayLevel) {
        if (rocketMqTemplate == null) {
            log.error("RocketMQTemplate 未初始化");
            throw new IllegalStateException("RocketMQTemplate 未正确注入");
        }

        if (destination == null || data == null) {
            log.warn("发送延迟消息失败，参数为空: destination={}, data={}", destination, JSON.toJSONString(data));
            throw new IllegalArgumentException("destination 和 data 不能为空");
        }

        try {
            // 构建消息并设置延迟等级
            Message<?> message = MessageBuilder.withPayload(data).build();
            // 同步发送并传递延迟等级
            SendResult sendResult = rocketMqTemplate.syncSend(destination, message, 5000, delayLevel);
            // 打印发送结果
            log.info("RocketMQ 延迟消息发送结果: destination={}, status={}, msgId={}, messageId={}, delayLevel={}",
                    destination, sendResult.getSendStatus(), sendResult.getMsgId(), data.getMessageId(), delayLevel);
        } catch (Exception e) {
            log.error("RocketMQ 延迟消息发送失败，destination: {}, data: {}, delayLevel: {}", destination, JSON.toJSONString(data), delayLevel, e);
            throw new RuntimeException("RocketMQ 延迟消息发送失败：" + e.getMessage(), e);
        }
    }

}
