package com.ykl.med.rocketmq;


import com.ykl.med.framework.common.pojo.BaseMqMessage;

/**
 * Rocket MQ 发送消息相关接口
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface RocketMQService {

    /**
     * 发送消息
     *
     * @param destination 消息发送目标
     * @param data        消息数据
     */
    void send(String destination, BaseMqMessage data);

    /**
     * 发送延迟消息
     *
     * @param destination 消息发送目标
     * @param data        消息数据
     * @param delayLevel  消息延迟级别
     */
    void sendDelayed(String destination, BaseMqMessage data, int delayLevel);

}
