package com.ykl.med.ca.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/17
 */
@Data
public class ApplySignAddVO {

    /*** 合同编号（合同发起接口中生成的APL开头的编号） */
    private String applyNo;

    /*** 合同html源码,utf8编码 */
    private String htmlContent;

    /*** 处理方式:为空或0时默认为5; 2只保全; 5部份自动签; 6HASH只保全; 17收集信息批量签 */
    private Integer dealType = 5;

    /*** 是否归档:0不归档,1归档;归档后无法追加盖章 */
    private Integer isArchive = 1;

    /*** 签约方名称,不超过50个字符 */
    private String fullName;

    /*** 身份类型:1身份证,2护照,3台胞证,4港澳居民来往内地通行证,11营业执照,12统一社会信用代码 */
    private Integer identityType = 1;

    /*** 个人传身份证号，企业传营业执照号/统一社会信用代码号 */
    private String identityCard;

    /*** 手机号码（个人必传），11个字符 */
    private String mobile;

    /*** 签字位置-表单域名ID */
    private String chapteName;

}
