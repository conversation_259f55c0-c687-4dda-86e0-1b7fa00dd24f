package com.ykl.med.mix.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.constants.topic.NutritionalTopic;
import com.ykl.med.framework.common.constants.topic.PatientTopic;
import com.ykl.med.framework.common.pojo.IdMessage;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.message.NutritionalAiRecipeMessage;
import com.ykl.med.mix.mq.rocket.PatientUpdateMedicalHistoryListener;
import com.ykl.med.patient.api.PatientEsFeign;
import com.ykl.med.patient.vo.patient.PatientSearchVO;
import com.ykl.med.patient.vo.patient.QueryPatientEsVO;
import com.ykl.med.rocketmq.RocketMQService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragely;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RocketMQTest {


    @Resource
    private RocketMQService rocketMQService;
    @Resource
    private PatientEsFeign patientEsFeign;
    @Resource
    private PatientUpdateMedicalHistoryListener patientUpdateMedicalHistoryListener;

    @Test
    public void test1() {
        NutritionalAiRecipeMessage adminActionMessage = new NutritionalAiRecipeMessage();
        adminActionMessage.setMessageId(UUID.randomUUID().toString());
        adminActionMessage.setCode(200);
        adminActionMessage.setMsg("成功");
        adminActionMessage.setBizId(76346190338912665L);
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject breakfast = new JSONObject();
        breakfast.put("breakfast", Collections.singletonList(new NameAndValue("菜品breakfast", "菜品详情breakfast")));
        JSONObject lunch = new JSONObject();
        lunch.put("lunch", Collections.singletonList(new NameAndValue("菜品lunch", "菜品详情lunch")));
        JSONObject dinner = new JSONObject();
        dinner.put("dinner", Collections.singletonList(new NameAndValue("菜品dinner", "菜品详情dinner")));
        JSONObject breakfastSnack = new JSONObject();
        breakfastSnack.put("breakfastSnack", Collections.singletonList(new NameAndValue("菜品breakfastSnack", "菜品详情breakfastSnack")));
        JSONObject lunchSnack = new JSONObject();
        lunchSnack.put("lunchSnack", Collections.singletonList(new NameAndValue("菜品lunchSnack", "菜品详情lunchSnack")));
        JSONObject dinnerSnack = new JSONObject();
        dinnerSnack.put("dinnerSnack", Collections.singletonList(new NameAndValue("菜品dinnerSnack", "菜品详情dinnerSnack")));
        jsonArray.add(breakfast);
        jsonArray.add(lunch);
        jsonArray.add(dinner);
        jsonArray.add(breakfastSnack);
        jsonArray.add(lunchSnack);
        jsonArray.add(dinnerSnack);
        jsonObject.put("report", "综述");
        jsonObject.put("reportReasoning", "综述的思考过程");
        jsonObject.put("recipeDetails", jsonArray);
        adminActionMessage.setData(jsonObject);
        rocketMQService.send(NutritionalTopic.AiRecipe.DESTINATION, adminActionMessage);
    }


    @Test
    public void test2() {
        IdMessage message = new IdMessage();
        message.setMessageId(UUID.randomUUID().toString());
        message.setId(100084L);
        rocketMQService.send(PatientTopic.UpdateMedicalHistory.DESTINATION, message);
    }


    @Test
    public void test3() {
        patientUpdateMedicalHistoryListener.onMessage(new IdMessage().setId(100087L));
    }

    @Test
    public void test4() {
        PageResult<PatientSearchVO> page = patientEsFeign.page(new QueryPatientEsVO());
        log.info(JSON.toJSONString(page.getList()));
    }


    @Test
    public void test5() throws Exception {
        AclClientRPCHook aclClientRPCHook = new AclClientRPCHook(new SessionCredentials("admin123", "23456789"));
        DefaultMQProducer producer = new DefaultMQProducer("product_group", aclClientRPCHook);
        producer.setNamesrvAddr("***************:9876");
        producer.start();
        try {
            Message ms = new Message("ProductTestTopic2","TagA","Hello RocketMQ".getBytes());
            SendResult send = producer.send(ms);
            log.info("MQ发送结果：{}", send);
        }catch (Exception e){
            log.error("MQ发送异常",e);
        }finally {
            producer.shutdown();
        }
    }

    @Test
    public void test6() throws Exception {
//        AclClientRPCHook aclClientRPCHook = new AclClientRPCHook(new SessionCredentials("admin123", "23456789"));
//        DefaultMQProducer producer = new DefaultMQProducer("product_group", aclClientRPCHook);
//        producer.setNamesrvAddr("***************:9876");
//        producer.start();
//        try {
//            Message ms = new Message("ProductTestTopic2","TagA","Hello RocketMQ".getBytes());
//            SendResult send = producer.send(ms);
//            log.info("MQ发送结果：{}", send);
//        }catch (Exception e){
//            log.error("MQ发送异常",e);
//        }finally {
//            producer.shutdown();
//        }

        // 1. 配置ACL认证信息
        AclClientRPCHook aclHook = new AclClientRPCHook(
                new SessionCredentials("admin123", "23456789")
        );
        // 2. 初始化带ACL的消费者
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(
                "consumer_group",
                aclHook,
                new AllocateMessageQueueAveragely()
        );
        // 3. 基础配置
        consumer.setNamesrvAddr("***************:9876");
        consumer.subscribe("ProductTestTopic", "TagA");
        // 4. 注册消息监听器
        consumer.registerMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(
                    List<MessageExt> msgs,
                    ConsumeConcurrentlyContext context
            ) {
                msgs.forEach(msg -> System.out.println(
                        "消费消息: " + new String(msg.getBody())
                ));
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });

        consumer.start();
        System.out.println("安全消费者已启动");
    }


    @Data
    static class NameAndValue {
        private String name;
        private String details;

        public NameAndValue(String name, String details) {
            this.name = name;
            this.details = details;
        }

    }

}
