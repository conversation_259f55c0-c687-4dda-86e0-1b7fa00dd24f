package com.ykl.med.mix.service.user;

import cn.hutool.core.util.PhoneUtil;
import com.ykl.med.doctors.api.HospitalFeign;
import com.ykl.med.doctors.api.MedicalTeamFeign;
import com.ykl.med.doctors.api.PersonnelUserFeign;
import com.ykl.med.doctors.api.SectionFeign;
import com.ykl.med.doctors.entity.dto.HospitalQueryDTO;
import com.ykl.med.doctors.entity.dto.PersonnelUserQueryDTO;
import com.ykl.med.doctors.entity.dto.SectionQueryDTO;
import com.ykl.med.doctors.entity.vo.HospitalVO;
import com.ykl.med.doctors.entity.vo.MedicalTeamSimpleRespVO;
import com.ykl.med.doctors.entity.vo.PersonnelDataVO;
import com.ykl.med.doctors.entity.vo.SectionVO;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.framework.common.util.date.DateUtils;
import com.ykl.med.framework.common.util.number.NumberUtils;
import com.ykl.med.mix.vo.UserInfoVO;
import com.ykl.med.push.api.SmsFeign;
import com.ykl.med.push.constans.PushErrorCodeConstants;
import com.ykl.med.push.enums.SmsType;
import com.ykl.med.push.vo.CheckCaptchaReqVO;
import com.ykl.med.push.vo.CheckCodeRespVO;
import com.ykl.med.push.vo.SmsCodeCheckReqVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.constants.UserErrorCode;
import com.ykl.med.user.enums.Gender;
import com.ykl.med.user.vo.req.LoginOrRegisterReqVO;
import com.ykl.med.user.vo.req.RefreshTokenReqVO;
import com.ykl.med.user.vo.req.SimpleUserPageReqVO;
import com.ykl.med.user.vo.req.UserPasswordUpdateReqVO;
import com.ykl.med.user.vo.resp.LoginOrRegisterRespVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MixUserService {

    @Resource
    private UserFeign userFeign;
    @Resource
    private SmsFeign smsFeign;
    @Resource
    private PersonnelUserFeign personnelUserFeign;
    @Resource
    private MedicalTeamFeign medicalTeamFeign;
    @Resource
    private HospitalFeign hospitalFeign;
    @Resource
    private SectionFeign sectionFeign;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    private static final String ERROR_COUNT_KEY_PREFIX = "login:error_count:";
    private static final String LOCK_KEY_PREFIX = "login:lock:";
    private static final int MAX_ERROR_COUNT_3 = 3;
    private static final int MAX_ERROR_COUNT_6 = 6;
    private static final int MAX_ERROR_COUNT_10 = 10;
    private static final int LOCK_TIME_5_MINUTES = 5;
    private static final int LOCK_TIME_30_MINUTES = 30;
    private static final int LOCK_TIME_1_DAY = 24 * 60;


    public UserInfoVO info(IdReqVO idReqVO) {
        if (idReqVO.getId() == null) {
            return null;
        }

        UserSimpleVO userSimpleVO = userFeign.getByUserId(idReqVO.getId());
        if (userSimpleVO == null) {
            return null;
        }

        UserInfoVO userInfoVO = CopyPropertiesUtil.copy(userSimpleVO, UserInfoVO::new);

        List<UserInfoVO> userSimpleVOList = new ArrayList<>();
        userSimpleVOList.add(userInfoVO);
        this.fillUserVOInfo(userSimpleVOList, null);

        return userSimpleVOList.get(0);
    }

    public PageResult<UserInfoVO> page(SimpleUserPageReqVO reqVO) {
        /**
         * 如果有医疗人员信息字段查询: 先获取相关数用户ID
         * */
        List<Long> ids = null;
        // 返回的医疗人员数据
        List<PersonnelDataVO> personnelDataVOList = null;
        if (StringUtils.isNotBlank(reqVO.getMedicalTeamId())
                || StringUtils.isNotBlank(reqVO.getOutpatientDoctor())
        ) {
            PersonnelUserQueryDTO personnelUserQueryDTO = new PersonnelUserQueryDTO();
            personnelUserQueryDTO.setMedicalTeamId(reqVO.getMedicalTeamId());
            personnelUserQueryDTO.setOutpatientDoctor(reqVO.getOutpatientDoctor());
            personnelUserQueryDTO.setPageSize(9999);
            PageResult<PersonnelDataVO> personnelDataVOPageResult = personnelUserFeign.queryUserInfosByMedicalTeamIds(personnelUserQueryDTO);

            // 返回空数据
            if (personnelDataVOPageResult.getList() == null || personnelDataVOPageResult.getList().size() == 0) {
                PageResult<UserInfoVO> userSimpleVOPageResult = new PageResult<>();
                userSimpleVOPageResult.setTotal(0L);
                userSimpleVOPageResult.setList(new ArrayList<>());
                return userSimpleVOPageResult;
            }

            ids = personnelDataVOPageResult.getList().stream().map(obj -> Long.parseLong(obj.getUserId()))
                    .distinct().collect(Collectors.toList());
            personnelDataVOList = personnelDataVOPageResult.getList();
        }
        //取两个id集合的交集
        if (CollectionUtils.isNotEmpty(reqVO.getIds())) {
            if (CollectionUtils.isNotEmpty(ids)) {
                reqVO.getIds().retainAll(ids);
                if (CollectionUtils.isEmpty(reqVO.getIds())) {
                    return PageResult.empty();
                }
            }
        } else {
            reqVO.setIds(ids);
        }
        // 查询账号信息
        PageResult<UserSimpleVO> simpleVOPageResult = userFeign.page(reqVO);
        if (simpleVOPageResult == null || simpleVOPageResult.getList() == null || simpleVOPageResult.getList().size() == 0) {
            PageResult<UserInfoVO> userSimpleVOPageResult = new PageResult<>();
            userSimpleVOPageResult.setTotal(0L);
            userSimpleVOPageResult.setList(new ArrayList<>());
            return userSimpleVOPageResult;
        }

        // 拷贝对象
        PageResult<UserInfoVO> userInfoVOPageResult = new PageResult<>();
        userInfoVOPageResult.setTotal(simpleVOPageResult.getTotal());
        userInfoVOPageResult.setList(CopyPropertiesUtil.copyAndConvertList(simpleVOPageResult.getList(), UserInfoVO::new, null));

        this.fillUserVOInfo(userInfoVOPageResult.getList(), personnelDataVOList);

        return userInfoVOPageResult;
    }

    public LoginOrRegisterRespVO loginOrRegister(LoginOrRegisterReqVO reqVO) {
        // 登录用户key
        String loginName = reqVO.getUsername() != null ? reqVO.getUsername() + reqVO.getUserType() + reqVO.getClientId() : reqVO.getMobile() + reqVO.getUserType() + reqVO.getClientId();

        // 检查用户是否被锁定
        isUserLocked(loginName);

        //人机校验
        if (StringUtils.isNotEmpty(reqVO.getSceneId()) && StringUtils.isNotEmpty(reqVO.getCaptchaVerifyParam())) {
            AssertUtils.isTrue(smsFeign.checkCaptcha(new CheckCaptchaReqVO().setSceneId(reqVO.getSceneId()).setCaptchaVerifyParam(reqVO.getCaptchaVerifyParam())), PushErrorCodeConstants.CAPTCHA_ERROR);
        }

        // 是验证码登录: 判断验证码
        if (StringUtils.isNotEmpty(reqVO.getMobile()) && StringUtils.isNotEmpty(reqVO.getCode())) {
            smsCheck(new SmsCodeCheckReqVO()
                    .setPhone(reqVO.getMobile())
                    .setCode(reqVO.getCode())
                    .setSmsCodeType(SmsType.LOGIN));
        }

        try {
            LoginOrRegisterRespVO loginOrRegisterRespVO = userFeign.loginOrRegister(reqVO);

            // 密码正确，清除错误次数
            clearErrorCount(loginName);

            return loginOrRegisterRespVO;
        } catch (Exception e) {
            if (e.getMessage().contains("密码错误")) {
                incrementError(loginName, e.getMessage());
            }
            throw new ServiceException(UserErrorCode.USERNAME_OR_PASSWORD_ERROR.getCode(), e.getMessage());
        }

    }


    private void incrementError(String loginName, String message) {
        // 密码错误，增加错误次数
        int errorCount = incrementErrorCount(loginName);
        if (errorCount >= MAX_ERROR_COUNT_10) {
            lockUser(loginName, DateTimeUtils.dateTimeDifferenceInMinutes(), MAX_ERROR_COUNT_10);
            throw new ServiceException(UserErrorCode.PASSWORD_ERROR_MAX.getCode(), String.format(UserErrorCode.PASSWORD_ERROR_MAX.getMsg(), MAX_ERROR_COUNT_10));
        } else if (errorCount == MAX_ERROR_COUNT_6) {
            // 超过 6 次，锁定 30 分钟
            lockUser(loginName, LOCK_TIME_30_MINUTES, MAX_ERROR_COUNT_6);
            throw new ServiceException(UserErrorCode.PASSWORD_ERROR_COUNT.getCode(), String.format(UserErrorCode.PASSWORD_ERROR_COUNT.getMsg(), MAX_ERROR_COUNT_6, LOCK_TIME_30_MINUTES));
        } else if (errorCount == MAX_ERROR_COUNT_3) {
            // 超过 3 次，锁定 5 分钟
            lockUser(loginName, LOCK_TIME_5_MINUTES, MAX_ERROR_COUNT_3);
            throw new ServiceException(UserErrorCode.PASSWORD_ERROR_COUNT.getCode(), String.format(UserErrorCode.PASSWORD_ERROR_COUNT.getMsg(), MAX_ERROR_COUNT_3, LOCK_TIME_5_MINUTES));
        }
        throw new ServiceException(UserErrorCode.USERNAME_OR_PASSWORD_ERROR.getCode(), message);
    }

    private int incrementErrorCount(String username) {
        String key = ERROR_COUNT_KEY_PREFIX + username;
        Long count = stringRedisTemplate.opsForValue().increment(key);
        if (count == null) {
            return 0;
        }

        stringRedisTemplate.expire(key, DateTimeUtils.dateTimeDifferenceInMinutes(), TimeUnit.MINUTES);
        return count.intValue();
    }

    private void isUserLocked(String username) {
        // 判断是否锁定
        String lockKey = LOCK_KEY_PREFIX + username;
        String lockValue = stringRedisTemplate.opsForValue().get(lockKey);
        if (lockValue == null) {
            return;
        }

        Long expire = stringRedisTemplate.getExpire(lockKey, TimeUnit.MINUTES);
        int errorCount = Integer.parseInt(lockValue);
        if (errorCount >= MAX_ERROR_COUNT_10) {
            throw new ServiceException(UserErrorCode.PASSWORD_ERROR_MAX.getCode(), String.format(UserErrorCode.PASSWORD_ERROR_MAX.getMsg(), MAX_ERROR_COUNT_10));
        } else if (errorCount >= MAX_ERROR_COUNT_6) {
            throw new ServiceException(UserErrorCode.PASSWORD_ERROR_COUNT.getCode(), String.format(UserErrorCode.PASSWORD_ERROR_COUNT.getMsg(), MAX_ERROR_COUNT_6, expire));
        } else if (errorCount >= MAX_ERROR_COUNT_3) {
            throw new ServiceException(UserErrorCode.PASSWORD_ERROR_COUNT.getCode(), String.format(UserErrorCode.PASSWORD_ERROR_COUNT.getMsg(), MAX_ERROR_COUNT_3, expire));
        }
    }

    private void clearErrorCount(String username) {
        String errorCountKey = ERROR_COUNT_KEY_PREFIX + username;
        String lockKey = LOCK_KEY_PREFIX + username;
        stringRedisTemplate.delete(errorCountKey);
        stringRedisTemplate.delete(lockKey);
    }

    private void lockUser(String username, long minutes, int maxErrorCount) {
        String key = LOCK_KEY_PREFIX + username;

        String s = stringRedisTemplate.opsForValue().get(key);
        if (s == null || Integer.parseInt(s) < maxErrorCount) {
            stringRedisTemplate.opsForValue().set(key, maxErrorCount + "");
            stringRedisTemplate.expire(key, minutes, TimeUnit.MINUTES);
        }
    }


    public LoginOrRegisterRespVO refreshToken(RefreshTokenReqVO reqVO) {
        return userFeign.refreshToken(reqVO);
    }

    public void ignorePassword(UserPasswordUpdateReqVO reqVO) {
        smsCheck(new SmsCodeCheckReqVO()
                .setPhone(reqVO.getMobile())
                .setCode(reqVO.getCode())
                .setSmsCodeType(SmsType.CHANGE_PASSWORD));
        userFeign.ignorePassword(reqVO);
    }

    public void updatePassword(UserPasswordUpdateReqVO reqVO) {
        smsCheck(new SmsCodeCheckReqVO()
                .setPhone(reqVO.getMobile())
                .setCode(reqVO.getCode())
                .setSmsCodeType(SmsType.CHANGE_PASSWORD));
        userFeign.updatePassword(reqVO);
    }

    /**
     * 判断短信验证码
     */
    public void smsCheck(SmsCodeCheckReqVO smsCodeType) {
        if (!PhoneUtil.isMobile(smsCodeType.getPhone())) {
            throw new ServiceException(UserErrorCode.INVALID_MOBILE_NUMBER);
        }

        CheckCodeRespVO checkCodeRespVO = smsFeign.checkCode(smsCodeType);
        if (checkCodeRespVO == null || !Objects.equals(Boolean.TRUE, checkCodeRespVO.getResult())) {
            throw new ServiceException(UserErrorCode.CHECK_CODE_ERROR);
        }
    }

    /**
     * 填充用户的医疗人员信息
     */
    public void fillUserVOInfo(List<UserInfoVO> userSimpleVOList, List<PersonnelDataVO> personnelDataVOList) {

        if (userSimpleVOList == null || userSimpleVOList.size() == 0) {
            return;
        }

        // 获取账号ID列表
        List<String> userIds = userSimpleVOList.stream().map(obj -> String.valueOf(obj.getId())).
                filter(Objects::nonNull).distinct().collect(Collectors.toList());

        /**
         * 医疗人员数据列表为空: 获取信息
         * */
        // 医疗人员账号ID map
        Map<String, PersonnelDataVO> personnelDataVOMap = new HashMap<>();
        // 医疗组ID列表
        List<Long> medicalIds = new ArrayList<>();
        // 医院ID列表
        List<String> hospitalIds = new ArrayList<>();
        // 科室ID列表
        List<String> sectionIds = new ArrayList<>();

        // 医疗人员信息列表为空: 获取数据信息
        if (personnelDataVOList == null) {
            PersonnelUserQueryDTO personnelUserQueryDTO = new PersonnelUserQueryDTO();
            personnelUserQueryDTO.setUserId(String.join(",", userIds));
            PageResult<PersonnelDataVO> personnelDataVOPageResult = personnelUserFeign.query(personnelUserQueryDTO);
            personnelDataVOList = personnelDataVOPageResult.getList();
        }
        if (personnelDataVOList != null && personnelDataVOList.size() != 0) {
            for (PersonnelDataVO personnelDataVO : personnelDataVOList) {
                if (StringUtils.isNotBlank(personnelDataVO.getUserId())) {
                    personnelDataVOMap.put(personnelDataVO.getUserId(), personnelDataVO);
                }
                // 医疗人员的医疗组ID列表
                if (StringUtils.isNotBlank(personnelDataVO.getMedicalTeamId())) {
                    medicalIds.add(Long.parseLong(personnelDataVO.getMedicalTeamId()));
                }
                // 医院ID列表
                if (StringUtils.isNotBlank(personnelDataVO.getHospitalId())) {
                    hospitalIds.add(personnelDataVO.getHospitalId());
                }
                // 科室ID列表
                if (StringUtils.isNotBlank(personnelDataVO.getSectionId())) {
                    sectionIds.add(personnelDataVO.getSectionId());
                }
            }
        }

        /**
         * 获取医疗组信息
         * */
        // 医疗组ID map
        Map<String, MedicalTeamSimpleRespVO> medicalTeamSimpleRespVOMap = new HashMap<>();
        List<MedicalTeamSimpleRespVO> medicalTeamSimpleRespVOList = medicalTeamFeign.listByMedicalTeamIds(medicalIds);
        if (medicalTeamSimpleRespVOList != null && medicalTeamSimpleRespVOList.size() != 0) {
            medicalTeamSimpleRespVOMap = medicalTeamSimpleRespVOList.stream().collect(Collectors.toMap(
                    MedicalTeamSimpleRespVO::getId, Function.identity(), (key1, key2) -> key2));
        }

        /**
         * 获取医院信息
         * */
        // 医院ID map
        Map<String, HospitalVO> hospitalVOMap = new HashMap<>();
        HospitalQueryDTO hospitalQueryDTO = new HospitalQueryDTO();
        hospitalQueryDTO.setId(String.join(",", hospitalIds));
        PageResult<HospitalVO> hospitalVOPageResult = hospitalFeign.query(hospitalQueryDTO);
        if (hospitalVOPageResult.getList() != null && hospitalVOPageResult.getList().size() != 0) {
            hospitalVOMap = hospitalVOPageResult.getList().stream().collect(Collectors.toMap(
                    HospitalVO::getId, Function.identity(), (key1, key2) -> key2));
        }

        /**
         * 获取科室信息
         * */
        // 科室ID map
        Map<String, SectionVO> sectionVOMap = new HashMap<>();
        SectionQueryDTO sectionQueryDTO = new SectionQueryDTO();
        sectionQueryDTO.setId(String.join(",", sectionIds));
        PageResult<SectionVO> sectionVOPageResult = sectionFeign.query(sectionQueryDTO);
        if (sectionVOPageResult.getList() != null && sectionVOPageResult.getList().size() != 0) {
            sectionVOMap = sectionVOPageResult.getList().stream().collect(Collectors.toMap(
                    SectionVO::getId, Function.identity(), (key1, key2) -> key2));
        }

        // 赋值
        for (UserInfoVO userSimpleVO : userSimpleVOList) {
            String userId = String.valueOf(userSimpleVO.getId());
            if (personnelDataVOMap.containsKey(userId)) {
                // 医疗人员信息
                PersonnelDataVO personnelDataVO = personnelDataVOMap.get(userId);

                // 赋值医疗组信息
                if (StringUtils.isNotBlank(personnelDataVO.getMedicalTeamId())
                        && medicalTeamSimpleRespVOMap.containsKey(personnelDataVO.getMedicalTeamId())) {
                    personnelDataVO.setMedicalTeamName(medicalTeamSimpleRespVOMap.get(personnelDataVO.getMedicalTeamId()).getName());
                }
                // 赋值医院信息
                if (StringUtils.isNotBlank(personnelDataVO.getHospitalId())
                        && hospitalVOMap.containsKey(personnelDataVO.getHospitalId())) {
                    personnelDataVO.setHospitalName(hospitalVOMap.get(personnelDataVO.getHospitalId()).getName());
                }
                // 赋值科室信息
                if (StringUtils.isNotBlank(personnelDataVO.getSectionId())
                        && sectionVOMap.containsKey(personnelDataVO.getSectionId())) {
                    personnelDataVO.setSectionName(sectionVOMap.get(personnelDataVO.getSectionId()).getName());
                }

                // 医疗人员ID与类型
                userSimpleVO.setPersonnelId(personnelDataVO.getId());
                userSimpleVO.setPersonnelType(personnelDataVO.getPersonnelType());

                // 拼接字段
                if (StringUtils.isNotBlank(personnelDataVO.getRemark())) {
                    userSimpleVO.setRemark(userSimpleVO.getRemark() + ";" + personnelDataVO.getRemark());
                }

                // 其他属性信息
                if (Gender.FEMALE.name().equals(personnelDataVO.getGender())) {
                    userSimpleVO.setGender(Gender.FEMALE);
                } else if (Gender.MALE.name().equals(personnelDataVO.getGender())) {
                    userSimpleVO.setGender(Gender.MALE);
                }
                userSimpleVO.setHospitalId(personnelDataVO.getHospitalId());
                userSimpleVO.setHospitalName(personnelDataVO.getHospitalName());
                userSimpleVO.setDepartmentId(personnelDataVO.getSectionId());
                userSimpleVO.setDepartmentName(personnelDataVO.getSectionName());
                userSimpleVO.setMedicalTeamId(NumberUtils.parseLong(personnelDataVO.getMedicalTeamId()));
                userSimpleVO.setMedicalTeamName(personnelDataVO.getMedicalTeamName());
                // 生日年龄
                LocalDateTime localDateTime = DateUtils.convertToLocalDateTime(personnelDataVO.getBirthday(), true);
                if (localDateTime != null) {
                    userSimpleVO.setBirthday(localDateTime.toLocalDate());
                    userSimpleVO.setAge(LocalDateTime.now().getYear() - localDateTime.getYear());
                }
                userSimpleVO.setGoodAt(personnelDataVO.getGoodAt());
                userSimpleVO.setTitle(personnelDataVO.getTitle());
                userSimpleVO.setIntroduction(personnelDataVO.getIntroduction());
                userSimpleVO.setMedicalCertificateNumber(personnelDataVO.getMedicalCertificateNumber());
                userSimpleVO.setPracticeInformation(personnelDataVO.getPracticeInfo());
                if (StringUtils.isNotBlank(personnelDataVO.getOutpatientDoctorFlag()) && personnelDataVO.getOutpatientDoctorFlag().equals("1")) {
                    userSimpleVO.setOutpatientDoctor(true);
                } else {
                    userSimpleVO.setOutpatientDoctor(false);
                }
            }
        }
    }


}
