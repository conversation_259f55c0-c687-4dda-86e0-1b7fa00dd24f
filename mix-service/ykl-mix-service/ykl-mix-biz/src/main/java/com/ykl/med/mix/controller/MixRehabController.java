package com.ykl.med.mix.controller;

import com.ykl.med.mix.api.MixRehabFeign;
import com.ykl.med.mix.service.rehab.MixRehabService;
import com.ykl.med.mix.vo.MixFirstPageRehabMergeVO;
import com.ykl.med.rehab.vo.req.RehabPlanItemExecSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.req.func.BatchAddBreathingTrainingRecords;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2025/3/18
 */
@RestController
@RequestMapping("/rehab")
public class MixRehabController implements MixRehabFeign {

    @Resource
    private MixRehabService mixRehabService;


    @Override
    @PostMapping("/first/page/merge")
    public MixFirstPageRehabMergeVO firstPageRehabMerge(@RequestParam(name = "patientId") Long patientId) {
        return mixRehabService.firstPageRehabMerge(patientId);
    }

    @Override
    @PostMapping("/plan/itemExecute/execute")
    public void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        mixRehabService.execute(reqVO);
    }


    @Override
    @PostMapping("/funcRehab/breathingTrainingRecords/batchAdd")
    public void batchAdd(@RequestBody BatchAddBreathingTrainingRecords reqVO) {
        mixRehabService.batchAdd(reqVO);
    }

}
