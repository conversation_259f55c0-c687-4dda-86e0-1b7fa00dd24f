package com.ykl.med.mix.mq.rocket;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.constants.topic.ConsultTopic;
import com.ykl.med.framework.common.constants.topic.FollowupTopic;
import com.ykl.med.framework.common.constants.topic.NutritionalTopic;
import com.ykl.med.framework.common.pojo.IdMessage;
import com.ykl.med.mix.service.consult.MixConsultService;
import com.ykl.med.mix.service.follow.MixFollowupService;
import com.ykl.med.mix.vo.consult.MixConsultVO;
import com.ykl.med.rocketmq.RocketMQService;
import com.ykl.med.shift.enums.consult.ConsultTypeEnum;
import com.ykl.med.shift.vo.dto.ConsultVideoVO;
import com.ykl.med.shift.vo.req.SaveVideoReqVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = ConsultTopic.TOPIC,
        consumerGroup = ConsultTopic.TOPIC + ConsultTopic.CloseVideo.SELECTOR_EXPRESSION,
        selectorExpression = ConsultTopic.CloseVideo.SELECTOR_EXPRESSION
)
public class ConsultCloseVideoListener implements RocketMQListener<ConsultVideoVO> {

    private final MixConsultService mixConsultService;
    private final RocketMQService rocketMQService;


    @Override
    public void onMessage(ConsultVideoVO message) {
        log.info("【关闭视频】 监听：request:{}", JSONObject.toJSONString(message));
        Boolean check = mixConsultService.checkMessageId(message.getMessageId());
        if (!check) {
            log.info("【关闭视频】监听-处理失败：messageId 验证失败 或 重复消费!");
            return;
        }
        if (message.getConsultId() == null) {
            log.info("【关闭视频】监听-处理失败：consultId is null");
            return;
        }

        if (!message.getSaveVideoFlag()) {
            log.info("【关闭视频】监听-处理失败：不用添加视频");
            return;
        }

        MixConsultVO details = mixConsultService.details(message.getConsultId());
        if (details == null) {
            log.info("【关闭视频】监听-处理失败：问诊信息不存在");
            return;
        }

        // 保存视频 & 语音
        mixConsultService.saveVideo(new SaveVideoReqVO()
                .setConsultId(message.getConsultId())
                .setVideoUrl(message.getVideoUrl())
                .setRoomName(message.getVideoName()));

        // 生成随访小结
        if (details.getConsultType().equals(ConsultTypeEnum.FOLLOWUP_CONSULTATION) && details.getBizId() != null) {
            IdMessage idMessage = new IdMessage();
            idMessage.setMessageId(UUID.randomUUID().toString());
            idMessage.setId(details.getBizId());
            rocketMQService.send(FollowupTopic.AiFollowupSummary.DESTINATION, idMessage);
        }
    }
}
