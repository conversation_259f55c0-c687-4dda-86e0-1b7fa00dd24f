package com.ykl.med.mix.service.rehab;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.enums.ConsultChatTypeEnum;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.vo.form.PatientFormAddVO;
import com.ykl.med.masterdata.vo.form.PatientFormDeleteReqVO;
import com.ykl.med.masterdata.vo.form.PatientFormQueryVO;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.mix.api.consult.MixConsulFeign;
import com.ykl.med.mix.service.MixPatientFormService;
import com.ykl.med.mix.service.event.strategy.rehab.PsychoRehabPlanChangeStrategy;
import com.ykl.med.mix.vo.rehab.psycho.MixPsychoRehabConsultVO;
import com.ykl.med.mix.vo.rehab.psycho.MixPsychoRehabPlanDetailRespVO;
import com.ykl.med.mix.vo.rehab.psycho.MixPsychoRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.constans.MessageConstants;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.records.api.BasicReportFeign;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.rehab.api.PsychoRehabFeign;
import com.ykl.med.rehab.api.PsychologyFormFeign;
import com.ykl.med.rehab.constants.RehabErrorCode;
import com.ykl.med.rehab.vo.req.PatientIdAndPlanIdVO;
import com.ykl.med.rehab.vo.req.psycho.PsychoRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychologyFormResultVO;
import com.ykl.med.shift.api.ConsultFeign;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.vo.req.CreateByDirectlyReqVO;
import com.ykl.med.shift.vo.req.UpdateConsultVO;
import com.ykl.med.shift.vo.resp.ConsultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MixPsychoRehabService {
    @Resource
    private PsychoRehabFeign psychoRehabFeign;

    @Resource
    private EventTaskFeign eventTaskFeign;

    @Resource
    private PatientFormFeign patientFormFeign;

    @Resource
    private ConsultFeign consultFeign;

    @Resource
    private MixConsulFeign mixConsulFeign;

    @Resource
    private PsychologyFormFeign psychologyFormFeign;

    @Resource
    private PatientFeign patientFeign;

    @Resource
    private BasicReportFeign basicReportFeign;

    @Resource
    private PsychoRehabPlanChangeStrategy psychoRehabPlanChangeStrategy;

    @Resource
    private MixPatientFormService mixPatientFormService;
    /**
     * 首诊记录表id
     */
    @Value("${form.firstConsultFormId:11}")
    private Long firstConsultFormId;

    /**
     * 咨询记录表
     */
    @Value("${form.consultFormId:12}")
    private Long consultFormId;

    /**
     * 总结表
     */
    @Value("${form.summaryFormId:13}")
    private Long summaryFormId;

    /**
     * 会谈作业表
     */
    @Value("${form.assignmentFormId:14}")
    private Long assignmentFormId;


    public Long saveOrUpdate(MixPsychoRehabPlanSaveOrUpdateReqVO reqVO) {
        log.info("Saving or updating psycho rehab plan, reqVO: {}", JSON.toJSONString(reqVO));
        MixPsychoRehabPlanDetailRespVO oldPlan = this.getPlanById(new PatientIdAndPlanIdVO().setPlanId(reqVO.getId()).setPatientId(reqVO.getPatientId()));
        Long id = psychoRehabFeign.saveOrUpdate(reqVO);
        savePlanAfter(reqVO, id, oldPlan);
        return id;
    }


    public void addBasePlan(Long patientId) {
        log.info("Adding base plan, patientId: {}", patientId);
        PsychoRehabPlanSaveOrUpdateReqVO reqVO = psychoRehabFeign.getBasePlanReq(patientId);
        if (reqVO == null) {
            return;
        }
        Long id = psychoRehabFeign.saveOrUpdate(reqVO);
        MixPsychoRehabPlanSaveOrUpdateReqVO webReqVO = CopyPropertiesUtil.normalCopyProperties(reqVO, MixPsychoRehabPlanSaveOrUpdateReqVO.class);
        savePlanAfter(webReqVO, id, null);
    }

    public void addFirstConsult(CreateByDirectlyReqVO param) {
        log.info("Adding first consult, param: {}", JSON.toJSONString(param));
        ConsultVO consultVO = consultFeign.createByDirectly(param);
        addConsultForm(consultVO, firstConsultFormId);
        addConsultForm(consultVO, assignmentFormId);
    }

    public void updateConsult(UpdateConsultVO updateConsultVO, Long operateUserId) {
        log.info("Updating consult, updateConsultVO: {}, operateUserId: {}", JSON.toJSONString(updateConsultVO), operateUserId);
        mixConsulFeign.updateConsult(updateConsultVO);
        ConsultVO consultVO = consultFeign.details(updateConsultVO.getConsultId());
        if (updateConsultVO.getConsultState() == ConsultStateEnum.CANCEL) {
            //基础方案
            this.addBasePlanEvent(consultVO.getPatientId());
            //这种不需要填表
            PatientFormDeleteReqVO reqVO = new PatientFormDeleteReqVO()
                    .setType(PatientFormBizType.CONSULT)
                    .setBizId(consultVO.getId());
            patientFormFeign.delete(reqVO);
        } else {
            //改时间了
            psychoRehabPlanChangeStrategy.onlyChangeConsult(consultVO.getBizId(), consultVO.getPatientId(), operateUserId);
        }
    }


    /**
     * 填完量表后 校验并发送基础方案
     *
     * @param patientFormVO 填完的表单
     */
    public void checkAndSendBasePlan(PatientFormVO patientFormVO) {
        Long dtFormId = psychologyFormFeign.getDtFormId();
        if (dtFormId.equals(patientFormVO.getFormId())) {
            PatientBaseVO patient = patientFeign.getPatientBaseById(patientFormVO.getPatientId());
            if (patient.getMemberStatus() == CommonStatusEnum.ENABLE) {
                Long planId = psychoRehabFeign.getCurrentOrNextPlanId(patientFormVO.getPatientId());
                List<Long> secondFromIds = psychologyFormFeign.getSecondFromIds(patientFormVO);
                if (CollectionUtils.isNotEmpty(secondFromIds)) {
                    for (Long secondFromId : secondFromIds) {
                        PatientFormAddVO patientFormAddVO = new PatientFormAddVO();
                        patientFormAddVO.setFormId(secondFromId);
                        patientFormAddVO.setPatientId(patientFormVO.getPatientId());
                        patientFormAddVO.setBizId(planId);
                        patientFormAddVO.setType(PatientFormBizType.PSYCHOLOGY);
                        PatientFormVO patientFormNew = patientFormFeign.add(patientFormAddVO);
                        mixPatientFormService.send(patientFormNew.getId(), MessageConstants.SYSTEM_USER);
                    }
                } else {
                    this.addBasePlanEvent(patientFormVO.getPatientId());
                }
            } else {
                this.addBasePlanEvent(patientFormVO.getPatientId());
            }
        } else {
            BasicReportVO basicReportVO = basicReportFeign.getByPatientId(patientFormVO.getPatientId());
            String stage = basicReportVO == null ? "" : basicReportVO.getStage();
            PsychologyFormResultVO formResult = psychologyFormFeign.getFormResult(patientFormVO.getPatientId(), stage);
            Set<String> itemNames = new HashSet<>(Lists.newArrayList("家庭支持", "正念呼吸训练", "正念扫描训练", "正念冥想训练"));
            List<String> filteredItemNames = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(formResult.getItemNames())) {
                filteredItemNames = formResult.getItemNames().stream()
                        .filter(item -> !itemNames.contains(item))
                        .collect(Collectors.toList());
            }
            //判断 推荐的项目是不是只有基础方案
            if (formResult.getFlowStatus() == PsychologyFormResultVO.PsychologyFlowStatus.COMPLETED
                    && CollectionUtils.isEmpty(formResult.getAttachmentNames())
                    && CollectionUtils.isEmpty(filteredItemNames)) {
                this.addBasePlanEvent(patientFormVO.getPatientId());
            }
        }
    }

    public PatientFormVO getLastConsultForm(PatientIdAndPlanIdVO reqVO) {
        List<ConsultVO> consultVOS = consultFeign.listBizId(reqVO.getPlanId());
        List<Long> consultIds = consultVOS.stream().map(ConsultVO::getId).collect(Collectors.toList());
        List<PatientFormVO> patientForms = patientFormFeign.query(new PatientFormQueryVO()
                .setBizIds(consultIds)
                .setType(PatientFormBizType.CONSULT));
        //找到上一个已经填写的咨询记录表
        return patientForms.stream()
                .filter(PatientFormVO::getWriteStatus)
                .filter(e -> consultFormId.equals(
                        e.getFormId()
                ))
                .max(Comparator.comparing(PatientFormVO::getWriteTime))
                .orElse(null);
    }

    public MixPsychoRehabPlanDetailRespVO getCurrentPlan(Long patientId) {
        PsychoRehabPlanDetailRespVO resp = psychoRehabFeign.getCurrentPlan(patientId);
        if (resp == null) {
            //虽然没方案，但也要去把他的问诊 和 表单查出来
            Long nextPlanId = psychoRehabFeign.getCurrentOrNextPlanId(patientId);
            resp = new PsychoRehabPlanDetailRespVO();
            resp.setId(nextPlanId);
            resp.setPatientForms(patientFormFeign.query(new PatientFormQueryVO().setPatientId(patientId).setType(PatientFormBizType.PSYCHOLOGY).setBizId(nextPlanId)));
        }
        return buildMixDetail(resp);
    }

    public MixPsychoRehabPlanDetailRespVO getPlanById(PatientIdAndPlanIdVO reqVO) {
        return buildMixDetail(psychoRehabFeign.getPlanById(reqVO));
    }


    private void savePlanAfter(MixPsychoRehabPlanSaveOrUpdateReqVO reqVO, Long id, MixPsychoRehabPlanDetailRespVO oldPlan) {
        String errorMsg = "";
        if (CollectionUtils.isNotEmpty(reqVO.getAddConsultations())) {
            try {
                List<ConsultVO> consultVOS = consultFeign.listBizId(id);
                //取请求的时间最大的时间
                for (CreateByDirectlyReqVO directlyReqVO : reqVO.getAddConsultations()) {
                    directlyReqVO.setBizId(id);
                    directlyReqVO.setPatientId(reqVO.getPatientId());
                    directlyReqVO.setDoctorId(reqVO.getCurrentUserId());
                    directlyReqVO.setChatType(ConsultChatTypeEnum.VIDEO_CHAT);
                    directlyReqVO.setConsultState(directlyReqVO.getConsultState() == null ? ConsultStateEnum.WAIT : directlyReqVO.getConsultState());
                    ConsultVO consultVO = consultFeign.createByDirectly(directlyReqVO);
                    if (CollectionUtils.isEmpty(consultVOS)) {
                        //首诊记录表
                        addConsultForm(consultVO, firstConsultFormId);
                        addConsultForm(consultVO, assignmentFormId);
                    } else if (consultVO.getVisitStartTime().isAfter(reqVO.getEndTime().plusDays(-14))) {
                        //问诊时间在 康复计划结束前14天内, 添加总结表单
                        addConsultForm(consultVO, summaryFormId);
                    } else {
                        //其他时间发咨询记录表
                        addConsultForm(consultVO, consultFormId);
                        addConsultForm(consultVO, assignmentFormId);
                    }
                }
            } catch (Exception e) {
                errorMsg = errorMsg + "预约问诊出现异常：" + e.getMessage();
            }
        }
        if (CollectionUtils.isNotEmpty(reqVO.getAddFromIds())) {
            try {
                for (Long addFromId : reqVO.getAddFromIds()) {
                    //手动加的量表，可以随便加，shaCode乱写
                    patientFormFeign.add(new PatientFormAddVO()
                            .setPatientId(reqVO.getPatientId())
                            .setBizId(id)
                            .setType(PatientFormBizType.PSYCHOLOGY)
                            .setFormId(addFromId)
                            .setShaCode(DigestUtil.sha256Hex(UUID.randomUUID().toString())));
                }
            } catch (Exception e) {
                errorMsg = errorMsg + "添加心理康复表单出现异常：" + e.getMessage();
            }
        }

        // 添加事件任务
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString())
                .setEventTime(LocalDateTime.now())
                .setExecuteTime(LocalDateTime.now())
                .setBizId(id.toString())
                .setBizType(EventTaskType.PSYCHO_REHAB_PLAN_CHANGE);
        JSONObject extJson = new JSONObject();
        extJson.put("oldPlan", oldPlan);
        eventTaskAddVO.setExtJson(extJson);
        eventTaskAddVO.setUserId(reqVO.getCurrentUserId());
        eventTaskFeign.addEventTask(eventTaskAddVO);

        if (StringUtils.isNotBlank(errorMsg)) {
            throw new ServiceException(RehabErrorCode.PLAN_SAVE_PARTIAL_FAILED.getCode(), errorMsg);
        }
    }


    private void addConsultForm(ConsultVO consultVO, Long formId) {
        patientFormFeign.add(new PatientFormAddVO()
                .setBizId(consultVO.getId())
                .setType(PatientFormBizType.CONSULT)
                .setFormId(formId)
                .setShaCode(DigestUtil.sha256Hex(String.format("%s_%s_%s", PatientFormBizType.CONSULT, consultVO.getId(), formId))));
    }


    private MixPsychoRehabPlanDetailRespVO buildMixDetail(PsychoRehabPlanDetailRespVO resp) {
        if (resp == null) {
            return null;
        }
        MixPsychoRehabPlanDetailRespVO webResp = CopyPropertiesUtil.normalCopyProperties(resp, MixPsychoRehabPlanDetailRespVO.class);
        List<ConsultVO> consults = consultFeign.listBizId(resp.getId());
        if (CollectionUtils.isNotEmpty(consults)) {
            List<MixPsychoRehabConsultVO> webConsults = new ArrayList<>();
            List<Long> consultIds = consults.stream().map(ConsultVO::getId).collect(Collectors.toList());
            List<PatientFormVO> patientForms = patientFormFeign.query(new PatientFormQueryVO()
                    .setBizIds(consultIds)
                    .setType(PatientFormBizType.CONSULT));
            Map<Long, List<PatientFormVO>> patientFormMap = patientForms.stream().collect(Collectors.groupingBy(PatientFormVO::getBizId));
            for (ConsultVO consult : consults) {
                MixPsychoRehabConsultVO webConsult = CopyPropertiesUtil.normalCopyProperties(consult, MixPsychoRehabConsultVO.class);
                webConsult.setConsultForms(patientFormMap.get(consult.getId()));
                webConsults.add(webConsult);
            }
            webResp.setConsults(webConsults);
        }
        //评估表单和consults倒序展示 判空
        if (CollectionUtils.isNotEmpty(webResp.getPatientForms())) {
            //PatientFormVO::getWriteTime 有可能是空的
            webResp.getPatientForms().sort(Comparator.comparing(PatientFormVO::getCreateTime).reversed());
        }
        if (CollectionUtils.isNotEmpty(webResp.getConsults())) {
            webResp.getConsults().sort(Comparator.comparing(ConsultVO::getVisitStartTime).reversed());
        }
        return webResp;
    }

    private void addBasePlanEvent(Long patientId) {
        log.info("Adding base plan Event, patientId: {}", patientId);
        PsychoRehabPlanSaveOrUpdateReqVO reqVO = psychoRehabFeign.getBasePlanReq(patientId);
        if (reqVO == null) {
            return;
        }
        // 添加事件任务
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(patientId)
                .setRequestId(UUID.randomUUID().toString())
                .setExecuteTime(LocalDateTime.now().plusMinutes(5))
                .setBizId(patientId.toString())
                .setBizType(EventTaskType.PSYCHO_REHAB_BASE_PLAN);
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }
}
