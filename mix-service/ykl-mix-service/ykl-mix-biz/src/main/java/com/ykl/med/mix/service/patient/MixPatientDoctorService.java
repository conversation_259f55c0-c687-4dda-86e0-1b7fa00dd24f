package com.ykl.med.mix.service.patient;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.doctors.api.DoctorFeign;
import com.ykl.med.doctors.entity.vo.DoctorListVO;
import com.ykl.med.doctors.entity.vo.DoctorVO;
import com.ykl.med.doctors.entity.vo.QueryDoctorReqVO;
import com.ykl.med.doctors.enums.PatientDoctorRoleEnum;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.mix.vo.patient.MixPatientBasicVO;
import com.ykl.med.mix.vo.patient.PatientDoctorBindReqVO;
import com.ykl.med.mix.vo.patient.QueryDoctorByPatientMedicalTeamReqVO;
import com.ykl.med.patient.api.MemberVersionFeign;
import com.ykl.med.patient.api.PatientDoctorFeign;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
public class MixPatientDoctorService {
    @Resource
    private PatientDoctorFeign patientDoctorFeign;
    @Resource
    private PatientFeign patientFeign;
    @Resource
    private EventTaskFeign eventTaskFeign;
    @Resource
    private MemberVersionFeign memberVersionFeign;
    @Resource
    private DoctorFeign doctorFeign;
    @Resource
    private MixPatientService mixPatientService;
    @Resource
    private CommonConfigFeign commonConfigFeign;
    private static final String YKL_DEFAULT_DOCTOR = "ykl_default_doctor";


    public void patientDoctorBind(PatientDoctorBindReqVO reqVO) {
        memberVersionFeign.changePatientMemberVersionBind(reqVO.getPatientId(), reqVO.getMedicalTeamId());
        if (reqVO.getDoctorId() == null) {
            return;
        }
        patientDoctorFeign.patientDoctorBind(reqVO.getDoctorId(), reqVO.getPatientId());

        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString())
                .setBizId(reqVO.getPatientId().toString())
                .setBizType(EventTaskType.PATIENT_DOCTOR_BIND_CHANGE);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bindType", "bind");
        eventTaskAddVO.setExtJson(jsonObject);
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    public List<DoctorListVO> getDoctorsInPatientMedicalGroup(QueryDoctorByPatientMedicalTeamReqVO reqVO) {
        PatientVO patientVO = patientFeign.getPatientById(reqVO.getPatientId());
        if (patientVO == null || patientVO.getMedicalTeamId() == null) {
            return new ArrayList<>();
        }

        List<DoctorListVO> list = doctorFeign.list(new QueryDoctorReqVO().setMedicalTeamIds(Collections.singletonList(patientVO.getMedicalTeamId())));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return CopyPropertiesUtil.normalCopyProperties(list, DoctorListVO.class);
    }

    public List<DoctorListVO> getPatientMedicalTeam(QueryDoctorByPatientMedicalTeamReqVO reqVO) {
        MixPatientBasicVO patientVO = mixPatientService.getPatientBasicById(reqVO.getPatientId());
        if (patientVO.getPatientBaseVO() == null) {
            return new ArrayList<>();
        }
        // 非会员 或 没有绑定医生 默认展示固定的几个医生信息
        if (patientVO.getPatientBaseVO().getMemberStatus().equals(CommonStatusEnum.DISABLE)
                || patientVO.getPatientBaseVO().getBindDoctorId() == null) {
            return defaultDoctorList();
        }

        List<DoctorListVO> result = new ArrayList<>();
        List<Long> doctorIds = new ArrayList<>();
        // 病史的主治医生
        if (patientVO.getBasicReportVO() != null && patientVO.getBasicReportVO().getAttendingDoctorId() != null) {
            DoctorVO attendingDoctor = doctorFeign.getById(patientVO.getBasicReportVO().getAttendingDoctorId());
            if (attendingDoctor != null) {
                result.add(CopyPropertiesUtil.copyAndConvert(attendingDoctor, DoctorListVO::new, (source, target) -> target.setPatientDoctorRoleEnum(PatientDoctorRoleEnum.TEAM_MEMBER)));
                doctorIds.add(patientVO.getBasicReportVO().getAttendingDoctorId());
            }
        }
        // 绑定医生
        if (patientVO.getPatientBaseVO().getBindDoctorId() != null && !doctorIds.contains(patientVO.getPatientBaseVO().getBindDoctorId())) {
            DoctorVO bindDoctor = doctorFeign.getById(patientVO.getPatientBaseVO().getBindDoctorId());
            if (bindDoctor != null) {
                result.add(CopyPropertiesUtil.copyAndConvert(bindDoctor, DoctorListVO::new, (source, target) -> target.setPatientDoctorRoleEnum(PatientDoctorRoleEnum.TEAM_MEMBER)));
                doctorIds.add(patientVO.getPatientBaseVO().getBindDoctorId());
            }
        }
        // 团队成员
        List<DoctorListVO> list = doctorFeign.list(new QueryDoctorReqVO().setMedicalTeamIds(Collections.singletonList(patientVO.getPatientBaseVO().getMedicalTeamId())).setTeamFlag(true));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                e.setPatientDoctorRoleEnum(PatientDoctorRoleEnum.TEAM_MEMBER);
                if (!doctorIds.contains(e.getId())) {
                    result.add(e);
                }
            });
        }
        return result;
    }

    private List<DoctorListVO> defaultDoctorList() {
        String configKey = commonConfigFeign.getCommonConfigValueByKey(YKL_DEFAULT_DOCTOR);
        if (StringUtils.isBlank(configKey)) {
            return new ArrayList<>();
        }
        List<Long> doctorIds = JSONObject.parseArray(configKey, Long.class);
        if (CollectionUtils.isEmpty(doctorIds)) {
            return new ArrayList<>();
        }

        // 团队成员
        List<DoctorListVO> list = doctorFeign.list(new QueryDoctorReqVO().setIds(doctorIds));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        list.forEach(e -> {
            e.setPatientDoctorRoleEnum(PatientDoctorRoleEnum.TEAM_MEMBER);
        });
        return list;
    }
}
