package com.ykl.med.mix.service.event.strategy;

import com.ykl.med.framework.common.constants.JumpConstants;
import com.ykl.med.framework.common.constants.topic.PatientTopic;
import com.ykl.med.framework.common.constants.topic.RehabMangeTopic;
import com.ykl.med.framework.common.pojo.IdMessage;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.vo.dict.DictDataRespVO;
import com.ykl.med.medical.api.MedicalOrderFeign;
import com.ykl.med.medical.vo.order.MedicalOrderDetailVO;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientUserDetailVO;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.push.enums.EventTaskOperationType;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.enums.SystemMessageType;
import com.ykl.med.push.vo.event.EventTaskVO;
import com.ykl.med.push.vo.message.BizMessageBaseVO;
import com.ykl.med.push.vo.message.MessageSendPatientReqVO;
import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import com.ykl.med.rocketmq.RocketMQService;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

/**
 * 患者信息变更事件策略
 **/
@Service
@Slf4j
@EventTaskTypeAnnotation({
        EventTaskType.PATIENT_RECORDS_CHANGE,
        EventTaskType.PATIENT_INFO_CHANGE,
        EventTaskType.PATIENT_DRUG_CHANGE,
        EventTaskType.PATIENT_METRIC_CHANGE,
        EventTaskType.PATIENT_SYMPTOM_CHANGE,
        EventTaskType.PATIENT_ADVERSE_REACTION_CHANGE})
public class PatientInfoChangeTaskStrategy extends AbstractEventTaskStrategy {
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private DictDataFeign dictDataFeign;
    @Resource
    private RocketMQTemplate rocketMqTemplate;
    @Resource
    private RocketMQService rocketMQService;
    @Resource
    private UserFeign userFeign;
    @Resource
    private PushSocketFeign pushSocketFeign;
    @Resource
    private MedicalOrderFeign medicalOrderFeign;
    @Resource
    private MessageFeign messageFeign;

    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {
        if (eventTaskVO.getUserId() != null) {
            UserSimpleVO user = userFeign.getByUserId(eventTaskVO.getUserId());
            if (user.getUserType() == UserType.PATIENT) {
                this.sendSystemMessage(eventTaskVO);
            }
        }
        if (eventTaskVO.getBizType() == EventTaskType.PATIENT_RECORDS_CHANGE || eventTaskVO.getBizType() == EventTaskType.PATIENT_INFO_CHANGE) {
            RehabManageMqMessageVO rehabManageMessage = new RehabManageMqMessageVO().setPatientId(eventTaskVO.getPatientId());
            rocketMqTemplate.syncSend(RehabMangeTopic.TOPIC, rehabManageMessage);
        }
        if (eventTaskVO.getBizType() == EventTaskType.PATIENT_DRUG_CHANGE) {
            pushSocketFeign.sendMedicalOrderChangeNotice(eventTaskVO.getPatientId());
            if (eventTaskVO.getOperation() == EventTaskOperationType.DELETE) {
                //本次是作废或者停止，发送消息
                this.sendDisableDrugMessage(eventTaskVO);
            }
        }

        // 更新ES用户信息
        IdMessage message = new IdMessage();
        message.setMessageId(UUID.randomUUID().toString());
        message.setId(eventTaskVO.getPatientId());
        rocketMQService.send(PatientTopic.UpdateMedicalHistory.DESTINATION, message);
    }

    private void sendDisableDrugMessage(EventTaskVO eventTaskVO) {
        UserSimpleVO userSimpleVO = userFeign.getByUserId(eventTaskVO.getUserId());
        if (userSimpleVO.getUserType() != UserType.PATIENT) {
            String doctorName = userSimpleVO.getName();
            if (StringUtils.isNotEmpty(userSimpleVO.getMedicalCategory())) {
                DictDataRespVO dictDataRespVO = dictDataFeign.getDictDataByValue(userSimpleVO.getMedicalCategory());
                if (Objects.nonNull(dictDataRespVO)) {
                    doctorName = doctorName + dictDataRespVO.getLabel();
                }
            }
            MedicalOrderDetailVO medicalOrderDetailVO = medicalOrderFeign.getById(Long.valueOf(eventTaskVO.getBizId()));
            MessageSendPatientReqVO messageSendPatientReqVO = new MessageSendPatientReqVO();
            messageSendPatientReqVO.setPatientId(eventTaskVO.getPatientId());
            messageSendPatientReqVO.setRequestId(UUID.randomUUID().toString());
            messageSendPatientReqVO.setType(MessageType.SYSTEM_MESSAGE);
            //xx医生已作废/停止您的xx药品，请打开用药管理查看，如有疑问请联系医生
            messageSendPatientReqVO.setMsg(doctorName + "已" + medicalOrderDetailVO.getStatus().getDesc() + "您的" + medicalOrderDetailVO.getItemName() + "药品，请打开用药管理查看，如有疑问请联系医生");
            messageSendPatientReqVO.setExtra(new BizMessageBaseVO().setYklJumpUrl(JumpConstants.MEDICAL_ORDER));
            messageSendPatientReqVO.setCurrentUserId(eventTaskVO.getUserId());
            messageFeign.sendMessagePatient(messageSendPatientReqVO);
        }
    }

    private void sendSystemMessage(EventTaskVO eventTaskVO) {
        PatientUserDetailVO patientVO = patientUserFeign.getPatientDetailByUserId(eventTaskVO.getUserId());
        if (patientVO == null || patientVO.getBindDoctorId() == null) {
            return;
        }
        String relation = dictDataFeign.getDictDataByValue(patientVO.getRelation()).getLabel();
        SystemMessageType type = SystemMessageType.valueOf(eventTaskVO.getBizType().name());
        String content = getSystemMessageContent(type)
                .replace("patientName", patientVO.getName())
                .replace("relation", relation)
                .replace("changeBiz", eventTaskVO.getExtJson().getString("changeBiz"))
                .replace("operation", eventTaskVO.getOperation() != null ? eventTaskVO.getOperation().getDesc() : "");
        createAndAddSystemMessage(patientVO.getBindDoctorId(), content, eventTaskVO.getId().toString(), patientVO.getPatientId(), type, eventTaskVO.getBizId());
    }
}
