package com.ykl.med.mix.service.event.strategy;

import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.enums.SystemMessageType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.push.vo.event.EventTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 待处理预警事件策略
 */
@Service
@Slf4j
@EventTaskTypeAnnotation({EventTaskType.PENDING_WARN})
public class PendingWarnTaskStrategy extends AbstractEventTaskStrategy {
    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {
        PatientVO patientVO = patientFeign.getPatientById(eventTaskVO.getPatientId());
        String content = getSystemMessageContent(SystemMessageType.PENDING_WARN)
                .replace("patientName", patientVO.getName())
                .replace("number1", "1");
        if (patientVO.getBindDoctorId() == null) {
            return;
        }
        Long messageId = createAndAddSystemMessage(patientVO.getBindDoctorId(), content,
                eventTaskVO.getId().toString(), eventTaskVO.getPatientId(), SystemMessageType.PENDING_WARN, eventTaskVO.getBizId());
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setRequestId(messageId.toString())
                .setBizId(eventTaskVO.getBizId())
                .setBizType(EventTaskType.SYSTEM_MESSAGE_OVERDUE)
                .setEventTime(LocalDateTime.now())
                .setExecuteTime(getMessageOverdueTime(SystemMessageType.PENDING_WARN.name()))
                .setPatientId(eventTaskVO.getPatientId())
                .setUserId(eventTaskVO.getUserId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }
}
