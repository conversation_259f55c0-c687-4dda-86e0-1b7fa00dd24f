package com.ykl.med.mix.service.personnel;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.ca.service.CaService;
import com.ykl.med.ca.vo.UploadPersonSignVO;
import com.ykl.med.doctors.api.HospitalFeign;
import com.ykl.med.doctors.api.MedicalTeamFeign;
import com.ykl.med.doctors.api.PersonnelUserFeign;
import com.ykl.med.doctors.api.SectionFeign;
import com.ykl.med.doctors.entity.dto.HospitalQueryDTO;
import com.ykl.med.doctors.entity.dto.PersonnelUserQueryDTO;
import com.ykl.med.doctors.entity.dto.SectionQueryDTO;
import com.ykl.med.doctors.entity.vo.*;
import com.ykl.med.doctors.enums.PersonnelType;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.CommonResult;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.mix.vo.OSSConfigVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.vo.UserAuthorityVO;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.req.SimpleUserPageReqVO;
import com.ykl.med.user.vo.req.UserBaseInfoUpdateReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MixPersonnelService {

    @Resource
    private CommonConfigFeign commonConfigFeign;

    @Resource
    private CaService caService;

    @Resource
    private UserFeign userFeign;

    @Resource
    private PersonnelUserFeign personnelUserFeign;

    @Resource
    private HospitalFeign hospitalFeign;

    @Resource
    private SectionFeign sectionFeign;

    @Resource
    private MedicalTeamFeign medicalTeamFeign;

    /**
     * 查询医疗人员及用户信息
     * */
    public PageResult<PersonnelDataVO> query(PersonnelUserQueryDTO requestBody){
        PageResult<PersonnelDataVO> personnelDataVOPageResult = personnelUserFeign.query(requestBody);
        this.fillPersonnelUserVOInfo(personnelDataVOPageResult);
        return personnelDataVOPageResult;
    }

    /**
     * 查询医疗人员及用户信息(单条)
     * */
    public PersonnelDataVO querySingle(PersonnelUserQueryDTO requestBody){
        PersonnelDataVO personnelDataVO = personnelUserFeign.querySingle(requestBody);
        if ( personnelDataVO == null ){
            return null;
        }

        PageResult<PersonnelDataVO> personnelDataVOPageResult = new PageResult<>();
        personnelDataVOPageResult.setTotal(1L);
        personnelDataVOPageResult.setList(new ArrayList<>());
        personnelDataVOPageResult.getList().add(personnelDataVO);

        this.fillPersonnelUserVOInfo(personnelDataVOPageResult);
        return personnelDataVOPageResult.getList().get(0);
    }

    /**
     * 根据医疗组，门诊标志查询医疗人员信息
     * */
    public PageResult<PersonnelDataVO> queryUserInfosByMedicalTeamIds(PersonnelUserQueryDTO requestBody) {
        PageResult<PersonnelDataVO> personnelDataVOPageResult = personnelUserFeign.query(requestBody);
        this.fillPersonnelUserVOInfo(personnelDataVOPageResult);
        return personnelDataVOPageResult;
    }

    /**
     * 填充医疗人员用户，医院，科室，医疗组等信息
     * */
    public void fillPersonnelUserVOInfo(PageResult<PersonnelDataVO> personnelDataVOPageResult){

        if ( personnelDataVOPageResult == null
                || personnelDataVOPageResult.getList() == null
                || personnelDataVOPageResult.getList().size() == 0 ){
            return;
        }

        // 获取用户ID列表
        List<Long> userIds = new ArrayList<>();
        // 获取科室ID列表
        List<String> sectionIds = new ArrayList<>();
        // 获取医疗组ID列表
        List<Long> medicalTeamIds = new ArrayList<>();
        // 获取医院ID列表
        List<String> hospitalIds = new ArrayList<>();

        for ( PersonnelDataVO personnelDataVO : personnelDataVOPageResult.getList() ){
            if ( StringUtils.isNotBlank(personnelDataVO.getUserId()) ) {
                userIds.add(Long.parseLong(personnelDataVO.getUserId()));
            }
            if ( StringUtils.isNotBlank(personnelDataVO.getSectionId()) ){
                sectionIds.add(personnelDataVO.getSectionId());
            }
            if ( StringUtils.isNotBlank(personnelDataVO.getMedicalTeamId()) ){
                medicalTeamIds.add(Long.parseLong(personnelDataVO.getMedicalTeamId()));
            }
            if ( StringUtils.isNotBlank(personnelDataVO.getHospitalId()) ) {
                hospitalIds.add(personnelDataVO.getHospitalId());
            }
        }

        /**
         * 获取用户信息
         * */
        Map<String, UserSimpleVO> userSimpleVOMap = new HashMap<>();
        if ( userIds.size() != 0 ) {
            IdListReqVO idListReqVO = new IdListReqVO();
            idListReqVO.setIdList(userIds);
            List<UserSimpleVO> userSimpleVOList = userFeign.listByUserIds(idListReqVO);
            userSimpleVOMap = userSimpleVOList.stream().collect(Collectors.toMap(
                    obj -> String.valueOf(obj.getId()), Function.identity(), (key1, key2) -> key2));
        }

        /**
         * 获取科室信息
         * */
        SectionQueryDTO sectionQueryDTO = new SectionQueryDTO();
        sectionQueryDTO.setId(String.join(",", sectionIds));
        PageResult<SectionVO> sectionVOPageResult = sectionFeign.query(sectionQueryDTO);
        // 转map
        Map<String, SectionVO> sectionVOMap = sectionVOPageResult.getList().stream().collect(Collectors.toMap(
                SectionVO::getId, Function.identity(),(key1,key2) -> key2));

        /**
         * 获取医院信息
         * */
        HospitalQueryDTO hospitalQueryDTO = new HospitalQueryDTO();
        hospitalQueryDTO.setId(String.join(",", hospitalIds));
        PageResult<HospitalVO> hospitalVOPageResult = hospitalFeign.query(hospitalQueryDTO);
        // 转map
        Map<String, HospitalVO> hospitalVOMap = hospitalVOPageResult.getList().stream().collect(Collectors.toMap(
                HospitalVO::getId, Function.identity(),(key1,key2) -> key2));

        /**
         * 获取医疗组信息
         * */
        List<MedicalTeamSimpleRespVO> medicalTeamSimpleRespVOList = medicalTeamFeign.listByMedicalTeamIds(medicalTeamIds);
        // 转map
        Map<String, MedicalTeamSimpleRespVO> medicalTeamSimpleRespVOMap = medicalTeamSimpleRespVOList.stream().collect(Collectors.toMap(
                MedicalTeamSimpleRespVO::getId, Function.identity(),(key1,key2) -> key2));

        // 循环赋值
        for ( PersonnelDataVO personnelDataVO : personnelDataVOPageResult.getList() ){
            // 赋值用户信息
            if ( StringUtils.isNotBlank(personnelDataVO.getUserId()) && userSimpleVOMap.containsKey(personnelDataVO.getUserId()) ){
                UserSimpleVO userSimpleVO = userSimpleVOMap.get(personnelDataVO.getUserId());
                personnelDataVO.setUserName(userSimpleVO.getUsername());
                personnelDataVO.setMedicalCategory(userSimpleVO.getMedicalCategory());

                /**
                 * 赋值用户信息
                 * */
                personnelDataVO.setName(userSimpleVO.getName());
                personnelDataVO.setMobile(userSimpleVO.getMobile());
                personnelDataVO.setEmail(userSimpleVO.getEmail());
                personnelDataVO.setAvatar(userSimpleVO.getAvatar());
                personnelDataVO.setMedicalCategory(userSimpleVO.getMedicalCategory());
            }

            // 赋值科室名称
            if ( StringUtils.isNotBlank(personnelDataVO.getSectionId()) && sectionVOMap.containsKey(personnelDataVO.getSectionId()) ){
                personnelDataVO.setSectionName(sectionVOMap.get(personnelDataVO.getSectionId()).getName());
            }

            // 赋值医疗组名称
            if ( StringUtils.isNotBlank(personnelDataVO.getMedicalTeamId()) && medicalTeamSimpleRespVOMap.containsKey(personnelDataVO.getMedicalTeamId()) ){
                personnelDataVO.setMedicalTeamName(medicalTeamSimpleRespVOMap.get(personnelDataVO.getMedicalTeamId()).getName());
            }

            // 赋值医院名称
            if ( StringUtils.isNotBlank(personnelDataVO.getHospitalId()) && hospitalVOMap.containsKey(personnelDataVO.getHospitalId()) ){
                personnelDataVO.setHospitalName(hospitalVOMap.get(personnelDataVO.getHospitalId()).getName());
            }
        }
    }

    /**
     * 上传签名图片
     * */
    public void uploadPhotoSign(String photoSignUrl, String idCard){
        if ( StringUtils.isBlank(photoSignUrl) ){
            return;
        }

        // 获取OSS配置
        String configJsonString = commonConfigFeign.getCommonConfigValueByKey("oss_config");
        // 转对象
        OSSConfigVO ossConfigVO = JSONObject.parseObject(configJsonString, OSSConfigVO.class);
        if ( ossConfigVO == null || StringUtils.isBlank(ossConfigVO.getHost()) ) {
            throw new ServiceException(100001,"获取OSS配置信息失败");
        }

        // 去掉最后的斜杆
        if ( ossConfigVO.getHost().endsWith("/") ){
            ossConfigVO.setHost(ossConfigVO.getHost().substring(0, ossConfigVO.getHost().length() - 1));
        }
        // 签名图片完整路径
        String photoSignFullUrl = ossConfigVO.getHost() + photoSignUrl;

        /**
         * 上传签名图
         * */
        UploadPersonSignVO uploadPersonSignVO = new UploadPersonSignVO();
        uploadPersonSignVO.setIdentityCard(idCard);
        uploadPersonSignVO.setSignImgFile(photoSignFullUrl);
        CommonResult<String> commonResult = caService.uploadPersonSign(uploadPersonSignVO);
        if (commonResult.getCode() != 0) {
            throw new ServiceException(100001, "上传签名图片错误:"+commonResult.getMsg());
        }
    }

    /**
     * 同步用户信息: 手机号, 邮箱, 头像地址
     * */
    public void syncUserInfos(UserBaseInfoUpdateReqVO userBaseInfoUpdateReqVO){
        if ( userBaseInfoUpdateReqVO.getId() == null  ){
            return;
        }

        if ( StringUtils.isBlank(userBaseInfoUpdateReqVO.getMobile())
                && StringUtils.isBlank(userBaseInfoUpdateReqVO.getEmail())
                && StringUtils.isBlank(userBaseInfoUpdateReqVO.getAvatar())
        ){
            return;
        }

        /**
         * 判断电话号码是否已经存在
         * */
        if ( StringUtils.isNotBlank(userBaseInfoUpdateReqVO.getMobile()) ){
            UserAuthorityVO userAuthorityVO = userFeign.getByMobileAndUserType(userBaseInfoUpdateReqVO.getMobile(), UserType.DOCTOR);
            // 用户ID与要更新的用户ID不一致: 说明已经存在, 返回错误
            if ( userAuthorityVO != null && !userAuthorityVO.getId().equals(userBaseInfoUpdateReqVO.getId()) ){
                throw new ServiceException(100001, "电话号码已经被使用");
            }
        }

        UserBaseInfoUpdateReqVO reqVO = new UserBaseInfoUpdateReqVO();
        reqVO.setId(userBaseInfoUpdateReqVO.getId());
        reqVO.setName(userBaseInfoUpdateReqVO.getName());
        reqVO.setMobile(userBaseInfoUpdateReqVO.getMobile());
        reqVO.setEmail(userBaseInfoUpdateReqVO.getEmail());
        reqVO.setAvatar(userBaseInfoUpdateReqVO.getAvatar());
        userFeign.update(reqVO);
    }

    /**
     * 根据用户字段查询
     * */
    public List<String> getPersonnelIdsByUserInfo(SimpleUserPageReqVO simpleUserPageReqVO, PersonnelType personnelType){
        // 返回列表
        List<String> personnelIds = new ArrayList<>();

        if ( simpleUserPageReqVO == null ){
            return personnelIds;
        }

        simpleUserPageReqVO.setUserType(UserType.DOCTOR);
        PageResult<UserSimpleVO> userSimpleVOPageResult = userFeign.page(simpleUserPageReqVO);
        if ( userSimpleVOPageResult.getList() != null && userSimpleVOPageResult.getList().size() != 0 ){
            /**
             * 查询用户对应的医生信息
             * */
            // 用户ID列表
            List<String> userIds = userSimpleVOPageResult.getList().stream().map(obj -> obj.getId().toString())
                    .distinct().collect(Collectors.toList());
            PersonnelUserQueryDTO personnelUserQueryDTO = new PersonnelUserQueryDTO();
            personnelUserQueryDTO.setUserIds(userIds);
            if ( personnelType != null ){
                personnelUserQueryDTO.setPersonnelType(personnelType.getValue());
            }
            PageResult<PersonnelUserVO> personnelUserVOPageResult = personnelUserFeign.queryRelationList(personnelUserQueryDTO);
            if ( personnelUserVOPageResult.getList() != null && personnelUserVOPageResult.getList().size() != 0 ){
                // 赋值医生ID列表
                personnelIds = personnelUserVOPageResult.getList().stream().map(PersonnelUserVO::getPersonnelId).
                        filter(Objects::nonNull).distinct().collect(Collectors.toList());
            }
        }

        return personnelIds;
    }
}
