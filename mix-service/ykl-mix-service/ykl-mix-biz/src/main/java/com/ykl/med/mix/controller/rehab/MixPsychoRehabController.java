package com.ykl.med.mix.controller.rehab;

import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.mix.api.rehab.MixPsychoRehabFeign;
import com.ykl.med.mix.service.rehab.MixPsychoRehabService;
import com.ykl.med.mix.vo.rehab.psycho.MixPsychoRehabPlanDetailRespVO;
import com.ykl.med.mix.vo.rehab.psycho.MixPsychoRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.req.PatientIdAndPlanIdVO;
import com.ykl.med.shift.vo.req.CreateByDirectlyReqVO;
import com.ykl.med.shift.vo.req.UpdateConsultVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/mixPsychoRehab")
public class MixPsychoRehabController implements MixPsychoRehabFeign {
    @Resource
    private MixPsychoRehabService mixPsychoRehabService;

    @PostMapping("/saveOrUpdate")
    @Override
    public Long saveOrUpdate(@RequestBody MixPsychoRehabPlanSaveOrUpdateReqVO reqVO) {
        return mixPsychoRehabService.saveOrUpdate(reqVO);
    }

    @PostMapping("/getLastConsultForm")
    @Override
    public PatientFormVO getLastConsultForm(@RequestBody PatientIdAndPlanIdVO reqVO) {
        return mixPsychoRehabService.getLastConsultForm(reqVO);
    }

    @PostMapping("/addFirstConsult")
    @Override
    public void addFirstConsult(@RequestBody CreateByDirectlyReqVO param) {
        mixPsychoRehabService.addFirstConsult(param);
    }


    @PostMapping("/updateConsult")
    @Override
    public void updateConsult(@RequestBody UpdateConsultVO updateConsultVO,
                              @RequestParam(name = "operateUserId") Long operateUserId) {
        mixPsychoRehabService.updateConsult(updateConsultVO,operateUserId);
    }

    @PostMapping("/plan/getPlanById")
    @Operation(summary = "获取方案详情")
    @Override
    public MixPsychoRehabPlanDetailRespVO getPlanById(@Valid @RequestBody PatientIdAndPlanIdVO reqVO) {
        return mixPsychoRehabService.getPlanById(reqVO);
    }

    @PostMapping("/getCurrentPlan")
    @Operation(summary = "获取当前康复计划")
    @Override
    public MixPsychoRehabPlanDetailRespVO getCurrentPlan(@RequestParam(name = "patientId") Long patientId) {
        return mixPsychoRehabService.getCurrentPlan(patientId);
    }
}
