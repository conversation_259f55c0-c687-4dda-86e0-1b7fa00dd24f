package com.ykl.med.mix.controller.patient;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.patient.MixPatientUserFeign;
import com.ykl.med.mix.service.patient.MixPatientUserService;
import com.ykl.med.mix.vo.patient.MixPatientUserDetailVO;
import com.ykl.med.mix.vo.patient.MixPatientUserWebListVO;
import com.ykl.med.patient.vo.patient.PatientDoctorQueryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Tag(name = "患者-家庭成员接口")
@RestController
@RequestMapping("/mixPatientUser")
@Validated
public class MixPatientUserController implements MixPatientUserFeign {

    @Resource
    private MixPatientUserService mixPatientUserService;

    @Override
    @PostMapping("/getPatientUserByDoctor")
    @Operation(summary = "获取一个医生的所有病人的详细信息.")
    public PageResult<MixPatientUserDetailVO> getPatientUserByDoctor(@RequestBody PatientDoctorQueryVO queryVO) {
        return mixPatientUserService.getPatientUserByDoctor(queryVO);
    }

    @Override
    @PostMapping("/getPatientUserByUserIds")
    @Operation(summary = "根据用户id查询患者家庭成员详细信息")
    public List<MixPatientUserDetailVO> getPatientUserByUserIds(@RequestBody List<Long> userIds) {
        return mixPatientUserService.getPatientUserByUserIds(userIds);
    }

    @Override
    @PostMapping("/getPatientUserByPatientIds")
    @Operation(summary = "根据患者id查询患者家庭成员详细信息")
    public List<MixPatientUserDetailVO> getPatientUserByPatientIds(@RequestBody List<Long> patientIds) {
        return mixPatientUserService.getPatientUserByPatientIds(patientIds);
    }

    @Override
    @PostMapping("/getAllFamily")
    @Operation(summary = "根据患者id查询患者所有的家庭成员列表信息")
    public List<MixPatientUserWebListVO> getAllFamily(@RequestParam(value = "patientId") Long patientId) {
        return mixPatientUserService.getAllFamily(patientId);
    }

}