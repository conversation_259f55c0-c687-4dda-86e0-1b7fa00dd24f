package com.ykl.med.mix.controller.consult;

import com.ykl.med.framework.common.pojo.IdListReqVO;
import com.ykl.med.mix.api.consult.MixConsulMessageFeign;
import com.ykl.med.mix.service.consult.MixConsultSendMessage;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 线上门诊 mix
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
@RestController
@RequestMapping("/mix/consult/message")
public class MixConsultMessageController implements MixConsulMessageFeign {

    @Resource
    private MixConsultSendMessage mixConsultSendMessage;

    @Override
    @PostMapping("/noticeAdvanceThreeDays")
    public void noticeAdvanceThreeDays(@RequestBody IdListReqVO reqVO) {
        mixConsultSendMessage.noticeAdvanceThreeDays(reqVO.getIdList());
    }

    @Override
    @PostMapping("/noticeTodDayEightClock")
    public void noticeTodDayEightClock(@RequestBody IdListReqVO reqVO) {
        mixConsultSendMessage.noticeTodDayEightClock(reqVO.getIdList());
    }

    @Override
    @PostMapping("/noticeAdvanceOneDays")
    public void noticeAdvanceOneDays(@RequestBody IdListReqVO reqVO) {
        mixConsultSendMessage.noticeAdvanceOneDays(reqVO.getIdList());
    }

}
