package com.ykl.med.mix.service.consult;

import com.ykl.med.followup.api.FollowupTrackFeign;
import com.ykl.med.followup.entity.param.QueryFollowupTrackInfoParam;
import com.ykl.med.followup.entity.vo.FollowupTrackVO;
import com.ykl.med.framework.common.constants.JumpConstants;
import com.ykl.med.framework.common.enums.ConsultChatTypeEnum;
import com.ykl.med.framework.common.pojo.IdListReqVO;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.push.api.SmsFeign;
import com.ykl.med.push.api.SystemMessageFeign;
import com.ykl.med.push.constans.MessageConstants;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.enums.SmsType;
import com.ykl.med.push.enums.SystemMessageType;
import com.ykl.med.push.vo.message.BizMessageBaseVO;
import com.ykl.med.push.vo.message.LiveMessageSendReqVO;
import com.ykl.med.push.vo.message.MessageSendPatientReqVO;
import com.ykl.med.push.vo.sms.SmsNoticeSendReqVO;
import com.ykl.med.push.vo.sys.SystemMessageReqVO;
import com.ykl.med.shift.api.ConsultFeign;
import com.ykl.med.shift.enums.consult.ConsultTypeEnum;
import com.ykl.med.shift.vo.resp.ConsultVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MixConsultSendMessage {

    private final SmsFeign smsFeign;
    private final ConsultFeign consultFeign;
    private final MessageFeign messageFeign;
    private final PatientFeign patientFeign;
    private final UserFeign userFeign;
    private final FollowupTrackFeign followupTrackFeign;
    private final SystemMessageFeign systemMessageFeign;
    private final PushSocketFeign pushSocketFeign;
    private static final Long SYSTEM_USER_ID = 0L;
    private static final String CHAT_VIDEO = "视频";
    private static final String CHAT_VOICE = "语音";
    private static final String PATIENT_NAME = "患者";
    private static final String DOCTOR_NAME = "医生";

    // 随访
    // 提前3天
    private final static String PATIENT_ADVANCE_THREE_DAYS_FOLLOWUP = "[%s]您好，系统预约随访专员的随访%s已成功，时间为 [%s]，请[%s]登录医康链参加问诊！";
    private final static String DOCTOR_ADVANCE_THREE_DAYS_FOLLOWUP = "「%s」的「%s」预约日期为「%s」，请及时处理，确认已读。";
    // 当天8点
    private final static String PATIENT_TOD_DAY_EIGHT_CLOCK_FOLLOWUP = "%s，您好，温馨提醒您，今日安排了与随访专员的随访%s！";
    private final static String DOCTOR_TOD_DAY_EIGHT_CLOCK_FOLLOWUP = "%s您好，今日有与[%s]的问诊安排，请按时参加。";
    // 随访小结
    private final static String PATIENT_SUMMARY_FOLLOWUP = "[%s]您好！您的随访小结已送达，请您及时查收并仔细阅读，若您有任何疑问，欢迎随时联系我们的医护团队！";


    // 心理
    // 提前3天
    private final static String PATIENT_ADVANCE_THREE_DAYS_PSYCHOLOGY = "%s %s 医生已经预约了「%s」的视频问诊！";
    // 当天8点
    private final static String PATIENT_TOD_DAY_EIGHT_CLOCK_PSYCHOLOGY = "您的问诊订单即将开始！";
    private final static String DOCTOR_TOD_DAY_EIGHT_CLOCK_PSYCHOLOGY = "「%s」的「%s」预约日期为「%s」，请及时处理";
    private final static String DOCTOR_ADVANCE_ONE_DAYS_PSYCHOLOGY = "「%s」的预约问诊周期是「%s」，请及时预约";

    // 修改预约时间
    private final static String CONSULT_UPDATE_TIME = "%s %s 医生变更了预约「%s」的视频问诊！";
    // 发起视频
    private final static String CONSULT_START_VIDEO = "%s已发起%s通话";
    // 关闭视频
    private final static String CONSULT_CLOSE_VIDEO = "%s已关闭%s通话";
    // 拒绝视频
    private final static String CONSULT_REFUSE_VIDEO = "%s拒绝了%s通话";


    private Map<Long, String> userMap(List<Long> userIds) {
        List<UserSimpleVO> userSimpleVOS = userFeign.listByUserIds(new com.ykl.med.user.vo.req.IdListReqVO().setIdList(userIds));
        if (CollectionUtils.isEmpty(userSimpleVOS)) {
            return new HashMap<>();
        }
        return userSimpleVOS.stream()
                .collect(Collectors.toMap(UserSimpleVO::getId, UserSimpleVO::getMobile));
    }


    /**
     * 提前三天通知
     */
    public void noticeAdvanceThreeDays(List<Long> consultIds) {
        if (CollectionUtils.isEmpty(consultIds)) {
            return;
        }
        List<ConsultVO> consultVOS = consultFeign.listIds(new IdListReqVO().setIdList(consultIds));
        if (CollectionUtils.isEmpty(consultVOS)) {
            return;
        }

        List<Long> userIds = consultVOS.stream().map(ConsultVO::getUserId).collect(Collectors.toList());
        Map<Long, String> longStringMap = userMap(userIds);
        for (ConsultVO consultVO : consultVOS) {
            String patient_msg = null;
            String doctor_msg = null;
            String patient_mobile = null;
            String timeToString = DateTimeUtils.convertDateTimeToString(consultVO.getPlanVisitStartTime());
            switch (consultVO.getConsultType()) {
                case FOLLOWUP_CONSULTATION:
                    patient_msg = String.format(PATIENT_ADVANCE_THREE_DAYS_FOLLOWUP,
                            consultVO.getPatientName(),
                            converChatType(consultVO.getChatType()),
                            timeToString,
                            timeToString);
                    doctor_msg = String.format(DOCTOR_ADVANCE_THREE_DAYS_FOLLOWUP,
                            consultVO.getPatientName(),
                            consultVO.getConsultType().getDesc(),
                            timeToString);
                    patient_mobile = longStringMap.get(consultVO.getUserId());
                    break;
                case PSYCHOLOGY_CONSULTATION:
                    patient_msg = String.format(PATIENT_ADVANCE_THREE_DAYS_PSYCHOLOGY,
                            consultVO.getDepartmentName(),
                            consultVO.getDoctorName(),
                            timeToString);
                    break;
            }
            String consultIdStr = consultVO.getId().toString();
            if (StringUtils.isNotBlank(patient_msg)) {
                sendMag(MessageConstants.SYSTEM_USER,
                        consultVO.getPatientId(),
                        MessageType.TEXT_AND_LINK,
                        patient_msg,
                        consultIdStr,
                        null,
                        null,
                        new BizMessageBaseVO().setBizId(consultIdStr)
                                .setYklJumpUrl(JumpConstants.CONSULT_ORDER)
                                .setLinkText("查看详情")
                );
            }
            if (StringUtils.isNotBlank(doctor_msg)) {
                sendSystemMsg(consultVO.getDoctorId(),
                        doctor_msg,
                        consultVO.getPatientId(),
                        SystemMessageType.COMMON_TO_DO,
                        consultIdStr
                );
            }

            if (StringUtils.isNotBlank(patient_mobile)) {
                SmsNoticeSendReqVO sms = new SmsNoticeSendReqVO();
                sms.setPhone(patient_mobile);
                sms.setSmsType(consultVO.getChatType().equals(ConsultChatTypeEnum.VOICE_CHAT) ? SmsType.AUDIO_FOLLOW_UP_ADD : SmsType.VIDEO_FOLLOW_UP_ADD);
                sms.setDoctorName(consultVO.getDoctorName());
                sms.setPatientName(consultVO.getPatientName());
                sms.setVideoFollowUpTime(timeToString);
                smsFeign.sendNotice(sms);
            }
        }
    }


    /**
     * 当天早上8点通知
     */
    public void noticeTodDayEightClock(List<Long> consultIds) {
        if (CollectionUtils.isEmpty(consultIds)) {
            return;
        }
        List<ConsultVO> consultVOS = consultFeign.listIds(new IdListReqVO().setIdList(consultIds));
        if (CollectionUtils.isEmpty(consultVOS)) {
            return;
        }
        List<Long> userIds = consultVOS.stream().map(ConsultVO::getUserId).collect(Collectors.toList());
        Map<Long, String> longStringMap = userMap(userIds);
        for (ConsultVO consultVO : consultVOS) {
            String patient_msg = null;
            String doctor_msg = null;
            String patient_mobile = null;
            String timeToString = DateTimeUtils.convertDateTimeToString(consultVO.getPlanVisitStartTime());
            switch (consultVO.getConsultType()) {
                case FOLLOWUP_CONSULTATION:
                    patient_msg = String.format(PATIENT_TOD_DAY_EIGHT_CLOCK_FOLLOWUP,
                            consultVO.getPatientName(),
                            converChatType(consultVO.getChatType()));
                    doctor_msg = String.format(DOCTOR_TOD_DAY_EIGHT_CLOCK_FOLLOWUP,
                            consultVO.getDoctorName(),
                            consultVO.getPatientName());
                    patient_mobile = longStringMap.get(consultVO.getUserId());
                    break;
                case PSYCHOLOGY_CONSULTATION:
                    patient_msg = PATIENT_TOD_DAY_EIGHT_CLOCK_PSYCHOLOGY;
                    doctor_msg = String.format(DOCTOR_TOD_DAY_EIGHT_CLOCK_PSYCHOLOGY,
                            consultVO.getPatientName(),
                            consultVO.getConsultType().getDesc(),
                            timeToString);
                    break;
            }
            String consultIdStr = consultVO.getId().toString();
            if (StringUtils.isNotBlank(patient_msg)) {
                sendMag(MessageConstants.SYSTEM_USER,
                        consultVO.getPatientId(),
                        MessageType.TEXT_AND_LINK,
                        patient_msg,
                        consultIdStr,
                        null,
                        null,
                        new BizMessageBaseVO().setBizId(consultIdStr)
                                .setYklJumpUrl(JumpConstants.CONSULT_ORDER)
                                .setLinkText("查看详情")
                );
            }
            if (StringUtils.isNotBlank(doctor_msg)) {
                sendSystemMsg(consultVO.getDoctorId(),
                        doctor_msg,
                        consultVO.getPatientId(),
                        SystemMessageType.COMMON_TO_DO,
                        consultIdStr
                );
            }

            if (StringUtils.isNotBlank(patient_mobile)) {
                SmsNoticeSendReqVO sms = new SmsNoticeSendReqVO();
                sms.setPhone(patient_mobile);
                sms.setSmsType(consultVO.getChatType().equals(ConsultChatTypeEnum.VOICE_CHAT) ? SmsType.AUDIO_FOLLOW_UP_TODAY : SmsType.VIDEO_FOLLOW_UP_TODAY);
                sms.setDoctorName(consultVO.getDoctorName());
                sms.setPatientName(consultVO.getPatientName());
                sms.setVideoFollowUpTime(timeToString);
                smsFeign.sendNotice(sms);
            }
        }
    }


    /**
     * 提前一天通知
     */
    public void noticeAdvanceOneDays(List<Long> consultIds) {
        if (CollectionUtils.isEmpty(consultIds)) {
            return;
        }
        List<ConsultVO> consultVOS = consultFeign.listIds(new IdListReqVO().setIdList(consultIds));
        if (CollectionUtils.isEmpty(consultVOS)) {
            return;
        }
        for (ConsultVO consultVO : consultVOS) {
            String doctor_msg = null;
            String timeToString = DateTimeUtils.convertDateToString(consultVO.getPlanVisitStartTime().toLocalDate());
            switch (consultVO.getConsultType()) {
                case PSYCHOLOGY_CONSULTATION:
                    doctor_msg = String.format(DOCTOR_ADVANCE_ONE_DAYS_PSYCHOLOGY,
                            consultVO.getPatientName(),
                            timeToString);
                    break;
            }
            String consultIdStr = consultVO.getId().toString();
            if (StringUtils.isNotBlank(doctor_msg)) {
                sendSystemMsg(consultVO.getDoctorId(),
                        doctor_msg,
                        consultVO.getPatientId(),
                        SystemMessageType.COMMON_TO_DO,
                        consultIdStr
                );
            }
        }
    }

    /**
     * 修改预约时间
     */
    public void noticeUpdateConsultTime(Long consultId, LocalDateTime time) {
        ConsultVO consultVO = consultFeign.details(consultId);
        if (consultVO == null) {
            return;
        }
        String patient_msg = null;
        String timeToString = DateTimeUtils.convertDateTimeToString(time);
        switch (consultVO.getConsultType()) {
            case PSYCHOLOGY_CONSULTATION:
                patient_msg = String.format(CONSULT_UPDATE_TIME,
                        consultVO.getDepartmentName(),
                        consultVO.getDoctorName(),
                        timeToString);
                break;
        }
        String consultIdStr = consultVO.getId().toString();
        if (StringUtils.isNotBlank(patient_msg)) {
            sendMag(MessageConstants.SYSTEM_USER,
                    consultVO.getPatientId(),
                    MessageType.TEXT_AND_LINK,
                    patient_msg,
                    consultIdStr,
                    null,
                    null,
                    new BizMessageBaseVO().setBizId(consultIdStr)
                            .setYklJumpUrl(JumpConstants.CONSULT_ORDER)
                            .setLinkText("查看详情")
            );
        }
    }


    /**
     * 随访小结
     */
    public void noticeSummaryFollowup(Long followupId) {
        FollowupTrackVO followupTrackVO = followupTrackFeign.infoWeb(new QueryFollowupTrackInfoParam(followupId));
        if (followupTrackVO == null) {
            return;
        }
        PatientBaseVO patientBaseById = patientFeign.getPatientBaseById(followupTrackVO.getPatientId());
        String patient_msg = String.format(PATIENT_SUMMARY_FOLLOWUP,
                patientBaseById.getName());
        String followupIdStr = followupId.toString();
        sendMag(MessageConstants.SYSTEM_USER,
                followupTrackVO.getPatientId(),
                MessageType.TEXT_AND_LINK,
                patient_msg,
                followupIdStr,
                null,
                null,
                new BizMessageBaseVO().setBizId(followupIdStr)
                        .setYklJumpUrl(JumpConstants.FOLLOWUP_DETAILS)
                        .setLinkText("查看详情")
        );
    }


    /**
     * 打视频
     */
    public void noticeStartVideo(Long userId, Long consultId) {
        ConsultVO consultVO = consultFeign.details(consultId);
        if (consultVO == null) {
            return;
        }
        UserSimpleVO userSimpleVO = userFeign.getByUserId(consultVO.getUserId());
        if (userSimpleVO == null) {
            return;
        }
        // socket推送
        pushSocketFeign.sendLiveNotice(consultVO.getUserId(), consultVO.getId(), null);
        // im消息
        messageFeign.sendLiveMsg(new LiveMessageSendReqVO()
                .setRequestId(UUID.randomUUID().toString())
                .setCode(String.valueOf(consultId))
                .setType(MessageType.SYSTEM_MESSAGE)
                .setCurrentUserId(userId)
                .setMsg(String.format(CONSULT_START_VIDEO, getUserType(userId), converChatType(consultVO.getChatType())))
                .setExtra(new BizMessageBaseVO()
                        .setBizType("TEXT")
                        .setBizId(String.valueOf(consultId))));
        // 随访才发短信
        if (consultVO.getConsultType().equals(ConsultTypeEnum.FOLLOWUP_CONSULTATION)
                && StringUtils.isNotBlank(userSimpleVO.getMobile())) {
            SmsNoticeSendReqVO sms = new SmsNoticeSendReqVO();
            sms.setPhone(userSimpleVO.getMobile());
            sms.setSmsType(consultVO.getChatType().equals(ConsultChatTypeEnum.VOICE_CHAT) ? SmsType.AUDIO_CONSULT_BEGIN : SmsType.VIDEO_CONSULT_BEGIN);
            sms.setDoctorName(consultVO.getDoctorName());
            sms.setPatientName(consultVO.getPatientName());
            sms.setVideoFollowUpTime(DateTimeUtils.convertDateTimeToString(consultVO.getPlanVisitStartTime()));
            smsFeign.sendNotice(sms);
        }
    }


    /**
     * 关闭视频
     */
    public void noticeCloseVideo(Long userId, Boolean joinFlag, Long consultId) {
        ConsultVO consultVO = consultFeign.details(consultId);
        if (consultVO == null) {
            return;
        }
        // 关闭视频
        messageFeign.sendLiveMsg(new LiveMessageSendReqVO()
                .setRequestId(UUID.randomUUID().toString())
                .setCode(String.valueOf(consultId))
                .setType(MessageType.SYSTEM_MESSAGE)
                .setCurrentUserId(userId)
                .setMsg(String.format(joinFlag ? CONSULT_CLOSE_VIDEO : CONSULT_REFUSE_VIDEO, getUserType(userId), converChatType(consultVO.getChatType())))
                .setExtra(new BizMessageBaseVO()
                        .setBizType("TEXT").setBizId(String.valueOf(consultId))));
    }

    private String getUserType(Long userId) {
        UserSimpleVO userSimpleVO = userFeign.getByUserId(userId);
        if (userSimpleVO == null) {
            return DOCTOR_NAME;
        }
        return userSimpleVO.getUserType().equals(UserType.PATIENT) ? PATIENT_NAME : DOCTOR_NAME;
    }


    /**
     * 发送系统消息
     *
     * @param fromUserId  发送者id
     * @param patientId   患者id
     * @param MessageType 消息类型
     * @param msg         消息内容
     * @param bizId       业务id
     * @param bizName     业务名字
     * @param bizType     业务类型
     * @param extra       拓展信息
     */
    private void sendMag(Long fromUserId,
                         Long patientId,
                         MessageType MessageType,
                         String msg,
                         String bizId,
                         String bizName,
                         String bizType,
                         BizMessageBaseVO extra) {
        MessageSendPatientReqVO messageSendPatientReqVO = new MessageSendPatientReqVO();
        messageSendPatientReqVO.setExtra(extra);
        messageSendPatientReqVO.setRequestId(UUID.randomUUID().toString());
        messageSendPatientReqVO.setCurrentUserId(fromUserId);
        messageSendPatientReqVO.setPatientId(patientId);
        messageSendPatientReqVO.setType(MessageType);
        messageSendPatientReqVO.setMsg(msg);
        messageSendPatientReqVO.getExtra().setBizId(bizId);
        messageSendPatientReqVO.getExtra().setBizName(bizName);
        messageSendPatientReqVO.getExtra().setBizType(bizType);
        messageFeign.sendMessagePatient(messageSendPatientReqVO);
    }


    /**
     * 创建并添加系统消息
     *
     * @param userId    用户ID
     * @param msg       消息内容
     * @param patientId 患者ID
     * @param type      消息类型
     * @param bizId     业务ID
     */
    private Long sendSystemMsg(Long userId,
                               String msg,
                               Long patientId,
                               SystemMessageType type,
                               String bizId) {
        SystemMessageReqVO systemMessageReqVO = new SystemMessageReqVO()
                .setUserId(userId)
                .setContent(msg)
                .setRequestId(UUID.randomUUID().toString())
                .setSenderId(type.getNoticeSubType() == SystemMessageType.NoticeSubType.PATIENT_OPERATE ? patientId : SYSTEM_USER_ID)
                .setPatientId(patientId)
                .setType(type)
                .setBizId(bizId);
        return systemMessageFeign.addSystemMessage(systemMessageReqVO);
    }


    private static String converChatType(ConsultChatTypeEnum chatTypeEnum) {
        return chatTypeEnum.equals(ConsultChatTypeEnum.VIDEO_CHAT) ? CHAT_VIDEO : CHAT_VOICE;
    }


}
