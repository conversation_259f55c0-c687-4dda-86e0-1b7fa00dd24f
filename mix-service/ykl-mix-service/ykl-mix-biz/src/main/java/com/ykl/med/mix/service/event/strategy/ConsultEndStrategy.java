package com.ykl.med.mix.service.event.strategy;

import com.ykl.med.doctors.api.DoctorInfoFeign;
import com.ykl.med.doctors.api.SectionFeign;
import com.ykl.med.mix.service.consult.MixConsultService;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskVO;
import com.ykl.med.supervise.service.SuperviseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/11/6
 */
@Slf4j
@Service
@EventTaskTypeAnnotation({EventTaskType.CONSULT_END})
public class ConsultEndStrategy extends AbstractEventTaskStrategy {

    @Resource
    private MixConsultService mixConsultService;

    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {

        // 推送监管平台
        mixConsultService.sendSupervise(Long.valueOf(eventTaskVO.getBizId()));

        // 推送His平台
        mixConsultService.sendHisSaveInquiryRecord(Long.valueOf(eventTaskVO.getBizId()));
    }

}
