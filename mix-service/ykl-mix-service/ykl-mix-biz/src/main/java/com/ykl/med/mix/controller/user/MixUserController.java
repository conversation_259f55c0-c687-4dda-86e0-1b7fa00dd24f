package com.ykl.med.mix.controller.user;

import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.user.MixUserFeign;
import com.ykl.med.mix.service.user.MixUserService;
import com.ykl.med.mix.vo.UserInfoVO;
import com.ykl.med.push.vo.SmsCodeCheckReqVO;
import com.ykl.med.user.vo.req.*;
import com.ykl.med.user.vo.resp.LoginOrRegisterRespVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Tag(name = "用户角色")
@RestController
@RequestMapping("/user")
@Validated
public class MixUserController implements MixUserFeign {

    @Resource
    private MixUserService mixUserService;

    @PostMapping("/info")
    public UserInfoVO info(@RequestBody IdReqVO idReqVO){
        return mixUserService.info(idReqVO);
    }

    @Override
    @PostMapping("/page")
    public PageResult<UserInfoVO> page(@RequestBody SimpleUserPageReqVO reqVO) {
        return mixUserService.page(reqVO);
    }

    @Override
    @PostMapping("/loginOrRegister")
    public LoginOrRegisterRespVO loginOrRegister(@RequestBody LoginOrRegisterReqVO reqVO) {
        return mixUserService.loginOrRegister(reqVO);
    }

    @Override
    @PostMapping("/refreshToken")
    public LoginOrRegisterRespVO refreshToken(@Valid @RequestBody RefreshTokenReqVO reqVO) {
        return mixUserService.refreshToken(reqVO);
    }


    @Override
    @PostMapping("/ignorePassword")
    public void ignorePassword(@RequestBody UserPasswordUpdateReqVO reqVO) {
        mixUserService.ignorePassword(reqVO);
    }

    @Override
    @PostMapping("/updatePassword")
    public void updatePassword(@RequestBody UserPasswordUpdateReqVO reqVO) {
        mixUserService.updatePassword(reqVO);
    }

    @Override
    @PostMapping("/smsCheck")
    public void smsCheck(@Valid @RequestBody SmsCodeCheckReqVO reqVO) {
        mixUserService.smsCheck(reqVO);
    }


}
