package com.ykl.med.mix.controller.edu;

import com.ykl.med.mix.api.edu.MixEduFeign;
import com.ykl.med.mix.service.edu.MixEduService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Tag(name = "患教")
@RestController
@RequestMapping("/mixEdu")
@Validated
public class MixEduController implements MixEduFeign {

    @Resource
    private MixEduService mixEduService;

    @PostMapping(value = "/transcribe", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "转录")
    @Override
    public String transcribe(@RequestPart("file") MultipartFile file){
        return mixEduService.transcribe(file);
    }
}
