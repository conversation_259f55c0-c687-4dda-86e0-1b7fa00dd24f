package com.ykl.med.mix.service.event.strategy.rehab;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.constants.topic.RehabMangeTopic;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.service.event.strategy.AbstractEventTaskStrategy;
import com.ykl.med.mix.service.event.strategy.EventTaskTypeAnnotation;
import com.ykl.med.mix.service.user.MixUserService;
import com.ykl.med.mix.vo.UserInfoVO;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.enums.FormChangeNoticeType;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.enums.SystemMessageType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.push.vo.event.EventTaskCancelVO;
import com.ykl.med.push.vo.event.EventTaskVO;
import com.ykl.med.push.vo.message.BizMessageBaseVO;
import com.ykl.med.push.vo.message.MessageSendPatientReqVO;
import com.ykl.med.rehab.api.SportRehabFeign;
import com.ykl.med.rehab.enums.RehabType;
import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanDetailRespVO;
import com.ykl.med.user.vo.req.SimpleUserPageReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@EventTaskTypeAnnotation({EventTaskType.SPORTS_REHAB_PLAN_CHANGE})
public class SportRehabPlanChangeStrategy extends AbstractEventTaskStrategy {
    @Resource
    private SportRehabFeign sportRehabFeign;
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private PushSocketFeign pushSocketFeign;
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private RocketMQTemplate rocketMqTemplate;
    @Resource
    private MixUserService mixUserService;

    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {
        SportRehabPlanDetailRespVO plan = sportRehabFeign.getById(Long.valueOf(eventTaskVO.getBizId()));
        if (plan.getStatus() == CommonStatusEnum.ENABLE) {
            patientUserFeign.getFamilyByPatientId(eventTaskVO.getPatientId()).forEach(patientUserVO -> {
                pushSocketFeign.sendFormChangeNotice(patientUserVO.getUserId(), FormChangeNoticeType.SPORTS_PLAN);
            });
            MessageSendPatientReqVO messageSendReqVO = new MessageSendPatientReqVO()
                    .setMsg("运动康复方案")
                    .setType(MessageType.SPORTS_PLAN)
                    .setExtra(new BizMessageBaseVO().setBizId(eventTaskVO.getBizId()))
                    .setPatientId(eventTaskVO.getPatientId())
                    .setRequestId(eventTaskVO.getId().toString());
            messageSendReqVO.setCurrentUserId(eventTaskVO.getUserId());
            messageFeign.sendMessagePatient(messageSendReqVO);

            // 添加事件任务，先把旧的任务取消掉，再添加新的任务
            eventTaskFeign.cancel(new EventTaskCancelVO()
                    .setPatientId(eventTaskVO.getPatientId())
                    .setBizType(EventTaskType.SPORTS_PLAN_EXPIRE));
            eventTaskFeign.addEventTask(new EventTaskAddVO()
                    .setUserId(eventTaskVO.getUserId())
                    .setPatientId(eventTaskVO.getPatientId())
                    .setRequestId(UUID.randomUUID().toString())
                    .setEventTime(LocalDateTime.now())
                    .setExpireTime(plan.getEndTime())
                    .setBizId(plan.getId().toString())
                    .setBizType(EventTaskType.SPORTS_PLAN_EXPIRE));
        }

        sendSystemMessage(eventTaskVO);

        //康复管理es变动
        RehabManageMqMessageVO rehabManageMessage = new RehabManageMqMessageVO()
                .setPatientId(eventTaskVO.getPatientId())
                .setRehabId(plan.getId())
                .setRehabType(RehabType.SPORT);
        rocketMqTemplate.syncSend(RehabMangeTopic.TOPIC, rehabManageMessage);
    }



    public void sendSystemMessage(EventTaskVO eventTask) {
        PatientBaseVO patient = patientFeign.getPatientBaseById(eventTask.getPatientId());
        if (patient.getMemberStatus() != CommonStatusEnum.ENABLE || patient.getMedicalTeamId() == null) {
            return;
        }
        String content = getSystemMessageContent(SystemMessageType.REHAB_PLAN_ADD)
                .replace("patientName", patient.getName())
                .replace("rehabType", RehabType.SPORT.getDesc());
        JSONObject defaultDoctor = commonConfigFeign.getCommonConfigValueJsonByKey("default_followup_doctor_key");
        List<Long> doctorIds = new ArrayList<>();
        if (defaultDoctor.containsKey("doctor_sport")) {
            JSONArray doctorPsycho = defaultDoctor.getJSONArray("doctor_sport");
            for (int i = 0; i < doctorPsycho.size(); i++) {
                doctorIds.add(doctorPsycho.getLong(i));
            }
        } else {
            SimpleUserPageReqVO reqVO = new SimpleUserPageReqVO();
            reqVO.setPageSize(1000);
            reqVO.setMedicalTeamId(String.valueOf(patient.getMedicalTeamId()));
            PageResult<UserInfoVO> userSimpleVOList = mixUserService.page(reqVO);
            doctorIds = userSimpleVOList.getList().stream().map(UserInfoVO::getId).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(doctorIds)) {
            for (Long doctorId : doctorIds) {
                createAndAddSystemMessage(doctorId, content, eventTask.getId() + "_" + doctorId, patient.getPatientId(), SystemMessageType.REHAB_PLAN_ADD, eventTask.getBizId());
            }
        }
    }
}
