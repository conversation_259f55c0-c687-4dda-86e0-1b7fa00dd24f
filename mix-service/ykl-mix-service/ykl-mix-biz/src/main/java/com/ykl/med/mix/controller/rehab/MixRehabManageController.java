package com.ykl.med.mix.controller.rehab;

import com.ykl.med.mix.api.rehab.MixRehabManageFeign;
import com.ykl.med.mix.mq.rocket.RehabManageListener;
import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/mixRehabManage")
public class MixRehabManageController implements MixRehabManageFeign {
    @Resource
    private RehabManageListener rehabManageListener;

    @RequestMapping("/refreshRehabManage")
    @Override
    public void refreshRehabManage(@RequestBody RehabManageMqMessageVO message) {
        rehabManageListener.onMessage(message);
    }
}
