package com.ykl.med.mix.service.consult;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.base.utils.NumberUtils;
import com.ykl.med.doctors.api.DoctorInfoFeign;
import com.ykl.med.doctors.api.SectionFeign;
import com.ykl.med.doctors.entity.vo.DoctorInfoVO;
import com.ykl.med.doctors.entity.vo.SectionVO;
import com.ykl.med.followup.api.FollowupTrackFeign;
import com.ykl.med.followup.entity.param.UpdateFollowupConsultTimeReqVO;
import com.ykl.med.framework.common.constants.topic.ConsultTopic;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.CommonResult;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.framework.common.vo.ConsultDiagnosisVO;
import com.ykl.med.his.service.HisService;
import com.ykl.med.his.vo.*;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.api.DiseaseFeign;
import com.ykl.med.masterdata.vo.dict.DictDataRespVO;
import com.ykl.med.masterdata.vo.dict.DictHisVO;
import com.ykl.med.masterdata.vo.dict.QueryDictHisReqVO;
import com.ykl.med.masterdata.vo.resp.DiseaseVO;
import com.ykl.med.medical.api.MedicalAdviceFeign;
import com.ykl.med.medical.enums.PrescriptionAuditStateEnum;
import com.ykl.med.medical.vo.medicalAdvice.PrescriptionVO;
import com.ykl.med.medical.vo.medicalAdvice.QueryMedicalAdviceConsultReqVO;
import com.ykl.med.mix.config.AiConfig;
import com.ykl.med.mix.constants.MixErrorCodeConstants;
import com.ykl.med.mix.service.order.MixOrderService;
import com.ykl.med.mix.service.patient.MixPatientService;
import com.ykl.med.mix.service.patient.MixPatientUserService;
import com.ykl.med.mix.service.records.MixRecordService;
import com.ykl.med.mix.vo.consult.*;
import com.ykl.med.mix.vo.patient.MixPatientBasicVO;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.push.api.ChatFeign;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.enums.LiveChatStatus;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.push.vo.message.BizMessageBaseVO;
import com.ykl.med.push.vo.message.LiveMessageSendReqVO;
import com.ykl.med.push.vo.message.MessageStatusChangeVO;
import com.ykl.med.records.api.BasicReportFeign;
import com.ykl.med.records.vo.AllergyHistoryVO;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.records.vo.MedicalHistoryVO;
import com.ykl.med.records.vo.resp.FullRecordRespVO;
import com.ykl.med.rocketmq.RocketMQService;
import com.ykl.med.shift.api.ConsultFeign;
import com.ykl.med.shift.constans.ShiftErrorCodeConstants;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.vo.dto.ConsultVideoVO;
import com.ykl.med.shift.vo.req.*;
import com.ykl.med.shift.vo.resp.*;
import com.ykl.med.supervise.service.SuperviseService;
import com.ykl.med.supervise.vo.UploadFurtherConsult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MixConsultService {

    private final ConsultFeign consultFeign;
    private final MixRecordService mixRecordService;
    private final DictDataFeign dictDataFeign;
    private final DiseaseFeign diseaseFeign;
    private final MixPatientService mixPatientService;
    private final MixPatientUserService mixPatientUserService;
    private final MedicalAdviceFeign medicalAdviceFeign;
    private final DoctorInfoFeign doctorInfoFeign;
    private final MessageFeign messageFeign;
    private final IdServiceImpl idService;
    private final BasicReportFeign basicReportFeign;
    private final CommonConfigFeign commonConfigFeign;
    private final HisService hisService;
    private final SuperviseService superviseService;
    private final PatientFeign patientFeign;
    private final SectionFeign sectionFeign;
    private final EventTaskFeign eventTaskFeign;
    private final ChatFeign chatFeign;
    private final StringRedisTemplate stringRedisTemplate;
    private final FollowupTrackFeign followupTrackFeign;
    private final AiConfig aiConfig;
    private final MixConsultSendMessage mixConsultSendMessage;
    private final RocketMQService rocketMQService;


    @Autowired(required = false) // 无奈之举
    private MixOrderService mixOrderService;

    private static final String min_time_kay = "min_time";
    private static final String CONSULT_REFUND_STATUS_KAY = "consult:refund:status:";
    private static final String CLOSE_VIDEO_STATUS_KAY = "consult:video:close:messageId:";
    private long default_min_time = 24L; // 小时

    public MixConsultMedicalRecordsVO medicalRecords(QueryMedicalRecordReqVO param) {
        // 有问诊记录，获取患者填写的电子病历，如果没有填写，从患者病史获取
        FullRecordRespVO fullRecord = mixRecordService.getFullRecord(param.getPatientId());
        MixConsultMedicalRecordsVO result = new MixConsultMedicalRecordsVO();
        BasicReportVO basicReport = fullRecord.getBasicReport();

        if (basicReport != null) {
            DiseaseVO diseaseVO = diseaseFeign.queryById(basicReport.getDiseaseId());
            result.setDiagnosis(CopyPropertiesUtil.normalCopyProperties(diseaseVO, ConsultDiagnosisVO.class));
            result.setMedicalHistoryDesc(basicReport.getMedicalHistoryDescription());
            result.setPastMedicalHistory(buildPastMedicalHistory(fullRecord.getMedicalHistories()));
            result.setAllergyHistory(buildAllergyHistory(fullRecord.getAllergyHistories()));
        }

        if (param.getConsultId() != null) {
            ConsultDiseaseHistoryVO consultDiseaseHistoryVO = consultFeign.diseaseHistory(param.getConsultId());
            if (consultDiseaseHistoryVO != null) {
                result.setConsultId(consultDiseaseHistoryVO.getConsultId());
                DiseaseVO diseaseVO = diseaseFeign.queryById(consultDiseaseHistoryVO.getDiseaseId());
                result.setDiagnosis(CopyPropertiesUtil.normalCopyProperties(diseaseVO, ConsultDiagnosisVO.class));
                result.setOnsetTime(consultDiseaseHistoryVO.getOnsetTime());
                result.setMedicalHistoryDesc(consultDiseaseHistoryVO.getMedicalHistoryDesc());
                result.setDiseaseImg(consultDiseaseHistoryVO.getDiseaseImg());
            }
        }

        return result;
    }

    private String buildAllergyHistory(List<AllergyHistoryVO> allergyHistories) {
        if (CollectionUtils.isEmpty(allergyHistories)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (AllergyHistoryVO allergyHistoryVO : allergyHistories) {
            String allergen = allergyHistoryVO.getAllergen();
            DictDataRespVO dictDataByValue = dictDataFeign.getDictDataByValue(allergen);
            if (dictDataByValue != null) {
                allergen = dictDataByValue.getLabel();
            }

            sb.append(allergen);
            if (CollectionUtils.isNotEmpty(allergyHistoryVO.getAllergySymptoms())) {
                List<String> collect = allergyHistoryVO.getAllergySymptoms().stream().map(AllergyHistoryVO.AllergySymptomsVO::getName).collect(Collectors.toList());
                sb.append("(").append(String.join(",", collect)).append(")").append("\n");
            }
        }

        return sb.toString();
    }

    private String buildPastMedicalHistory(List<MedicalHistoryVO> medicalHistories) {
        if (CollectionUtils.isEmpty(medicalHistories)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (MedicalHistoryVO medicalHistory : medicalHistories) {
            String diseaseName = medicalHistory.getDiseaseName();
            DictDataRespVO dictDataByValue = dictDataFeign.getDictDataByValue(diseaseName);
            if (dictDataByValue != null) {
                diseaseName = dictDataByValue.getLabel();
            }

            sb.append(diseaseName);
            if (StringUtils.isNotBlank(medicalHistory.getDiseaseDuration())) {
                sb.append(" ").append(medicalHistory.getDiseaseDuration()).append("年").append("\n");
            }
        }

        return sb.toString();
    }

    public void finish(FinishConclusionReqVO param) {
        // 是否还有待审核的处方
        Long consultId = param.getConsultId();
        List<Long> medicalAdvices = medicalAdviceFeign.queryWaitAuditMedicalAdvice(consultId);
        AssertUtils.isTrue(CollectionUtils.isEmpty(medicalAdvices), MixErrorCodeConstants.WAIT_AUDIT_MEDICAL_ADVICE);
        ConsultVO details = consultFeign.details(consultId);
        String orderCode = consultFeign.finish(param);

        switch (details.getConsultType()) {
            case ONLINE_VIDEO_CLINIC:
            case MDT_EXPERT_CONSULTATION:
                mixOrderService.completedOrder(orderCode);

                // 结束问诊延迟任务
                eventTaskFeign.addEventTask(new EventTaskAddVO()
                        .setUserId(param.getUserId())
                        .setPatientId(details.getPatientId())
                        .setRequestId(String.valueOf(idService.nextId()))
                        .setBizId(String.valueOf(consultId))
                        .setBizType(EventTaskType.CONSULT_END)
                        .setEventTime(DateTimeUtils.getNow())
                        .setExecuteTime(DateTimeUtils.getNow().plusSeconds(3)));

                messageFeign.sendLiveMsg(new LiveMessageSendReqVO()
                        .setCode(String.valueOf(consultId))
                        .setType(MessageType.SYSTEM_MESSAGE)
                        .setRequestId(String.valueOf(idService.nextId()))
                        .setCurrentUserId(param.getUserId())
                        .setMsg("接诊已结束")
                        .setExtra(new BizMessageBaseVO().setBizType("TEXT").setBizId(String.valueOf(consultId))));

                messageFeign.sendLiveMsg(new LiveMessageSendReqVO()
                        .setCode(String.valueOf(consultId))
                        .setType(MessageType.MEDICAL_CONSULTATION_SUMMARY)
                        .setRequestId(String.valueOf(idService.nextId()))
                        .setCurrentUserId(param.getUserId())
                        .setMsg("医生已为您填写咨询小结")
                        .setExtra(new BizMessageBaseVO().setBizId(String.valueOf(consultId))));
                break;
            case FOLLOWUP_CONSULTATION:
                followupTrackFeign.updateConsultTime(new UpdateFollowupConsultTimeReqVO().setFollowupId(details.getBizId()).setConsultEndTime(DateTimeUtils.getNow()));
                // 更新跟踪随访的问诊结束时间
                break;
        }

        // 问诊快照
        saveConsultSnapshot(details.getId(), details.getPatientId());

        // 更新聊天室
        chatFeign.changeLiveChatStatus(String.valueOf(consultId), LiveChatStatus.CLOSE);
    }

    private void saveConsultSnapshot(Long consultId, Long patientId) {
        MixPatientBasicVO patientBasicVO = mixPatientService.getPatientBasicById(patientId);
        PatientBaseVO patientBaseVO = patientBasicVO.getPatientBaseVO();
        CreateConsultSnapshotVO snapshotVO = new CreateConsultSnapshotVO();
        snapshotVO.setConsultId(consultId);
        snapshotVO.setPatientId(patientBaseVO.getPatientId());
        snapshotVO.setPatientName(patientBaseVO.getName());
        snapshotVO.setPatientSex(patientBaseVO.getSex());
        snapshotVO.setPatientAge(String.valueOf(patientBaseVO.getAge()));
        BasicReportVO basicReportVO = patientBasicVO.getBasicReportVO();
        if (basicReportVO != null) {
            snapshotVO.setPatientDiseases(basicReportVO.getDiseaseName());
            snapshotVO.setPatientStage(basicReportVO.getStage());
            snapshotVO.setPatientClinicalStaging(basicReportVO.getClinicalStaging());
            snapshotVO.setPatientPathologicalType(basicReportVO.getPathologicalType());
            snapshotVO.setPatientOtherDisease(String.join(",", basicReportVO.getOtherDiseaseNames()));
            snapshotVO.setPatientCurrentMedication(basicReportVO.getCurrentMedication());
        }
        consultFeign.saveConsultSnapshot(snapshotVO);
    }

    public void start(StartConclusionReqVO param) {
        Long consultId = param.getConsultId();
        ConsultVO details = consultFeign.details(consultId);

        consultFeign.start(param);

        // 接诊延迟任务
        eventTaskFeign.addEventTask(new EventTaskAddVO()
                .setUserId(param.getUserId())
                .setPatientId(details.getPatientId())
                .setRequestId(String.valueOf(idService.nextId()))
                .setBizId(String.valueOf(consultId))
                .setBizType(EventTaskType.CONSULT_START)
                .setEventTime(DateTimeUtils.getNow())
                .setExecuteTime(DateTimeUtils.getNow().plusSeconds(3)));
    }

    public MixConsultVO details(Long consultId) {
        ConsultVO details = consultFeign.details(consultId);
        if (details == null) {
            return null;
        }

        MixConsultVO mixConsultVO = CopyPropertiesUtil.normalCopyProperties(details, MixConsultVO.class);
        mixConsultVO.setPatientUserInfo(mixPatientUserService.getPatientUserByUserId(details.getUserId()));
        mixConsultVO.setDoctorUserInfo(CopyPropertiesUtil.normalCopyProperties(doctorInfoFeign.querySingle(details.getDoctorId()), DoctorUserDetailVO.class));
        mixConsultVO.setLastMedicalAdvice(buildLastMedicalAdvice(details.getElectronicsRecords()));

        mixConsultVO.setConclusion(consultFeign.conclusion(consultId));
        List<PrescriptionVO> prescriptionVOS = medicalAdviceFeign.getByConsult(new QueryMedicalAdviceConsultReqVO().setConsultId(consultId).setAuditState(Collections.singletonList(PrescriptionAuditStateEnum.PASS)));
        if (CollectionUtils.isNotEmpty(prescriptionVOS)) {
            mixConsultVO.setPrescription(prescriptionVOS);
        }
        return mixConsultVO;
    }

    private MixConsultMedicalAdviceVO buildLastMedicalAdvice(ConsultConclusionDiagnosisVO diagnosisVO) {
        if (diagnosisVO != null) {
            MixConsultMedicalAdviceVO mixConsultMedicalAdviceVO = new MixConsultMedicalAdviceVO();
            mixConsultMedicalAdviceVO.setDiagnosis(diagnosisVO.getDiagnosis());
            mixConsultMedicalAdviceVO.setOnsetTime(diagnosisVO.getOnsetTime());
            mixConsultMedicalAdviceVO.setAppeal(diagnosisVO.getAppeal());
            mixConsultMedicalAdviceVO.setPastMedicalHistory(diagnosisVO.getPastMedicalHistory());
            mixConsultMedicalAdviceVO.setAllergyHistory(diagnosisVO.getAllergyHistory());
            mixConsultMedicalAdviceVO.setMedicalHistoryDesc(diagnosisVO.getMedicalHistoryDesc());
            return mixConsultMedicalAdviceVO;
        }

        return null;
    }

    public void saveDiseaseHistory(SaveDiseaseHistoryReqVO param) {
        Long consultId = param.getConsultId();

        ConsultVO details = consultFeign.details(consultId);

        consultFeign.saveDiseaseHistory(param);

        // 完成电子病历填写
        messageFeign.messageStatusChange(new MessageStatusChangeVO()
                .setIsPatient(true)
                .setMessageType(MessageType.MEDICAL_HISTORY_FORM)
                .setBizId(String.valueOf(details.getId())));

        // 电子病历消息
        String msgText = convertChatMsg(param.getDiseaseId(), param.getOnsetTime(), param.getMedicalHistoryDesc());
        messageFeign.sendLiveMsg(new LiveMessageSendReqVO()
                .setCode(String.valueOf(consultId))
                .setType(MessageType.TEXT)
                .setRequestId(String.valueOf(idService.nextId()))
                .setCurrentUserId(details.getUserId())
                .setMsg(msgText)
                .setExtra(new BizMessageBaseVO()));

        // 电子病历图片消息
        if (CollectionUtils.isNotEmpty(param.getDiseaseImg())) {
            for (SaveDiseaseHistoryReqVO.SaveDiseaseHistoryImg saveDiseaseHistoryImg : param.getDiseaseImg()) {
                messageFeign.sendLiveMsg(new LiveMessageSendReqVO()
                        .setCode(String.valueOf(consultId))
                        .setType(MessageType.PIC)
                        .setRequestId(String.valueOf(idService.nextId()))
                        .setCurrentUserId(details.getUserId())
                        .setMsg("")
                        .setExtra(new BizMessageBaseVO().setMediaIds(Collections.singletonList(JSONObject.toJSONString(saveDiseaseHistoryImg)))));
            }
        }
    }

    private String convertChatMsg(Long diseaseId, Long onsetTime, String medicalHistoryDesc) {
        String diseaseName = "";
        if (diseaseId != null) {
            DiseaseVO diseaseVO = diseaseFeign.queryById(diseaseId);
            diseaseName = diseaseVO.getSystemName();
        }
        String time = "";
        if (onsetTime != null) {
            time = DateTimeUtils.convertMillisecondToDateTime(onsetTime).toLocalDate().toString();
        }

        return String.format("复诊疾病：%s\n" +
                "发病时间：%s\n" +
                "病史描述：%s", diseaseName, time, medicalHistoryDesc);
    }

    public List<ConsultDoctorGroupVO> listDoctor(QueryConsultListDoctorReqVO param) {
        List<ConsultDoctorPageVO> consultDoctorVOS = consultFeign.listDoctor(param);
        if (CollectionUtils.isEmpty(consultDoctorVOS)) {
            return new ArrayList<>();
        }

        List<Long> patientIds = consultDoctorVOS.stream().map(ConsultDoctorPageVO::getPatientId).collect(Collectors.toList());
        List<BasicReportVO> basicReportVOS = basicReportFeign.listByPatientIds(patientIds);
        Map<Long, List<BasicReportVO>> basicReportMap = basicReportVOS.stream().collect(Collectors.groupingBy(BasicReportVO::getPatientId));

        consultDoctorVOS.forEach(e -> {
            List<BasicReportVO> basicReport = basicReportMap.get(e.getPatientId());
            if (CollectionUtils.isNotEmpty(basicReport)) {
                e.setPatientDiseases(basicReport.get(0).getDiseaseName());
                e.setPatientStage(basicReport.get(0).getStage());
            }
        });

        // 按照时间分组
        Map<LocalDate, List<ConsultDoctorPageVO>> collect1 = consultDoctorVOS.stream().collect(Collectors.groupingBy(e -> e.getVisitTime().toLocalDate()));
        List<ConsultDoctorGroupVO> result = new ArrayList<>();
        for (LocalDate localDate : collect1.keySet()) {
            ConsultDoctorGroupVO consultDoctorGroupVO = new ConsultDoctorGroupVO();
            consultDoctorGroupVO.setVisitTime(localDate);
            consultDoctorGroupVO.setConsultList(collect1.get(localDate));
            result.add(consultDoctorGroupVO);
        }
        return result;
    }

    public PageResult<ConsultDoctorPageVO> listWait(QueryConsultWaitPageReqVO param) {
        PageResult<ConsultDoctorPageVO> consultDoctorVOS = consultFeign.listWait(param);
        if (CollectionUtils.isEmpty(consultDoctorVOS.getList())) {
            return new PageResult<>();
        }

        List<ConsultDoctorPageVO> list = consultDoctorVOS.getList();
        List<Long> patientIds = list.stream().map(ConsultDoctorPageVO::getPatientId).collect(Collectors.toList());
        List<BasicReportVO> basicReportVOS = basicReportFeign.listByPatientIds(patientIds);
        Map<Long, List<BasicReportVO>> basicReportMap = basicReportVOS.stream().collect(Collectors.groupingBy(BasicReportVO::getPatientId));

        list.forEach(e -> {
            List<BasicReportVO> basicReport = basicReportMap.get(e.getPatientId());
            if (CollectionUtils.isNotEmpty(basicReport)) {
                e.setPatientDiseases(basicReport.get(0).getDiseaseName());
                e.setPatientStage(basicReport.get(0).getStage());
                e.setClinicalStaging(basicReport.get(0).getClinicalStaging());
                e.setPathologicalType(basicReport.get(0).getPathologicalType());
                e.setSurgeryDateDistance(basicReport.get(0).buildSurgeryDate());
            }
        });

        return consultDoctorVOS;
    }

    public ConsultVO createByDirectly(CreateByDirectlyReqVO param) {
        ConsultVO consultVO = consultFeign.createByDirectly(param);
        saveConclusionByMedicalOrder(consultVO.getId(), param.getDiagnosisIds(), param.getMedicalHistoryDesc());

        // 推送His平台
        HisQuickVisitVO hisQuickVisitVO = sendHisQuickVisit(consultVO.getId());
        if (hisQuickVisitVO != null) {
            sendHisSaveInquiryRecord(consultVO.getId());
            consultVO.setHisVisitId(hisQuickVisitVO.getVisitId());
            consultVO.setHisVisitNo(hisQuickVisitVO.getVisitNo());
            consultVO.setHisPatientId(hisQuickVisitVO.getPatientId());
        }

        // 推送监管平台
        sendSupervise(consultVO.getId());
        return consultVO;
    }

    private void saveConclusionByMedicalOrder(Long consultId, List<Long> diagnosisIds, String medicalHistoryDesc) {
        SaveConclusionReqVO saveConclusionReqVO = new SaveConclusionReqVO();
        saveConclusionReqVO.setConsultId(consultId);
        saveConclusionReqVO.setMedicalHistoryDesc(medicalHistoryDesc);
        List<DiseaseVO> diseaseVOS = diseaseFeign.queryByIds(diagnosisIds);
        if (CollectionUtils.isNotEmpty(diseaseVOS)) {
            List<String> diseaseNames = diseaseVOS.stream().map(DiseaseVO::getSystemName).collect(Collectors.toList());
            saveConclusionReqVO.setClinicalDiagnosis(String.join(",", diseaseNames));
        }
        saveConclusionReqVO.setDiagnosisIds(diagnosisIds);
        saveConclusionReqVO.setConclusion("无");
        consultFeign.saveConclusion(saveConclusionReqVO);
    }

    public void consultTimeOutTask() {
        QueryConsultPageWebReqVO queryConsultPageWebReqVO = new QueryConsultPageWebReqVO().setConsultState(ConsultStateEnum.WAIT);
        queryConsultPageWebReqVO.setPageNo(1);
        queryConsultPageWebReqVO.setPageSize(100);
        PageResult<ConsultWebPageVO> page = consultFeign.pageWeb(queryConsultPageWebReqVO);
        if (CollectionUtils.isEmpty(page.getList())) {
            return;
        }
        LocalDateTime now = DateTimeUtils.getNow();
        JSONObject jsonObject = commonConfigFeign.getCommonConfigValueJsonByKey("consult_config");
        if (jsonObject.containsKey(min_time_kay)) {
            default_min_time = jsonObject.getLong(min_time_kay);
        }
        for (ConsultWebPageVO consultWebPageVO : page.getList()) {
            try {
                LocalDateTime visitTime = consultWebPageVO.getVisitTime();
                if (visitTime.plusHours(default_min_time).isBefore(now)) {  // 超过设定的时间
                    if (consultWebPageVO.getOrderCode() != null) {
                        // 缓存下退款入口，后续改为order通知
                        stringRedisTemplate.opsForValue().set(CONSULT_REFUND_STATUS_KAY + consultWebPageVO.getId(), ConsultStateEnum.TIME_OUT_CANCEL.getType(), 5, TimeUnit.MINUTES);
                        // 退款
                        mixOrderService.returnOrder(consultWebPageVO.getOrderCode());
                    } else {
                        UpdateConsultVO updateConsultVO = new UpdateConsultVO();
                        updateConsultVO.setConsultId(consultWebPageVO.getId());
                        updateConsultVO.setConsultState(ConsultStateEnum.TIME_OUT_CANCEL);
                        consultFeign.updateConsult(updateConsultVO);
                    }
                }
            } catch (Exception e) {
                log.error("问诊超时自动退款执行 error,{}", JSONObject.toJSON(consultWebPageVO));
                log.error(e.getMessage(), e);
            }
        }
    }


    public HisQuickVisitVO sendHisQuickVisit(Long consultId) {
        try {
            MixConsultVO consultVO = details(consultId);
            if (consultVO == null) {
                log.info("接诊 推送his平台 : 问诊信息为空");
                return null;
            }
            DoctorInfoVO doctorVO = doctorInfoFeign.querySingle(consultVO.getDoctorId());
            if (doctorVO == null || doctorVO.getHisId() == null) {
                log.info("接诊 推送his平台 : 医生信息为空");
                return null;
            }
            PatientVO patientVO = patientFeign.getPatientById(consultVO.getPatientId());
            if (patientVO == null) {
                log.info("接诊 推送his平台 : 患者信息为空");
                return null;
            }
            SectionVO sectionVO = sectionFeign.querySingle(consultVO.getDepartmentId());
            if (sectionVO == null || sectionVO.getHisId() == null) {
                log.info("接诊 推送his平台 : 科室信息为空");
                return null;
            }
            // 字段对应his值
            Map<String, DictHisVO> dictHisMap = dictDataFeign.getDictHisMap(new QueryDictHisReqVO());

            // His平台：接诊
            QuickVisitReq quickVisitReq = buildQuickVisitReq(doctorVO, patientVO, sectionVO, dictHisMap);
            CommonResult<HisQuickVisitVO> quickVisitResp = hisService.quickVisit(quickVisitReq);
            if (!quickVisitResp.isSuccess()) {
                throw new ServiceException(quickVisitResp.getCode(), quickVisitResp.getMsg());
            }

            HisQuickVisitVO data = quickVisitResp.getData();
            // 更新问诊表
            updateConsultHisData(consultVO.getId(), data);
            return data;
        } catch (Exception e) {
            log.error("接诊 推送his平台 : 异常", e);
            return null;
        }
    }


    public void sendHisSaveInquiryRecord(Long consultId) {
        try {
            MixConsultVO consultVO = details(consultId);
            if (consultVO == null || !consultVO.getConsultState().equals(ConsultStateEnum.FINISH)) {
                log.info("问诊结束 推送his平台 : 问诊信息为空");
                return;
            }

            if (consultVO.getHisVisitId() == null || consultVO.getHisPatientId() == null || consultVO.getHisVisitNo() == null) {
                log.info("问诊结束 推送his平台 : His问诊信息为空");
                return;
            }

            DoctorInfoVO doctorVO = doctorInfoFeign.querySingle(consultVO.getDoctorId());
            if (doctorVO == null) {
                log.info("问诊结束 推送his平台 : 医生信息为空");
                return;
            }

            PatientVO patientVO = patientFeign.getPatientById(consultVO.getPatientId());
            if (patientVO == null) {
                log.info("问诊结束 推送his平台 : 患者信息为空");
                return;
            }

            SectionVO sectionVO = sectionFeign.querySingle(consultVO.getDepartmentId());
            if (sectionVO == null) {
                log.info("问诊结束 推送his平台 : 科室信息为空");
                return;
            }

            // 字段对应his值
            Map<String, DictHisVO> dictHisMap = dictDataFeign.getDictHisMap(new QueryDictHisReqVO());

            // His平台：提交问诊记录
            SaveInquiryRecordReq param = buildSaveInquiryRecordReq(consultVO, doctorVO, patientVO, sectionVO, dictHisMap);
            CommonResult<String> saveInquiryRecord = hisService.saveInquiryRecord(param);
            if (!saveInquiryRecord.isSuccess()) {
                throw new ServiceException(saveInquiryRecord.getCode(), saveInquiryRecord.getMsg());
            }
        } catch (Exception e) {
            log.error("问诊结束 推送his平台 : 异常", e);
        }
    }


    private QuickVisitReq buildQuickVisitReq(DoctorInfoVO doctorVO, PatientVO patientVO, SectionVO sectionVO, Map<String, DictHisVO> dictHisMap) {
        QuickVisitReq quickVisitReq = new QuickVisitReq();
        quickVisitReq.setIdNo(patientVO.getLicenseNumber());
        quickVisitReq.setName(patientVO.getName());
        quickVisitReq.setBirthday(patientVO.getBirthday() != null ? DateTimeUtils.convertDateTimeToString(patientVO.getBirthday().atStartOfDay()) : null);
        quickVisitReq.setGender(dictHisMap.get(patientVO.getSex()).getHisId());
        quickVisitReq.setTel(patientVO.getContactPhone());
        quickVisitReq.setDeptId(sectionVO.getHisId());
        quickVisitReq.setDoctorId(doctorVO.getHisId());
        quickVisitReq.setVisitMode("1");
        return quickVisitReq;
    }

    private SaveInquiryRecordReq buildSaveInquiryRecordReq(MixConsultVO consultVO, DoctorInfoVO doctorVO, PatientVO patientVO, SectionVO sectionVO, Map<String, DictHisVO> dictHisMap) {
        SaveInquiryRecordReq saveInquiryRecordReq = new SaveInquiryRecordReq();
        saveInquiryRecordReq.setVisitId(consultVO.getHisVisitId());
        saveInquiryRecordReq.setPatientId(consultVO.getHisPatientId());
        saveInquiryRecordReq.setVisitNo(consultVO.getHisVisitNo());
        saveInquiryRecordReq.setName(patientVO.getName());
        saveInquiryRecordReq.setGender(dictHisMap.get(patientVO.getSex()).getHisId());
        saveInquiryRecordReq.setDeptId(sectionVO.getHisId());
        saveInquiryRecordReq.setDoctorId(doctorVO.getHisId());

        // 结论
        ConsultConclusionVO conclusion = consultVO.getConclusion();
        saveInquiryRecordReq.setBs(conclusion.getMedicalHistoryDesc());
        saveInquiryRecordReq.setLczd(conclusion.getClinicalDiagnosis());
        saveInquiryRecordReq.setClyj(conclusion.getConclusion());
        List<SaveInquiryRecordReq.HisDiagnoseVO> hisDiagnoseVOS = getHisDiagnoseVOS(conclusion);
        saveInquiryRecordReq.setDiagnoseList(hisDiagnoseVOS);
        return saveInquiryRecordReq;
    }

    private static List<SaveInquiryRecordReq.HisDiagnoseVO> getHisDiagnoseVOS(ConsultConclusionVO conclusion) {
        List<SaveInquiryRecordReq.HisDiagnoseVO> hisDiagnoseVOS = new ArrayList<>();
        List<ConsultDiagnosisVO> diagnosis = conclusion.getDiagnosis();
        for (ConsultDiagnosisVO consultDiagnosisVO : diagnosis) {
            SaveInquiryRecordReq.HisDiagnoseVO hisDiagnoseVO = new SaveInquiryRecordReq.HisDiagnoseVO();
            hisDiagnoseVO.setDiagnosMode("1");
//            hisDiagnoseVO.setSerialNo() // 非必填
            hisDiagnoseVO.setIsMainDiagnos(true);
            hisDiagnoseVO.setIcdCode(consultDiagnosisVO.getIcdCode());
            hisDiagnoseVO.setDiseaseName(consultDiagnosisVO.getStandardName());
            hisDiagnoseVO.setDiagnosStatus("2");
//            hisDiagnoseVO.setChineseSyndrome() // 中医才需要
//            hisDiagnoseVO.setChineseSyndromeCode() // 中医才需要
            hisDiagnoseVO.setDiagnosTime(DateTimeUtils.convertDateTimeToString(conclusion.getCreateTime()));
            hisDiagnoseVOS.add(hisDiagnoseVO);
        }
        return hisDiagnoseVOS;
    }

    private void updateConsultHisData(Long id, HisQuickVisitVO data) {
        UpdateConsultHisDataVO updateConsultHisDataVO = new UpdateConsultHisDataVO();
        updateConsultHisDataVO.setId(id);
        updateConsultHisDataVO.setHisVisitId(data.getVisitId());
        updateConsultHisDataVO.setHisVisitNo(data.getVisitNo());
        updateConsultHisDataVO.setHisPatientId(data.getPatientId());
        consultFeign.updateConsultHisData(updateConsultHisDataVO);
    }


    public void sendSupervise(Long consultId) {
        try {
            MixConsultVO consultVO = details(consultId);
            if (consultVO == null || !consultVO.getConsultState().equals(ConsultStateEnum.FINISH)) {
                log.info("问诊结束 推送监管平台 : 问诊信息为空");
                return;
            }

            DoctorInfoVO doctorVO = doctorInfoFeign.querySingle(consultVO.getDoctorId());
            if (doctorVO == null) {
                log.info("问诊结束 推送监管平台 : 医生信息为空");
                return;
            }

            PatientVO patientVO = patientFeign.getPatientById(consultVO.getPatientId());
            if (patientVO == null) {
                log.info("问诊结束 推送监管平台 : 患者信息为空");
                return;
            }

            SectionVO sectionVO = sectionFeign.querySingle(consultVO.getDepartmentId());
            if (sectionVO == null) {
                log.info("问诊结束 推送监管平台 : 科室信息为空");
                return;
            }

            UploadFurtherConsult param = buildUploadFurtherConsult(consultVO, doctorVO, patientVO, sectionVO);
            CommonResult<String> result = superviseService.uploadFurtherConsult(param);
            if (!result.isSuccess()) {
                throw new ServiceException(result.getCode(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("问诊结束 推送监管平台 : 异常", e);
        }
    }

    private UploadFurtherConsult buildUploadFurtherConsult(MixConsultVO consultVO, DoctorInfoVO doctorVO, PatientVO patientVO, SectionVO sectionVO) {
        UploadFurtherConsult param = new UploadFurtherConsult();
        param.setThirdUniqueid(String.valueOf(consultVO.getId()));
        param.setSection(sectionVO.getName());
        param.setSectionCode(sectionVO.getPlatformCode());
        param.setDocName(consultVO.getDoctorName());
        param.setCertificateNum(doctorVO.getMedicalCertificateNumber());
        param.setPatientName(consultVO.getPatientName());
        param.setPatientAge(String.valueOf(patientVO.getAge()));
        param.setPatientSex(patientVO.getSex().equals("SEX_female") ? "女" : "男");
        param.setPatientIdcardNum(patientVO.getLicenseNumber());
        param.setFurtherConsultNo(consultVO.getConsultNo());
        param.setMedicalHistory(consultVO.getConclusion().getMedicalHistoryDesc());
        param.setConsultDiagnosis(consultVO.getPatientUserInfo().getDiseases());
        param.setConsultTime(DateTimeUtils.convertDateTimeToString(consultVO.getPatientUserInfo().getDiagnosisDate().atStartOfDay()));
        param.setConsultOrg(consultVO.getPatientUserInfo().getDiagnosisHospital());
        param.setFurtherConsultApplyTime(DateTimeUtils.convertDateTimeToString(consultVO.getVisitTime()));
        param.setFurtherConsulStartTime(DateTimeUtils.convertDateTimeToString(consultVO.getVisitStartTime()));
        param.setFurtherConsulEndTime(DateTimeUtils.convertDateTimeToString(consultVO.getVisitEndTime()));
        List<String> systemName = consultVO.getConclusion().getDiagnosis().stream().map(ConsultDiagnosisVO::getSystemName).collect(Collectors.toList());
        List<String> icdCode = consultVO.getConclusion().getDiagnosis().stream().map(ConsultDiagnosisVO::getIcdCode).collect(Collectors.toList());
        param.setFurtherConsultDiagnosis(String.join("|", systemName));
        param.setFurtherConsultDiagnosisNo(String.join("|", icdCode));
        param.setFurtherConsultPrice(NumberUtils.fenToYuanToBigDecimal(Long.valueOf(consultVO.getTotalAmount())));
        param.setPatientEvaluate(1);
        param.setComplainInfo("无");
        param.setDisposeResult("无");
        param.setIsRiskWarn(1);
        param.setIsPatientSign(1);
        param.setIsPrescription(consultVO.getLastMedicalAdvice() != null ? 1 : 0);
        param.setUploadTime(DateTimeUtils.getNowDayTimeString());
        return param;
    }


    public List<ConsultOfflineVO> offlineList(ConsultOfflineReqVO param) {
        PatientVO patient = patientFeign.getPatientById(param.getPatientId());
        if (patient == null || StringUtils.isBlank(patient.getName()) || StringUtils.isBlank(patient.getLicenseNumber())) {
            return new ArrayList<>();
        }
        QueryVisitHistoryReq query = new QueryVisitHistoryReq();
        query.setName(patient.getName());
        query.setIdNo(patient.getLicenseNumber());
        CommonResult<List<HisVisitHistoryVO>> listCommonResult = hisService.visitHistory(query);
        if (!listCommonResult.isSuccess()) {
            log.info("线下门诊列表-获取失败：{}", listCommonResult.getMsg());
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(listCommonResult.getData())) {
            return new ArrayList<>();
        }
        List<ConsultOfflineVO> result = new ArrayList<>();
        for (HisVisitHistoryVO datum : listCommonResult.getData()) {
            if (param.getStartTime() != null && datum.getVisitDate().isBefore(param.getStartTime())) {
                continue;
            }
            if (param.getEndTime() != null && datum.getVisitDate().isAfter(DateTimeUtils.someTimeEnd(param.getEndTime().toLocalDate()))) {
                continue;
            }
            ConsultOfflineVO consultOfflineVO = CopyPropertiesUtil.normalCopyProperties(datum, ConsultOfflineVO.class);
            consultOfflineVO.setPatientId(patient.getPatientId());
            consultOfflineVO.setPatientName(patient.getName());
            consultOfflineVO.setPatientAge(patient.getAge());
            consultOfflineVO.setOrderList(CopyPropertiesUtil.normalCopyProperties(datum.getOrderList(), ConsultOfflineVO.HisOrderVO.class));
            consultOfflineVO.setDiagList(CopyPropertiesUtil.copyAndConvertList(datum.getDiagList(),
                    ConsultOfflineVO.HisDiagVO::new, (source, target) -> {
                        target.setIsMainDiagnos(source.getIsMainDiagnos());
                        target.setIcdCode(source.getIcdCode());
                        target.setDiseaseName(source.getDiseaseName());
                        target.setDiagnosMode(source.getDiagnosMode().equals("1") ? "西医诊断" : "中医诊断");
                    }));
            result.add(consultOfflineVO);
        }

        if (param.getSortType() != null && param.getSortType() == 1) {
            result = result.stream().sorted(Comparator.comparing(ConsultOfflineVO::getVisitDate)).collect(Collectors.toList());
        } else {
            result = result.stream().sorted(Comparator.comparing(ConsultOfflineVO::getVisitDate).reversed()).collect(Collectors.toList());
        }
        return result;
    }

    public void closeVideo(Long consultId, Long userId) {
        ConsultVO details = consultFeign.details(consultId);
        AssertUtils.notNull(details, ShiftErrorCodeConstants.CONSULT_IS_NULL);

        ConsultVideoVO consultVideoVO = consultFeign.closeVideo(consultId, userId);

        mixConsultSendMessage.noticeCloseVideo(userId, consultId);

        if (consultVideoVO.getSaveVideoFlag()) {
            String messageId = UUID.randomUUID().toString();
            // 缓存请求messageId
            stringRedisTemplate.opsForValue().set(CLOSE_VIDEO_STATUS_KAY + messageId, messageId, 1, TimeUnit.HOURS);

            // 保存视频、语音、随访小结等
            consultVideoVO.setMessageId(messageId);
            rocketMQService.send(ConsultTopic.CloseVideo.DESTINATION, consultVideoVO);
        }
    }

    public void updateConsult(UpdateConsultVO param) {
        consultFeign.updateConsult(param);
        if (param.getPlanVisitStartTime() != null && param.getPlanVisitEndTime() != null) {
            mixConsultSendMessage.noticeUpdateConsultTime(param.getConsultId(), param.getPlanVisitStartTime());
        }
    }

    public RoomCreateRespVO startVideo(Long userId, Long consultId) {
        RoomCreateRespVO roomCreateRespVO = consultFeign.startVideo(userId, consultId);
        mixConsultSendMessage.noticeStartVideo(userId, consultId);
        return roomCreateRespVO;
    }

    public Boolean checkMessageId(String messageId) {
        String key = CLOSE_VIDEO_STATUS_KAY + messageId;
        try {
            String value = stringRedisTemplate.opsForValue().getAndDelete(key);
            return value != null;
        } catch (Exception e) {
            log.error("Redis operation failed for messageId: {}", messageId, e);
            return false;
        }
    }

    public void saveVideo(SaveVideoReqVO reqVO) {
        // 保存视频
        String videoUrl = consultFeign.saveVideo(reqVO);
        // 视频转语音
        if (StringUtils.isNotBlank(videoUrl)) {
            consultFeign.saveChat(aiConfig.getTranscribeUrl(), reqVO.getConsultId(), videoUrl, reqVO.getRoomName());
        }
    }


}
