package com.ykl.med.mix.service.symptom;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.rule.RuleManagerFeign;
import com.ykl.med.mix.config.AiConfig;
import com.ykl.med.rehab.api.NutritionalExecuteFeign;
import com.ykl.med.rehab.api.NutritionalFeign;
import com.ykl.med.rehab.vo.req.nutritional.QueryExecDayReportReqVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalExecDayFoodVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalExecDayReportVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalExecDayVO;
import com.ykl.med.symptoms.api.report.IntelligentReportFeign;
import com.ykl.med.symptoms.vo.report.IntelligentReportExtra;
import com.ykl.med.symptoms.vo.report.IntelligentReportReasonReqVO;
import com.ykl.med.symptoms.vo.report.IntelligentReportSummaryReqVO;
import com.ykl.med.symptoms.vo.report.IntelligentReportVO;
import com.ykl.med.symptoms.vo.report.drug.IntelligentReportDrugVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class MixIntelligentReportService {
    @Resource
    private RuleManagerFeign ruleManagerFeign;
    @Resource
    private IntelligentReportFeign intelligentReportFeign;
    @Resource
    private AiConfig aiConfig;
    @Resource
    private NutritionalExecuteFeign nutritionalExecuteFeign;
    @Resource
    private NutritionalFeign nutritionalFeign;

    public void aiNutritionReport(IntelligentReportVO vo) {
        JSONObject request = getPatientInfo(vo.getPatientId());
        if (request == null) {
            return;
        }
        RestTemplate restTemplate = new RestTemplate();
        JSONObject result = restTemplate.postForObject(aiConfig.getNutritionReportUrl(), request, JSONObject.class);
        log.info("AI aiNutritionReport result: {}", result);
        if (checkResult(result,vo.getId())) {
            JSONObject data = result.getJSONObject("data");
            IntelligentReportReasonReqVO reqVO = JSONObject.toJavaObject(data, IntelligentReportReasonReqVO.class);
            reqVO.setId(vo.getId());
            intelligentReportFeign.reportGenerated(reqVO);
        }
    }

    public void aiNutritionIntakeAssessmentReportUrl(IntelligentReportVO vo) {
        JSONObject request = getPatientInfo(vo.getPatientId());
        if (request == null) {
            return;
        }
        RestTemplate restTemplate = new RestTemplate();

        QueryExecDayReportReqVO execDayReportReqVO = new QueryExecDayReportReqVO();
        execDayReportReqVO.setPatientId(vo.getPatientId());
        execDayReportReqVO.setPageNo(1);
        execDayReportReqVO.setPageSize(999);
        execDayReportReqVO.setStartTime(vo.getStartDate().atStartOfDay());
        execDayReportReqVO.setEndTime(vo.getEndDate().atTime(23, 59, 59));
        PageResult<NutritionalExecDayReportVO> pageResult = nutritionalExecuteFeign.queryExecDayPage(execDayReportReqVO);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
            intelligentReportFeign.reportGenerated(new IntelligentReportReasonReqVO().setId(vo.getId()).setSummary("此时间范围没打卡"));
            return;
        }
        JSONArray intakeData = new JSONArray();
        //把pageResult.getList()  按天分组
        for (NutritionalExecDayReportVO dayReportVO : pageResult.getList()) {
            JSONObject intakeDataObj = new JSONObject();
            //格式为YYYY-MM-DD
            intakeDataObj.put("recordDate", LocalDateTimeUtil.format(dayReportVO.getTime(), "yyyy-MM-dd"));
            JSONArray meals = new JSONArray();
            for (NutritionalExecDayVO nutritionalExecDayVO : dayReportVO.getList()) {
                JSONObject mealsObj = new JSONObject();
                mealsObj.put("mealType", nutritionalExecDayVO.getMealsType().getDesc());
                JSONArray food = new JSONArray();
                for (NutritionalExecDayFoodVO foodVO : nutritionalExecDayVO.getFood()) {
                    JSONObject foodObj = new JSONObject();
                    foodObj.put("foodName", foodVO.getName());
                    foodObj.put("quantity", foodVO.getWeight());
                    foodObj.put("unit", foodVO.getWeightUnit());
                    foodObj.put("calories", foodVO.getCalories());
                    food.add(foodObj);
                }
                mealsObj.put("foodItems", food);
                meals.add(mealsObj);
            }
            intakeDataObj.put("meals", meals);
            intakeData.add(intakeDataObj);
        }
//        JSONObject requestObj = new JSONObject();
//        requestObj.put("patientInfo", request);
        request.put("intakeData", intakeData);
        log.info("AI aiNutritionIntakeAssessmentReportUrl request: {}", JSONObject.toJSONString(intakeData));
        JSONObject result = restTemplate.postForObject(aiConfig.getNutritionIntakeAssessmentReportUrl(), request, JSONObject.class);
        log.info("AI aiNutritionIntakeAssessmentReportUrl result: {}", result);
        if (checkResult(result,vo.getId())) {
            JSONObject data = result.getJSONObject("data");
            IntelligentReportReasonReqVO reqVO = JSONObject.toJavaObject(data, IntelligentReportReasonReqVO.class);
            reqVO.setId(vo.getId());
            intelligentReportFeign.reportGenerated(reqVO);
        }
    }

    public void aiInterpretationOfInspectionReport(IntelligentReportVO vo) {
        JSONObject request = getPatientInfo(vo.getPatientId());
        if (request == null) {
            return;
        }
        RestTemplate restTemplate = new RestTemplate();
        JSONObject result = restTemplate.postForObject(aiConfig.getInterpretationOfInspectionReportUrl(), request, JSONObject.class);
        log.info("AI interpretationOfInspectionReport result: {}", result);
        if (checkResult(result,vo.getId())) {
            JSONObject data = result.getJSONObject("data");
            IntelligentReportReasonReqVO reqVO = JSONObject.toJavaObject(data, IntelligentReportReasonReqVO.class);
            reqVO.setId(vo.getId());
            intelligentReportFeign.reportGenerated(reqVO);
        }
    }


    public void aiInterpretationOfImageReport(IntelligentReportVO vo) {
        JSONObject request = getPatientInfo(vo.getPatientId());
        if (request == null) {
            return;
        }
        RestTemplate restTemplate = new RestTemplate();
        JSONObject result = restTemplate.postForObject(aiConfig.getInterpretationOfImageReportUrl(), request, JSONObject.class);
        log.info("AI interpretationOfImageReport result: {}", result);
        if (checkResult(result,vo.getId())) {
            JSONObject data = result.getJSONObject("data");
            IntelligentReportReasonReqVO reqVO = JSONObject.toJavaObject(data, IntelligentReportReasonReqVO.class);
            reqVO.setId(vo.getId());
            intelligentReportFeign.reportGenerated(reqVO);
        }
    }


    public void aiMedicationInstructionsReport(IntelligentReportVO vo) {
        JSONObject request = getPatientInfo(vo.getPatientId());
        if (request == null) {
            return;
        }
        RestTemplate restTemplate = new RestTemplate();
        JSONObject result = restTemplate.postForObject(aiConfig.getMedicationInstructionsReportUrl(), request, JSONObject.class);
        log.info("AI aiMedicationInstructionsReport result: {}", result);
        if (checkResult(result,vo.getId())) {
            JSONObject data = result.getJSONObject("data");
            IntelligentReportReasonReqVO reqVO = JSONObject.toJavaObject(data, IntelligentReportReasonReqVO.class);
            reqVO.setId(vo.getId());
            IntelligentReportExtra extra = JSONObject.toJavaObject(data, IntelligentReportExtra.class);
            reqVO.setExtra(extra);
            if (data.containsKey("details")) {
                JSONArray drugs = data.getJSONArray("details");
                List<IntelligentReportDrugVO> drugsList = new ArrayList<>();
                for (Object object : drugs) {
                    JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(object));
                    IntelligentReportDrugVO intelligentReportDrugVO = JSONObject.toJavaObject(jsonObject, IntelligentReportDrugVO.class);
                    if (!jsonObject.containsKey("id")) {
                        continue;
                    }
                    intelligentReportDrugVO.setDrugId(jsonObject.getLong("id"));
                    intelligentReportDrugVO.setId(null);
                    drugsList.add(intelligentReportDrugVO);
                }
                reqVO.setDrugs(drugsList);
            }
            intelligentReportFeign.reportGenerated(reqVO);
        }
    }

    public void aiSymptomReport(IntelligentReportVO vo) {
        JSONObject request = getPatientInfo(vo.getPatientId());
        if (request == null) {
            return;
        }
        RestTemplate restTemplate = new RestTemplate();
        JSONObject result = restTemplate.postForObject(aiConfig.getSymptomReportUrl(), request, JSONObject.class);
        log.info("AI SymptomReport result: {}", result);
        if (checkResult(result,vo.getId())) {
            JSONObject data = result.getJSONObject("data");
            IntelligentReportReasonReqVO reqVO = JSONObject.toJavaObject(data, IntelligentReportReasonReqVO.class);
            reqVO.setId(vo.getId());
            intelligentReportFeign.reportGenerated(reqVO);
        }
    }


    public String aiSymptomReportSummary(IntelligentReportSummaryReqVO reqVO) {
        RestTemplate restTemplate = new RestTemplate();
        JSONObject request = this.getPatientInfo(reqVO.getPatientId());
        if (request == null) {
            return "";
        }
        request.put("report", reqVO.getReport());
        JSONObject result = restTemplate.postForObject(aiConfig.getSymptomReportSummaryUrl(), request, JSONObject.class);
        log.info("AI SymptomReport Summary result: {}", result);
        if (result != null && result.containsKey("data")) {
            JSONObject data = result.getJSONObject("data");
            return data.getString("summary");
        }
        return "";
    }

    public String aiNutritionOnlySummary(IntelligentReportSummaryReqVO reqVO) {
        JSONObject request = getPatientInfo(reqVO.getPatientId());
        if (request == null) {
            return "";
        }
        RestTemplate restTemplate = new RestTemplate();
        JSONObject result = restTemplate.postForObject(aiConfig.getNutritionOnlySummaryUrl(), request, JSONObject.class);
        log.info("AI nutritionOnlySummary result: {}", result);
        if (result != null && result.containsKey("data")) {
            JSONObject data = result.getJSONObject("data");
            return data.getString("summary");
        }
        return "";
    }


    public String aiNutritionIntakeAssessmentOnlySummary(Long  intelligentReportId) {
        IntelligentReportVO reqVO = intelligentReportFeign.getById(intelligentReportId);

    }

    private JSONObject getPatientInfo(Long patientId) {
        Object patientInfo = ruleManagerFeign.portraitQuery(patientId);
        if (patientInfo == null) {
            log.info("大数据 AI SymptomReport Summary patientInfo is null, patientId: {}", patientId);
            return null;
        }
        JSONObject request = new JSONObject();
        request.put("patientInfo", patientInfo);
        return request;
    }

    private Boolean checkResult(JSONObject result, Long reportId) {
        if (result == null) {
            return false;
        }
        if (result.containsKey("code") && result.getInteger("code") != 200) {
            //参数异常，就终止报告生成
            intelligentReportFeign.reportGenerated(new IntelligentReportReasonReqVO()
                    .setId(reportId)
                    .setSummary("AI服务提示异常," + result.toJSONString()));
            return false;
        }
        return result.containsKey("data");
    }

    private JSONObject getNutritionIntakeAssessment
}
