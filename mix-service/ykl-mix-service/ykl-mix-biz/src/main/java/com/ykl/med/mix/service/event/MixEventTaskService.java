package com.ykl.med.mix.service.event;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.service.event.strategy.EventTaskStrategy;
import com.ykl.med.mix.service.event.strategy.EventTaskTypeAnnotation;
import com.ykl.med.mix.service.user.MixUserService;
import com.ykl.med.mix.vo.UserInfoVO;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.enums.PatientStatus;
import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.push.vo.event.EventTaskVO;
import com.ykl.med.user.vo.req.SimpleUserPageReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class MixEventTaskService {
    @Resource
    private MixUserService mixUserService;
    @Resource
    private PatientFeign patientFeign;
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private PushSocketFeign pushSocketFeign;
    @Resource
    private EventTaskFeign eventTaskFeign;
    @Resource
    private ApplicationContext applicationContext;
    private final Map<EventTaskType, EventTaskStrategy> strategies = new HashMap<>();

    @PostConstruct
    public void init() {
        Map<String, Object> strategyBeans = applicationContext.getBeansWithAnnotation(EventTaskTypeAnnotation.class);
        for (Object bean : strategyBeans.values()) {
            if (bean instanceof EventTaskStrategy) {
                EventTaskTypeAnnotation annotation = bean.getClass().getAnnotation(EventTaskTypeAnnotation.class);
                EventTaskType[] types = annotation.value();
                for (EventTaskType type : types) {
                    if (strategies.containsKey(type)) {
                        throw new RuntimeException("EventTaskType重复:" + type);
                    }
                    strategies.put(type, (EventTaskStrategy) bean);
                }
            }
        }
    }

    /**
     * 执行事件任务
     *
     * @param eventTaskVO 事件任务
     */
    public void executeEventTask(EventTaskVO eventTaskVO) {
        log.info("executeEventTask req:{}", JSONObject.toJSONString(eventTaskVO));
        EventTaskStrategy strategy = strategies.get(eventTaskVO.getBizType());
        if (strategy != null) {
            strategy.handleEvent(eventTaskVO);
        }
        eventTaskFeign.success(eventTaskVO.getId());
    }


}
