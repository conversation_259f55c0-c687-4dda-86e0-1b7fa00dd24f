package com.ykl.med.mix.mq.rocket;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.constants.topic.PatientTopic;
import com.ykl.med.framework.common.pojo.IdMessage;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.medical.api.MedicalOrderFeign;
import com.ykl.med.medical.enums.MedicalOrderStatus;
import com.ykl.med.medical.vo.order.MedicalOrderListWebVO;
import com.ykl.med.medical.vo.order.MedicalOrderQueryWebVO;
import com.ykl.med.mix.service.records.MixRecordService;
import com.ykl.med.patient.api.PatientEsFeign;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.patient.vo.patient.LifestyleHistoryEs;
import com.ykl.med.patient.vo.patient.MedicalOrderEs;
import com.ykl.med.patient.vo.patient.SavePatientEsVO;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.records.vo.DriverGeneVO;
import com.ykl.med.records.vo.GeneticTestingVO;
import com.ykl.med.records.vo.resp.FullRecordRespVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/30
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = PatientTopic.TOPIC,
        consumerGroup = PatientTopic.TOPIC + PatientTopic.UpdateMedicalHistory.SELECTOR_EXPRESSION,
        selectorExpression = PatientTopic.UpdateMedicalHistory.SELECTOR_EXPRESSION
)
public class PatientUpdateMedicalHistoryListener implements RocketMQListener<IdMessage> {

    private final PatientEsFeign patientEsFeign;
    private final PatientFeign patientFeign;
    private final MedicalOrderFeign medicalOrderFeign;
    private final MixRecordService mixRecordService;

    @Override
    public void onMessage(IdMessage message) {
        log.info("【更新患者信息】 监听：request:{}", JSONObject.toJSONString(message));
        PatientVO patientVO = patientFeign.getPatientById(message.getId());
        if (patientVO == null) {
            log.info("【更新患者信息】监听-失败:患者信息为null");
            return;
        }
        FullRecordRespVO fullRecord = mixRecordService.getFullRecord(message.getId());
        try {
            SavePatientEsVO req = buildPatientHistory(patientVO, fullRecord);
            patientEsFeign.saveOrUpdate(req);
            log.info("【更新患者信息】监听-刷新ES成功");
        } catch (Exception e) {
            log.info("【更新患者信息】监听-失败:{}", e.getMessage(), e);
        }
    }

    private SavePatientEsVO buildPatientHistory(PatientVO patientVO, FullRecordRespVO fullRecordRespVO) {
        SavePatientEsVO savePatientEsVO = new SavePatientEsVO();
        savePatientEsVO.setPatientId(patientVO.getPatientId());
        savePatientEsVO.setStatus(patientVO.getStatus());
        savePatientEsVO.setNameCode(patientVO.getNameCode());
        savePatientEsVO.setName(patientVO.getName());
        savePatientEsVO.setSex(patientVO.getSex());
        savePatientEsVO.setAge(patientVO.getAge());
        savePatientEsVO.setContactPhone(patientVO.getContactPhone());
        savePatientEsVO.setMedicalTeamId(patientVO.getMedicalTeamId());
        savePatientEsVO.setBindDoctorId(patientVO.getBindDoctorId());
        savePatientEsVO.setMemberVersionTime(patientVO.getMemberVersionTime());
        if (fullRecordRespVO.getBasicReport() != null) {
            BasicReportVO basicReport = fullRecordRespVO.getBasicReport();
            savePatientEsVO.setDiseaseId(basicReport.getDiseaseId());
            savePatientEsVO.setStage(basicReport.getStage());
            savePatientEsVO.setSurgery(basicReport.getSurgery());
            savePatientEsVO.setSurgeryName(basicReport.getSurgeryName());
            savePatientEsVO.setSurgeryDate(basicReport.getSurgeryDate());
            savePatientEsVO.setClinicalStaging(basicReport.getClinicalStaging());
            savePatientEsVO.setNutritionAssessmentResult(basicReport.getNutritionAssessmentResult());
            savePatientEsVO.setPtnmStaging(basicReport.getPtnmStaging());
            savePatientEsVO.setCtnmStaging(basicReport.getCtnmStaging());
            savePatientEsVO.setPathologicalType(basicReport.getPathologicalType());
            savePatientEsVO.setOtherDiseaseIds(basicReport.getOtherDiseaseIds());
            savePatientEsVO.setComplicationIds(basicReport.getComplicationIds());
            savePatientEsVO.setNeedPostOpTreatment(basicReport.getNeedPostOpTreatment());
            savePatientEsVO.setPostOpTreatmentMethods(basicReport.getPostOpTreatmentMethods());
            savePatientEsVO.setMedicalHistoryDescription(basicReport.getMedicalHistoryDescription());
            savePatientEsVO.setAdverseReactions(basicReport.getAdverseReactions());
            savePatientEsVO.setCurrentTreatmentStatuses(basicReport.getCurrentTreatmentStatuses());
        }
        // 生活方式
        List<LifestyleHistoryEs> lifestyleHistory = Optional.ofNullable(fullRecordRespVO.getLifestyleHistories())
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(e -> e.getStatus() != null)
                .map(e -> {
                    LifestyleHistoryEs lifestyleHistoryEs = new LifestyleHistoryEs();
                    lifestyleHistoryEs.setType(e.getType());
                    lifestyleHistoryEs.setStatus(e.getStatus());
                    lifestyleHistoryEs.setLifestyleYears(e.getLifestyleYears());
                    lifestyleHistoryEs.setQuitYears(e.getQuitYears());
                    lifestyleHistoryEs.setDailyAmount(e.getDailyAmount());
                    return lifestyleHistoryEs;
                }).collect(Collectors.toList());
        // 基因突变
        List<String> allDriverGenes = Optional.ofNullable(fullRecordRespVO.getGeneticTestings())
                .orElseGet(Collections::emptyList)
                .stream()
                .map(GeneticTestingVO::getDriverGenes)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(DriverGeneVO::getDictValue)
                .collect(Collectors.toList());
        // 用药
        MedicalOrderQueryWebVO medicalOrderQueryWebVO = new MedicalOrderQueryWebVO()
                .setPatientId(patientVO.getPatientId())
                .setStatus(MedicalOrderStatus.ENABLE)
                .setStartExecTimeSort("ASC");
        medicalOrderQueryWebVO.setPageSize(10000);
        PageResult<MedicalOrderListWebVO> medicalOrderPageResult = medicalOrderFeign.queryWeb(medicalOrderQueryWebVO);
        List<MedicalOrderEs> medicalOrder = Optional.ofNullable(medicalOrderPageResult.getList())
                .orElseGet(Collections::emptyList)
                .stream()
                .map(e -> {
                    MedicalOrderEs medicalOrderEs = new MedicalOrderEs();
                    medicalOrderEs.setDrugName(e.getItemName());
                    medicalOrderEs.setProducerName(e.getItemProducerName());
                    return medicalOrderEs;
                }).collect(Collectors.toList());
        savePatientEsVO.setLifestyleHistory(lifestyleHistory);
        savePatientEsVO.setGeneticTesting(allDriverGenes);
        savePatientEsVO.setMedicalOrder(medicalOrder);
        return savePatientEsVO;
    }
}
