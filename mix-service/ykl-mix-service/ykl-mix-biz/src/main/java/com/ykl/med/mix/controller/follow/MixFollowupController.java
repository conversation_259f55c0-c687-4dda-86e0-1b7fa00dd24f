package com.ykl.med.mix.controller.follow;

import com.ykl.med.followup.entity.AiFollowupSummaryVO;
import com.ykl.med.followup.entity.param.*;
import com.ykl.med.followup.entity.vo.FollowupInspectVO;
import com.ykl.med.followup.entity.vo.FollowupListByDoctorGroupVO;
import com.ykl.med.followup.entity.vo.FollowupTrackVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.follow.MixFollowupFeign;
import com.ykl.med.mix.service.follow.MixFollowupService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/28
 */
@RestController
@RequestMapping("/mix/follow")
public class MixFollowupController implements MixFollowupFeign {


    @Resource
    private MixFollowupService mixFollowupService;

    @Override
    @PostMapping("/followupListByDoctorGroup")
    public PageResult<FollowupListByDoctorGroupVO> followupListByDoctorGroup(@Valid @RequestBody QueryFollowupListByDoctorGroupParam param) {
        return mixFollowupService.followupListByDoctorGroup(param);
    }

    @Override
    @PostMapping("/list")
    public List<FollowupTrackVO> list(@Valid @RequestBody QueryFollowupTrackParam param) {
        return mixFollowupService.list(param);
    }

    @Override
    @PostMapping("/details")
    public FollowupTrackVO details(@RequestParam(name = "id") Long id) {
        return mixFollowupService.details(id);
    }


    @Override
    @PostMapping("/info")
    public FollowupInspectVO info(@Valid @RequestBody QueryFollowupInspectInfoParam param) {
        return mixFollowupService.info(param);
    }

    @Override
    @PostMapping("/current")
    public FollowupTrackVO current(@Valid @RequestBody QueryCurrentFollowupTrackParam param) {
        return mixFollowupService.current(param);
    }

    @Override
    @PostMapping("/create")
    public void create(@Valid @RequestBody CreateFollowupTrackParam param) {
        mixFollowupService.create(param);
    }

    @Override
    @PostMapping("/start")
    public void start(@Valid @RequestBody StartFollowupTrackParam param) {
        mixFollowupService.start(param);
    }

    @Override
    @PostMapping("/finish")
    public void finish(@Valid @RequestBody FinishFollowupTrackParam param) {
        mixFollowupService.finish(param);
    }

    @Override
    @PostMapping("/finishItemQuestion")
    public void finishItemQuestion(@Valid @RequestBody FinishItemQuestionParam param) {
        mixFollowupService.finishItemQuestion(param);
    }

    @Override
    @PostMapping("/startConsult")
    public Long startConsult(@Valid @RequestBody StartFollowupConsultReqVO reqVO) {
        return mixFollowupService.startConsult(reqVO);
    }

    @Override
    @PostMapping("/updateConsultTime")
    public void updateConsultTime(@Valid @RequestBody UpdateFollowupConsultTimeReqVO reqVO) {
        mixFollowupService.updateConsultTime(reqVO);
    }

    @Override
    @PostMapping("/initAllFollowupTrackState")
    public void initAllStateByNuStart() {
        mixFollowupService.initAllFollowupTrackState();
    }


}
