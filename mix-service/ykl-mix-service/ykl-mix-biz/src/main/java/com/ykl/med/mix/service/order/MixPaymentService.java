package com.ykl.med.mix.service.order;

import com.ykl.med.mix.vo.order.MixPayOrderCreateReqVO;
import com.ykl.med.mix.vo.order.MixPayOrderCreateRespVO;
import com.ykl.med.mix.vo.order.MixPaySuccessOrderReqVO;

public interface MixPaymentService {
    MixPayOrderCreateRespVO createPayOrder(MixPayOrderCreateReqVO reqVO);

    void paySuccessOrder(MixPaySuccessOrderReqVO reqVO);

    void cancelOrder(String orderCode);

    void refundOrder(String orderCode);

    void refreshWaitPayOrder(String orderCode);

    void refreshRefundOrder(String orderCode);
}
