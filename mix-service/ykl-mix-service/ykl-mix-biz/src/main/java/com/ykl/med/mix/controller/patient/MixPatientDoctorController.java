package com.ykl.med.mix.controller.patient;

import com.ykl.med.doctors.entity.vo.DoctorListVO;
import com.ykl.med.mix.api.patient.MixPatientDoctorFeign;
import com.ykl.med.mix.service.patient.MixPatientDoctorService;
import com.ykl.med.mix.vo.patient.PatientDoctorBindReqVO;
import com.ykl.med.mix.vo.patient.QueryDoctorByPatientMedicalTeamReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "医生患者关系服务")
@RestController
@RequestMapping("/mixPatientDoctor")
@Validated
public class MixPatientDoctorController implements MixPatientDoctorFeign {

    @Resource
    private MixPatientDoctorService mixPatientDoctorService;


    @Override
    @PostMapping("/patientDoctorBind")
    @Operation(summary = "绑定医生和患者和医疗组")
    public void patientDoctorBind(@RequestBody PatientDoctorBindReqVO reqVO) {
        mixPatientDoctorService.patientDoctorBind(reqVO);
    }


    @Override
    @PostMapping("/getDoctorsInPatientMedicalGroup")
    @Operation(summary = "根据患者的医疗团队id查找医生列表")
    public List<DoctorListVO> getDoctorsInPatientMedicalGroup(@RequestBody QueryDoctorByPatientMedicalTeamReqVO reqVO) {
        return mixPatientDoctorService.getDoctorsInPatientMedicalGroup(reqVO);
    }


    @Override
    @PostMapping("/getPatientMedicalTeam")
    @Operation(summary = "获取患者的医疗团队")
    public List<DoctorListVO> getPatientMedicalTeam(@RequestBody QueryDoctorByPatientMedicalTeamReqVO reqVO) {
        return mixPatientDoctorService.getPatientMedicalTeam(reqVO);
    }

}
