package com.ykl.med.mix.service.follow;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.doctors.api.DoctorFeign;
import com.ykl.med.doctors.entity.vo.DoctorVO;
import com.ykl.med.followup.api.FollowupInspectFeign;
import com.ykl.med.followup.api.FollowupTrackFeign;
import com.ykl.med.followup.constants.FollowupErrorCodeConstants;
import com.ykl.med.followup.entity.AiFollowupSummaryVO;
import com.ykl.med.followup.entity.enums.FollowupItemCategory;
import com.ykl.med.followup.entity.enums.FollowupTrackStatusEnums;
import com.ykl.med.followup.entity.param.*;
import com.ykl.med.followup.entity.vo.*;
import com.ykl.med.followup.enums.FollowupTrackTypeEnum;
import com.ykl.med.framework.common.enums.ConsultChatTypeEnum;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.HutHttpUtils;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.api.DiseaseFeign;
import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.api.treatment.TreatmentProjectFeign;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.vo.TreatmentProjectVO;
import com.ykl.med.masterdata.vo.form.PatientFormAddVO;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.masterdata.vo.resp.DiseaseVO;
import com.ykl.med.mix.config.AiConfig;
import com.ykl.med.mix.service.consult.MixConsultSendMessage;
import com.ykl.med.mix.service.records.MixRecordService;
import com.ykl.med.mix.vo.DefaultFollowupDoctorVO;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskCancelVO;
import com.ykl.med.records.api.BasicReportFeign;
import com.ykl.med.records.api.RecordFeign;
import com.ykl.med.records.api.ReportFeign;
import com.ykl.med.records.enums.ReportCategory;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.records.vo.ImagingExaminationVO;
import com.ykl.med.records.vo.req.ImagingExaminationReqVO;
import com.ykl.med.records.vo.req.ReportListReqVO;
import com.ykl.med.records.vo.resp.FullRecordRespVO;
import com.ykl.med.records.vo.resp.ReportListRespVO;
import com.ykl.med.shift.api.ConsultFeign;
import com.ykl.med.shift.constans.ShiftErrorCodeConstants;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.enums.consult.ConsultTypeEnum;
import com.ykl.med.shift.vo.dto.ConsultChatVO;
import com.ykl.med.shift.vo.req.CreateByDirectlyReqVO;
import com.ykl.med.shift.vo.req.UpdateConsultVO;
import com.ykl.med.shift.vo.resp.ConsultVO;
import com.ykl.med.symptoms.api.records.SymptomFeign;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MixFollowupService {

    private final ReportFeign reportFeign;
    private final RecordFeign recordFeign;
    private final SymptomFeign symptomFeign;
    private final DiseaseFeign diseaseFeign;
    private final EventTaskFeign eventTaskFeign;
    private final BasicReportFeign basicReportFeign;
    private final FollowupTrackFeign followupTrackFeign;
    private final TreatmentProjectFeign treatmentProjectFeign;
    private final FollowupInspectFeign followupInspectFeign;
    private final ConsultFeign consultFeign;
    private final PatientFormFeign patientFormFeign;
    private final DoctorFeign doctorFeign;
    private final CommonConfigFeign commonConfigFeign;
    private final StringRedisTemplate stringRedisTemplate;
    private final PatientFeign patientFeign;
    private final MixConsultSendMessage mixConsultSendMessage;
    private final AiConfig aiConfig;
    private final MixRecordService mixRecordService;
    private final static String DEFAULT_FOLLOWUP_DOCTOR_KEY = "default_followup_doctor_key";
    private static final String FOLLOWUP_START_CONSULT_LOCK = "followup:start:consult:lock:";


    public PageResult<FollowupListByDoctorGroupVO> followupListByDoctorGroup(QueryFollowupListByDoctorGroupParam param) {
        String jsonString = commonConfigFeign.getCommonConfigValueByKey(DEFAULT_FOLLOWUP_DOCTOR_KEY);
        DefaultFollowupDoctorVO reqVO = JSONObject.parseObject(jsonString, DefaultFollowupDoctorVO.class);
        param.setDoctor_manage(reqVO.getDoctor_manage());
        PageResult<FollowupListByDoctorGroupVO> page = followupTrackFeign.followupListByDoctorGroup(param);
        if (page == null || CollectionUtils.isEmpty(page.getList())) {
            return PageResult.empty();
        }
        List<FollowupListByDoctorGroupVO> list = page.getList();
        List<Long> collect = list.stream().map(FollowupListByDoctorGroupVO::getPatientId).map(Long::parseLong).collect(Collectors.toList());
        // 获取患者疾病信息
        Map<Long, BasicReportVO> basicReportVOMap = new HashMap<>();
        List<BasicReportVO> basicReportVOList = basicReportFeign.listByPatientIds(collect);
        if (basicReportVOList != null) {
            // 获取疾病信息列表
            List<Long> diseaseIdList = basicReportVOList.stream().map(BasicReportVO::getDiseaseId).
                    filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<DiseaseVO> diseaseVOList = diseaseFeign.queryByIds(diseaseIdList);
            Map<Long, DiseaseVO> diseaseVOMap = diseaseVOList.stream().collect(Collectors.toMap(DiseaseVO::getId, Function.identity(), (key1, key2) -> key2));
            for (BasicReportVO basicReportVO : basicReportVOList) {
                if (diseaseVOMap.containsKey(basicReportVO.getDiseaseId())) {
                    basicReportVO.setDiseaseName(diseaseVOMap.get(basicReportVO.getDiseaseId()).getSystemName());
                }
            }
            basicReportVOMap = basicReportVOList.stream().collect(Collectors.toMap(BasicReportVO::getPatientId, Function.identity(), (key1, key2) -> key2));
        }
        for (FollowupListByDoctorGroupVO doctorGroupVO : list) {
            Long patientId = Long.valueOf(doctorGroupVO.getPatientId());
            if (basicReportVOMap.containsKey(patientId)) {
                doctorGroupVO.setDiagnosis(basicReportVOMap.get(patientId).getDiseaseName());
                doctorGroupVO.setStage(basicReportVOMap.get(patientId).getStage());
                doctorGroupVO.setClinicalStaging(basicReportVOMap.get(patientId).getClinicalStaging());
                doctorGroupVO.setPathologicalType(basicReportVOMap.get(patientId).getPathologicalType());
                doctorGroupVO.setSurgeryDateDistance(basicReportVOMap.get(patientId).buildSurgeryDate());
            }
        }
        return page;
    }

    public FollowupInspectVO info(QueryFollowupInspectInfoParam param) {
        FollowupInspectVO followupInspectVO = followupInspectFeign.info(param);
        if (followupInspectVO == null) {
            return null;
        }

        BasicReportVO basicReportVO = basicReportFeign.getByPatientId(followupInspectVO.getPatientId());
        if (basicReportVO != null) {
            followupInspectVO.setDiagnosis(basicReportVO.getDiseaseName());
            followupInspectVO.setStage(basicReportVO.getStage());
        }

        List<FollowupInspectItemVO> followupInspectItemVOS = followupInspectFeign.getByFollowupId(param.getFollowupId());
        followupInspectItemVOS.forEach(e -> {
            // 是表单
            if (e.getItemClass() != null && e.getItemClass().equals(FollowupItemCategory.QUESTIONNAIRES.getValue())) {
                e.setItemName("随访表单");
            }
        });
        followupInspectVO.setFollowupItemList(followupInspectItemVOS);
        // 最近一次检查报告
        queryLastReport(followupInspectVO, followupInspectItemVOS);
        return followupInspectVO;
    }


    public List<FollowupTrackVO> list(QueryFollowupTrackParam param) {
        List<FollowupTrackVO> list = followupTrackFeign.list(param);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(e -> e.setConsultEndFlag(queryConsultState(e)));
        }
        return list;
    }


    public FollowupTrackVO current(QueryCurrentFollowupTrackParam param) {
        FollowupTrackVO followupTrackVO = followupTrackFeign.current(param);
        if (followupTrackVO != null) {
            followupTrackVO.setLoginDoctorRole(verifyDoctorRole(followupTrackVO, param.getUserId()));
            List<ConsultVO> consultVOS = consultFeign.listBizId(followupTrackVO.getId());
            if (!CollectionUtils.isEmpty(consultVOS)) {
                ConsultVO consultVO = consultVOS.get(0);
                followupTrackVO.setConsultEndFlag(consultVO.getConsultState().equals(ConsultStateEnum.FINISH));
                followupTrackVO.setSaveConsultUserIdFlag(consultVO.getSaveConsultUserIdFlag());
                followupTrackVO.setConsultUserId(consultVO.getUserId());
                // 拼接聊天内容
                if (!CollectionUtils.isEmpty(consultVO.getChat())) {
                    String chatName = consultVO.getChat().get(0).getChatName();
                    List<String> collect = consultVO.getChat().stream().map(ConsultChatVO::getChatStr).collect(Collectors.toList());
                    String join = String.join("", collect);
                    followupTrackVO.setTitle(chatName);
                    followupTrackVO.setChatStr(join);
                }
            }
        }
        return followupTrackVO;
    }


    public void create(CreateFollowupTrackParam param) {
        String jsonString = commonConfigFeign.getCommonConfigValueByKey(DEFAULT_FOLLOWUP_DOCTOR_KEY);
        DefaultFollowupDoctorVO reqVO = JSONObject.parseObject(jsonString, DefaultFollowupDoctorVO.class);
        // 随机取一个
        int index = ThreadLocalRandom.current().nextInt(reqVO.getDoctor_followup().size());
        param.setDoctorId(reqVO.getDoctor_followup().get(index));
        // 只返回待确认的随访
        List<Long> followupIds = followupTrackFeign.create(param);
        for (Long followupId : followupIds) {
            handlerInfoPatientForm(followupId);
        }
    }


    public void start(StartFollowupTrackParam param) {
        FollowupTrackVO followupTrackVO = followupTrackFeign.infoWeb(new QueryFollowupTrackInfoParam(param.getFollowupId()));
        followupTrackFeign.start(param);
        // 修改问诊医生
        List<ConsultVO> consultVOS = consultFeign.listBizId(followupTrackVO.getId());
        if (!CollectionUtils.isEmpty(consultVOS)) {
            Long consultId = consultVOS.get(0).getId();
            consultFeign.updateConsult(new UpdateConsultVO().setConsultId(consultId).setDoctorId(param.getUserId()));
        }
        // 关闭 随访开始 通知
        eventTaskFeign.cancel(new EventTaskCancelVO()
                .setPatientId(followupTrackVO.getPatientId())
                .setBizId(String.valueOf(followupTrackVO.getId()))
                .setBizType(EventTaskType.FOLLOW_START)
        );
    }


    public void finishItemQuestion(FinishItemQuestionParam param) {
        List<Long> symptomIds = followupTrackFeign.finishItemQuestion(param);
        if (!CollectionUtils.isEmpty(symptomIds)) {
            // 停止症状
            symptomIds.forEach(symptomFeign::stopSymptom);
        }
    }


    public Long startConsult(StartFollowupConsultReqVO reqVO) {
        FollowupTrackVO followupTrackVO = followupTrackFeign.infoWeb(new QueryFollowupTrackInfoParam(reqVO.getFollowupId()));
        AssertUtils.notNull(followupTrackVO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);
        AssertUtils.isTrue(followupTrackVO.getFollowupState().equals(FollowupTrackStatusEnums.STARTING), FollowupErrorCodeConstants.FOLLOWUP_STATE_IS_NOT_STARTING);
        Integer role = verifyDoctorRole(followupTrackVO, reqVO.getUserId());
        checkFollowupDoctor(role, followupTrackVO.getDoctorId(), reqVO.getUserId());
        String followupId = reqVO.getFollowupId().toString();
        String lockKey = FOLLOWUP_START_CONSULT_LOCK + followupId;
        try {
            Boolean lockAcquired = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, followupId, 30, TimeUnit.SECONDS);
            if (lockAcquired != null && lockAcquired) {
                log.info("成功获取锁: followupId:{}", lockKey);

                // 一个随访只能开一个视频
                List<ConsultVO> consultVOS = consultFeign.listBizId(followupTrackVO.getId());
                if (!CollectionUtils.isEmpty(consultVOS)) {
                    Long consultId = consultVOS.get(0).getId();
                    // 修改问诊状态
                    consultFeign.updateConsult(new UpdateConsultVO().setConsultId(consultId).setConsultState(ConsultStateEnum.STARTING));
                    return consultId;
                }

                // 创建一个默认的问诊数据，只做记录
                ConsultVO consultVO = consultFeign.createByDirectly(new CreateByDirectlyReqVO()
                        .setBizId(followupTrackVO.getId())
                        .setChatType(followupTrackVO.getFollowupMode().equals(FollowupTrackTypeEnum.followup_voice.name()) ? ConsultChatTypeEnum.VOICE_CHAT : ConsultChatTypeEnum.VIDEO_CHAT)
                        .setConsultState(ConsultStateEnum.STARTING)
                        .setConsultType(ConsultTypeEnum.FOLLOWUP_CONSULTATION)
                        .setPlanVisitStartTime(followupTrackVO.getPlanConsultStartTime())
                        .setPlanVisitEndTime(followupTrackVO.getPlanConsultEndTime())
                        .setDoctorId(reqVO.getUserId())
                        .setPatientId(followupTrackVO.getPatientId()));
                return consultVO.getId();
            } else {
                throw new ServiceException(FollowupErrorCodeConstants.LOCK_FAIL);
            }
        } finally {
            // 释放锁
            if (followupId.equals(stringRedisTemplate.opsForValue().get(lockKey))) {
                stringRedisTemplate.delete(lockKey);
                log.info("成功释放锁: {}", lockKey);
            }
        }
    }


    public void initAllFollowupTrackState() {
        List<Long> followupIds = followupTrackFeign.initAllStateByNuStart();
        if (!CollectionUtils.isEmpty(followupIds)) {
            followupIds.forEach(this::handlerInfoPatientForm);
        }
    }


    public void finish(FinishFollowupTrackParam reqVO) {
        FollowupTrackVO followupTrackVO = followupTrackFeign.infoWeb(new QueryFollowupTrackInfoParam(reqVO.getFollowupId()));
        Integer role = verifyDoctorRole(followupTrackVO, reqVO.getUserId());
        checkFollowupDoctor(role, followupTrackVO.getDoctorId(), reqVO.getUserId());
        if (followupTrackVO.getFollowupMode().equals(FollowupTrackTypeEnum.followup_video.name())
                || followupTrackVO.getFollowupMode().equals(FollowupTrackTypeEnum.followup_voice.name())) {
            if (role == 1) {
                reqVO.setStatus(FollowupTrackStatusEnums.FINISH);
            } else {
                reqVO.setStatus(FollowupTrackStatusEnums.AUDIT);
            }
        } else {
            reqVO.setStatus(FollowupTrackStatusEnums.FINISH);
        }
        followupTrackFeign.finish(reqVO);
        if (reqVO.getStatus().equals(FollowupTrackStatusEnums.FINISH)) {
            mixConsultSendMessage.noticeSummaryFollowup(reqVO.getFollowupId());
        }

        List<ConsultVO> consultVOS = consultFeign.listBizId(followupTrackVO.getId());
        if (!CollectionUtils.isEmpty(consultVOS)) {
            ConsultVO consultVO = consultVOS.get(0);
            if (!consultVO.getConsultState().equals(ConsultStateEnum.FINISH)) {
                consultFeign.updateConsult(new UpdateConsultVO()
                        .setConsultId(consultVO.getId())
                        .setConsultState(ConsultStateEnum.FINISH));
            }
        }

    }


    public AiFollowupSummaryVO aiFollowupSummary(AiFollowupSummaryReqVO reqVO) {
        FollowupTrackVO followupTrackVO = followupTrackFeign.infoWeb(new QueryFollowupTrackInfoParam(reqVO.getFollowupId()));
        List<ConsultVO> consultVOS = consultFeign.listBizId(reqVO.getFollowupId());
        if (CollectionUtils.isEmpty(consultVOS)) {
            throw new ServiceException(ShiftErrorCodeConstants.VOICE_TEXT_DOES_NOT_EXIST);
        }
        ConsultVO consultVO = consultVOS.get(0);
        if (CollectionUtils.isEmpty(consultVO.getChat())) {
            throw new ServiceException(ShiftErrorCodeConstants.VOICE_TEXT_DOES_NOT_EXIST);
        }
        // 拼接聊天内容
        String chatName = consultVO.getChat().get(0).getChatName();
        List<String> collect = consultVO.getChat().stream().map(ConsultChatVO::getChatStr).collect(Collectors.toList());
        String join = String.join("", collect);
        // 调用AI生成随访小结
        String summary = callAIFollowupSummary(followupTrackVO, join);

        if (StringUtils.isNotBlank(summary)) {
            // 保存到随访
            followupTrackFeign.saveSummary(new SaveFollowupSummaryReqVO().setFollowupId(reqVO.getFollowupId()).setSummary(summary));
            // TODO socket通知医生刷新
        }
        return new AiFollowupSummaryVO()
                .setTitle(chatName)
                .setSummary(summary)
                .setChatStr(join);
    }

    private String callAIFollowupSummary(FollowupTrackVO followupTrackVO, String chatStr) {
        PatientBaseVO patientBase = patientFeign.getPatientBaseById(followupTrackVO.getPatientId());
        FullRecordRespVO fullRecord = mixRecordService.getFullRecord(followupTrackVO.getPatientId());
        BasicReportVO basicReport = fullRecord.getBasicReport();
        JSONObject jsonReq = new JSONObject();
        jsonReq.put("name", patientBase.getName());
        jsonReq.put("gender", patientBase.getSex());
        jsonReq.put("age", patientBase.getAge());
        jsonReq.put("surgeryDate", basicReport.getSurgeryDate());
        jsonReq.put("surgicalName", basicReport.getSurgeryName());

        Map<String, Object> params = new HashMap<>();
        params.put("patientInfo", jsonReq);
        params.put("followupDate", followupTrackVO.getFollowupTime());
        params.put("ttsText", chatStr);
        String str = HutHttpUtils.post(aiConfig.getFollowupSummaryUrl(), params);
        log.info("调用AI获取随访小结 ： 发送http请求-----> response:{}", str);
        if (StringUtils.isBlank(str)) {
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), "请求没有详情");
        }
        JSONObject jsonObject = JSONObject.parseObject(str);
        Integer success = jsonObject.getInteger("code");
        if (success != 200) {
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), jsonObject.getString("msg"));
        }
        return jsonObject.getJSONObject("data").getString("summary");
    }


    private boolean queryConsultState(FollowupTrackVO followupTrackVO) {
        // 查询问诊的状态
        List<ConsultVO> consultVOS = consultFeign.listBizId(followupTrackVO.getId());
        if (!CollectionUtils.isEmpty(consultVOS)) {
            return consultVOS.get(0).getConsultState().equals(ConsultStateEnum.FINISH);
        }
        return false;
    }


    private void queryLastReport(FollowupInspectVO followupInspectVO, List<FollowupInspectItemVO> followupInspectItemVOS) {
        Long patientId = followupInspectVO.getPatientId();
        LocalDateTime startTime = followupInspectVO.getUpStartTime();
        LocalDateTime endTime = followupInspectVO.getUpEndTime();
        // 影像报告
        List<ImagingExaminationVO> imagingExaminationVOS = recordFeign.listImages(new ImagingExaminationReqVO().setPatientId(patientId));
        for (FollowupInspectItemVO followupInspectItemVO : followupInspectItemVOS) {
            if (followupInspectItemVO.getItemClass().equals("TREATMENT")) {
                TreatmentProjectVO treatmentProjectVO = treatmentProjectFeign.getById(followupInspectItemVO.getItemId());
                if (treatmentProjectVO == null) {
                    continue;
                }
                ReportCategory category = doReportCategory(treatmentProjectVO);
                if (category == null) {
                    continue;
                }
                if (category.equals(ReportCategory.IMAGE)) { // 影像报告单独查，存的地方不一样
                    if (CollectionUtils.isEmpty(imagingExaminationVOS)) {
                        continue;
                    }
                    // 过滤,去最新的一条数据
                    Optional<ImagingExaminationVO> first = imagingExaminationVOS.stream()
                            .filter(e -> StringUtils.isNotBlank(e.getExaminationType()) && e.getExaminationType().equals(treatmentProjectVO.getSubType()))
                            .filter(e -> !CollectionUtils.isEmpty(e.getExaminationPart()) && e.getExaminationPart().stream().anyMatch(k -> treatmentProjectVO.getExaminationParts().contains(k)))
                            .filter(e -> e.getExaminationLocalDateTime() != null && e.getExaminationLocalDateTime().isAfter(startTime))
                            .filter(e -> e.getExaminationLocalDateTime().isBefore(endTime))
                            .max(Comparator.comparing(ImagingExaminationVO::getExaminationLocalDateTime));
                    if (first.isPresent()) {
                        ImagingExaminationVO imagingExaminationVO = first.get();
                        followupInspectItemVO.setLastReportId(imagingExaminationVO.getReportId());
                        followupInspectItemVO.setCategory(category.name());
                        followupInspectItemVO.setSubCategory(imagingExaminationVO.getExaminationType());
                    }
                } else {
                    List<ReportListRespVO> list = reportFeign.listWeb(new ReportListReqVO()
                            .setPatientId(patientId)
                            .setCategory(category)
                            .setSubCategory(treatmentProjectVO.getSubType())
                            .setSortType(1)
                            .setSort("DESC")
                            .setRecordTimeStart(startTime)
                            .setRecordTimeEnd(endTime));
                    if (!CollectionUtils.isEmpty(list)) {
                        ReportListRespVO reportListRespVO = list.get(0);
                        followupInspectItemVO.setLastReportId(reportListRespVO.getId());
                        followupInspectItemVO.setCategory(category.name());
                        followupInspectItemVO.setSubCategory(reportListRespVO.getSubCategory());
                    }
                }
            }
        }
    }


    private ReportCategory doReportCategory(TreatmentProjectVO treatmentProjectVO) {
        switch (treatmentProjectVO.getType()) {
            case IMAGING_EXAMINATION:
                return ReportCategory.IMAGE;
            case ELECTROCARDIOGRAM:
                return ReportCategory.ECG;
            case PATHOLOGICAL_EXAMINATION:
                return ReportCategory.PATHOLOGY;
            case DNA_TEST:
                return ReportCategory.GENE;
            case OTHER_EXAMINATION:
                return ReportCategory.OTHER;
            case LABORATORY_TEST:
                return ReportCategory.LAB;
            default:
                return null;
        }
    }

    private void checkFollowupDoctor(Integer role, Long followupDoctorId, Long operateDoctorId) {
        if (role != 1 && !followupDoctorId.equals(operateDoctorId)) {
            DoctorVO doctorVO = doctorFeign.getById(followupDoctorId);
            throw new ServiceException(FollowupErrorCodeConstants.FOLLOWUP_DOCTOR_NOT_EQUALS_LOGIN_DOCTOR.getCode(),
                    String.format(FollowupErrorCodeConstants.FOLLOWUP_DOCTOR_NOT_EQUALS_LOGIN_DOCTOR.getMsg(), doctorVO.getName()));
        }
    }


    /**
     * 判断当前用户的角色情况
     *
     * @param followupTrackVO 随访详情
     * @param userId          当前操作用户id
     * @return 1-主管医生、2-团队医生
     */
    private Integer verifyDoctorRole(FollowupTrackVO followupTrackVO, Long userId) {
        if (followupTrackVO.getFollowupState().equals(FollowupTrackStatusEnums.STARTING)
                || followupTrackVO.getFollowupState().equals(FollowupTrackStatusEnums.AUDIT)) {
            String jsonString = commonConfigFeign.getCommonConfigValueByKey(DEFAULT_FOLLOWUP_DOCTOR_KEY);
            DefaultFollowupDoctorVO reqVO = JSONObject.parseObject(jsonString, DefaultFollowupDoctorVO.class);
            if (reqVO.getDoctor_manage().contains(userId)) { // 主管医生
                return 1;
            } else {
                return 2;
            }
        } else {
            return 2;
        }
    }


    private void handlerInfoPatientForm(Long followupId) {
        FollowupTrackVO followupTrackVO = followupTrackFeign.infoWeb(new QueryFollowupTrackInfoParam(followupId));
        if (followupTrackVO == null) {
            return;
        }
        // 创建一个默认的问诊数据
        if (followupTrackVO.getFollowupMode().equals(FollowupTrackTypeEnum.followup_video.name())
                || followupTrackVO.getFollowupMode().equals(FollowupTrackTypeEnum.followup_voice.name())) {
            consultFeign.createByDirectly(new CreateByDirectlyReqVO()
                    .setBizId(followupTrackVO.getId())
                    .setChatType(followupTrackVO.getFollowupMode().equals(FollowupTrackTypeEnum.followup_voice.name()) ? ConsultChatTypeEnum.VOICE_CHAT : ConsultChatTypeEnum.VIDEO_CHAT)
                    .setConsultState(ConsultStateEnum.WAIT)
                    .setConsultType(ConsultTypeEnum.FOLLOWUP_CONSULTATION)
                    .setPlanVisitStartTime(followupTrackVO.getPlanConsultStartTime())
                    .setPlanVisitEndTime(followupTrackVO.getPlanConsultEndTime())
                    .setDoctorId(followupTrackVO.getDoctorId())
                    .setPatientId(followupTrackVO.getPatientId()));
        }
        // 初始化表单
        if (!CollectionUtils.isEmpty(followupTrackVO.getItems())) {
            for (FollowupTrackItemVO item : followupTrackVO.getItems()) {
                if (item.getFormId() == null) {
                    continue;
                }
                PatientFormVO patientFormVO = patientFormFeign.add(new PatientFormAddVO()
                        .setPatientId(followupTrackVO.getPatientId())
                        .setType(PatientFormBizType.FOLLOWUP_TRACK)
                        .setBizId(followupId)
                        .setFormId(item.getFormId())
                        .setSendStatus(false));
                followupTrackFeign.updateFollowupItemPatientFormId(item.getItemId(), patientFormVO.getId());
            }
        }
    }

    public void updateConsultTime(UpdateFollowupConsultTimeReqVO reqVO) {
        List<ConsultVO> consultVOS = consultFeign.listBizId(reqVO.getFollowupId());
        if (!CollectionUtils.isEmpty(consultVOS)) {
            ConsultVO consultVO = consultVOS.get(0);
            AssertUtils.isTrue(consultVO.getConsultState().equals(ConsultStateEnum.WAIT), FollowupErrorCodeConstants.UPDATE_TIME_STATUS_NOT_UN_CONFIRM);

            // 修改问诊的问诊人
            consultFeign.updateConsult(new UpdateConsultVO()
                    .setConsultId(consultVO.getId())
                    .setPatientUserId(reqVO.getPatientUserId()));
        }
        followupTrackFeign.updateConsultTime(reqVO);
    }

    public FollowupTrackVO details(Long id) {
        FollowupTrackVO followupTrackVO = followupTrackFeign.infoWeb(new QueryFollowupTrackInfoParam(id));
        if (followupTrackVO != null) {
            List<ConsultVO> consultVOS = consultFeign.listBizId(followupTrackVO.getId());
            if (!CollectionUtils.isEmpty(consultVOS)) {
                followupTrackVO.setConsultId(consultVOS.get(0).getId());
            }
        }
        return followupTrackVO;
    }
}
