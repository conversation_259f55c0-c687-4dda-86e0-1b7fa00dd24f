package com.ykl.med.mix.service.event.strategy;

import com.alibaba.fastjson.JSON;
import com.ykl.med.medical.api.MedicalOrderFeign;
import com.ykl.med.medical.enums.MedicalOrderStatus;
import com.ykl.med.medical.vo.order.MedicalOrderDetailVO;
import com.ykl.med.medical.vo.order.MedicalOrderUpdateVO;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
@EventTaskTypeAnnotation({EventTaskType.MEDICAL_ORDER_START})
public class MedicalOrderStartStrategy extends AbstractEventTaskStrategy {
    @Resource
    private MedicalOrderFeign medicalOrderFeign;

    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {
        log.info("处理MedicalOrderStart事件任务: {}", JSON.toJSONString(eventTaskVO));
        MedicalOrderDetailVO medicalOrderDetailVO = medicalOrderFeign.getById(Long.valueOf(eventTaskVO.getBizId()));

        if (medicalOrderDetailVO.getStatus() == MedicalOrderStatus.NOT_STARTED) {
            MedicalOrderUpdateVO updateVO = new MedicalOrderUpdateVO();
            updateVO.setId(medicalOrderDetailVO.getId());
            updateVO.setPatientId(medicalOrderDetailVO.getPatientId());
            updateVO.setStatus(MedicalOrderStatus.ENABLE);
            medicalOrderFeign.update(updateVO);
        }
    }
}
