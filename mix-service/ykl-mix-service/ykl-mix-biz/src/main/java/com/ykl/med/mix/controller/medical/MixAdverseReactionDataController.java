package com.ykl.med.mix.controller.medical;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.medical.enums.AdverseReactionDataType;
import com.ykl.med.medical.vo.data.AdverseReactionDataQueryVO;
import com.ykl.med.medical.vo.data.AdverseReactionDataRelationQueryVO;
import com.ykl.med.medical.vo.data.AdverseReactionDataRelationVO;
import com.ykl.med.medical.vo.data.AdverseReactionDataVO;
import com.ykl.med.mix.api.medical.MixAdverseReactionDataFeign;
import com.ykl.med.mix.service.medical.MixAdverseReactionDataService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mixAdverseReactionData")
@Validated
public class MixAdverseReactionDataController implements MixAdverseReactionDataFeign {
    @Resource
    private MixAdverseReactionDataService mixAdverseReactionDataService;

    @Override
    @PostMapping("/pageList")
    public PageResult<AdverseReactionDataVO> pageList(@RequestBody @Validated AdverseReactionDataQueryVO queryVO) {
        return mixAdverseReactionDataService.pageList(queryVO);
    }

    @Override
    @PostMapping("/findById")
    public AdverseReactionDataVO findById(@RequestParam("id") Long id) {
        return mixAdverseReactionDataService.findById(id);
    }



    @Override
    @PostMapping("/queryPatientDrugAdverseReaction")
    public List<AdverseReactionDataRelationVO> queryPatientDrugAdverseReaction(@RequestParam(value = "drugId", required = false) Long drugId, @RequestParam("patientId") Long patientId) {
        return mixAdverseReactionDataService.queryPatientDrugAdverseReaction(drugId, patientId);
    }

    @Override
    @PostMapping("/queryAdverseReactionDataRelation")
    public List<AdverseReactionDataRelationVO> queryAdverseReactionDataRelation(@RequestBody AdverseReactionDataRelationQueryVO relationQueryVO){
        return mixAdverseReactionDataService.queryAdverseReactionDataRelation(relationQueryVO);
    }


    @Override
    @PostMapping("/getByTypeAndBizIds")
    public List<AdverseReactionDataVO> getByTypeAndBizIds(@RequestParam("type") AdverseReactionDataType type,
                                                          @RequestBody List<Long> bizIds) {
        return mixAdverseReactionDataService.getByTypeAndBizIds(type,bizIds);
    }


}