package com.ykl.med.mix.service.event.strategy;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.constants.topic.PatientTopic;
import com.ykl.med.framework.common.constants.topic.RehabMangeTopic;
import com.ykl.med.framework.common.pojo.IdMessage;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientUserDetailVO;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.ToDoMessageFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.enums.SystemMessageType;
import com.ykl.med.push.enums.ToDoMessageType;
import com.ykl.med.push.vo.event.EventTaskVO;
import com.ykl.med.push.vo.message.MessageStatusChangeVO;
import com.ykl.med.push.vo.todo.ToDoMessageSystemExecutedReqVO;
import com.ykl.med.rehab.api.PsychoRehabOperateLogFeign;
import com.ykl.med.rehab.enums.RehabType;
import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabOperateLogAddVO;
import com.ykl.med.rocketmq.RocketMQService;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 完成表单事件策略
 */
@Service
@Slf4j
@EventTaskTypeAnnotation({
        EventTaskType.PATIENT_FINISH_FOLLOW_FORM,
        EventTaskType.PATIENT_FINISH_FUNCTIONAL_FORM,
        EventTaskType.PATIENT_FINISH_SPORTS_FORM,
        EventTaskType.PATIENT_FINISH_PSYCHOLOGICAL_FORM,
        EventTaskType.PATIENT_FINISH_NUTRITION_FORM})
public class FinishFormTaskStrategy extends AbstractEventTaskStrategy {
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private DictDataFeign dictDataFeign;
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private PsychoRehabOperateLogFeign psychoRehabOperateLogFeign;
    @Resource
    private PatientFormFeign patientFormFeign;
    @Resource
    private RocketMQTemplate rocketMqTemplate;
    @Resource
    private RocketMQService rocketMQService;
    @Resource
    private ToDoMessageFeign toDoMessageFeign;
    @Resource
    private UserFeign userFeign;

    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {
        UserSimpleVO eventOperator = userFeign.getByUserId(eventTaskVO.getUserId());
        Boolean isPatient = eventOperator.getUserType() == UserType.PATIENT;
        if (isPatient) {
            sendSystemMessage(eventTaskVO);
        }

        Long planId = null;
        RehabType rehabType = null;
        MessageType messageType = null;
        // 修改表单填写状态
        switch (eventTaskVO.getBizType()) {
            case PATIENT_FINISH_FOLLOW_FORM: // 随访表单
                MessageType followType = MessageType.FOLLOW_UP_FORM;
                JSONObject jsonObject = eventTaskVO.getExtJson();
                if (jsonObject != null && jsonObject.get("followTrackFlag") != null && jsonObject.getBoolean("followTrackFlag")) {
                    followType = MessageType.FOLLOW_TRACK_FORM;
                }
                messageType = followType;
                break;
            case PATIENT_FINISH_NUTRITION_FORM: // 营养表单
                PatientFormVO nutritionForm = patientFormFeign.getById(Long.valueOf(eventTaskVO.getBizId()));
                planId = nutritionForm.getBizId();
                rehabType = RehabType.NUTRITION;
                messageType = MessageType.NUTRITION_FORM;
                break;
            case PATIENT_FINISH_PSYCHOLOGICAL_FORM:
                PatientFormVO patientFormVO = patientFormFeign.getById(Long.valueOf(eventTaskVO.getBizId()));
                planId = patientFormVO.getBizId();
                rehabType = RehabType.PSYCHO;
                messageType = MessageType.PSYCHOLOGICAL_FORM;
                if (!isPatient) {
                    //医生操作的需要记录操作日志
                    PsychoRehabOperateLogAddVO logAddVO = new PsychoRehabOperateLogAddVO()
                            .setPsychoRehabId(patientFormVO.getBizId())
                            .setOperatorId(eventTaskVO.getUserId());
                    List<String> operateContentList = new ArrayList<>();
                    operateContentList.add("填写了" + patientFormVO.getFormName());
                    logAddVO.setOperateContentList(operateContentList);
                    logAddVO.setRequestId(eventTaskVO.getId().toString());
                    psychoRehabOperateLogFeign.saveOperateLog(logAddVO);
                }
                break;
            case PATIENT_FINISH_FUNCTIONAL_FORM:
                messageType = MessageType.FUNCTIONAL_FORM;
                rehabType = RehabType.FUNC;
                toDoMessageFeign.toDoMessageExecutedSystem(new ToDoMessageSystemExecutedReqVO()
                        .setExecutedTime(LocalDateTime.now())
                        .setOutBizId(eventTaskVO.getBizId())
                        .setType(ToDoMessageType.FUNCTIONAL_FORM)
                        .setPatientId(eventTaskVO.getPatientId())
                        .setNeedError(false));
                break;
            case PATIENT_FINISH_SPORTS_FORM:
                messageType = MessageType.SPORTS_FORM;
                rehabType = RehabType.SPORT;
                toDoMessageFeign.toDoMessageExecutedSystem(new ToDoMessageSystemExecutedReqVO()
                        .setExecutedTime(LocalDateTime.now())
                        .setOutBizId(eventTaskVO.getBizId())
                        .setType(ToDoMessageType.SPORTS_FORM)
                        .setPatientId(eventTaskVO.getPatientId())
                        .setNeedError(false));
                break;
        }

        //患者端的消息要变动状态
        if (messageType != null) {
            messageFeign.messageStatusChange(new MessageStatusChangeVO()
                    .setIsPatient(isPatient)
                    .setBizId(eventTaskVO.getBizId())
                    .setMessageType(messageType));
        }
        //康复管理es变动
        if (rehabType != null) {
            RehabManageMqMessageVO rehabManageMessage = new RehabManageMqMessageVO()
                    .setPatientId(eventTaskVO.getPatientId())
                    .setRehabId(planId)
                    .setRehabType(rehabType);
            rocketMqTemplate.syncSend(RehabMangeTopic.TOPIC, rehabManageMessage);
        }
        // 更新ES用户信息
        IdMessage message = new IdMessage();
        message.setMessageId(UUID.randomUUID().toString());
        message.setId(eventTaskVO.getPatientId());
        rocketMQService.send(PatientTopic.UpdateMedicalHistory.DESTINATION, message);
    }

    private void sendSystemMessage(EventTaskVO eventTaskVO) {
        SystemMessageType type = SystemMessageType.valueOf(eventTaskVO.getBizType().name());
        PatientUserDetailVO patientVO = patientUserFeign.getPatientDetailByUserId(eventTaskVO.getUserId());
        if (patientVO == null || patientVO.getBindDoctorId() == null) {
            return;
        }
        String relation = dictDataFeign.getDictDataByValue(patientVO.getRelation()).getLabel();
        String content = getSystemMessageContent(type)
                .replace("patientName", patientVO.getName())
                .replace("relation", relation);
        createAndAddSystemMessage(patientVO.getBindDoctorId(), content, eventTaskVO.getId().toString(), patientVO.getPatientId(), type, eventTaskVO.getBizId());

    }
}
