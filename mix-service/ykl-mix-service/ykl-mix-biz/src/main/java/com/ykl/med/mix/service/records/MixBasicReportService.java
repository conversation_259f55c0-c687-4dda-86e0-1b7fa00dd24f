package com.ykl.med.mix.service.records;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CrudUtils;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.medical.api.MedicalOrderFeign;
import com.ykl.med.medical.enums.MedicalOrderStatus;
import com.ykl.med.medical.vo.order.MedicalOrderListWebVO;
import com.ykl.med.medical.vo.order.MedicalOrderQueryWebVO;
import com.ykl.med.pharmacy.api.DrugFeign;
import com.ykl.med.pharmacy.entity.vo.DrugVO;
import com.ykl.med.records.api.BasicReportFeign;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.rehab.api.NutritionalFeign;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPlanVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MixBasicReportService {

    private final DrugFeign drugFeign;
    private final NutritionalFeign nutritionalFeign;
    private final BasicReportFeign basicReportFeign;
    private final MedicalOrderFeign medicalOrderFeign;

    private static final String FORMAT_NUTRITION_ASSESSMENT_RESULTS = "量表填写后系统自动展示";
    private static final String FORM_PG = "PG-SGA";
    private static final String TARGET_DRUGS = "DRUG_TYPES_RADIOACTIVE_7";

    public BasicReportVO getByPatientId(Long patientId) {
        BasicReportVO basicReportVO = basicReportFeign.getByPatientId(patientId);
        if (basicReportVO == null) {
            return null;
        }
        basicReportVO.setNutritionAssessmentResult(formatNutritionAssessmentResults(patientId));
        MedicalOrderQueryWebVO medicalOrderQueryWebVO = new MedicalOrderQueryWebVO()
                .setPatientId(patientId)
                .setStatus(MedicalOrderStatus.ENABLE)
                .setStartExecTimeSort("ASC");
        medicalOrderQueryWebVO.setPageSize(10000);
        PageResult<MedicalOrderListWebVO> medicalOrderPageResult = medicalOrderFeign.queryWeb(medicalOrderQueryWebVO);
        if (!CollectionUtils.isEmpty(medicalOrderPageResult.getList())) {
            List<MedicalOrderListWebVO> list = medicalOrderPageResult.getList();
            basicReportVO.setCurrentMedication(list.stream().map(MedicalOrderListWebVO::getItemName).collect(Collectors.joining(",")));
            // 判断靶向药
            List<Long> outBizIds = list.stream().map(MedicalOrderListWebVO::getItemId).collect(Collectors.toList());
            List<DrugVO> drugVOS = CrudUtils.getByIdsLong(drugFeign::crud, outBizIds);
            List<DrugVO> collect = drugVOS.stream().filter(e -> !CollectionUtils.isEmpty(e.getDrugType()) && e.getDrugType().contains(TARGET_DRUGS)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                basicReportVO.setTargetDrugs(collect.stream().map(DrugVO::getName).collect(Collectors.joining(",")));
            }
        }
        return basicReportVO;
    }

    public String formatNutritionAssessmentResults(Long patientId) {
        NutritionalPlanVO details = nutritionalFeign.current(patientId);
        if (details == null || CollectionUtils.isEmpty(details.getPatientForm())) {
            return FORMAT_NUTRITION_ASSESSMENT_RESULTS;
        }
        PatientFormVO patientFormVO = details.getPatientForm().stream()
                .filter(e -> e.getFormName().contains(FORM_PG))
                .findFirst().orElse(null);
        if (patientFormVO == null || patientFormVO.getScore() == null || StringUtils.isBlank(patientFormVO.getResult())) {
            return FORMAT_NUTRITION_ASSESSMENT_RESULTS;
        }

        String result;
        String[] split = patientFormVO.getResult().split("：");
        if (split.length > 1) {
            result = split[0];
        }else{
            result = patientFormVO.getResult();
        }
        return String.format("%s %s分 %s", patientFormVO.getFormName(), patientFormVO.getScore(), result);
    }
}
