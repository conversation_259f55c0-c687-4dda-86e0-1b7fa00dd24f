package com.ykl.med.mix.service.push;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.doctors.api.DoctorFeign;
import com.ykl.med.doctors.entity.vo.DoctorListVO;
import com.ykl.med.doctors.entity.vo.QueryDoctorReqVO;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.vo.dict.DictAppReqVO;
import com.ykl.med.masterdata.vo.dict.DictAppVO;
import com.ykl.med.masterdata.vo.dict.DictDataAppVO;
import com.ykl.med.mix.vo.push.MixChatUserInfoVO;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientUserDetailVO;
import com.ykl.med.push.utils.SystemUserUtil;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MixChatService {
    @Resource
    private UserFeign userFeign;
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private DictDataFeign dictDataFeign;
    @Resource
    private DoctorFeign doctorFeign;
    @Resource
    private CommonConfigFeign commonConfigFeign;

    public List<MixChatUserInfoVO> getChatUserInfoByUserIds(IdListReqVO idListReqVO) {

        List<MixChatUserInfoVO> result = new ArrayList<>();
        List<Long> systemIds = idListReqVO.getIdList().stream()
                .filter(SystemUserUtil::isSystemUser)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(systemIds)) {
            JSONObject systemUserJson = commonConfigFeign.getCommonConfigValueJsonByKey("system_user");
            for (Long systemUserId : systemIds) {
                JSONObject systemUser = systemUserJson.getJSONObject(systemUserId.toString());
                MixChatUserInfoVO mixChatUserInfoVO = new MixChatUserInfoVO();
                mixChatUserInfoVO.setId(systemUserId);
                mixChatUserInfoVO.setName(systemUser.getString("name"));
                mixChatUserInfoVO.setAvatar(systemUser.getString("logo"));
                result.add(mixChatUserInfoVO);
            }
            idListReqVO.getIdList().removeAll(systemIds);
        }
        if (CollectionUtils.isEmpty(idListReqVO.getIdList())){
            return result;
        }
        List<UserSimpleVO> userSimpleVOS = userFeign.listByUserIds(idListReqVO);

        List<Long> patientUserIds = new ArrayList<>();
        List<Long> doctorUserIds = new ArrayList<>();
        for (UserSimpleVO userSimpleVO : userSimpleVOS) {
            MixChatUserInfoVO mixChatUserInfoVO = new MixChatUserInfoVO();
            mixChatUserInfoVO.setId(userSimpleVO.getId());
            mixChatUserInfoVO.setName(userSimpleVO.getName());
            mixChatUserInfoVO.setAvatar(userSimpleVO.getAvatar());
            mixChatUserInfoVO.setUserType(userSimpleVO.getUserType());
            result.add(mixChatUserInfoVO);
            if (userSimpleVO.getUserType() == UserType.PATIENT) {
                patientUserIds.add(userSimpleVO.getId());
            } else {
                doctorUserIds.add(userSimpleVO.getId());
            }
        }
        QueryDoctorReqVO reqVO = new QueryDoctorReqVO();
        reqVO.setIds(doctorUserIds);
        List<DoctorListVO> doctors = doctorFeign.list(reqVO);
        if (CollectionUtils.isNotEmpty(doctors)) {
            Map<Long, DoctorListVO> doctorMap = doctors.stream().collect(Collectors.toMap(DoctorListVO::getId, Function.identity()));
            for (MixChatUserInfoVO mixChatUserInfoVO : result) {
                if (doctorMap.containsKey(mixChatUserInfoVO.getId())) {
                    DoctorListVO doctorListVO = doctorMap.get(mixChatUserInfoVO.getId());
                    mixChatUserInfoVO.setSex(doctorListVO.getGender());
                }
            }
        }

        DictAppReqVO dictAppReqVO = new DictAppReqVO(Collections.singletonList("patient_account_relationship"));
        List<DictAppVO> dictAppVOS = dictDataFeign.getDictByType(dictAppReqVO);
        Map<String, String> dictMap = dictAppVOS.stream().flatMap(v -> v.getDataList().stream()).collect(Collectors.toMap(DictDataAppVO::getValue, DictDataAppVO::getLabel));

        if (CollectionUtils.isNotEmpty(patientUserIds)) {
            List<PatientUserDetailVO> patientUserDetailVOS = patientUserFeign.getPatientUserByUserIds(patientUserIds);
            Map<Long, PatientUserDetailVO> patientUserDetailVOMap = patientUserDetailVOS.stream().collect(Collectors.toMap(PatientUserDetailVO::getUserId, Function.identity()));
            for (MixChatUserInfoVO mixChatUserInfoVO : result) {
                if (patientUserDetailVOMap.containsKey(mixChatUserInfoVO.getId())) {
                    PatientUserDetailVO patientUserDetailVO = patientUserDetailVOMap.get(mixChatUserInfoVO.getId());
                    mixChatUserInfoVO.setName(patientUserDetailVO.getName() + "(" + dictMap.get(patientUserDetailVO.getRelation()) + ")");
                    mixChatUserInfoVO.setSex(patientUserDetailVO.getSex());
                }
            }
        }
        return result;
    }
}
