package com.ykl.med.mix.controller.medical;

import com.ykl.med.medical.vo.medicalAdvice.AuditStateMedicalAdviceReqVO;
import com.ykl.med.medical.vo.medicalAdvice.CreateMedicalAdviceReqVO;
import com.ykl.med.medical.vo.medicalAdvice.DiscardMedicalAdviceReqVO;
import com.ykl.med.medical.vo.medicalAdvice.HisDispenseCallbackReqVO;
import com.ykl.med.mix.api.medical.MixMedicalAdviceFeign;
import com.ykl.med.mix.service.medical.MixMedicalAdviceService;
import com.ykl.med.mix.vo.MixMedicalAdviceConsultVO;
import com.ykl.med.mix.vo.MixMedicalAdviceVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2024/10/12
 */

@RestController
@RequestMapping("/mix/medicalAdvice")
public class MixMedicalAdviceController implements MixMedicalAdviceFeign {

    @Resource
    private MixMedicalAdviceService mixMedicalAdviceService;

    @Override
    @GetMapping("/detailsConsult")
    public MixMedicalAdviceConsultVO detailsConsult(@RequestParam(name = "medicalAdviceId") Long medicalAdviceId) {
        return mixMedicalAdviceService.detailsConsult(medicalAdviceId);
    }

    @Override
    @GetMapping("/details")
    public MixMedicalAdviceVO details(@RequestParam(name = "medicalAdviceId") Long medicalAdviceId) {
        return mixMedicalAdviceService.details(medicalAdviceId);
    }

    @Override
    @PostMapping("/discard")
    public void discard(DiscardMedicalAdviceReqVO param) {
        mixMedicalAdviceService.discard(param);
    }

    @Override
    @PostMapping("/create")
    public Long create(@Valid @RequestBody CreateMedicalAdviceReqVO param) {
        return mixMedicalAdviceService.create(param);
    }

    @Override
    @PostMapping("/auditState")
    public void auditState(@Valid @RequestBody AuditStateMedicalAdviceReqVO param) {
        mixMedicalAdviceService.auditState(param);
    }

    @Override
    @PostMapping("/dispenseCallback")
    public void dispenseCallback(@Valid @RequestBody HisDispenseCallbackReqVO param) {
        mixMedicalAdviceService.dispenseCallback(param);
    }

    @Override
    @GetMapping("/rollback")
    public void rollback(@RequestParam(name = "id") Long id) {
        mixMedicalAdviceService.rollback(id);
    }
}
