package com.ykl.med.mix.controller.consult;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.mix.api.consult.MixConsulFeign;
import com.ykl.med.mix.service.consult.MixConsultService;
import com.ykl.med.mix.vo.consult.*;
import com.ykl.med.shift.vo.req.*;
import com.ykl.med.shift.vo.resp.ConsultDoctorGroupVO;
import com.ykl.med.shift.vo.resp.ConsultDoctorPageVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 线上门诊 mix
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
@RestController
@RequestMapping("/mix/consult")
public class MixConsultController implements MixConsulFeign {

    @Resource
    private MixConsultService mixConsultService;


    @PostMapping("/list/doctor")
    public List<ConsultDoctorGroupVO> listDoctor(@Valid @RequestBody QueryConsultListDoctorReqVO param) {
        return mixConsultService.listDoctor(param);
    }

    @PostMapping("/list/wait")
    public PageResult<ConsultDoctorPageVO> listWait(@Valid @RequestBody QueryConsultWaitPageReqVO param) {
        return mixConsultService.listWait(param);
    }


    @Override
    @PostMapping("/medicalRecords")
    public MixConsultMedicalRecordsVO medicalRecords(@Valid @RequestBody QueryMedicalRecordReqVO param) {
        return mixConsultService.medicalRecords(param);
    }

    @Override
    @PostMapping("/start")
    public void start(@Valid @RequestBody StartConclusionReqVO param) {
        mixConsultService.start(param);
    }

    @Override
    @PostMapping("/finish")
    public void finish(@Valid @RequestBody FinishConclusionReqVO param) {
        mixConsultService.finish(param);
    }

    @Override
    @GetMapping("/details")
    public MixConsultVO details(@RequestParam(name = "consultId") Long consultId) {
        return mixConsultService.details(consultId);
    }

    @Override
    @PostMapping("/saveDiseaseHistory")
    public void saveDiseaseHistory(@Valid @RequestBody SaveDiseaseHistoryReqVO param) {
        mixConsultService.saveDiseaseHistory(param);
    }

    @Override
    @PostMapping("/consultTimeOutTask")
    public void consultTimeOutTask() {
        mixConsultService.consultTimeOutTask();
    }

    @Override
    @PostMapping("/offline/page")
    public List<ConsultOfflineVO> offlineList(@Valid @RequestBody ConsultOfflineReqVO param) {
        return mixConsultService.offlineList(param);
    }

    @Override
    @PostMapping("/startVideo")
    public RoomCreateRespVO startVideo(@RequestParam(name = "userId") Long userId,
                                       @RequestParam(name = "consultId") Long consultId) {
        return mixConsultService.startVideo(userId, consultId);
    }

    @Override
    @PostMapping("/closeVideo")
    public void closeVideo(@RequestParam(name = "consultId") Long consultId,
                           @RequestParam(name = "joinFlag") Boolean joinFlag,
                           @RequestParam(name = "userId") Long userId) {
        mixConsultService.closeVideo(consultId, joinFlag, userId);
    }

    @Override
    @PostMapping("/updateConsult")
    public void updateConsult(@Valid @RequestBody UpdateConsultVO param) {
        mixConsultService.updateConsult(param);
    }

}
