package com.ykl.med.mix.service.patient;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.ykl.med.doctors.api.DoctorFeign;
import com.ykl.med.doctors.api.MedicalTeamFeign;
import com.ykl.med.doctors.entity.vo.DoctorListVO;
import com.ykl.med.doctors.entity.vo.MedicalTeamSimpleRespVO;
import com.ykl.med.doctors.entity.vo.QueryDoctorReqVO;
import com.ykl.med.followup.api.FollowupFeign;
import com.ykl.med.followup.api.FollowupTrackFeign;
import com.ykl.med.followup.entity.dto.FollowupQueryDTO;
import com.ykl.med.followup.entity.param.QueryFollowupTrackParam;
import com.ykl.med.followup.entity.vo.FollowupTrackVO;
import com.ykl.med.followup.entity.vo.FollowupVO;
import com.ykl.med.framework.common.enums.LifestyleType;
import com.ykl.med.framework.common.pojo.IdListReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.CrudUtils;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.api.DiseaseFeign;
import com.ykl.med.masterdata.api.RegionFeign;
import com.ykl.med.masterdata.entiry.dto.RegionQueryDTO;
import com.ykl.med.masterdata.entiry.vo.RegionVO;
import com.ykl.med.masterdata.vo.dict.DictDataRespVO;
import com.ykl.med.masterdata.vo.dict.DictDataSimpleRespVO;
import com.ykl.med.masterdata.vo.resp.DiseaseVO;
import com.ykl.med.medical.api.MedicalOrderFeign;
import com.ykl.med.medical.enums.MedicalOrderItemClass;
import com.ykl.med.medical.vo.order.MedicalOrderListAppVO;
import com.ykl.med.medical.vo.order.MedicalOrderQueryAppVO;
import com.ykl.med.medical.vo.order.MedicalOrderVO;
import com.ykl.med.mix.service.records.MixBasicReportService;
import com.ykl.med.mix.service.records.MixRecordService;
import com.ykl.med.mix.vo.patient.*;
import com.ykl.med.patient.api.MemberVersionFeign;
import com.ykl.med.patient.api.PatientEsFeign;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientUserVO;
import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.patient.vo.patient.LifestyleHistoryEs;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.patient.vo.patient.PatientSearchVO;
import com.ykl.med.patient.vo.patient.QueryPatientEsVO;
import com.ykl.med.records.api.*;
import com.ykl.med.records.enums.RecognitionStatus;
import com.ykl.med.records.vo.AllergyHistoryVO;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.records.vo.FamilyHistoryVO;
import com.ykl.med.records.vo.MetricDataVO;
import com.ykl.med.records.vo.req.MetricDataListReqVO;
import com.ykl.med.records.vo.req.ReportListReqVO;
import com.ykl.med.records.vo.resp.FullRecordRespVO;
import com.ykl.med.records.vo.resp.ReportListRespVO;
import com.ykl.med.rehab.api.FuncRehabFeign;
import com.ykl.med.rehab.api.NutritionalFeign;
import com.ykl.med.rehab.api.PsychoRehabFeign;
import com.ykl.med.rehab.api.SportRehabFeign;
import com.ykl.med.rehab.enums.NutritionalStatusEnum;
import com.ykl.med.rehab.vo.req.nutritional.QueryNutritionalPlanReqVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPlanVO;
import com.ykl.med.symptoms.api.records.SymptomFeign;
import com.ykl.med.symptoms.api.scale.ExecSymptomScaleFeign;
import com.ykl.med.symptoms.vo.records.SymptomVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleQueryVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class MixPatientServiceImpl implements MixPatientService {
    private final PatientFeign patientFeign;
    private final RecordFeign recordFeign;
    private final MixRecordService mixRecordService;
    private final MixBasicReportService mixBasicReportService;
    private final DictDataFeign dictDataFeign;
    private final BasicReportFeign basicReportFeign;
    private final ExecSymptomScaleFeign execSymptomScaleFeign;
    private final MedicalOrderFeign medicalOrderFeign;
    private final FollowupFeign followupFeign;
    private final SportRehabFeign sportRehabFeign;
    private final DiseaseFeign diseaseFeign;
    private final MetricFeign metricFeign;
    private final PatientUserFeign patientUserFeign;
    private final LifestyleFeign lifestyleFeign;
    private final MemberVersionFeign memberVersionFeign;
    private final SymptomFeign symptomFeign;
    private final ReportFeign reportFeign;
    private final PatientEsFeign patientEsFeign;
    private final MedicalTeamFeign medicalTeamFeign;
    private final DoctorFeign doctorFeign;
    private final RegionFeign regionFeign;
    private final FuncRehabFeign funcRehabFeign;
    private final PsychoRehabFeign psychoRehabFeign;
    private final FollowupTrackFeign followupTrackFeign;
    private final NutritionalFeign nutritionalFeign;

    @Override
    public MixPatientWaitDataVO getPatientWaitDataInfo(Long patientId) {
        MixPatientWebDetailVO patientWebDetail = this.getPatientWebDetailById(patientId);
        MixPatientWaitDataVO patientWaitDataVO = new MixPatientWaitDataVO();
        patientWaitDataVO.setPatientWebDetail(patientWebDetail);
        BasicReportVO basicReportVO = basicReportFeign.getByPatientId(patientId);
        if (basicReportVO != null) {
            BeanUtils.copyProperties(basicReportVO, patientWaitDataVO);
        }
        // 是否上传报告
        patientWaitDataVO.setHasUploadFile(hasUploadFile(patientId));
        // 是否填写病史
        patientWaitDataVO.setHasMedicalHistories(basicReportVO != null);
        return patientWaitDataVO;
    }

    private Boolean hasUploadFile(Long patientId) {
        List<ReportListRespVO> list = reportFeign.list(new ReportListReqVO().setPatientId(patientId).setRecognitionStatus(RecognitionStatus.WAITING_CONFIRM));
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }
        list = reportFeign.list(new ReportListReqVO().setPatientId(patientId).setRecognitionStatus(RecognitionStatus.SUCCESS));
        return CollectionUtils.isNotEmpty(list);
    }


    @Override
    public MixPatientWaitPlanVO getPatientWaitPlanInfo(Long patientId) {
        MixPatientWaitPlanVO mixPatientWaitPlanVO = new MixPatientWaitPlanVO();

        ExecSymptomScaleQueryVO requestBody = new ExecSymptomScaleQueryVO();
        requestBody.setPatientId(patientId);
        requestBody.setPageNo(1);
        requestBody.setPageSize(1);
        PageResult<ExecSymptomScaleVO> pageResult = execSymptomScaleFeign.query(requestBody);

        mixPatientWaitPlanVO.setHasSymptomPlan(pageResult.getTotal() > 0);

        MedicalOrderQueryAppVO medicalOrderQueryAppVO = new MedicalOrderQueryAppVO();
        medicalOrderQueryAppVO.setPatientId(patientId);
        medicalOrderQueryAppVO.setItemClass(MedicalOrderItemClass.MEDICAL_ORDER_ITEM_CLASS_DRUG);
        medicalOrderQueryAppVO.setPageNo(1);
        medicalOrderQueryAppVO.setPageSize(1);
        PageResult<MedicalOrderListAppVO> medicalOrderVO = medicalOrderFeign.queryApp(medicalOrderQueryAppVO);
        mixPatientWaitPlanVO.setHasMedicinePlan(medicalOrderVO.getTotal() > 0);

        FollowupQueryDTO followupQueryDTO = new FollowupQueryDTO();
        followupQueryDTO.setPatientId(patientId.toString());
        followupQueryDTO.setPageNo(1);
        followupQueryDTO.setPageSize(1);
        FollowupVO followupVO = CrudUtils.getOne(followupFeign::crud, followupQueryDTO);

        QueryFollowupTrackParam followupTrackParam = new QueryFollowupTrackParam();
        followupTrackParam.setPatientId(patientId);
        List<FollowupTrackVO> followupTrackVOS = followupTrackFeign.list(followupTrackParam);
        mixPatientWaitPlanVO.setHasFollowPlan(followupVO != null || CollectionUtils.isNotEmpty(followupTrackVOS));

        QueryNutritionalPlanReqVO reqVO = new QueryNutritionalPlanReqVO();
        reqVO.setPatientId(patientId);
        reqVO.setStatus(Lists.newArrayList(NutritionalStatusEnum.STARTING, NutritionalStatusEnum.FROM_FINISH));
        List<NutritionalPlanVO> nutritionalPlanVOS = nutritionalFeign.list(reqVO);
        mixPatientWaitPlanVO.setHasNutritionPlan(CollectionUtils.isNotEmpty(nutritionalPlanVOS));
        mixPatientWaitPlanVO.setHasSportPlan(sportRehabFeign.isExistPlanExcludeDraft(patientId));
        mixPatientWaitPlanVO.setHasFuncPlan(funcRehabFeign.isExistPlanExcludeDraft(patientId));
        mixPatientWaitPlanVO.setHasPsychoPlan(psychoRehabFeign.isExistPlanExcludeDraft(patientId));
        return mixPatientWaitPlanVO;
    }

    @Override
    public MixPatientAppDetailVO getPatientByUserId(Long userId) {
        PatientVO patientVO = patientUserFeign.getPatientByUserId(userId);
        MixPatientAppDetailVO mixPatientAppDetailVO = CopyPropertiesUtil.normalCopyProperties(patientVO, MixPatientAppDetailVO.class);
        if (mixPatientAppDetailVO != null && mixPatientAppDetailVO.getPatientId() != null) {
            mixPatientAppDetailVO.setDiseases(getDiseaseName(mixPatientAppDetailVO.getPatientId()));
            mixPatientAppDetailVO.setWeight(getWeight(mixPatientAppDetailVO.getPatientId()));
            if (patientVO.getHeight() != null && mixPatientAppDetailVO.getWeight() != null) {
                mixPatientAppDetailVO.setBmi((calculateBMI(patientVO.getHeight(), mixPatientAppDetailVO.getWeight())) + "");
            }
            // 查询阶段
            BasicReportVO basicReportVO = basicReportFeign.getByPatientId(mixPatientAppDetailVO.getPatientId());
            if (basicReportVO != null) {
                mixPatientAppDetailVO.setStage(basicReportVO.getStage());
            }
            PatientUserVO patientUserVO = patientUserFeign.getPatientUserByUserIdRedis(userId);
            if (patientUserVO != null) {
                mixPatientAppDetailVO.setRelation(patientUserVO.getRelation());
            }
            // 过敏史
            FullRecordRespVO fullRecord = mixRecordService.getFullRecord(patientVO.getPatientId());
            if (fullRecord != null && CollectionUtil.isNotEmpty(fullRecord.getAllergyHistories())) {
                mixPatientAppDetailVO.setAllergyHistories(fullRecord.getAllergyHistories());
            }
        }
        return mixPatientAppDetailVO;
    }


    @Override
    public MixPatientWebDetailVO getPatientWebDetailById(Long id) {
        PatientVO patientVO = patientFeign.getPatientById(id);
        MixPatientWebDetailVO patientWebDetailVO = CopyPropertiesUtil.normalCopyProperties(patientVO, MixPatientWebDetailVO.class);
        if (patientVO != null && patientVO.getPatientId() != null) {
            patientWebDetailVO.setDiseases(getDiseaseName(patientWebDetailVO.getPatientId()));
            patientWebDetailVO.setWeight(getWeight(patientWebDetailVO.getPatientId()));
            if (patientVO.getHeight() != null && patientWebDetailVO.getWeight() != null) {
                patientWebDetailVO.setBmi((calculateBMI(patientVO.getHeight(), patientWebDetailVO.getWeight())) + "");
            }
            // 查询阶段
            BasicReportVO basicReportVO = basicReportFeign.getByPatientId(patientVO.getPatientId());
            if (basicReportVO != null) {
                patientWebDetailVO.setStage(basicReportVO.getStage());
            }
            // 过敏史
            FullRecordRespVO fullRecord = mixRecordService.getFullRecord(patientVO.getPatientId());
            if (fullRecord != null && CollectionUtil.isNotEmpty(fullRecord.getAllergyHistories())) {
                patientWebDetailVO.setAllergyHistories(fullRecord.getAllergyHistories());
            }
        }
        return patientWebDetailVO;
    }

    public static BigDecimal calculateBMI(BigDecimal height, BigDecimal weight) {
        height = height.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        // 计算身高平方（保留4位小数避免精度损失）
        BigDecimal heightSquared = height.pow(2)
                .setScale(4, RoundingMode.HALF_UP);
        // 计算BMI（除法需指定精度和舍入模式）
        BigDecimal bmi = weight.divide(heightSquared, 4, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP); // 最终保留两位小数
        return bmi;
    }


    @Override
    public MixPatientBasicVO getPatientBasicById(Long id) {
        PatientBaseVO patientBaseVO = patientFeign.getPatientBaseById(id);
        BasicReportVO basicReportVO = mixBasicReportService.getByPatientId(id);
        MixPatientBaseVO mixPatientBaseVO = CopyPropertiesUtil.normalCopyProperties(patientBaseVO, MixPatientBaseVO.class);
        mixPatientBaseVO.setMemberVersionTime(patientBaseVO.getMemberVersionTime());
        mixPatientBaseVO.setDiseases(getDiseaseName(id));
        mixPatientBaseVO.setStage(null); // 阶段从患者表里移到病史表
        MixPatientBasicVO patientBasicVO = new MixPatientBasicVO();
        patientBasicVO.setPatientBaseVO(mixPatientBaseVO);
        if (basicReportVO != null) {
            basicReportVO.setSurgeryDateDistance(basicReportVO.buildSurgeryDate());
            basicReportVO.setDiseaseName(mixPatientBaseVO.getDiseases());
            mixPatientBaseVO.setStage(basicReportVO.getStage());
        }
        patientBasicVO.setBasicReportVO(basicReportVO);
        patientBasicVO.setLifestyleHistoryVOList(lifestyleFeign.listByPatientId(id));
        patientBasicVO.setPatientMemberVersionVO(memberVersionFeign.getLastMemberVersionByPatient(id));
        return patientBasicVO;
    }


    @Override
    public MixPatientWeightVO getPatientWeightForm(Long patientId) {
        MetricDataListReqVO metricDataListReqVO = new MetricDataListReqVO();
        metricDataListReqVO.setPatientId(patientId);
        metricDataListReqVO.setSpecimen(StringUtils.EMPTY);
        metricDataListReqVO.setExaminationTestProjectId(681L);
        List<MetricDataVO> metricDataVOList = metricFeign.listData(metricDataListReqVO);
        if (CollectionUtil.isEmpty(metricDataVOList) || StringUtils.isBlank(metricDataVOList.get(0).getValue())) {
            return null;
        }
        LocalDateTime nowDay = DateTimeUtils.getNow();
        // 最新一条体重
        MetricDataVO latest = metricDataVOList.stream()
                .max(Comparator.comparing(MetricDataVO::getRecordTime)).orElse(null);

        // 1个月内最早的一条数据
        LocalDateTime oneMonthAgo = nowDay.minusMonths(1);
        MetricDataVO earliest1Month = metricDataVOList.stream()
                .filter(r -> r.getRecordTime().isAfter(oneMonthAgo))
                .min(Comparator.comparing(MetricDataVO::getRecordTime)).orElse(null);

        // 6个月内的最早的一条数据
        LocalDateTime sixMonthsAgo = nowDay.minusMonths(6);
        MetricDataVO earliest6Months = metricDataVOList.stream()
                .filter(r -> r.getRecordTime().isAfter(sixMonthsAgo))
                .min(Comparator.comparing(MetricDataVO::getRecordTime)).orElse(null);

        return new MixPatientWeightVO()
                .setWeightCurrent(latest != null ? new BigDecimal(latest.getValue()) : null)
                .setWeightOneMonth(earliest1Month != null ? new BigDecimal(earliest1Month.getValue()) : null)
                .setWeightSixMonth(earliest6Months != null ? new BigDecimal(earliest6Months.getValue()) : null);
    }


    private String getDiseaseName(Long patientId) {
        BasicReportVO basicReportVO = basicReportFeign.getByPatientId(patientId);
        if (basicReportVO != null && basicReportVO.getDiseaseId() != null) {
            return diseaseFeign.queryById(basicReportVO.getDiseaseId()).getSystemName();
        }
        return StringUtils.EMPTY;
    }


    private BigDecimal getWeight(Long patientId) {
        MetricDataListReqVO metricDataListReqVO = new MetricDataListReqVO();
        metricDataListReqVO.setPatientId(patientId);
        metricDataListReqVO.setSpecimen(StringUtils.EMPTY);
        metricDataListReqVO.setExaminationTestProjectId(681L);
        List<MetricDataVO> metricDataVOList = metricFeign.listData(metricDataListReqVO);
        if (CollectionUtil.isNotEmpty(metricDataVOList) && StringUtils.isNotBlank(metricDataVOList.get(0).getValue())) {
            return new BigDecimal(metricDataVOList.get(0).getValue());
        }
        return null;
    }


    @Override
    public List<MixPatientSymptomAlignmentVO> getPatientSymptomForm(Long patientId) {
        List<SymptomVO> symptomVOS = symptomFeign.getByPatientId(patientId);
        if (CollectionUtil.isEmpty(symptomVOS)) {
            return defaultDataAlignment();
        }
        return dataAlignment(symptomVOS);
    }


    @Override
    public PageResult<MixPatientSearchVO> getPatientSearchList(QueryPatientEsVO reqVO) {
        PageResult<PatientSearchVO> page = patientEsFeign.page(reqVO);
        if (CollectionUtils.isEmpty(page.getList())) {
            return new PageResult<>();
        }
        List<MixPatientSearchVO> mixPatientSearchVOS = convertMixPatientSearchVO(page.getList());
        return new PageResult<>(mixPatientSearchVOS, page.getTotal());
    }

    private List<MixPatientSearchVO> convertMixPatientSearchVO(List<PatientSearchVO> list) {
        Map<Long, List<PatientUserVO>> familyMap = getFamilyData(list);
        Map<Long, String> doctorNameMap = getDoctorNames(list);
        Map<String, String> medicalTeamMap = getMedicalTeams(list);
        Map<Long, String> diseaseMap = getDiseaseNames(list);
        // 使用并行流处理转换
        return list.parallelStream()
                .map(patient -> {
                    MixPatientSearchVO vo = new MixPatientSearchVO();
                    BeanUtils.copyProperties(patient, vo);

                    Optional.ofNullable(familyMap.get(patient.getId()))
                            .ifPresent(vo::setFamilyList);
                    Optional.ofNullable(doctorNameMap.get(patient.getBindDoctorId()))
                            .ifPresent(vo::setBindDoctorName);
                    Optional.ofNullable(medicalTeamMap.get(String.valueOf(patient.getMedicalTeamId())))
                            .ifPresent(vo::setMedicalTeamName);
                    Optional.ofNullable(diseaseMap.get(patient.getDiseaseId()))
                            .ifPresent(vo::setDiseaseName);
                    if (CollectionUtils.isNotEmpty(patient.getOtherDiseaseIds())) {
                        List<String> otherDiseaseName = patient.getOtherDiseaseIds().stream().map(e -> diseaseMap.get(Long.valueOf(e))).collect(Collectors.toList());
                        vo.setOtherDiseaseName(otherDiseaseName);
                    }
                    if (CollectionUtils.isNotEmpty(patient.getComplicationIds())) {
                        List<String> complicationName = patient.getComplicationIds().stream().map(e -> diseaseMap.get(Long.valueOf(e))).collect(Collectors.toList());
                        vo.setComplicationName(complicationName);
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private Map<Long, List<PatientUserVO>> getFamilyData(List<PatientSearchVO> list) {
        List<Long> ids = list.stream().map(PatientSearchVO::getId).collect(Collectors.toList());
        return Optional.ofNullable(patientUserFeign.getFamilyByPatientIds(ids))
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.groupingBy(PatientUserVO::getPatientId));
    }

    private Map<Long, String> getDoctorNames(List<PatientSearchVO> list) {
        List<Long> ids = list.stream()
                .map(PatientSearchVO::getBindDoctorId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        return Optional.ofNullable(doctorFeign.list(new QueryDoctorReqVO().setIds(ids)))
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.toMap(
                        DoctorListVO::getId,
                        DoctorListVO::getName,
                        (oldVal, newVal) -> oldVal));
    }

    private Map<String, String> getMedicalTeams(List<PatientSearchVO> list) {
        List<Long> ids = list.stream()
                .map(PatientSearchVO::getMedicalTeamId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        return Optional.ofNullable(medicalTeamFeign.listByMedicalTeamIds(ids))
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.toMap(
                        MedicalTeamSimpleRespVO::getId,
                        MedicalTeamSimpleRespVO::getName,
                        (oldVal, newVal) -> oldVal));
    }

    private Map<Long, String> getDiseaseNames(List<PatientSearchVO> list) {
        List<Long> ids = new ArrayList<>();
        for (PatientSearchVO patientSearchVO : list) {
            if (patientSearchVO.getDiseaseId() != null) {
                ids.add(patientSearchVO.getDiseaseId());
            }
            if (CollectionUtils.isNotEmpty(patientSearchVO.getOtherDiseaseIds())) {
                ids.addAll(patientSearchVO.getOtherDiseaseIds().stream().map(Long::parseLong).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(patientSearchVO.getComplicationIds())) {
                ids.addAll(patientSearchVO.getComplicationIds().stream().map(Long::parseLong).collect(Collectors.toList()));
            }
        }
        return Optional.ofNullable(diseaseFeign.queryByIds(ids))
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.toMap(
                        DiseaseVO::getId,
                        DiseaseVO::getSystemName,
                        (oldVal, newVal) -> oldVal));
    }


    @Override
    public List<MixExportPatientVO> exportPatientSearch(QueryPatientEsVO reqVO) {
        PageResult<PatientSearchVO> page = patientEsFeign.page(reqVO);
        if (CollectionUtils.isEmpty(page.getList())) {
            return new ArrayList<>();
        }
        return convertMixExportPatientVO(page.getList());
    }

    private List<MixExportPatientVO> convertMixExportPatientVO(List<PatientSearchVO> list) {
        List<Long> patientIds = list.stream().map(PatientSearchVO::getId).collect(Collectors.toList());
        List<BasicReportVO> basicReportVOS = basicReportFeign.listByPatientIds(patientIds);
        Map<Long, BasicReportVO> basicReportVOMap = Optional.ofNullable(basicReportVOS)
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.toMap(BasicReportVO::getPatientId, Function.identity()));

        List<PatientVO> patientBaseVOS = patientFeign.getPatientByIds(patientIds);
        Map<Long, PatientVO> patientVOMap = Optional.ofNullable(patientBaseVOS)
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.toMap(PatientVO::getPatientId, Function.identity()));
        // 地区
        Map<String, String> regionMap = getRegionMap();
        // 字典
        List<DictDataSimpleRespVO> simpleDictDataList = dictDataFeign.getSimpleDictDataList();
        Map<String, String> dictDataMap = Optional.ofNullable(simpleDictDataList)
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.toMap(DictDataSimpleRespVO::getValue, DictDataSimpleRespVO::getLabel));
        // 家族史
        List<FamilyHistoryVO> familyHistoryVOS = recordFeign.familyHistoryByPatientIds(new IdListReqVO().setIdList(patientIds));
        Map<Long, List<FamilyHistoryVO>> familyHistoryMap = Optional.ofNullable(familyHistoryVOS)
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.groupingBy(FamilyHistoryVO::getPatientId));
        // 过敏史
        List<AllergyHistoryVO> allergyHistoryVOS = recordFeign.allergyHistoryByPatientIds(new IdListReqVO().setIdList(patientIds));
        Map<Long, List<AllergyHistoryVO>> allergyHistoryMap = Optional.ofNullable(allergyHistoryVOS)
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.groupingBy(AllergyHistoryVO::getPatientId));
        // 靶向药
        List<MedicalOrderVO> targetDrugsVOS = medicalOrderFeign.queryTargetDrugsByPatientIds(new IdListReqVO().setIdList(patientIds));
        Map<Long, List<MedicalOrderVO>> targetDrugsMap = Optional.ofNullable(targetDrugsVOS)
                .orElse(Collections.emptyList())
                .parallelStream()
                .collect(Collectors.groupingBy(MedicalOrderVO::getPatientId));
        List<MixExportPatientVO> data = new ArrayList<>();
        for (PatientSearchVO t : list) {
            PatientVO patientVO = patientVOMap.get(t.getId());
            BigDecimal weight = getWeight(t.getId());
            MixExportPatientVO vo = new MixExportPatientVO();
            vo.setName(t.getName());
            vo.setHeight(patientVO.getHeight() != null ? patientVO.getHeight() + "cm" : null);
            vo.setWeight(weight != null ? weight + "kg" : null);
            vo.setAddress(String.format("%s%s%s%s",
                    regionMap.getOrDefault(patientVO.getProvince(), ""),
                    regionMap.getOrDefault(patientVO.getCity(), ""),
                    regionMap.getOrDefault(patientVO.getDistrict(), ""),
                    patientVO.getContactAddress()));
            vo.setProfession(dictDataMap.getOrDefault(patientVO.getProfession(), ""));
            vo.setLaborCapacity(dictDataMap.getOrDefault(patientVO.getLaborCapacity(), ""));
            vo.setExercisePreferences(CollectionUtils.isNotEmpty(patientVO.getExercisePreferences())
                    ? patientVO.getExercisePreferences().stream().map(e -> dictDataMap.getOrDefault(e, "")).collect(Collectors.joining(",")) : null);
            BasicReportVO basicReportVO = basicReportVOMap.get(t.getId());
            if (basicReportVO != null) {
                vo.setNutritionAssessmentResult(t.getNutritionAssessmentResult());
                vo.setMedicalHistories(basicReportVO.getMedicalHistoryDescription());
                vo.setDiseaseName(basicReportVO.getDiseaseName());
                vo.setOtherDiseaseNames(CollectionUtils.isNotEmpty(basicReportVO.getOtherDiseaseNames()) ? String.join(",", basicReportVO.getOtherDiseaseNames()) : null);
                vo.setClinicalStaging(dictDataMap.getOrDefault(basicReportVO.getClinicalStaging(), ""));
                vo.setCtnmStaging(dictDataMap.getOrDefault(basicReportVO.getCtnmStaging(), ""));
                vo.setPtnmStaging(dictDataMap.getOrDefault(basicReportVO.getPtnmStaging(), ""));
                vo.setPathologicalType(dictDataMap.getOrDefault(basicReportVO.getPathologicalType(), ""));
                vo.setSurgery(dictDataMap.getOrDefault(basicReportVO.getSurgery(), ""));
                vo.setSurgeryName(basicReportVO.getSurgeryName());
                vo.setSurgeryDate(basicReportVO.getSurgeryDate() != null ? basicReportVO.getSurgeryDate().toString() : null);
                vo.setTargetDrugs(basicReportVO.getTargetDrugs());
                vo.setComplicationNames(CollectionUtils.isNotEmpty(basicReportVO.getComplicationNames()) ? String.join(",", basicReportVO.getComplicationNames()) : null);
            }
            List<LifestyleHistoryEs> lifestyleHistory = t.getLifestyleHistory();
            if (CollectionUtils.isNotEmpty(lifestyleHistory)) {
                for (LifestyleHistoryEs lifestyleHistoryEs : lifestyleHistory) {
                    if (lifestyleHistoryEs.getType().equals(LifestyleType.SMOKE)) {
                        vo.setSmoking(lifestyleHistoryEs.getStatus() != null ? lifestyleHistoryEs.getStatus().getDescription() : null);
                        vo.setSmokingYears(lifestyleHistoryEs.getLifestyleYears());
                        vo.setSmokingDayTotal(lifestyleHistoryEs.getDailyAmount());
                    }
                    if (lifestyleHistoryEs.getType().equals(LifestyleType.DRINK)) {
                        vo.setAlcohol(lifestyleHistoryEs.getStatus() != null ? lifestyleHistoryEs.getStatus().getDescription() : null);
                        vo.setAlcoholYears(lifestyleHistoryEs.getLifestyleYears());
                        vo.setAlcoholDayTotal(lifestyleHistoryEs.getDailyAmount());
                    }
                }
            }
            vo.setSex(dictDataMap.getOrDefault(patientVO.getSex(), ""));
            vo.setAge(patientVO.getAge() != null ? patientVO.getAge() + "岁" : null);
            vo.setGeneticTesting(CollectionUtils.isNotEmpty(t.getGeneticTesting())
                    ? t.getGeneticTesting().stream().map(e -> dictDataMap.getOrDefault(e, "")).collect(Collectors.joining(",")) : null);

            vo.setNeedPostOpTreatment(dictDataMap.getOrDefault(t.getNeedPostOpTreatment(), ""));
            vo.setPostOpTreatmentMethods(CollectionUtils.isNotEmpty(t.getPostOpTreatmentMethods())
                    ? t.getPostOpTreatmentMethods().stream().map(e -> dictDataMap.getOrDefault(e, "")).collect(Collectors.joining(",")) : null);
            vo.setMedicalHistoryDescription(t.getMedicalHistoryDescription());
            vo.setAdverseReactions(t.getAdverseReactions());
            vo.setMedicalOrder(CollectionUtils.isNotEmpty(t.getMedicalOrder())
                    ? t.getMedicalOrder().stream().map(e -> String.format("%s-%s", e.getDrugName(), e.getProducerName())).collect(Collectors.joining(",")) : null);
            List<FamilyHistoryVO> familyHistoryVOS1 = familyHistoryMap.get(t.getId());
            if (CollectionUtils.isNotEmpty(familyHistoryVOS1)) {
                vo.setHasFamilyMemberIll(familyHistoryVOS1.stream().anyMatch(e -> e.getHasFamilyMemberIll() != null) ? "有" : "无");
                vo.setHasFamilyMemberDied(familyHistoryVOS1.stream().anyMatch(e -> e.getHasFamilyMemberDied() != null) ? "有" : "无");
            } else {
                vo.setHasFamilyMemberIll("无");
                vo.setHasFamilyMemberDied("无");
            }
            List<AllergyHistoryVO> allergyHistoryVOS1 = allergyHistoryMap.get(t.getId());
            if (CollectionUtils.isNotEmpty(allergyHistoryVOS1)) {
                List<String> collect = allergyHistoryVOS1.stream()
                        .filter(e -> StringUtils.isNotBlank(e.getAllergen()))
                        .map(e -> dictDataMap.getOrDefault(e.getAllergen(), "")
                        ).collect(Collectors.toList());
                vo.setAllergyHistory(String.join(",", collect));
            }
            List<MedicalOrderVO> targetDrugsVOS1 = targetDrugsMap.get(t.getId());
            if (CollectionUtils.isNotEmpty(targetDrugsVOS1)) {
                List<String> collect = targetDrugsVOS1.stream()
                        .map(MedicalOrderVO::getItemName)
                        .collect(Collectors.toList());
                vo.setTargetDrugs(String.join(",", collect));
            }
            data.add(vo);
        }
        return data;
    }

    private Map<String, String> getRegionMap() {
        RegionQueryDTO requestBody = new RegionQueryDTO();
        requestBody.setPageNo(1);
        requestBody.setPageSize(9999);
        PageResult<RegionVO> regionVOPageResult = regionFeign.crud("GET", requestBody);
        if (CollectionUtils.isEmpty(regionVOPageResult.getList())) {
            return new HashMap<>();
        }
        return convertToIdNameMap(regionVOPageResult.getList());
    }

    public static Map<String, String> convertToIdNameMap(List<RegionVO> regions) {
        Map<String, String> resultMap = new HashMap<>();
        if (regions == null || regions.isEmpty()) {
            return resultMap;
        }
        for (RegionVO region : regions) {
            // 添加当前节点
            if (region.getCode() != null && region.getName() != null) {
                resultMap.put(region.getCode(), region.getName());
            }
            // 递归处理子节点
            if (region.getRegionList() != null && !region.getRegionList().isEmpty()) {
                resultMap.putAll(convertToIdNameMap(region.getRegionList()));
            }
        }
        return resultMap;
    }


    private List<MixPatientSymptomAlignmentVO> defaultDataAlignment() {
        List<MixPatientSymptomAlignmentVO> dataAlignmentStr = new ArrayList<>();
        for (SymptomAlignment symptomAlignment : SymptomAlignment.values()) {
            MixPatientSymptomAlignmentVO mixPatientSymptomAlignmentVO = new MixPatientSymptomAlignmentVO();
            mixPatientSymptomAlignmentVO.setName(symptomAlignment.getName());
            mixPatientSymptomAlignmentVO.setSelected(false);
            mixPatientSymptomAlignmentVO.setScore(symptomAlignment.getScore());
            dataAlignmentStr.add(mixPatientSymptomAlignmentVO);
        }
        // 单独处理疼痛
        MixPatientSymptomAlignmentVO mixPatientSymptomAlignmentVO = new MixPatientSymptomAlignmentVO();
        mixPatientSymptomAlignmentVO.setName("疼痛");
        mixPatientSymptomAlignmentVO.setSelected(false);
        mixPatientSymptomAlignmentVO.setScore(3);
        dataAlignmentStr.add(mixPatientSymptomAlignmentVO);
        return dataAlignmentStr;
    }

    private List<MixPatientSymptomAlignmentVO> dataAlignment(List<SymptomVO> symptomVOS) {
        List<MixPatientSymptomAlignmentVO> dataAlignmentStr = new ArrayList<>();
        // 过滤掉痛相关的症状
        List<String> collect = symptomVOS.stream()
                .filter(e -> !e.getSymptomName().contains("痛"))
                .map(e -> {
                    String symptomName = e.getSymptomName();
                    if ((symptomName.contains("抑郁") || symptomName.contains("牙痛"))) {
                        symptomName = "其他（如抑郁、经济问题、牙齿问题）";
                    } else if (symptomName.contains("食欲减退") || symptomName.contains("食欲不振")) {
                        symptomName = "没有食欲，不想吃饭";
                    }
                    return symptomName;
                }).collect(Collectors.toList());
        for (SymptomAlignment symptomAlignment : SymptomAlignment.values()) {
            MixPatientSymptomAlignmentVO mixPatientSymptomAlignmentVO = new MixPatientSymptomAlignmentVO();
            mixPatientSymptomAlignmentVO.setName(symptomAlignment.getName());
            mixPatientSymptomAlignmentVO.setSelected(collect.contains(symptomAlignment.getName()));
            mixPatientSymptomAlignmentVO.setScore(symptomAlignment.getScore());
            dataAlignmentStr.add(mixPatientSymptomAlignmentVO);
        }
        // 单独处理疼痛
        addPain(dataAlignmentStr, symptomVOS);
        return dataAlignmentStr;
    }

    private void addPain(List<MixPatientSymptomAlignmentVO> dataAlignmentStr, List<SymptomVO> symptomVOS) {
        MixPatientSymptomAlignmentVO mixPatientSymptomAlignmentVO = new MixPatientSymptomAlignmentVO();
        List<SymptomVO> painList = symptomVOS.stream()
                .filter(e -> e.getSymptomName().contains("痛"))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(painList)) {
            mixPatientSymptomAlignmentVO.setName("疼痛");
            mixPatientSymptomAlignmentVO.setSelected(false);
            dataAlignmentStr.add(mixPatientSymptomAlignmentVO);
            return;
        }
        List<String> positions = symptomVOS.stream().filter(e -> !CollectionUtil.isEmpty(e.getPosition())).flatMap(e -> e.getPosition().stream()).collect(Collectors.toList());
        Map<String, String> positionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(positions)) {
            List<DictDataRespVO> dictDataRespVOS = dictDataFeign.getDictDataByValues(positions);
            positionMap = dictDataRespVOS.stream().collect(Collectors.toMap(DictDataRespVO::getValue, DictDataRespVO::getLabel));
        }
        List<String> positionList = new ArrayList<>();
        for (String position : positions) {
            String positionStr = positionMap.get(position);
            if (StringUtils.isNotBlank(positionStr)) {
                positionList.add(positionStr);
            } else {
                positionList.add(position);
            }
        }
        mixPatientSymptomAlignmentVO.setName(CollectionUtil.isNotEmpty(positionList) ? String.format("疼痛(%s)", String.join(",", positionList)) : "疼痛");
        mixPatientSymptomAlignmentVO.setSelected(true);
        mixPatientSymptomAlignmentVO.setScore(3);
        dataAlignmentStr.add(mixPatientSymptomAlignmentVO);
    }


    @Getter
    @AllArgsConstructor
    enum SymptomAlignment {
        SYMPTOM_0("没有饮食问题", 0),
        SYMPTOM_1("恶心", 1),
        SYMPTOM_3("便秘", 1),
        SYMPTOM_4("食物气味不好", 1),
        SYMPTOM_5("其他（如抑郁、经济问题、牙齿问题）", 1),
        SYMPTOM_6("口干", 1),
        SYMPTOM_7("食物没有味道", 1),
        SYMPTOM_8("吃一会儿就饱了", 1),
        SYMPTOM_9("口腔溃疡", 2),
        SYMPTOM_10("吞咽困难", 2),
        SYMPTOM_11("腹泻", 3),
        SYMPTOM_12("没有食欲，不想吃饭", 3),
        SYMPTOM_13("呕吐", 3);
        private final String name;
        private final int score;
    }

}
