package com.ykl.med.mix.service.event.strategy.rehab;

import com.ykl.med.mix.service.event.strategy.AbstractEventTaskStrategy;
import com.ykl.med.mix.service.event.strategy.EventTaskTypeAnnotation;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskVO;
import com.ykl.med.rehab.api.FuncRehabFeign;
import com.ykl.med.rehab.vo.req.func.FuncRehabPlanSaveOrUpdateReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
@EventTaskTypeAnnotation({EventTaskType.FUNC_REHAB_BASE_PLAN})
public class FuncRehabBasePlanStrategy extends AbstractEventTaskStrategy {
    @Resource
    private FuncRehabFeign funcRehabFeign;

    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {
        FuncRehabPlanSaveOrUpdateReqVO basePlanReq = funcRehabFeign.getBasePlanReq(eventTaskVO.getPatientId());
        if (basePlanReq != null) {
            funcRehabFeign.saveOrUpdate(basePlanReq);
        }
    }
}
