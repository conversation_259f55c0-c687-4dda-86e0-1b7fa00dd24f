package com.ykl.med.mix.controller.patient;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.patient.MixMdtFeign;
import com.ykl.med.mix.service.patient.MixMdtService;
import com.ykl.med.mix.vo.patient.mdt.MixMdtListVO;
import com.ykl.med.patient.vo.mdt.WaitMdtQueryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "Mix MDT Controller")
@RestController
@RequestMapping("/mixMdt")
@Validated
public class MdtController implements MixMdtFeign {
    @Resource
    private MixMdtService mixMdtService;

    @Override
    @PostMapping("/waitMdtPage")
    @Operation(summary = "待参加MDT会诊列表")
    public PageResult<MixMdtListVO> waitMdtPage(@RequestBody WaitMdtQueryVO reqVO) {
        return mixMdtService.waitMdtPage(reqVO);
    }
}
