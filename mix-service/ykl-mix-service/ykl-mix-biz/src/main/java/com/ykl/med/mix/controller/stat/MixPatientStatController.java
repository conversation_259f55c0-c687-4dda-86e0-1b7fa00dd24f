package com.ykl.med.mix.controller.stat;

import com.ykl.med.mix.api.stat.MixPatientStatFeign;
import com.ykl.med.mix.service.stat.MixPatientStatService;
import com.ykl.med.mix.vo.stat.MixPatientStatVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/mixPatientStat")
@RequiredArgsConstructor
public class MixPatientStatController implements MixPatientStatFeign {
    private final MixPatientStatService mixPatientStatService;

    @PostMapping("/getStatByPatientId")
    @Override
    public MixPatientStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId) {
        return mixPatientStatService.getStatByPatientId(patientId);
    }
}
