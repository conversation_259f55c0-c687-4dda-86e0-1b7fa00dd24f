package com.ykl.med.mix.controller.doctor;

import com.ykl.med.doctors.entity.vo.*;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.doctor.MixDigitalDoctorFeign;
import com.ykl.med.mix.service.doctor.MixDigitalDoctorService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/21
 */
@RestController
@RequestMapping("/mix/digitalDoctor")
public class MixDigitalDoctorController implements MixDigitalDoctorFeign {

    @Resource
    private MixDigitalDoctorService mixDigitalDoctorService;

    @Override
    @PostMapping("/digitalDoctor/page")
    public PageResult<DigitalDoctorVO> page(@RequestBody QueryDigitalDoctorPageReqVO reqVO) {
        return mixDigitalDoctorService.page(reqVO);
    }

    @Override
    @PostMapping("/generateDigital/page")
    public PageResult<GenerateDigitalVideoVO> generateDigitalVideoPage(@RequestBody QueryGenerateDigitalVideoPageReqVO reqVO) {
        return mixDigitalDoctorService.generateDigitalVideoPage(reqVO);
    }

    @Override
    @PostMapping("/digitalDoctor/list")
    public List<DigitalDoctorVO> list(@RequestBody QueryDigitalDoctorReqVO reqVO) {
        return mixDigitalDoctorService.list(reqVO);
    }


    @Override
    @PostMapping("/generateVideo")
    public void generateVideo(@Valid @RequestBody GenerateVideoReqVO reqVO) {
        mixDigitalDoctorService.generateVideo(reqVO);
    }


    @Override
    @PostMapping("/generateVideoEnable")
    public void generateVideoEnable(@Valid @RequestBody GenerateVideoEnableReqVO reqVO) {
        mixDigitalDoctorService.generateVideoEnable(reqVO);
    }

    @Override
    @PostMapping("/generateVideoDelete")
    public void generateVideoDelete(@RequestParam(name = "id") Long id) {
        mixDigitalDoctorService.generateVideoDelete(id);
    }

    @Override
    @PostMapping("/saveVideoSelfIntroduction")
    public void saveVideoSelfIntroduction(@Valid @RequestBody SaveVideoSelfIntroduction reqVO) {
        mixDigitalDoctorService.saveVideoSelfIntroduction(reqVO);
    }


}
