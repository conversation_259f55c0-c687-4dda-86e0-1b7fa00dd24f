package com.ykl.med.mix.service.event.strategy;

import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.enums.SystemMessageType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.push.vo.event.EventTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 随访通知
 */
@Service
@Slf4j
@EventTaskTypeAnnotation({
        EventTaskType.FOLLOW_START,
        EventTaskType.FOLLOW_END,
        EventTaskType.FOLLOW_LOST})
public class FollowNoticeTaskStrategy extends AbstractEventTaskStrategy {
    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {
        SystemMessageType type = SystemMessageType.valueOf(eventTaskVO.getBizType().name());
        PatientVO patientVO = patientFeign.getPatientById(eventTaskVO.getPatientId());
        if (patientVO == null || patientVO.getBindDoctorId() == null) {
            return;
        }
        String content = getSystemMessageContent(type)
                .replace("patientName", patientVO.getName())
                .replace("followName", eventTaskVO.getExtJson().getString("followName"))
                .replace("time1", eventTaskVO.getExtJson().getString("time1"));
        Long messageId = createAndAddSystemMessage(patientVO.getBindDoctorId(), content, eventTaskVO.getId().toString(), patientVO.getPatientId(), type, eventTaskVO.getBizId());
        if (type == SystemMessageType.FOLLOW_END) {
            EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                    .setRequestId(messageId.toString())
                    .setBizId(eventTaskVO.getBizId())
                    .setBizType(EventTaskType.SYSTEM_MESSAGE_OVERDUE)
                    .setEventTime(LocalDateTime.now())
                    .setExecuteTime(getMessageOverdueTime(SystemMessageType.FOLLOW_END.name()))
                    .setPatientId(patientVO.getPatientId())
                    .setUserId(patientVO.getBindDoctorId());
            eventTaskFeign.addEventTask(eventTaskAddVO);
        }
    }
}
