package com.ykl.med.mix.mq.rocket;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.ykl.med.framework.common.constants.topic.PatientFormTopic;
import com.ykl.med.framework.common.constants.topic.RehabMangeTopic;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.api.FormFeign;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.enums.QuestionnairesType;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.masterdata.vo.req.FormPageReqVO;
import com.ykl.med.masterdata.vo.resp.FormListRespVO;
import com.ykl.med.mix.service.user.MixUserService;
import com.ykl.med.mix.vo.UserInfoVO;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.push.api.SystemMessageFeign;
import com.ykl.med.push.enums.SystemMessageType;
import com.ykl.med.push.vo.sys.SystemMessageReqVO;
import com.ykl.med.rehab.enums.RehabType;
import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import com.ykl.med.user.vo.req.SimpleUserPageReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.ykl.med.mix.service.event.strategy.AbstractEventTaskStrategy.SYSTEM_MESSAGE_CONTENT_KEY;
import static com.ykl.med.mix.service.event.strategy.AbstractEventTaskStrategy.SYSTEM_MESSAGE_USER_ID;


@Slf4j
@Component
@RocketMQMessageListener(
        topic = PatientFormTopic.TOPIC,
        consumerGroup = PatientFormTopic.CONSUMER_GROUP
)
public class PatientFormListener implements RocketMQListener<MessageExt> {
    @Resource
    private PatientFeign patientFeign;
    @Resource
    private CommonConfigFeign commonConfigFeign;
    @Resource
    private MixUserService mixUserService;
    @Resource
    private SystemMessageFeign systemMessageFeign;
    @Resource
    private RocketMQTemplate rocketMqTemplate;
    @Resource
    private FormFeign formFeign;


    @Override
    public void onMessage(MessageExt messageExt) {
        //todo 要保证准确性还是要走event task
        PatientFormVO patientFormVO = JSON.parseObject(messageExt.getBody(), PatientFormVO.class);
        log.info("PatientFormListener 接收到消息：{}", JSON.toJSONString(patientFormVO));
        if (patientFormVO.getPatientId() == null) {
            log.info(" patientId is null");
            return;
        }

        SystemMessageType type = SystemMessageType.FORM_ADD;
        PatientBaseVO patient = patientFeign.getPatientBaseById(patientFormVO.getPatientId());
        if (patient.getMemberStatus() != CommonStatusEnum.ENABLE || patient.getMedicalTeamId() == null) {
            return;
        }

        JSONObject jsonObject = commonConfigFeign.getCommonConfigValueJsonByKey(SYSTEM_MESSAGE_CONTENT_KEY);
        StringBuilder formName = new StringBuilder();
        if (patientFormVO.getType() == PatientFormBizType.SPORT) {
            List<FormListRespVO> forms = formFeign.list(new FormPageReqVO()
                    .setOnline(true)
                    .setCategory(QuestionnairesType.SPORT_REHAB.name())
                    .setStatus(CommonStatusEnum.ENABLE));
            if (CollectionUtils.isNotEmpty(forms)) {
                for (FormListRespVO form : forms) {
                    formName.append(",").append(form.getLabel());
                }
            }
        } else if (patientFormVO.getType() == PatientFormBizType.FUNC) {
            List<FormListRespVO> forms = formFeign.list(new FormPageReqVO()
                    .setOnline(true)
                    .setCategory(QuestionnairesType.FUNC_REHAB.name())
                    .setStatus(CommonStatusEnum.ENABLE));
            if (CollectionUtils.isNotEmpty(forms)) {
                for (FormListRespVO form : forms) {
                    formName.append(",").append(form.getLabel());
                }
            }
        } else {
            formName.append(patientFormVO.getFormName());
        }
        String content = jsonObject.getString(SystemMessageType.FORM_ADD.name())
                .replace("patientName", patient.getName())
                .replace("formName", formName.toString());


        JSONObject defaultDoctor = commonConfigFeign.getCommonConfigValueJsonByKey("default_followup_doctor_key");
        List<Long> doctorIds = new ArrayList<>();
        String doctorKey;
        RehabType rehabType = null;
        switch (patientFormVO.getType()) {
            case FUNC:
                doctorKey = "doctor_func";
                rehabType = RehabType.FUNC;
                break;
            case SPORT:
                doctorKey = "doctor_sport";
                rehabType = RehabType.SPORT;
                break;
            case NUTRITION:
                doctorKey = "doctor_nutritional";
                rehabType = RehabType.NUTRITION;
                break;
            case PSYCHOLOGY:
                doctorKey = "doctor_psycho";
                rehabType = RehabType.PSYCHO;
                break;
            case FOLLOWUP_TRACK:
            case FOLLOWUP_INSPECT:
                doctorKey = "doctor_followup";
                break;
            default:
                return;
        }
        if (defaultDoctor.containsKey(doctorKey)) {
            JSONArray doctorPsycho = defaultDoctor.getJSONArray(doctorKey);
            for (int i = 0; i < doctorPsycho.size(); i++) {
                doctorIds.add(doctorPsycho.getLong(i));
            }
        } else {
            SimpleUserPageReqVO reqVO = new SimpleUserPageReqVO();
            reqVO.setPageSize(1000);
            reqVO.setMedicalTeamId(String.valueOf(patient.getMedicalTeamId()));
            PageResult<UserInfoVO> userSimpleVOList = mixUserService.page(reqVO);
            doctorIds = userSimpleVOList.getList().stream().map(UserInfoVO::getId).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(doctorIds)) {
            for (Long doctorId : doctorIds) {
                SystemMessageReqVO systemMessageReqVO = new SystemMessageReqVO()
                        .setUserId(doctorId)
                        .setContent(content)
                        .setRequestId(messageExt.getMsgId() + "_" + doctorId)
                        .setSenderId(SYSTEM_MESSAGE_USER_ID)
                        .setPatientId(patientFormVO.getPatientId())
                        .setType(type)
                        .setBizId(patientFormVO.getBizId() == null ? null : patientFormVO.getBizId().toString());
                systemMessageFeign.addSystemMessage(systemMessageReqVO);
            }
        }

        if (rehabType != null) {
            //康复管理es变动
            RehabManageMqMessageVO rehabManageMessage = new RehabManageMqMessageVO()
                    .setPatientId(patientFormVO.getPatientId())
                    .setRehabId(patientFormVO.getBizId() != null ? patientFormVO.getBizId() : null)
                    .setRehabType(rehabType);
            rocketMqTemplate.syncSend(RehabMangeTopic.TOPIC, rehabManageMessage);
        }

    }
}
