package com.ykl.med.mix.mq.rocket;

import com.alibaba.fastjson2.JSON;
import com.ykl.med.followup.api.FollowupTrackFeign;
import com.ykl.med.followup.entity.enums.FollowupTrackSummaryStatusEnum;
import com.ykl.med.followup.entity.param.AiFollowupSummaryReqVO;
import com.ykl.med.followup.entity.param.SaveFollowupSummaryStatusReqVO;
import com.ykl.med.framework.common.constants.topic.FollowupTopic;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.mix.message.AiFollowupSummaryMessage;
import com.ykl.med.mix.service.follow.MixFollowupService;
import com.ykl.med.rocketmq.MessageDelayLevel;
import com.ykl.med.rocketmq.RocketMQService;
import com.ykl.med.shift.constans.ShiftErrorCodeConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2025/6/19
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = FollowupTopic.TOPIC,
        consumerGroup = FollowupTopic.AiFollowupSummary.CONSUMER_GROUP,
        selectorExpression = FollowupTopic.AiFollowupSummary.TAG
)
public class FollowupAiFollowupSummaryListener implements RocketMQListener<AiFollowupSummaryMessage> {

    private final FollowupTrackFeign followupTrackFeign;
    private final MixFollowupService mixFollowupService;
    private final RocketMQService rocketMQService;
    private final StringRedisTemplate stringRedisTemplate;

    // 最大重试次数
    private static final int MAX_RETRY_TIMES = 3;
    // 延迟等级映射表
    private static final int[] DELAY_LEVELS = {MessageDelayLevel.LEVEL_1, MessageDelayLevel.LEVEL_2, MessageDelayLevel.LEVEL_3}; // 对应延迟秒数
    // 消息重试计数Key
    private static final String RETRY_KEY_PREFIX = "followup:aiFollowupSummary:retry:";

    @Override
    public void onMessage(AiFollowupSummaryMessage message) {
        log.info("【AI 随访小结】 监听：request:{}", JSON.toJSONString(message));
        try {
            // 处理业务
            mixFollowupService.aiFollowupSummary(new AiFollowupSummaryReqVO()
                    .setDoctorId(message.getDoctorId())
                    .setFollowupId(message.getFollowupId()));
            // 成功处理后清除计数器
            this.resetRetryTimes(message.getMessageId());
        } catch (ServiceException serviceException) {
            if (serviceException.getCode().equals(ShiftErrorCodeConstants.VIDEO_DOES_NOT_EXIST.getCode())) {
                log.error("AI 随访小结失败:{}", serviceException.getMessage());
                // 没有视频文件，说明没有打视频，直接失败生成失败
                followupTrackFeign.saveFollowupSummaryStatus(new SaveFollowupSummaryStatusReqVO()
                        .setDoctorId(message.getDoctorId())
                        .setFollowupId(message.getFollowupId())
                        .setSummaryStatusEnums(FollowupTrackSummaryStatusEnum.FAIL));
            } else if (serviceException.getCode().equals(ShiftErrorCodeConstants.VOICE_TEXT_DOES_NOT_EXIST.getCode())) {
                // 有视频，没有转文本，重试
                retry(message);
            }
        } catch (Exception e) {
            log.error("AI 随访小结失败", e);
            // 生成失败
            followupTrackFeign.saveFollowupSummaryStatus(new SaveFollowupSummaryStatusReqVO()
                    .setDoctorId(message.getDoctorId())
                    .setFollowupId(message.getFollowupId())
                    .setSummaryStatusEnums(FollowupTrackSummaryStatusEnum.FAIL));
        }
    }

    private void retry(AiFollowupSummaryMessage message) {
        // 获取当前重试次数
        int retryTimes = this.incrementRetryTimes(message.getMessageId());
        // 超过最大重试次数，则丢弃消息
        if (retryTimes > MAX_RETRY_TIMES) {
            log.error("消息已达到最大重试次数，丢弃消息: {}", JSON.toJSONString(message));
            // 生成失败
            followupTrackFeign.saveFollowupSummaryStatus(new SaveFollowupSummaryStatusReqVO()
                    .setDoctorId(message.getDoctorId())
                    .setFollowupId(message.getFollowupId())
                    .setSummaryStatusEnums(FollowupTrackSummaryStatusEnum.FAIL));
            return;
        }
        // 计算下一次延迟等级
        int delayLevelIndex = retryTimes - 1; // 将重试次数转为数组索引
        int delayLevel = DELAY_LEVELS[Math.min(delayLevelIndex, DELAY_LEVELS.length - 1)];
        rocketMQService.sendDelayed(FollowupTopic.AiFollowupSummary.DESTINATION, message, delayLevel);
        log.info("消息已延迟重试，延迟等级: {}, 重试次数: {}", delayLevel, retryTimes);
    }

    private int incrementRetryTimes(String messageId) {
        String key = RETRY_KEY_PREFIX + messageId;
        try {
            // 原子递增操作
            Long current = stringRedisTemplate.opsForValue().increment(key);
            // 首次写入时设置过期时间(5分钟)
            if (current != null && current == 1) {
                stringRedisTemplate.expire(key, Duration.ofMinutes(5));
            }
            return current != null ? current.intValue() : MAX_RETRY_TIMES + 1;
        } catch (Exception e) {
            // 降级处理：Redis异常时返回最大允许值
            log.error("Redis操作失败 messageId={}", messageId, e);
            return MAX_RETRY_TIMES + 1;
        }
    }


    public void resetRetryTimes(String messageId) {
        String key = RETRY_KEY_PREFIX + messageId;
        stringRedisTemplate.delete(key);
    }
}
