package com.ykl.med.mix.service.event.strategy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.enums.SystemMessageType;
import com.ykl.med.push.vo.event.EventTaskVO;
import com.ykl.med.symptoms.api.report.IntelligentReportFeign;
import com.ykl.med.symptoms.enums.IntelligentReportType;
import com.ykl.med.symptoms.vo.report.IntelligentReportVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 待审核报告
 */
@Service
@Slf4j
@EventTaskTypeAnnotation({EventTaskType.REPORT_TO_BE_REVIEWED})
public class WaitReviewedReportTaskStrategy extends AbstractEventTaskStrategy {
    @Resource
    private PatientFeign patientFeign;
    @Resource
    private IntelligentReportFeign intelligentReportFeign;

    @Override
    public void handleEvent(EventTaskVO eventTaskVO) {
        PatientBaseVO patientVO = patientFeign.getPatientBaseById(eventTaskVO.getPatientId());
        if (patientVO == null || patientVO.getBindDoctorId() == null) {
            return;
        }
        String report = "";
        JSONObject jsonObject = eventTaskVO.getExtJson();
        if (jsonObject != null && jsonObject.get("report") != null) {
            report = jsonObject.getString("report");
        }
        SystemMessageType type = SystemMessageType.valueOf(eventTaskVO.getBizType().name());
        String content = getSystemMessageContent(type)
                .replace("patientName", patientVO.getName())
                .replace("report", report);
        IntelligentReportVO intelligentReportVO = intelligentReportFeign.getById(Long.valueOf(eventTaskVO.getBizId()));
        List<Long> doctorIds = new ArrayList<>();
        if (intelligentReportVO.getType() == IntelligentReportType.NUTRITIONAL_REPORT) {
            JSONObject defaultDoctor = commonConfigFeign.getCommonConfigValueJsonByKey("default_followup_doctor_key");
            if (defaultDoctor.containsKey("doctor_nutritional")) {
                JSONArray doctorPsycho = defaultDoctor.getJSONArray("doctor_nutritional");
                for (int i = 0; i < doctorPsycho.size(); i++) {
                    doctorIds.add(doctorPsycho.getLong(i));
                }
            } else {
                doctorIds.add(patientVO.getBindDoctorId());
            }
        }
        for (Long doctorId : doctorIds) {
            createAndAddSystemMessage(doctorId, content, eventTaskVO.getId() + "_" + doctorId, patientVO.getPatientId(), type, eventTaskVO.getBizId());
        }
    }
}
