package com.ykl.med.mix.service.stat;

import com.ykl.med.edu.api.EduStatFeign;
import com.ykl.med.medical.api.MedicalOrderStatFeign;
import com.ykl.med.mix.vo.stat.MixPatientStatVO;
import com.ykl.med.rehab.api.stat.FuncStatFeign;
import com.ykl.med.rehab.api.stat.NutritionalStatFeign;
import com.ykl.med.rehab.api.stat.PsychoStatFeign;
import com.ykl.med.rehab.api.stat.SportStatFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class MixPatientStatService {
    @Resource
    private EduStatFeign eduStatFeign;
    @Resource
    private SportStatFeign sportStatFeign;
    @Resource
    private FuncStatFeign funcStatFeign;
    @Resource
    private PsychoStatFeign psychoStatFeign;
    @Resource
    private NutritionalStatFeign nutritionalStatFeign;
    @Resource
    private MedicalOrderStatFeign medicalOrderStatFeign;

    public MixPatientStatVO getStatByPatientId(Long patientId) {
        MixPatientStatVO patientStat = new MixPatientStatVO();
        patientStat.setEduStat(eduStatFeign.getStatByPatientId(patientId));
        patientStat.setSportStat(sportStatFeign.getStatByPatientId(patientId));
        patientStat.setFuncStat(funcStatFeign.getStatByPatientId(patientId));
        patientStat.setPsychoStat(psychoStatFeign.getStatByPatientId(patientId));
        patientStat.setNutritionalStat(nutritionalStatFeign.getStatByPatientId(patientId));
        patientStat.setMedicalOrderStat(medicalOrderStatFeign.getStatByPatientId(patientId));
        return patientStat;
    }
}
