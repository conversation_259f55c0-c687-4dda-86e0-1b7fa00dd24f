package com.ykl.med.mix.vo.patient;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "患者医生绑定请求参数")
public class PatientDoctorBindReqVO {
    @Schema(description = "患者ID", example = "1")
    @NotNull(message = "患者ID不能为空")
    @Stringify
    private Long patientId;

    @Schema(description = "医生ID", example = "1")
    @Stringify
    private Long doctorId;

    @Schema(description = "医疗组ID", example = "1")
    @Stringify
    @NotNull(message = "医疗组ID不能为空")
    private Long medicalTeamId;

}
