package com.ykl.med.mix.api.user;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.user.vo.req.RolePageReqVO;
import com.ykl.med.user.vo.resp.RoleListRespVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "ykl-mix-service", path = "/user/role")
public interface MixUserRoleFeign {
    /**
     * 分页获取用户角色列表。
     *
     * @param reqVO 分页获取用户角色列表请求对象，含有分页信息。
     * @return 返回一个包含所有用户角色信息的列表。
     */
    @PostMapping("/page")
    PageResult<RoleListRespVO> pageRoles(@RequestBody RolePageReqVO reqVO);
}
