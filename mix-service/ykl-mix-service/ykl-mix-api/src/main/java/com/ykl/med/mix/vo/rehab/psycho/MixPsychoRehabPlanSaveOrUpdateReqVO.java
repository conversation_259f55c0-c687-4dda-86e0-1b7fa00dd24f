package com.ykl.med.mix.vo.rehab.psycho;

import com.ykl.med.rehab.vo.req.psycho.PsychoRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.shift.vo.req.CreateByDirectlyReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "WebPsychoRehabPlanSaveOrUpdateReqVO", description = "web心理康复计划 请求VO")
public class MixPsychoRehabPlanSaveOrUpdateReqVO extends PsychoRehabPlanSaveOrUpdateReqVO {
    @Schema(description = "新增的问诊参数")
    private List<CreateByDirectlyReqVO> addConsultations;
}
