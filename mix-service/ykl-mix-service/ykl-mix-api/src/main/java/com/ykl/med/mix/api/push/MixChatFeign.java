package com.ykl.med.mix.api.push;

import com.ykl.med.mix.vo.push.MixChatUserInfoVO;
import com.ykl.med.user.vo.req.IdListReqVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "ykl-mix-service", path = "mixChat")
public interface MixChatFeign {
    @PostMapping("/getChatUserInfoByUserIds")
    @Operation(summary = "获取会话中的用户信息")
    List<MixChatUserInfoVO> getChatUserInfoByUserIds(@RequestBody IdListReqVO idListReqVO);
}
