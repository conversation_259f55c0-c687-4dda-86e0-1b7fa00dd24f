package com.ykl.med.mix.api.stat;

import com.ykl.med.mix.vo.stat.MixPatientStatVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-mix-service", path = "mixPatientStat")
public interface MixPatientStatFeign {
    @PostMapping("/getStatByPatientId")
    MixPatientStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId);
}
