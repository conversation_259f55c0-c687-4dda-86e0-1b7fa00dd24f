package com.ykl.med.mix.vo.patient;

import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.records.vo.AllergyHistoryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "患者信息-app端的对象")
public class MixPatientAppDetailVO extends PatientVO {
    @Schema(description = "查询者与本人关系", defaultValue = "女儿")
    private String relation;

    @Schema(description = "病种", defaultValue = "病种")
    private String diseases;

    @Schema(description = "病种阶段", defaultValue = "病种阶段")
    private String stage;

    @Schema(description = "体重(kg)", defaultValue = "65")
    private BigDecimal weight;

    @Schema(description = "BMI")
    private String bmi;

    @Schema(description = "过敏史", example = "[]")
    private List<AllergyHistoryVO> allergyHistories;
}
