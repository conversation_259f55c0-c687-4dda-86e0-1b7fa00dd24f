package com.ykl.med.mix.api.follow;

import com.ykl.med.followup.entity.AiFollowupSummaryVO;
import com.ykl.med.followup.entity.param.*;
import com.ykl.med.followup.entity.vo.FollowupInspectVO;
import com.ykl.med.followup.entity.vo.FollowupListByDoctorGroupVO;
import com.ykl.med.followup.entity.vo.FollowupTrackVO;
import com.ykl.med.framework.common.pojo.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/28
 */
@FeignClient(value = "ykl-mix-service", path = "/mix/follow")
public interface MixFollowupFeign {

    @PostMapping("/followupListByDoctorGroup")
    PageResult<FollowupListByDoctorGroupVO> followupListByDoctorGroup(@Valid @RequestBody QueryFollowupListByDoctorGroupParam param);

    @PostMapping("/list")
    List<FollowupTrackVO> list(@Valid @RequestBody QueryFollowupTrackParam param);

    @PostMapping("/details")
    FollowupTrackVO details(@RequestParam(name = "id") Long id);

    @PostMapping("/info")
    FollowupInspectVO info(@Valid @RequestBody QueryFollowupInspectInfoParam param);

    @PostMapping("/current")
    FollowupTrackVO current(@Valid @RequestBody QueryCurrentFollowupTrackParam param);

    @PostMapping("/start")
    void start(@Valid @RequestBody StartFollowupTrackParam param);

    @PostMapping("/finish")
    void finish(@Valid @RequestBody FinishFollowupTrackParam param);

    @PostMapping("/finishItemQuestion")
    void finishItemQuestion(@Valid @RequestBody FinishItemQuestionParam param);

    @PostMapping("/startConsult")
    Long startConsult(@RequestBody StartFollowupConsultReqVO reqVO);

    @PostMapping("/updateConsultTime")
    void updateConsultTime(@Valid @RequestBody UpdateFollowupConsultTimeReqVO reqVO);

    @PostMapping("/aiFollowupSummary")
    AiFollowupSummaryVO aiFollowupSummary(@RequestBody AiFollowupSummaryReqVO reqVO);

    @PostMapping("/initAllFollowupTrackState")
    void initAllStateByNuStart();

    @PostMapping("/create")
    void create(@Valid @RequestBody CreateFollowupTrackParam param);
}
