package com.ykl.med.mix.vo.consult;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/11
 */
@Data
public class  ConsultOfflineVO {

    @Schema(description = "门诊类型")
    private String consultTypeDesc = "线下门诊(His)";

    @Schema(description = "患者ID")
    @Stringify
    private Long patientId;

    @Schema(description = "患者名称")
    private String patientName;

    @Schema(description = "患者年龄")
    private Integer patientAge;

    @Schema(description = "就诊ID")
    @Stringify
    private Long visitId;

    @Schema(description = "就诊号")
    private String visitNo;

    @Schema(description = "主诉")
    private String zs;

    @Schema(description = "发病时间")
    @TimestampConvert
    private LocalDateTime fbsj;

    @Schema(description = "既往史")
    private String jws;

    @Schema(description = "过敏史")
    private String gms;

    @Schema(description = "病史描述")
    private String bsms;

    @Schema(description = "诊断编码")
    private String zdbm;

    @Schema(description = "诊断名称")
    private String zdmc;

    @Schema(description = "就诊时间")
    @TimestampConvert
    private LocalDateTime visitDate;

    @Schema(description = "诊结时间")
    @TimestampConvert
    private LocalDateTime zjsj;

    @Schema(description = "门诊医生")
    private String mzys;

    @Schema(description = "就诊科室")
    private String jzks;

    @Schema(description = "诊断列表")
    private List<HisDiagVO> diagList;

    @Schema(description = "医嘱列表")
    private List<HisOrderVO> orderList;


    @Data
    public static class HisDiagVO{
        @Schema(description = "是否主诊断(false-不是、true-是)")
        private Boolean isMainDiagnos;

        @Schema(description = "ICD码")
        private String icdCode;

        @Schema(description = "诊断名称")
        private String diseaseName;

        @Schema(description = "中西医类别")
        private String diagnosMode;

    }

    @Data
    public static class HisOrderVO{
        @Schema(description = "医嘱ID")
        private String orderId;

        @Schema(description = "医嘱名称")
        private String orditemName;

        @Schema(description = "医嘱类型")
        private String orderType;

        @Schema(description = "开嘱时间")
        @TimestampConvert
        private LocalDateTime createDate;

        @Schema(description = "处方ID")
        private String recipeId;

        @Schema(description = "处方号")
        private String recipeNo;

        @Schema(description = "互联网医院处方笺pdf预览地址")
        private String pdfUrl;
    }

}
