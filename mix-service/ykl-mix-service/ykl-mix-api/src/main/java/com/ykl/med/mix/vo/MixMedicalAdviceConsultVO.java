package com.ykl.med.mix.vo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.vo.ConsultDiagnosisVO;
import com.ykl.med.medical.enums.PrescriptionAuditStateEnum;
import com.ykl.med.medical.vo.medicalAdvice.MedicalAdviceItemVO;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.enums.consult.ConsultTypeEnum;
import com.ykl.med.shift.vo.resp.ConsultConclusionVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/14
 */
@Data
public class MixMedicalAdviceConsultVO {

    @Schema(description = "问诊id")
    @Stringify
    private Long consultId;

    @Schema(description = " 问诊时间（预定开始时间）")
    @TimestampConvert
    private LocalDateTime planVisitStartTime;

    @Schema(description = " 问诊时间（预定结束时间）")
    @TimestampConvert
    private LocalDateTime planVisitEndTime;

    @Schema(description = "问诊开始时间")
    @TimestampConvert
    private LocalDateTime visitStartTime;

    @Schema(description = "问诊结束时间")
    @TimestampConvert
    private LocalDateTime visitEndTime;

    @Schema(description = "类型")
    private ConsultTypeEnum consultType;

    @Schema(description = "状态")
    private ConsultStateEnum consultState;

    @Schema(description = "支付时间")
    @TimestampConvert
    private LocalDateTime payTime;

    @Schema(description = "标准诊断")
    private List<ConsultDiagnosisVO> diagnosis;

    @Schema(description = "发病时间")
    @TimestampConvert
    private LocalDate onsetTime;

    @Schema(description = "主诉")
    private String appeal;

    @Schema(description = "既往史")
    private String pastMedicalHistory;

    @Schema(description = "过敏史")
    private String allergyHistory;

    @Schema(description = "病史描述")
    private String medicalHistoryDesc;

    @Schema(description = "处方号")
    private String prescriptionNo;

    @Schema(description = "处方文件PDF")
    private String prescriptionFile;

    @Schema(description = "审核状态 PrescriptionAuditStateEnum 枚举")
    private PrescriptionAuditStateEnum auditState;

    @Schema(description = "审方的时间")
    @TimestampConvert
    private LocalDateTime auditTime;

    @Schema(description = "药师id")
    @Stringify
    private Long auditOperationId;

    @Schema(description = "药师姓名")
    private String auditOperationName;

    @Schema(description = "审核结论")
    private String auditConclusion;

    @Schema(description = "问诊结论")
    private ConsultConclusionVO conclusion;

    @Schema(description = "明细")
    private List<MedicalAdviceItemVO> item;

}
