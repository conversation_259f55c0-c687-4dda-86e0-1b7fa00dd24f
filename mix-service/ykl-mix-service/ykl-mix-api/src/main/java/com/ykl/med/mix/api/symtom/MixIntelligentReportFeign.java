package com.ykl.med.mix.api.symtom;

import com.ykl.med.symptoms.vo.report.IntelligentReportSummaryReqVO;
import com.ykl.med.symptoms.vo.report.IntelligentReportVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "ykl-mix-service", path = "symptomReport")
public interface MixIntelligentReportFeign {
    @PostMapping("/aiMedicationInstructionsReport")
    void aiMedicationInstructionsReport(@RequestBody IntelligentReportVO vo);

    @Operation(summary = "ai症状报告生成")
    @PostMapping("/aiSymptomReport")
    void aiSymptomReport(@RequestBody IntelligentReportVO vo);

    @Operation(summary = "症状报告综述生成")
    @PostMapping("/aiSymptomReportSummary")
    String aiSymptomReportSummary(@RequestBody IntelligentReportSummaryReqVO vo);

    @PostMapping("/aiInterpretationOfImageReport")
    void aiInterpretationOfImageReport(@RequestBody IntelligentReportVO vo);

    @PostMapping("/aiInterpretationOfInspectionReport")
    void aiInterpretationOfInspectionReport(@RequestBody IntelligentReportVO vo);

    @PostMapping("/aiNutritionReport")
    void aiNutritionReport(@RequestBody IntelligentReportVO vo);

    @PostMapping("/aiNutritionIntakeAssessmentReportUrl")
    void aiNutritionIntakeAssessmentReportUrl(@RequestBody IntelligentReportVO vo);

    @PostMapping("/aiNutritionOnlySummary")
    String aiNutritionOnlySummary(@RequestBody IntelligentReportSummaryReqVO reqVO);
}
