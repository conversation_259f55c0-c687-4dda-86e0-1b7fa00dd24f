package com.ykl.med.mix.vo.stat;

import com.ykl.med.edu.vo.stat.EduStatVO;
import com.ykl.med.medical.vo.stat.MedicalOrderStatVO;
import com.ykl.med.rehab.vo.resp.stat.FuncStatVO;
import com.ykl.med.rehab.vo.resp.stat.NutritionalStatVO;
import com.ykl.med.rehab.vo.resp.stat.PsychoStatVO;
import com.ykl.med.rehab.vo.resp.stat.SportStatVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "患者统计(患者自我管理)")
public class MixPatientStatVO {
    @Schema(description = "用药统计")
    private MedicalOrderStatVO medicalOrderStat;
    @Schema(description = "患教统计")
    private EduStatVO eduStat;
    @Schema(description = "心理康复统计")
    private PsychoStatVO psychoStat;
    @Schema(description = "营养康复统计")
    private NutritionalStatVO nutritionalStat;
    @Schema(description = "功能康复统计")
    private FuncStatVO funcStat;
    @Schema(description = "运动康复统计")
    private SportStatVO sportStat;
}
