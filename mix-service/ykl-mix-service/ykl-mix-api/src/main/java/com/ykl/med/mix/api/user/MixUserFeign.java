package com.ykl.med.mix.api.user;

import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.vo.UserInfoVO;
import com.ykl.med.push.vo.SmsCodeCheckReqVO;
import com.ykl.med.user.vo.req.LoginOrRegisterReqVO;
import com.ykl.med.user.vo.req.RefreshTokenReqVO;
import com.ykl.med.user.vo.req.SimpleUserPageReqVO;
import com.ykl.med.user.vo.req.UserPasswordUpdateReqVO;
import com.ykl.med.user.vo.resp.LoginOrRegisterRespVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "ykl-mix-service", path = "/user")
public interface MixUserFeign {
    /**
     * 获取账号及对应的医疗人员信息
     * */
    @PostMapping("/info")
    UserInfoVO info(@RequestBody IdReqVO reqVO);

    @PostMapping("/page")
    PageResult<UserInfoVO> page(@RequestBody SimpleUserPageReqVO reqVO);

    /**
     * 用户登录
     * */
    @PostMapping("/loginOrRegister")
    LoginOrRegisterRespVO loginOrRegister(@RequestBody LoginOrRegisterReqVO reqVO);

    @PostMapping("/refreshToken")
    LoginOrRegisterRespVO refreshToken(@Valid @RequestBody RefreshTokenReqVO reqVO);

    /**
     * 忘记密码: 使用手机验证码找回
     * */
    @PostMapping("/ignorePassword")
    void ignorePassword(@RequestBody UserPasswordUpdateReqVO reqVO);

    @PostMapping("/updatePassword")
    void updatePassword(UserPasswordUpdateReqVO reqVO);

    @PostMapping("/smsCheck")
    void smsCheck(@Valid @RequestBody SmsCodeCheckReqVO reqVO);

}
