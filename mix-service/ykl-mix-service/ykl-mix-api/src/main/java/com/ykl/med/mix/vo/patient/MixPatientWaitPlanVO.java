package com.ykl.med.mix.vo.patient;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "患者确认方案页面信息")
public class MixPatientWaitPlanVO {

    /**
     * 是否有症状管理方案
     */
    @Schema(description = "是否有症状管理方案", example = "true")
    private Boolean hasSymptomPlan = false;
    /**
     * 是否有用药管理方案
     */
    @Schema(description = "是否有用药管理方案", example = "true")
    private Boolean hasMedicinePlan = false;

    /**
     * 是否有随访管理方案
     */
    @Schema(description = "是否有随访管理方案", example = "true")
    private Boolean hasFollowPlan = false;

    /**
     * 是否有运动康复方案
     */
    @Schema(description = "是否有运动康复方案", example = "true")
    private Boolean hasSportPlan = false;

    /**
     * 是否有营养康复方案
     */
    @Schema(description = "是否有营养康复方案", example = "true")
    private Boolean hasNutritionPlan = false;

    /**
     * 是否有功能康复方案
     */
    @Schema(description = "是否有功能康复方案", example = "true")
    private Boolean hasFuncPlan = false;

    /**
     * 是否有心理康复方案
     */
    @Schema(description = "是否有心理康复方案", example = "true")
    private Boolean hasPsychoPlan = false;

}
