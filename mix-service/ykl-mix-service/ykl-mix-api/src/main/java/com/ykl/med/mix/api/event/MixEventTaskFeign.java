package com.ykl.med.mix.api.event;

import com.ykl.med.push.vo.event.EventTaskVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "ykl-mix-service", path = "/mixEventTask")
public interface MixEventTaskFeign {

    @PostMapping("/executeEventTask")
    void executeEventTask(@RequestBody EventTaskVO eventTaskVO);
}
