package com.ykl.med.mix.api;

import com.ykl.med.masterdata.vo.form.PatientFormWriteReqVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-mix-service", path = "/mixPatientForm")
public interface MixPatientFormFeign {
    @PostMapping("/send")
    void send(@RequestParam(name = "id") Long id, @RequestParam(name = "fromUserId") Long fromUserId);

    @PostMapping("/write")
    void write(@RequestBody PatientFormWriteReqVO reqVO);

    @PostMapping("/aiNutritionQuestionnaireSummary")
    String aiNutritionQuestionnaireSummary(@RequestBody PatientFormWriteReqVO reqVO);
}
