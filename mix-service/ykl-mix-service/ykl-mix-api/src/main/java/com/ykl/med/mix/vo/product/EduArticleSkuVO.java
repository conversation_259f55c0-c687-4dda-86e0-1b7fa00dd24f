package com.ykl.med.mix.vo.product;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "EduArticleSkuVO", description = "患教视频SKU VO")
public class EduArticleSkuVO {
    @Stringify
    @Schema(description = "skuId", example = "1")
    private Long skuId;

    @Schema(description = "原价", defaultValue = "0")
    private Integer originalPrice;

    @Schema(description = "会员价", defaultValue = "0")
    private Integer memberPrice;

    @Schema(description = "是否以划线形式展示原价", defaultValue = "false")
    private Boolean lineOriginalPrice;

    @Schema(description = "零售价", defaultValue = "0")
    private Integer retailPrice;

    @Schema(description = "实际售价（会员就是会员价，其他就是零售价）", defaultValue = "0")
    private Integer price;
}
