package com.ykl.med.mix.vo.patient;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "患者待处理VO")
public class MixPatientPendingVO {
    @Schema(description = "新加入患者计数", defaultValue = "0")
    private Long newPatientCount;

    @Schema(description = "待处理预警计数", defaultValue = "0")
    private Long pendingEarlyWarnCount;

    @Schema(description = "待处理MDT会诊计数", defaultValue = "0")
    private Long pendingMdtCount;

    @Schema(description = "待处理随访计数", defaultValue = "0")
    private Long pendingFollowUpCount;

    @Schema(description = "待处理接诊计数", defaultValue = "0")
    private Long pendingReceptionCount;

    @Schema(description = "待处理审核计数", defaultValue = "0")
    private Long pendingWaitCheckCount;
}