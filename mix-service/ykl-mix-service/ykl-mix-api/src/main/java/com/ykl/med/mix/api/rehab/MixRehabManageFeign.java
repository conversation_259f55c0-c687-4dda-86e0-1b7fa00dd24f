package com.ykl.med.mix.api.rehab;

import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(name = "ykl-mix-service", path = "mixRehabManage")
public interface MixRehabManageFeign {


    @RequestMapping("/refreshRehabManage")
    void refreshRehabManage(@RequestBody RehabManageMqMessageVO message);
}
