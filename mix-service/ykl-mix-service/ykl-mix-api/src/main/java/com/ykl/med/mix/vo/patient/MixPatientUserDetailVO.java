package com.ykl.med.mix.vo.patient;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.patient.vo.PatientUserDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
@Schema(description = "患者用户详细信息")
public class MixPatientUserDetailVO extends PatientUserDetailVO {

    /**
     * 旧接口保留的字段，和com.ykl.med.patient.vo.PatientVO.name 相同
     * 后续开发的接口请勿使用
     */
    @Schema(description = "名字", defaultValue = "患者名字")
    private String patientName;

    @Schema(description = "病种", defaultValue = "病种")
    private String diseases;

    @Schema(description = "病种阶段", defaultValue = "病种阶段")
    private String stage;

    @Schema(description = "确诊医院")
    private String diagnosisHospital;

    @Schema(description = "确诊日期")
    @TimestampConvert
    private LocalDate diagnosisDate;

    @Schema(description = "首诊医生")
    private String diagnosisDoctor;

    @Schema(description = "头像", example = "avatar.png")
    private String avatar;

}
