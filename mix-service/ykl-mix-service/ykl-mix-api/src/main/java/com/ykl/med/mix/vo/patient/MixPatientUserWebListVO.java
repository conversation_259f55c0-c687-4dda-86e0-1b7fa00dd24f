package com.ykl.med.mix.vo.patient;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@Schema(description = "Mix患者用户关系")
public class MixPatientUserWebListVO {
    @Stringify
    private Long userId;
    @Stringify
    @Schema(description = "患者Id", defaultValue = "1")
    private Long patientId;
    @Schema(description = "该手机号与患者关系", defaultValue = "女儿")
    private String relation;
    @Schema(description = "手机号", defaultValue = "18109074912")
    private String phone;


    @Schema(description = "名字", defaultValue = "患者名字")
    private String patientName;


    @Schema(description = "用户注册时间", example = "12153245234")
    @TimestampConvert
    private LocalDateTime userRegisterTime;

    @Schema(description = "用户最后登录时间", example = "12153245234")
    @TimestampConvert
    private LocalDateTime lastLoginTime;
}
