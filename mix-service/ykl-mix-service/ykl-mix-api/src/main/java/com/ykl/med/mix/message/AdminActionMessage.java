package com.ykl.med.mix.message;

import com.ykl.med.framework.common.pojo.BaseMqMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 后台管理操作日志
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class AdminActionMessage extends BaseMqMessage {

    /*** 登陆ip */
    private String ip;
    /*** 操作地址 */
    private String actionUri;
    /*** 方法：GET、POST、PUT、DELETE */
    private String actionMethod;
    /*** 参数 */
    private String actionParam;
}
