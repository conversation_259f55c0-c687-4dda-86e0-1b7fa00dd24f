package com.ykl.med.mix.api.consult;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.mix.vo.consult.*;
import com.ykl.med.shift.vo.req.*;
import com.ykl.med.shift.vo.resp.ConsultDoctorGroupVO;
import com.ykl.med.shift.vo.resp.ConsultDoctorPageVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@FeignClient(value = "ykl-mix-service", path = "/mix/consult")
public interface MixConsulFeign {

    @PostMapping("/medicalRecords")
    MixConsultMedicalRecordsVO medicalRecords(@Valid @RequestBody QueryMedicalRecordReqVO param);

    @PostMapping("/start")
    void start(@Valid @RequestBody StartConclusionReqVO param);

    @PostMapping("/finish")
    void finish(@Valid @RequestBody FinishConclusionReqVO param);

    @GetMapping("/details")
    MixConsultVO details(@RequestParam(name = "consultId") Long consultId);

    @PostMapping("/saveDiseaseHistory")
    void saveDiseaseHistory(@Valid @RequestBody SaveDiseaseHistoryReqVO param);

    @PostMapping("/list/doctor")
    List<ConsultDoctorGroupVO> listDoctor(@Valid @RequestBody QueryConsultListDoctorReqVO param);

    @PostMapping("/list/wait")
    PageResult<ConsultDoctorPageVO> listWait(@Valid @RequestBody QueryConsultWaitPageReqVO param);

    @PostMapping("/consultTimeOutTask")
    void consultTimeOutTask();

    @PostMapping("/offline/page")
    List<ConsultOfflineVO> offlineList(@Valid @RequestBody ConsultOfflineReqVO param);

    @PostMapping("/startVideo")
    RoomCreateRespVO startVideo(@RequestParam(name = "userId") Long userId,
                                @RequestParam(name = "consultId") Long consultId);

    @PostMapping("/closeVideo")
    void closeVideo(@RequestParam(name = "consultId") Long consultId,
                    @RequestParam(name = "joinFlag") Boolean joinFlag,
                    @RequestParam(name = "userId") Long userId);

    @PostMapping("/updateConsult")
    void updateConsult(@Valid @RequestBody UpdateConsultVO param);
}
