<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ykl.med</groupId>
        <artifactId>ykl-mix-service</artifactId>
        <version>0.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ykl-mix-api</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>

    <dependencies>
        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-common</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-push-api</artifactId>
            <version>0.0.1</version>
        </dependency>


        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-patient-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-product-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-order-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-records-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-medical-order-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-shift-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-followup-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-user-api</artifactId>
            <version>0.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-doctors-api</artifactId>
            <version>0.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-tag-api</artifactId>
            <version>0.0.1</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-rehab-api</artifactId>
            <version>0.0.1</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-edu-api</artifactId>
            <version>0.0.1</version>
        </dependency>

    </dependencies>
</project>
