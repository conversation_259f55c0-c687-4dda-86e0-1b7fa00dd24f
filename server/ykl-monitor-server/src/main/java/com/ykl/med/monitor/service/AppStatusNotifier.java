package com.ykl.med.monitor.service;

import com.ykl.med.wechat.sender.WechatRobot;
import de.codecentric.boot.admin.server.domain.entities.Instance;
import de.codecentric.boot.admin.server.domain.entities.InstanceRepository;
import de.codecentric.boot.admin.server.domain.events.InstanceEvent;
import de.codecentric.boot.admin.server.domain.events.InstanceStatusChangedEvent;
import de.codecentric.boot.admin.server.notify.AbstractEventNotifier;
import de.codecentric.boot.admin.server.notify.LoggingNotifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

@Service
@Slf4j
public class AppStatusNotifier extends AbstractEventNotifier {

    @Resource
    private WechatRobot wechatRobot;

    public AppStatusNotifier(InstanceRepository repository) {
        super(repository);
    }

    @Override
    protected Mono<Void> doNotify(InstanceEvent event, Instance instance) {
        return Mono.fromRunnable(() -> {
            if (event instanceof InstanceStatusChangedEvent) {
                wechatRobot.sendMsg(instance.getRegistration().getName()+"服务状态变更为："+((InstanceStatusChangedEvent) event).getStatusInfo().getStatus());
            } else {
                //log.info("Instance {} ({}) {}", instance.getRegistration().getName(), event.getInstance(),event.getType());
            }
        });
    }
}