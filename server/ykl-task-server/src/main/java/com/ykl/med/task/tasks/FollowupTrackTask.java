package com.ykl.med.task.tasks;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ykl.med.followup.api.FollowupTaskFeign;
import com.ykl.med.followup.api.FollowupTrackFeign;
import com.ykl.med.mix.api.MixRehabFeign;
import com.ykl.med.mix.api.follow.MixFollowupFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */
@Slf4j
@Component
public class FollowupTrackTask {
    @Resource
    private MixFollowupFeign mixFollowupFeign;
    @Resource
    private FollowupTrackFeign followupTrackFeign;

    /**
     * 初始化跟踪随访 初始化所有未开始的随访
     */
    @XxlJob("initAllStateByNuStart")
    public void initAllStateByNuStart() {
        XxlJobHelper.log("执行 【初始化跟踪随访 初始化所有未开始的随访】");
        log.info("执行 【初始化跟踪随访 初始化所有未开始的随访】.每天00:01执行一次..");
        try {
            mixFollowupFeign.initAllStateByNuStart();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            XxlJobHelper.handleSuccess();
        }
        XxlJobHelper.log("【初始化跟踪随访 初始化所有未开始的随访】 - 执行完毕");
        log.info("【初始化跟踪随访 初始化所有未开始的随访】 - 执行完毕");
    }


    /**
     * 跟踪随访 随访表单对账
     */
    @XxlJob("initFollowupItemPatientForm")
    public void initFollowupItemPatientForm() {
        XxlJobHelper.log("执行 【跟踪随访 随访表单对账】");
        log.info("执行 【跟踪随访 随访表单对账】.每天00:01执行一次");
        try {
            followupTrackFeign.initFollowupItemPatientForm();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            XxlJobHelper.handleSuccess();
        }
        XxlJobHelper.log("【跟踪随访 随访表单对账】 - 执行完毕");
        log.info("【跟踪随访 随访表单对账】 - 执行完毕");
    }

}
