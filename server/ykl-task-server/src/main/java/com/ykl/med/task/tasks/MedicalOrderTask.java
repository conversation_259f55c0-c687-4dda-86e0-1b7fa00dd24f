package com.ykl.med.task.tasks;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ykl.med.medical.api.ExecMedicalOrderFeign;
import com.ykl.med.medical.api.MedicalOrderFeign;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderQueryVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderVO;
import com.ykl.med.medical.vo.order.MedicalOrderVO;
import com.ykl.med.push.api.ToDoMessageFeign;
import com.ykl.med.push.enums.ToDoMessageType;
import com.ykl.med.push.vo.todo.ToDoMessageReqVO;
import com.ykl.med.push.vo.todo.ToDoMessageSystemExecutedReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用药任务执行类
 */

@Slf4j
@Component
public class MedicalOrderTask {
    @Resource
    private MedicalOrderFeign medicalOrderFeign;
    @Resource
    private ExecMedicalOrderFeign execMedicalOrderFeign;
    @Resource
    private ToDoMessageFeign toDoMessageFeign;

    /**
     * 生成用药执行计划
     */
    @XxlJob("MedicalOrderExecPlanGenTask")
    public ReturnT<String> medicalOrderExecPlanGenTask() {
        List<MedicalOrderVO> medicalOrderVOS = medicalOrderFeign.waitExecPlanGen();
        if (CollectionUtils.isEmpty(medicalOrderVOS)) {
            return ReturnT.SUCCESS;
        }
        log.info("生成用药执行计划任务开始，size:{}", medicalOrderVOS.size());
        for (MedicalOrderVO medicalOrderVO : medicalOrderVOS) {
            try {
                medicalOrderFeign.genMedicalOrderExecPlan(medicalOrderVO.getId());
            } catch (Exception e) {
                log.error("生成用药执行计划失败,用药ID:{}", medicalOrderVO.getId(), e);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 加入用药执行到待办: 今天未执行的待执行数据
     */
    @XxlJob("AddExecMedicalOrderTodoTask")
    public ReturnT<String> addExecMedicalOrderTodoTask() {
        ExecMedicalOrderQueryVO queryVO = new ExecMedicalOrderQueryVO();
        queryVO.setExecFlag(0L);
        queryVO.setNotifyFlag(0L);
        List<ExecMedicalOrderVO> execMedicalOrderVOS = execMedicalOrderFeign.query(queryVO);
        List<Long> medicalOrderIds = execMedicalOrderVOS.stream().map(ExecMedicalOrderVO::getMedicalOrderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(medicalOrderIds)) {
            return ReturnT.SUCCESS;
        }
        log.info("加入用药执行到待办，size:{}", medicalOrderIds.size());
        List<MedicalOrderVO> medicalOrderVOS = medicalOrderFeign.getByIds(medicalOrderIds);
        Map<Long, MedicalOrderVO> medicalOrderMap = medicalOrderVOS.stream().collect(Collectors.toMap(MedicalOrderVO::getId, x -> x));
        for (ExecMedicalOrderVO execMedicalOrderVO : execMedicalOrderVOS) {
            try {
                ToDoMessageReqVO toDoMessageReqVO = new ToDoMessageReqVO();
                toDoMessageReqVO.setOutBizId(execMedicalOrderVO.getId().toString());
                toDoMessageReqVO.setPatientId(execMedicalOrderVO.getPatientId());
                toDoMessageReqVO.setType(ToDoMessageType.DRUGS);
                if (medicalOrderMap.containsKey(execMedicalOrderVO.getMedicalOrderId())) {
                    toDoMessageReqVO.setContent(medicalOrderMap.get(execMedicalOrderVO.getMedicalOrderId()).getItemName() + " 用药打卡");
                } else {
                    toDoMessageReqVO.setContent("未知药品 用药打卡");
                }
                toDoMessageReqVO.setExpireTime(execMedicalOrderVO.getShouldExecTime());
                toDoMessageFeign.addToDoMessage(toDoMessageReqVO);
                execMedicalOrderFeign.notify(execMedicalOrderVO.getId(), 1L);
            } catch (Exception e) {
                log.error("加入用药执行到待办失败,执行用药ID:{}", execMedicalOrderVO.getId(), e);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 用药已执行通知到待办服务
     */
    @XxlJob("ExecMedicalOrderCompleteTask")
    public ReturnT<String> execMedicalOrderCompleteTask() {
        ExecMedicalOrderQueryVO queryVO = new ExecMedicalOrderQueryVO();
        queryVO.setExecFlag(1L);
        queryVO.setNotifyFlag(1L);
        List<ExecMedicalOrderVO> execMedicalOrderVOS = execMedicalOrderFeign.query(queryVO);
        if (CollectionUtils.isEmpty(execMedicalOrderVOS)) {
            return ReturnT.SUCCESS;
        }
        log.info("用药已执行通知到待办服务，size:{}", execMedicalOrderVOS.size());
        for (ExecMedicalOrderVO execMedicalOrderVO : execMedicalOrderVOS) {
            try {
                ToDoMessageSystemExecutedReqVO toDoMessageSystemExecutedReqVO = new ToDoMessageSystemExecutedReqVO();
                toDoMessageSystemExecutedReqVO.setOutBizId(execMedicalOrderVO.getId().toString());
                toDoMessageSystemExecutedReqVO.setPatientId(execMedicalOrderVO.getPatientId());
                toDoMessageSystemExecutedReqVO.setType(ToDoMessageType.DRUGS);
                toDoMessageSystemExecutedReqVO.setExecutedTime(execMedicalOrderVO.getExecTime());
                toDoMessageSystemExecutedReqVO.setNeedError(false);
                toDoMessageFeign.toDoMessageExecutedSystem(toDoMessageSystemExecutedReqVO);
                execMedicalOrderFeign.notify(execMedicalOrderVO.getId(), 2L);
            } catch (Exception e) {
                log.error("用药已执行通知到待办服务失败,执行用药ID:{}", execMedicalOrderVO.getId(), e);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 用药结束时间已到: 处理停嘱标志
     */
    @XxlJob("MedicalOrderStopTask")
    public ReturnT<String> medicalOrderStopTask() {
        medicalOrderFeign.autoStopMedicalOrder();
        return ReturnT.SUCCESS;
    }
}
