package com.ykl.med.task.tasks;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ykl.med.nutritional.api.RecipeExecDayFeign;
import com.ykl.med.nutritional.api.RecommendRecipeFeign;
import com.ykl.med.nutritional.entity.vo.NewNutritionalVO;
import com.ykl.med.rehab.api.NutritionalFeign;
import com.ykl.med.rehab.api.NutritionalRecipeFeign;
import com.ykl.med.rehab.enums.NutritionalStatusEnum;
import com.ykl.med.rehab.vo.req.nutritional.QueryNutritionalPlanReqVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPlanVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
@Slf4j
@Component
public class RecommendRecipeTask {

    @Resource
    private NutritionalFeign nutritionalFeign;
    @Resource
    private NutritionalRecipeFeign nutritionalRecipeFeign;


    /**
     * 初始化食谱 本周推荐
     */
    @XxlJob("initRecommendRecipeWeek")
    public void initRecommendRecipeWeek() {
        XxlJobHelper.log("执行 【初始化食谱 本周推荐】");
        log.info("执行 【初始化食谱 本周推荐】.每周00:01执行一次..");
        try {
            List<NutritionalPlanVO> nutritionalPlanVOS = nutritionalFeign.list(new QueryNutritionalPlanReqVO().setStatus(Collections.singletonList(NutritionalStatusEnum.STARTING)));
            for (NutritionalPlanVO nutritionalPlanVO : nutritionalPlanVOS) {
                nutritionalRecipeFeign.execNewNutritionalRecipeWeek(nutritionalPlanVO.getId());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            XxlJobHelper.handleSuccess();
        }

        XxlJobHelper.log("【初始化食谱 本周推荐】 - 执行完毕");
        log.info("【初始化食谱 本周推荐】 - 执行完毕");
    }


}
