package com.ykl.med.task.tasks;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.patient.vo.patient.PatientQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
@SuppressWarnings("unused")
public class PatientMemberStatusTask {
    @Resource
    private PatientFeign patientFeign;

    @XxlJob("PatientMemberStatusTask")
    public ReturnT<String> patientMemberStatusTask() {
        PatientQueryVO queryVO = new PatientQueryVO();
        queryVO.setIsMember(true);
        List<PatientBaseVO> patientBaseVOList = patientFeign.queryPatient(queryVO);
        for (PatientBaseVO patientBaseVO : patientBaseVOList) {
            try {
                patientFeign.refreshPatientMemberStatus(patientBaseVO.getPatientId());
            } catch (Exception e) {
                log.error("refreshPatientMemberStatus error, patientId:{}", patientBaseVO.getPatientId(), e);
            }
        }
        return ReturnT.SUCCESS;
    }
}
