package com.ykl.med.task.tasks.edu;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class EduFileTask {
    @Resource
    private EduFileFeign eduFileFeign;

    @XxlJob("moveVideo")
    public ReturnT<String> moveVideo() {
        eduFileFeign.moveVideo();
        return ReturnT.SUCCESS;
    }

    @XxlJob("moveCover")
    public ReturnT<String> moveCover() {
        eduFileFeign.moveCover();
        return ReturnT.SUCCESS;
    }

    @XxlJob("coverThumb")
    public ReturnT<String> coverThumb() {
        eduFileFeign.coverThumb();
        return ReturnT.SUCCESS;
    }

    @XxlJob("videoThumb")
    public ReturnT<String> videoThumb() {
        eduFileFeign.videoThumb();
        return ReturnT.SUCCESS;
    }
}
