package com.ykl.med.task.tasks.order;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.order.MixOrderFeign;
import com.ykl.med.order.api.OrderFeign;
import com.ykl.med.framework.common.enums.OrderStatus;
import com.ykl.med.order.vo.req.OrderQueryReqVO;
import com.ykl.med.order.vo.resp.OrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class OrderDeliveredTask {
    @Resource
    private OrderFeign orderFeign;
    @Resource
    private MixOrderFeign mixOrderFeign;

    @XxlJob("OrderDeliveredTask")
    public ReturnT<String> execute() {
        log.info("处理已发货订单 start");
        OrderQueryReqVO reqVO = new OrderQueryReqVO().setStatus(OrderStatus.DELIVERED);
        PageResult<OrderVO> orderPage = orderFeign.queryOrder(reqVO);
        //循环处理已发货订单，直到没有订单
        while (orderPage != null && CollectionUtils.isNotEmpty(orderPage.getList())) {
            doCompletedOrder(orderPage.getList());
            if (orderPage.getList().size() < reqVO.getPageSize()) {
                break;
            }
            reqVO.setPageNo(reqVO.getPageNo() + 1);
            orderPage = orderFeign.queryOrder(reqVO);
        }
        log.info("处理已发货订单 end");
        return ReturnT.SUCCESS;
    }

    private void doCompletedOrder(List<OrderVO> orderList) {
        for (OrderVO orderVO : orderList) {
            log.info("处理已发货订单 order:{}", orderVO.getOrderCode());
            try {
                mixOrderFeign.completedOrder(orderVO.getOrderCode());
            } catch (Exception e) {
                log.error("处理已发货订单 error,{}", orderVO.getOrderCode(), e);
            }
        }
    }
}
