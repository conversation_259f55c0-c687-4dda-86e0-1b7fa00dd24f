package com.ykl.med.admin.controller.config;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.enums.CommonConfigType;
import com.ykl.med.masterdata.vo.common.CommonConfigBatchQueryVO;
import com.ykl.med.masterdata.vo.common.CommonConfigPageReqVO;
import com.ykl.med.masterdata.vo.common.CommonConfigSimpleVO;
import com.ykl.med.masterdata.vo.common.CommonConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "参数配置接口")
@RestController
@RequestMapping("/config")
@Slf4j
@EnableCommonResponseAdvice
public class CommonConfigController {
    @Resource
    private CommonConfigFeign commonConfigFeign;


    @PostMapping("/getCommonConfigKeysByMaxId")
    @Operation(summary = "根据最大Id获取配置值列表")
    public List<CommonConfigSimpleVO> getCommonConfigKeysByMaxId(@RequestBody IdReqVO idReqVO) {
        return commonConfigFeign.getCommonConfigKeysByMaxId(idReqVO.getId(), CommonConfigType.WEB);
    }

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存或更新公共配置")
    @OperateLog(eventType = LogConstants.LogEventType.ADD_OR_MODIFY, eventContent = "保存或修改参数配置【{configName}】,key:{configKey},", module = "参数配置")
    public void saveOrUpdate(@RequestBody CommonConfigVO vo) {
        commonConfigFeign.saveOrUpdate(vo);
    }


    @PostMapping("/getCommonConfigPage")
    @Operation(summary = "分页查询公共配置")
    public PageResult<CommonConfigVO> getCommonConfigPage(@RequestBody CommonConfigPageReqVO reqVO) {
        return commonConfigFeign.getCommonConfigPage(reqVO);
    }

    @PostMapping("/getCommonConfigListByKey")
    @Operation(summary = "根据配置键列表获取配置值列表")
    public List<CommonConfigSimpleVO> getCommonConfigListByKey(@RequestBody CommonConfigBatchQueryVO queryVO) {

        return commonConfigFeign.getCommonConfigListByKey(queryVO);
    }

}
