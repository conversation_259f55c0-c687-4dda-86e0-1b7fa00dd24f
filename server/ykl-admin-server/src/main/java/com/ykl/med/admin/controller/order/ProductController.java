package com.ykl.med.admin.controller.order;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSONObject;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.doctors.api.MedicalTeamFeign;
import com.ykl.med.doctors.entity.vo.MedicalTeamSimpleRespVO;
import com.ykl.med.edu.api.EduArticleFeign;
import com.ykl.med.edu.vo.resp.EduArticleAdminDetailVO;
import com.ykl.med.framework.common.enums.MemberServicePackageType;
import com.ykl.med.framework.common.enums.ProductType;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CrudUtils;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.api.treatment.TreatmentProjectFeign;
import com.ykl.med.order.api.OrderItemFeign;
import com.ykl.med.patient.api.MemberVersionFeign;
import com.ykl.med.patient.vo.member.MemberVersionVO;
import com.ykl.med.product.feign.ProductFeign;
import com.ykl.med.product.feign.ProductTreatmentProjectTypeFeign;
import com.ykl.med.product.feign.ServiceOrganizationFeign;
import com.ykl.med.product.feign.StockFeign;
import com.ykl.med.product.vo.req.*;
import com.ykl.med.product.vo.resp.ProductDetailVO;
import com.ykl.med.product.vo.resp.ProductListVO;
import com.ykl.med.product.vo.resp.SkuDetailVO;
import com.ykl.med.product.vo.resp.SkuListVO;
import com.ykl.med.product.vo.treatment.ProductTreatmentProjectTypeVO;
import com.ykl.med.product.vo.treatment.ServiceOrganizationVO;
import com.ykl.med.shift.api.RegisterCategoryFeign;
import com.ykl.med.shift.vo.vo.RegisterCategoryVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "商品服务")
@RestController
@RequestMapping("/product")
@EnableCommonResponseAdvice
public class ProductController {
    @Resource
    private ProductFeign productFeign;
    @Resource
    private StockFeign stockFeign;
    @Resource
    private MedicalTeamFeign medicalTeamFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private EduArticleFeign eduArticleFeign;
    @Resource
    private RegisterCategoryFeign registerCategoryFeign;
    @Resource
    private MemberVersionFeign memberVersionFeign;
    @Resource
    private CommonConfigFeign commonConfigFeign;
    @Resource
    private OrderItemFeign orderItemFeign;
    @Resource
    private TreatmentProjectFeign treatmentProjectFeign;
    @Resource
    private ServiceOrganizationFeign serviceOrganizationFeign;
    @Resource
    private ProductTreatmentProjectTypeFeign productTreatmentProjectTypeFeign;

    @PostMapping("/saveOrUpdateProduct")
    @Operation(summary = "创建或者保存商品")
    @OperateLog(eventType = LogConstants.LogEventType.ADD_OR_MODIFY, eventContent = "新增或修改商品", module = "商品管理")
    public void saveOrUpdateProduct(@RequestBody ProductReqVO reqVO) {
        reqVO.setCreatorId(AuthContextHolder.getInstance().getContext().getId());
        JSONObject jsonObject = commonConfigFeign.getCommonConfigValueJsonByKey("product_config");
        if (!CollectionUtils.isEmpty(reqVO.getSkuList())) {
            for (SkuReqVO skuReqVO : reqVO.getSkuList()) {
                switch (reqVO.getType()) {
                    case SERVICE_PACKAGE:
                        skuReqVO.setSkuImg(jsonObject.getString("servicePackageImg"));
                        break;
                    case MEMBER_VERSION:
                        MemberVersionVO memberVersionVO = memberVersionFeign.getMemberVersionById(Long.valueOf(reqVO.getOutBizId()));
                        skuReqVO.setName(memberVersionVO.getName());
                        skuReqVO.setSkuImg(jsonObject.getString("memberVersionImg"));
                        break;
                    case PATIENT_EDUCATION_COURSES:
                        Long articleId = Long.valueOf(reqVO.getOutBizId());
                        EduArticleAdminDetailVO eduArticleAdminDetailVO = eduArticleFeign.queryEduArticleDetail(articleId);
                        skuReqVO.setName(eduArticleAdminDetailVO.getTitle());
                        skuReqVO.setSkuImg(eduArticleAdminDetailVO.getCover());
                        //患教默认限购1
                        skuReqVO.setLimitBuy(true);
                        skuReqVO.setLimitBuyNum(1);
                        break;
                    case TREATMENT_PROJECT:
                        skuReqVO.setSkuImg(jsonObject.getString("treatmentProjectImg"));
                    default:
                        break;
                }
            }
        }
        productFeign.saveOrUpdateProduct(reqVO);
    }

    @PostMapping("/getProductDetailById")
    @Operation(summary = "获取商品详情")
    public ProductDetailVO getProductDetailById(@RequestBody IdReqVO reqVO) {
        ProductDetailVO productDetailVO = productFeign.getProductDetailById(reqVO.getId());
        if (productDetailVO == null) {
            return null;
        }
        List<Long> medicalTeamIds = getMedicalTeamIds(productDetailVO);
        if (medicalTeamIds.isEmpty()) {
            return productDetailVO;
        }
        List<MedicalTeamSimpleRespVO> medicalTeamSimpleRespVOS = medicalTeamFeign.listByMedicalTeamIds(medicalTeamIds);
        Map<String, String> medicalTeamMap = medicalTeamSimpleRespVOS.stream()
                .collect(Collectors.toMap(MedicalTeamSimpleRespVO::getId, MedicalTeamSimpleRespVO::getName));

        for (SkuDetailVO skuListVO : productDetailVO.getSkuList()) {
            if (productDetailVO.getType() == ProductType.SERVICE_PACKAGE && Objects.equals(productDetailVO.getOutBizId(), MemberServicePackageType.ONLINE_VIDEO_CLINIC.name())) {
                RegisterCategoryVO registerCategoryVO = CrudUtils.getById(registerCategoryFeign::crud, skuListVO.getOutBizId());
                skuListVO.setOutBizName(Optional.ofNullable(registerCategoryVO).map(RegisterCategoryVO::getName).orElse(null));
            } else if (productDetailVO.getType() == ProductType.MEMBER_VERSION
                    || productDetailVO.getType() == ProductType.SERVICE_PACKAGE
                    || productDetailVO.getType() == ProductType.TREATMENT_PROJECT) {
                if (skuListVO.getOutBizId() != null) {
                    skuListVO.setOutBizName(medicalTeamMap.get(skuListVO.getOutBizId()));
                }
            }
            if (skuListVO.getSkuAttribute() != null) {
                if (skuListVO.getSkuAttribute().getServiceOrganizationId() != null) {
                    skuListVO.getSkuAttribute().setServiceOrganizationName(serviceOrganizationFeign.getName(skuListVO.getSkuAttribute().getServiceOrganizationId()));
                }
            }

        }

        return productDetailVO;
    }

    @PostMapping("/pageProduct")
    @Operation(summary = "获取商品列表")
    public PageResult<ProductListVO> pageProduct(@RequestBody ProductQueryVO queryVO) {
        PageResult<ProductListVO> pageResult = productFeign.pageProduct(queryVO);
        if (pageResult.getList() == null || pageResult.getList().isEmpty()) {
            return pageResult;
        }
        List<Long> creatorIds = pageResult.getList().stream()
                .map(ProductListVO::getCreatorId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<UserSimpleVO> userSimpleVOS = userFeign.listByUserIds(new IdListReqVO().setIdList(creatorIds));
        Map<Long, String> userMap = userSimpleVOS.stream()
                .collect(Collectors.toMap(UserSimpleVO::getId, UserSimpleVO::getUsername));
        List<Long> medicalTeamIds = new ArrayList<>();
        for (ProductListVO productListVO : pageResult.getList()) {
            if (CollectionUtils.isEmpty(productListVO.getSkuInfos())) {
                continue;
            }
            for (SkuListVO skuListVO : productListVO.getSkuInfos()) {
                if (checkOutBizIsMedicalTeam(productListVO.getType(), productListVO.getOutBizId())) {
                    medicalTeamIds.add(Long.valueOf(skuListVO.getOutBizId()));
                }
            }
        }
        medicalTeamIds = medicalTeamIds.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (medicalTeamIds.isEmpty()) {
            return pageResult;
        }
        List<MedicalTeamSimpleRespVO> medicalTeamSimpleRespVOS = medicalTeamFeign.listByMedicalTeamIds(medicalTeamIds);
        Map<String, String> medicalTeamMap = medicalTeamSimpleRespVOS.stream()
                .collect(Collectors.toMap(MedicalTeamSimpleRespVO::getId, MedicalTeamSimpleRespVO::getName));
        List<Long> productIds = pageResult.getList().stream().map(ProductListVO::getId).collect(Collectors.toList());
        Map<Long, Integer> salesAmountMap = orderItemFeign.getSalesAmount(productIds);
        for (ProductListVO productListVO : pageResult.getList()) {
            if (CollectionUtils.isEmpty(productListVO.getSkuInfos())) {
                continue;
            }
            for (SkuListVO skuListVO : productListVO.getSkuInfos()) {
                if (checkOutBizIsMedicalTeam(productListVO.getType(), productListVO.getOutBizId())) {
                    skuListVO.setOutBizName(medicalTeamMap.get(skuListVO.getOutBizId()));
                } else {
                    RegisterCategoryVO registerCategoryVO = CrudUtils.getById(registerCategoryFeign::crud, skuListVO.getOutBizId());
                    skuListVO.setOutBizName(Optional.ofNullable(registerCategoryVO).map(RegisterCategoryVO::getName).orElse(null));
                }
                if (skuListVO.getSkuAttribute() != null) {
                    if (skuListVO.getSkuAttribute().getServiceOrganizationId() != null) {
                        skuListVO.getSkuAttribute().setServiceOrganizationName(serviceOrganizationFeign.getName(skuListVO.getSkuAttribute().getServiceOrganizationId()));
                    }
                }
            }
            productListVO.setTotalSalesAmount(salesAmountMap.getOrDefault(productListVO.getId(), 0));
            productListVO.setCreatorName(userMap.get(productListVO.getCreatorId()));
        }

        return pageResult;
    }

    @PostMapping("/changeSale")
    @Operation(summary = "批量上下架")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改商品上下架", module = "商品管理")
    public void changeSale(@RequestBody ProductChangeSaleReqVO reqVO) {
        for (Long productId : reqVO.getProductIds()) {
            productFeign.changeSale(productId, reqVO.getOnSale());
        }
    }

    @PostMapping("/changeStock")
    @Operation(summary = "修改库存")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改商品库存", module = "商品管理")
    public void changeStock(@RequestBody StockChangeReqVO reqVO) {
        stockFeign.changeStock(reqVO);
    }

    @PostMapping("/getAllProductTreatmentProjectType")
    @Operation(summary = "获取商品诊疗项目类别")
    public List<ProductTreatmentProjectTypeVO> getAllProductTreatmentProjectType() {
        return productTreatmentProjectTypeFeign.getAll();
    }

    @PostMapping("/getServiceOrganizationByTreatment")
    @Operation(summary = "获取商品诊疗项目服务机构", description = "请求参数传商品诊疗项目类别Id")
    public List<ServiceOrganizationVO> getServiceOrganizationByProductTreatmentProjectTypeId(@RequestBody IdReqVO reqVO) {
        return serviceOrganizationFeign.getServiceOrganizationByProductTreatmentProjectTypeId(reqVO.getId());
    }

    private List<Long> getMedicalTeamIds(ProductDetailVO productDetailVO) {
        if (productDetailVO == null || CollectionUtils.isEmpty(productDetailVO.getSkuList())) {
            return Collections.emptyList();
        }
        List<Long> medicalTeamIds = new ArrayList<>();
        for (SkuDetailVO skuListVO : productDetailVO.getSkuList()) {
            if (skuListVO.getOutBizId() == null) {
                continue;
            }
            if (checkOutBizIsMedicalTeam(productDetailVO.getType(), productDetailVO.getOutBizId())) {
                medicalTeamIds.add(Long.valueOf(skuListVO.getOutBizId()));
            }
        }
        return medicalTeamIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }


    private Boolean checkOutBizIsMedicalTeam(ProductType productType, String outBizId) {
        if (productType == ProductType.SERVICE_PACKAGE && !Objects.equals(outBizId, MemberServicePackageType.ONLINE_VIDEO_CLINIC.name())) {
            return true;
        } else return productType == ProductType.MEMBER_VERSION || productType == ProductType.TREATMENT_PROJECT;
    }
}
