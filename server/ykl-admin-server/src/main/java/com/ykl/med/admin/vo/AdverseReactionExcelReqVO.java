package com.ykl.med.admin.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = false)
public class AdverseReactionExcelReqVO {
    @ExcelProperty("药品")
    private String drugName;

    @ExcelProperty("实体")
    private String bizName;

    @ExcelProperty("实体类型")
    private String bizType;

    @ExcelProperty("异常")
    private String error;
}
