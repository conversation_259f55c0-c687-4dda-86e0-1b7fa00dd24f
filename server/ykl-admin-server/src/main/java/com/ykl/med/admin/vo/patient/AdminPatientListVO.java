package com.ykl.med.admin.vo.patient;

import com.ykl.med.patient.vo.patient.PatientListVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "admin患者列表信息对象")
public class AdminPatientListVO extends PatientListVO {
    @Schema(description = "会员版本名称", defaultValue = "会员版本名称")
    private String memberVersionName;

    @Schema(description = "主管医生", defaultValue = "张医生")
    private String bindDoctorName;

    @Schema(description = "病种", defaultValue = "病种")
    private String diseases;

    @Schema(description = "病种阶段", defaultValue = "病种阶段")
    private String stage;

    @Schema(description = "医疗组名称", defaultValue = "医疗组名称")
    private String medicalTeamName;
}
