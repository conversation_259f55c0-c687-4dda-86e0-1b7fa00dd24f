package com.ykl.med.admin.controller.edu;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.edu.vo.req.EduArticleChapterReqVO;
import com.ykl.med.edu.vo.resp.EduArticleChapterVO;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.interfaces.OperateLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "患教章节")
@RestController
@RequestMapping("/eduArticleChapter")
@Validated
@EnableCommonResponseAdvice
public class EduArticleChapterController {

    @Resource
    private EduArticleChapterFeign eduArticleChapterFeign;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "新增或修改患教章节")
    @OperateLog(eventType = LogConstants.LogEventType.ADD_OR_MODIFY, eventContent = "新增或修改患教章节", module = "患教管理")
    public void saveOrUpdate(@RequestBody EduArticleChapterReqVO reqVO) {
        eduArticleChapterFeign.saveOrUpdate(reqVO);
    }

    @PostMapping("/deleteChapter")
    @Operation(summary = "删除患教章节")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除患教章节", module = "患教管理")
    public void deleteChapter(@RequestBody IdReqVO reqVO) {
        eduArticleChapterFeign.deleteChapter(reqVO.getId());
    }

    @PostMapping("/queryEduArticleChapter")
    @Operation(summary = "查询患教章节")
    public List<EduArticleChapterVO> queryEduArticleChapter(@RequestBody IdReqVO reqVO) {
        return eduArticleChapterFeign.queryEduArticleChapter(reqVO.getId());
    }
}