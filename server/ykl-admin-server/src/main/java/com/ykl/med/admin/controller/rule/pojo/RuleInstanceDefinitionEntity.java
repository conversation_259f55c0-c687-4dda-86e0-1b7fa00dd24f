package com.ykl.med.admin.controller.rule.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: wang <EMAIL>
 * @Description: 规则模板表属性
 * @Date: 2023-10-18 14:43:35
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_rule_instance_definition")
public class RuleInstanceDefinitionEntity {

    /**
     * 主键id
     */

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 规则id
     */
    @TableField("rule_id")
    private String ruleId;

    /**
     * 规则名
     */
    @TableField("rule_name")
    private String ruleName;

    /**
     * 模版id（1,2,3）
     */
    @TableField("rule_model_id")
    private Integer ruleModelId;
    /**
     * 事件和属性的对应关系
     */
    @TableField("event_attribute")
    private String eventAttribute;

    /**
     * 用户画像bitmap
     */
    @TableField("rule_profile_user_bitmap")
    private byte[] ruleProfileUserBitmap;

    /**
     * groovy_code
     */
    @TableField("groovy_code")
    private String groovyCode;

    /**
     * 用户配置的rule规则备注
     */
    @TableField("rule_condition")
    private String ruleCondition;

    /**
     * 处理好的rule规则参数
     */
    @TableField("rule_param_json")
    private String ruleParamJson;

    /**
     * 用户配置的rule规则参数
     */
    @TableField("init_json")
    private String initJson;
    /**
     * 创建人
     */
    @TableField("creator_name")
    private String creatorName;
    /**
     * 更新人
     */
    @TableField("update_name")
    private String updateName;
    /**
     * 数据状态
     */
    @TableField("rule_status")
    private Integer ruleStatus;

    /**
     * 创建时间
     */
    @TableField( value = "create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private String updateTime;

    /**
     * 范围
     */
    @TableField("user_range")
    private Integer userRange;

    /**
     * 上架或者下架
     */
    @TableField("rule_operate")
    private Integer ruleOperate;

    /**
     * 规则类别
     */
    @TableField("rule_type")
    private Integer ruleType;
    /**
     * 匹配类别
     */
    @TableField("match_type")
    private String matchType;
    /**
     * 医学证据
     */
    @TableField("evidence")
    private String evidence;

    /**
     * 医院备注
     */
    @TableField("note")
    private String note;

    /**
     * 编辑方式
     */
    @TableField("edit_type")
    private Integer editType;

    /**
     * 医疗组
     */
    @TableField("medical_team")
    private Integer medicalTeam;

    /**
     * 预警类型
     */
    @TableField("early_warn_type")
    private String earlyWarnType;

    /**
     * 手动刷数据
     */
    @TableField("remark")
    private String remark;
}
