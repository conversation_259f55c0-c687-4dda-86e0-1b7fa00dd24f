package com.ykl.med.admin.controller.rule.pojo.rulejson;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: wang <EMAIL>
 * @Description: Output
 * @Date: 2023-12-19 16:29:02
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "干预项目数组")
public class Output {

    @Schema(description = "预警干预项目类型:DRUGS(药物), EDU(患教), SYMPTOM(症状), ADVICE(指导与建议), DEFAULT(默认)"
            ,example = "ADVICE")
    private String warnType;

    @Schema(description = "子类型示例例如warnType = ADVICE,可选值:GUIDANCE_AND_ADVICE_CATEGORIES_LIFESTYLE_GUIDANCE(生活指导)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_MEDICAL_INTERVENTION(医护干预建议)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_MEDICAL_GUIDANCE(就医指导)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_OTHER_RECOMMENDATIONS(其他建议)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_RESTRICTED_INTAKE(限制摄入建议)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_INCREASED_INTAKE(增加摄入建议)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_DIETARY_METHODS(饮食方法建议)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_FOOD_PREPARATION(食物制作建议)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_EXERCISE_GUIDANCE(运动指导建议)\n" +
            "GUIDANCE_AND_ADVICE_CATEGORIES_INSPECT_GUIDANCE(检查建议)\n ",
            example = "GUIDANCE_AND_ADVICE_CATEGORIES_MEDICAL_INTERVENTION")
    private String subType;

    @Schema(description = "干预方案内容数组id集合")
    private List<String> outBizIds;

    @Schema(description = "干预方案内容前缀")
    private String content;
}
