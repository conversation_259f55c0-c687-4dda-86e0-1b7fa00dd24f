package com.ykl.med.admin.controller.edu;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.edu.vo.req.EduCourseReqVO;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.interfaces.OperateLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/eduCourse")
@Tag(name = "患教课程")
@EnableCommonResponseAdvice
public class EduCourseController {
    @Resource
    private EduCourseFeign eduCourseFeign;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "新增或修改患教课程")
    @OperateLog(eventType = LogConstants.LogEventType.ADD_OR_MODIFY, eventContent = "新增或修改患教课程", module = "患教管理")
    public void saveOrUpdate(@RequestBody EduCourseReqVO reqVO) {
        eduCourseFeign.saveOrUpdate(reqVO);
    }

    @PostMapping("/deleteCourse")
    @Operation(summary = "删除患教课程")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除患教课程", module = "患教管理")
    public void deleteCourse(@RequestBody IdReqVO reqVO) {
        eduCourseFeign.deleteCourse(reqVO.getId());
    }
}