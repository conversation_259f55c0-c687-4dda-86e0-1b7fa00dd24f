package com.ykl.med.admin.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.tag.api.TagClassFeign;
import com.ykl.med.tag.entity.dto.TagDeleteDTO;
import com.ykl.med.tag.entity.dto.TagClassAddDTO;
import com.ykl.med.tag.entity.dto.TagClassQueryDTO;
import com.ykl.med.tag.entity.vo.TagClassVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Tag(name = "标签类别管理服务")
@RestController
@RequestMapping("/tagClass")
@Validated
@EnableCommonResponseAdvice
public class TagClassController {

    @Resource
    private TagClassFeign apiFeign;

    @Resource
    private UserFeign userFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<TagClassVO> query(@RequestBody TagClassQueryDTO requestBody){

        // 初始化返回参数
        PageResult<TagClassVO> pageResult = new PageResult<>();
        pageResult.setTotal(0L);
        pageResult.setList(new ArrayList<>());

        pageResult = apiFeign.crud("GET", requestBody);

        fixUserInfo(pageResult);

        return pageResult;
    }

    @PostMapping("/add")
    @Operation(summary = "增加数据", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "新增标签类别", module = "规则管理")
    public void add(@RequestBody TagClassAddDTO requestBody) {
        requestBody.setCreateUserId(requestBody.getCurrentUserId().toString());
        requestBody.setLastUserId(requestBody.getCurrentUserId().toString());
        apiFeign.crud("POST", requestBody);
    }

    @PostMapping("/update")
    @Operation(summary = "更新信息", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改标签类别信息", module = "规则管理")
    public void update(@RequestBody TagClassAddDTO requestBody) {
        requestBody.setLastUserId(requestBody.getCurrentUserId().toString());
        apiFeign.crud("PUT", requestBody);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除信息", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除标签类别信息", module = "规则管理")
    public void update(@RequestBody TagDeleteDTO requestBody) {
        apiFeign.crud("DELETE", requestBody);
    }

    /**
     * 补齐数据操作用户数据信息
     * */
    private void fixUserInfo(PageResult<TagClassVO> pageResult) {

        // 结果列表为null, 不处理
        if ( ( pageResult.getList() == null ) || pageResult.getList().size() == 0 ){
            return ;
        }

        List<String> createUserIds = pageResult.getList().stream().map(TagClassVO::getCreateUserId).distinct().collect(Collectors.toList());
        List<String> lastUserIds = pageResult.getList().stream().map(TagClassVO::getLastUserId).distinct().collect(Collectors.toList());
        createUserIds.addAll(lastUserIds);
        // 去null值
        createUserIds.removeAll(Collections.singleton(null));
        // 去重
        Set<String> set = new HashSet<>(createUserIds);
        List<String> UserIds = new ArrayList<>(set);

        if ( UserIds.size() != 0 ) {
            IdListReqVO idListReqVO = new IdListReqVO();
            idListReqVO.setIdList(new ArrayList<>());

            idListReqVO.setIdList(UserIds.stream().map(Long::valueOf).collect(Collectors.toList()));
            List<UserSimpleVO> userSimpleVOS = userFeign.listByUserIds(idListReqVO);

            Map<Long, UserSimpleVO> userSimpleVOMap = userSimpleVOS.stream().collect(
                    Collectors.toMap(UserSimpleVO::getId, Function.identity(), (key1, key2) -> key2));

            for (TagClassVO resultVO : pageResult.getList()) {
                if ( resultVO.getCreateUserId() != null ) {
                    Long userId = Long.parseLong(resultVO.getCreateUserId());
                    if (userSimpleVOMap.containsKey(userId)) {
                        resultVO.setCreateUserName(userSimpleVOMap.get(userId).getUsername());
                    }
                }
                if ( resultVO.getLastUserId() != null ) {
                    Long lastUserId = Long.parseLong(resultVO.getLastUserId());
                    if (userSimpleVOMap.containsKey(lastUserId)) {
                        resultVO.setLastUserName(userSimpleVOMap.get(lastUserId).getUsername());
                    }
                }
            }
        }
    }
}
