package com.ykl.med.admin;

import com.ykl.med.authority.interfaces.EnableYklAuthority;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@EnableYklAuthority
@SpringBootApplication
@ComponentScan(basePackages = "com.ykl.med")
@EnableFeignClients(basePackages = "com.ykl.med")
public class AdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
    }
}
