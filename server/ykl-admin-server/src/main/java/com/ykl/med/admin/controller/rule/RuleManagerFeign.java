package com.ykl.med.admin.controller.rule;

import com.ykl.med.admin.controller.rule.pojo.RulePageVO;
import com.ykl.med.admin.controller.rule.pojo.rulejson.JsonRootBean;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.util.List;
import java.util.Map;

/**
 * @Author: wang <EMAIL>
 * @Description: 微服务注册-规则管理系统
 * @Date: 2023-11-28 11:01:35
 * @Version: 1.0
 */
@FeignClient(name = "ykl-rule-manager-service", path = "/system/rule")
public interface RuleManagerFeign {

    @PostMapping("/eventSelect")
    @Operation(summary = "字段下拉列表",description = "下拉列表类型:userProfile(用户画像)、ruleCondition(变量和规则条件)")
    Map<String,Object> eventData(@RequestParam(required = false,name = "attributesType")String attributesType,
                                 @RequestParam(required = false,name = "attributeName")String attributeName) ;

    @PostMapping("/create")
    @Operation(summary = "新增规则")
    Map<String,Object> createRuleData(@RequestBody JsonRootBean jsonRootBean);

    @PostMapping("/update")
    @Operation(summary = "修改规则")
    Map<String,Object> updateRuleData(@RequestBody JsonRootBean jsonRootBean);

    @PostMapping("/select")
    @Operation(summary = "查询规则")
    Map<String,Object> selectAllRule(@RequestBody RulePageVO rulePageVO);

    @PostMapping("/listOutput")
    @Operation(summary = "根据id查询干预项目数组")
    String getListOutputById(@RequestParam("id") Long id);

    @PostMapping("/eventSelectById")
    @Operation(summary = "根据id查询事件属性")
    Map<String, Object> eventSelectById(@RequestBody List<String> eventIdAndAttributeId);

    @PostMapping("/exportRule")
    @Operation(summary = "根据id导出json数据")
    ResponseEntity<StreamingResponseBody> getJsonById(@RequestParam("id") Long id);
}
