package com.ykl.med.admin.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.object.ObjectUtils;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.masterdata.api.PsychotherapyFeign;
import com.ykl.med.masterdata.entiry.dto.*;
import com.ykl.med.masterdata.entiry.vo.PsychotherapyVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Tag(name = "心理治疗目录服务")
@RestController
@RequestMapping("/psychotherapy")
@Validated
@EnableCommonResponseAdvice
public class PsychotherapyController {

    @Resource
    private PsychotherapyFeign apiFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<PsychotherapyVO> query(@RequestBody PsychotherapyQueryDTO requestBody){
        return apiFeign.crud("GET", requestBody);
    }

    @PostMapping("/add")
    @Operation(summary = "增加数据", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "新增心理治疗", module = "方案项目目录")
    public void add(@RequestBody PsychotherapyAddDTO requestBody) {
        apiFeign.crud("POST", requestBody);
    }

    @PostMapping("/update")
    @Operation(summary = "更新信息", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改心理治疗", module = "方案项目目录")
    public void update(@RequestBody PsychotherapyAddDTO requestBody) {
        ObjectUtils.initObjectFieldToNullString(requestBody,"NULL",(byte)0,(short)0, 0,(long)0,new BigDecimal("0"));
        apiFeign.crud("PUT", requestBody);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除信息", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除心理治疗", module = "方案项目目录")
    public void update(@RequestBody DeleteDTO requestBody) {
        apiFeign.crud("DELETE", requestBody);
    }

}
