package com.ykl.med.admin.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.masterdata.entiry.dto.*;
import com.ykl.med.pharmacy.api.ProducerFeign;
import com.ykl.med.pharmacy.entity.dto.ProducerAddDTO;
import com.ykl.med.pharmacy.entity.dto.ProducerQueryDTO;
import com.ykl.med.pharmacy.entity.vo.ProducerVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "生产厂商目录服务")
@RestController
@RequestMapping("/producer")
@Validated
@EnableCommonResponseAdvice
public class ProducerController {

    @Resource
    private ProducerFeign apiFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<ProducerVO> query(@RequestBody ProducerQueryDTO requestBody){
        return apiFeign.crud("GET", requestBody);
    }

    @PostMapping("/add")
    @Operation(summary = "增加数据", description = "")
    @OperateLog
    public void add(@RequestBody ProducerAddDTO requestBody) {
        apiFeign.crud("POST", requestBody);
    }

    @PostMapping("/update")
    @Operation(summary = "更新信息", description = "")
    @OperateLog
    public void update(@RequestBody ProducerAddDTO requestBody) {
        apiFeign.crud("PUT", requestBody);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除信息", description = "")
    @OperateLog
    public void update(@RequestBody DeleteDTO requestBody) {
        apiFeign.crud("DELETE", requestBody);
    }
}
