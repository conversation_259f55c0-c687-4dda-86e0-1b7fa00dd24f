package com.ykl.med.admin.controller.rule.util;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wang <EMAIL>
 * @Description: 类型存储
 * @Date: 2023-12-08 11:19:30
 * @Version: 1.0
 */
public class RuleDataUtil {
    /**
     * 指导与建议对应关系map
     * @return data
     */
    public static Map<String,String> getData(){
        Map<String,String> guidsMap = new HashMap<>(8);
        guidsMap.put("LIFESTYLE_GUIDANCE","生活指导");
        guidsMap.put("MEDICAL_INTERVENTION","医护干预建议");
        guidsMap.put("MEDICAL_GUIDANCE","就医指导");
        guidsMap.put("OTHER_RECOMMENDATIONS","其他建议");
        guidsMap.put("RESTRICTED_INTAKE","限制摄入建议");
        guidsMap.put("INCREASED_INTAKE","增加摄入建议");
        guidsMap.put("DIETARY_METHODS","饮食方法建议");
        guidsMap.put("FOOD_PREPARATION","食物制作建议");
        guidsMap.put("EXERCISE_GUIDANCE","运动指导建议");
        guidsMap.put("INSPECT_GUIDANCE","检查建议");
        return guidsMap;
    }
}
