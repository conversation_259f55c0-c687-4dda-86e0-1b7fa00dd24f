package com.ykl.med.admin.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.pharmacy.api.ProducerFeign;
import com.ykl.med.pharmacy.api.SpecialMedicalFoodFeign;
import com.ykl.med.pharmacy.entity.dto.DeleteDTO;
import com.ykl.med.pharmacy.entity.dto.ProducerQueryDTO;
import com.ykl.med.pharmacy.entity.dto.SpecialMedicalFoodAddDTO;
import com.ykl.med.pharmacy.entity.dto.SpecialMedicalFoodQueryDTO;
import com.ykl.med.pharmacy.entity.vo.ProducerVO;
import com.ykl.med.pharmacy.entity.vo.SpecialMedicalFoodVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Tag(name = "特医食品目录服务")
@RestController
@RequestMapping("/specialMedicalFood")
@Validated
@EnableCommonResponseAdvice
public class SpecialMedicalFoodController {

    @Resource
    private SpecialMedicalFoodFeign apiFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<SpecialMedicalFoodVO> query(@RequestBody SpecialMedicalFoodQueryDTO requestBody){
        return apiFeign.query(requestBody);
    }

    @PostMapping("/add")
    @Operation(summary = "增加数据", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "新增特医食品目录", module = "方案项目目录")
    public void add(@RequestBody SpecialMedicalFoodAddDTO requestBody) {
        apiFeign.crud("POST", requestBody);
    }

    @PostMapping("/update")
    @Operation(summary = "更新信息", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改特医食品目录信息", module = "方案项目目录")
    public void update(@RequestBody SpecialMedicalFoodAddDTO requestBody) {
        apiFeign.crud("PUT", requestBody);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除信息", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除特医食品目录信息", module = "方案项目目录")
    public void update(@RequestBody DeleteDTO requestBody) {
        apiFeign.crud("DELETE", requestBody);
    }

}
