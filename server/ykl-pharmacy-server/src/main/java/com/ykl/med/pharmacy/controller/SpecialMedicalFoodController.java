package com.ykl.med.pharmacy.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.pharmacy.api.SpecialMedicalFoodFeign;
import com.ykl.med.pharmacy.entity.dto.DeleteDTO;
import com.ykl.med.pharmacy.entity.dto.SpecialMedicalFoodAddDTO;
import com.ykl.med.pharmacy.entity.dto.SpecialMedicalFoodQueryDTO;
import com.ykl.med.pharmacy.entity.vo.SpecialMedicalFoodVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Tag(name = "特医食品目录服务")
@RestController
@RequestMapping("/specialMedicalFood")
@Validated
@EnableCommonResponseAdvice
public class SpecialMedicalFoodController {

    @Resource
    private SpecialMedicalFoodFeign apiFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<SpecialMedicalFoodVO> query(@RequestBody SpecialMedicalFoodQueryDTO requestBody){
        PageResult<SpecialMedicalFoodVO> specialMedicalFoodVOPageResult = apiFeign.query(requestBody);

        // 厂家ID列表
        List<String> producerIdList = specialMedicalFoodVOPageResult.getList().stream().map(SpecialMedicalFoodVO::getProducerId).
                filter(Objects::nonNull).distinct().collect(Collectors.toList());

        // 赋值厂家名称
        if ( specialMedicalFoodVOPageResult.getList() != null && specialMedicalFoodVOPageResult.getList().size() != 0) {
            for (SpecialMedicalFoodVO specialMedicalFoodVO : specialMedicalFoodVOPageResult.getList()) {
                specialMedicalFoodVO.setProducerName(specialMedicalFoodVO.getProducerId());
            }
        }

        return specialMedicalFoodVOPageResult;
    }

    @PostMapping("/add")
    @Operation(summary = "增加数据", description = "")
    @OperateLog
    public void add(@RequestBody SpecialMedicalFoodAddDTO requestBody) {
        apiFeign.add(requestBody);
    }

    @PostMapping("/update")
    @Operation(summary = "更新信息", description = "")
    @OperateLog
    public void update(@RequestBody SpecialMedicalFoodAddDTO requestBody) {
        apiFeign.update(requestBody);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除信息", description = "")
    @OperateLog
    public void update(@RequestBody DeleteDTO requestBody) {
        apiFeign.delete(requestBody);
    }

}
