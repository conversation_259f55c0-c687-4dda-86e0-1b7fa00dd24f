package com.ykl.med.pharmacy.hander;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    private final AppInterceptor appInterceptor;

    public WebConfig(AppInterceptor appInterceptor) {
        this.appInterceptor = appInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(appInterceptor).order(10);
    }

}