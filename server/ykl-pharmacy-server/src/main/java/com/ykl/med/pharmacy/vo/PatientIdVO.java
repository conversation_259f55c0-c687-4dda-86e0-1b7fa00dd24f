package com.ykl.med.web.vo;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "PatientIdVO", description = "患者id请求vo")
public class PatientIdVO implements Serializable {

    private static final long serialVersionUID = 1585471020415068563L;

    @Schema(description = "患者id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Stringify
    private Long patientId;
}
