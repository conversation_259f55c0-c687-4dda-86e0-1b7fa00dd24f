package com.ykl.med.mini.controller.consult;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.consult.MixConsulFeign;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.vo.req.QueryConsultWaitPageReqVO;
import com.ykl.med.shift.vo.resp.ConsultDoctorPageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;

/**
 * 线上问诊
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
@Tag(name = "线上问诊")
@RestController
@RequestMapping("/consult")
@EnableCommonResponseAdvice
public class ConsultController {

    @Resource
    private MixConsulFeign mixConsulFeign;

    @Operation(summary = "线上门诊 - 待接诊列表")
    @PostMapping("/list/wait")
    public PageResult<ConsultDoctorPageVO> listWait(@Valid @RequestBody QueryConsultWaitPageReqVO param) {
        param.setDoctorId(AuthContextHolder.getInstance().getContext().getId());
        param.setConsultState(Arrays.asList(ConsultStateEnum.WAIT, ConsultStateEnum.STARTING));
        return mixConsulFeign.listWait(param);
    }


}
