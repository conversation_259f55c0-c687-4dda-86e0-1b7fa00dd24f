package com.ykl.med.mini.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.doctors.entity.vo.DoctorListVO;
import com.ykl.med.mix.api.patient.MixPatientDoctorFeign;
import com.ykl.med.mix.vo.patient.QueryDoctorByPatientMedicalTeamReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/18
 */

@Tag(name = "医生相关")
@RestController
@RequestMapping("/doctor")
@EnableCommonResponseAdvice
public class MiniDoctorController {

    @Resource
    private MixPatientDoctorFeign mixPatientDoctorFeign;

    @PostMapping("/getDoctorsInPatientMedicalGroup")
    @Operation(summary = "根据患者的医疗团队id查找医生列表")
    public List<DoctorListVO> getDoctorsInPatientMedicalGroup(@Valid @RequestBody QueryDoctorByPatientMedicalTeamReqVO reqVO) {
        return mixPatientDoctorFeign.getDoctorsInPatientMedicalGroup(reqVO);
    }


}
