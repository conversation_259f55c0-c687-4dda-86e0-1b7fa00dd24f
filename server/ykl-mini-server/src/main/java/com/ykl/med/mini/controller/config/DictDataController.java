package com.ykl.med.mini.controller.config;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.vo.dict.DictDataPageReqVO;
import com.ykl.med.masterdata.vo.dict.DictDataRespVO;
import com.ykl.med.masterdata.vo.dict.DictDataSimpleRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "管理后台 - 字典数据")
@RestController
@RequestMapping("/system/dict-data")
@Validated
@EnableCommonResponseAdvice
public class DictDataController {

    @Resource
    private DictDataFeign dictDataFeign;

    @PostMapping("/page")
    @Operation(summary = "/获得字典数据的分页列表")
    public PageResult<DictDataRespVO> getDictTypePage(@Valid @RequestBody DictDataPageReqVO reqVO) {
        return dictDataFeign.getDictTypePage(reqVO);
    }

    @PostMapping("/list-all-simple")
    @Operation(summary = "获得全部字典数据列表", description = "一般用于管理后台缓存字典数据在本地")
    public List<DictDataSimpleRespVO> getSimpleDictDataList() {
        return dictDataFeign.getSimpleDictDataList();
    }

    @PostMapping("/getDictDataByValues")
    @Operation(summary = "批量根据字典值查询字典数据")
    public List<DictDataRespVO> getDictDataByValues(@RequestBody List<String> values) {
        return dictDataFeign.getDictDataByValues(values);
    }

    @PostMapping("/getDictDataByValue")
    @Operation(summary = "根据字典值查询字典数据")
    public DictDataRespVO getDictDataByValue(@RequestParam(value = "value") String value){
        return dictDataFeign.getDictDataByValue(value);
    }
}
