package com.ykl.med.mini.controller.master;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.api.RegionFeign;
import com.ykl.med.masterdata.entiry.dto.RegionQueryDTO;
import com.ykl.med.masterdata.entiry.vo.RegionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "中国地区目录服务")
@RestController
@RequestMapping("/region")
@Validated
@EnableCommonResponseAdvice
public class RegionController {

    @Resource
    private RegionFeign apiFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<RegionVO> query(@RequestBody RegionQueryDTO requestBody){

        // 初始化返回结果
        PageResult<RegionVO> regionVOPageResult;

        requestBody.setPageNo(0);
        requestBody.setPageSize(9999);
        regionVOPageResult = apiFeign.crud("GET", requestBody);

        /**
         * 处理地区数据: 整理成树形结构
         * */
        // 数量
        Long provinceCount = 0L;
        Long areaCount = 0L;
        Long cityCount = 0L;
        // 区域数据列表
        List<RegionVO> provinceList = new ArrayList<>();
        List<RegionVO> areaList = new ArrayList<>();
        List<RegionVO> cityList = new ArrayList<>();
        // 区域数据Map
        Map<String, List<RegionVO>> provinceMap = new HashMap<>();
        Map<String, List<RegionVO>> areaMap = new HashMap<>();

        /**
         * 循环拆分数据
         * */
        for ( RegionVO regionVO : regionVOPageResult.getList() ){
            switch ( regionVO.getLevel() ){
                case 1:
                    provinceList.add(regionVO);
                    provinceCount++;
                    break;
                case 2:
                    areaList.add(regionVO);
                    areaCount++;

                    // 将数据分到对应的map中去
                    if ( !provinceMap.containsKey(regionVO.getParentId()) ){
                        provinceMap.put(regionVO.getParentId(), new ArrayList<>());
                    }
                    provinceMap.get(regionVO.getParentId()).add(regionVO);
                    break;
                case 3:
                    cityList.add(regionVO);
                    cityCount++;

                    // 将数据分到对应的map中去
                    if ( !areaMap.containsKey(regionVO.getParentId()) ){
                        areaMap.put(regionVO.getParentId(), new ArrayList<>());
                    }
                    areaMap.get(regionVO.getParentId()).add(regionVO);
                    break;
                default:
                    break;
            }
        }

        // 将city挂载area中的regionList
        for ( RegionVO regionVO : areaList ) {
            if ( areaMap.containsKey(regionVO.getId()) ){
                regionVO.setRegionList(areaMap.get(regionVO.getId()));
            }
        }

        // 将area挂载province中的regionList
        for ( RegionVO regionVO : provinceList ){
            if ( provinceMap.containsKey(regionVO.getId()) ){
                regionVO.setRegionList(provinceMap.get(regionVO.getId()));
            }
        }

        // 结果赋值
        if ( provinceList.size() != 0 ){
            regionVOPageResult.setTotal(provinceCount);
            regionVOPageResult.setList(provinceList);
        } else if ( areaList.size() != 0 ){
            regionVOPageResult.setTotal(areaCount);
            regionVOPageResult.setList(areaList);
        } else if ( cityList.size() != 0 ){
            regionVOPageResult.setTotal(cityCount);
            regionVOPageResult.setList(cityList);
        }

        return regionVOPageResult;
    }

}
