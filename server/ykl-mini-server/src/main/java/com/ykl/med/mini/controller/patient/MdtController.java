package com.ykl.med.mini.controller.patient;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.patient.MixMdtFeign;
import com.ykl.med.mix.vo.patient.mdt.MixMdtListVO;
import com.ykl.med.patient.vo.mdt.WaitMdtQueryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "MDT会诊服务")
@RestController
@RequestMapping("/mdt")
@Slf4j
@EnableCommonResponseAdvice
public class MdtController {
    @Resource
    private MixMdtFeign mixMdtFeign;

    @PostMapping("/waitMdtPage")
    @Operation(summary = "待参加MDT会诊列表")
    public PageResult<MixMdtListVO> waitMdtPage(@RequestBody WaitMdtQueryVO reqVO) {
        return mixMdtFeign.waitMdtPage(reqVO);
    }

}
