package com.ykl.med.mini.vo;

import com.ykl.med.mix.vo.patient.MixPatientPendingVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "小程序首页的数字项")
public class MiniPatientPendingVO extends MixPatientPendingVO {
    @Schema(description = "已绑定患者")
    private Long bindPatientCount;

    @Schema(description = "待处理事项")
    private Long pendingSum;

    @Schema(description = "已服务人数")
    private Long alreadyServiceCount;
}
