package com.ykl.med.mini.controller.push;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.push.api.UserPhrasesFeign;
import com.ykl.med.push.vo.message.UserPhrasesReqVO;
import com.ykl.med.push.vo.message.UserPhrasesVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "用户常用语")
@RestController
@RequestMapping("/userPhrases")
@EnableCommonResponseAdvice
public class UserPhrasesController {
    @Resource
    private UserPhrasesFeign userPhrasesFeign;

    @PostMapping("/add")
    @Operation(summary = "添加用户常用语")
    public void add(@RequestBody UserPhrasesReqVO reqVO) {
        userPhrasesFeign.add(reqVO);
    }

    @PostMapping("/getByUserId")
    @Operation(summary = "获取用户常用语")
    public UserPhrasesVO getByUserId() {
        return userPhrasesFeign.getByUserId(AuthContextHolder.getInstance().getContext().getId());
    }


}
