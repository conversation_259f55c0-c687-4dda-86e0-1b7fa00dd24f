package com.ykl.med.web.controller.rehab;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.rehab.api.SportRehabFeign;
import com.ykl.med.rehab.api.SportRehabPlanItemExecuteFeign;
import com.ykl.med.rehab.api.SportRehabSurveySnapshotFeign;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.sport.SportRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.FormAndSnapshotRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanExecuteRecordRespVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabSurveySnapshotVO;
import com.ykl.med.web.hander.CheckDoctorPatientPermission;
import com.ykl.med.web.vo.PatientIdVO;
import com.ykl.med.web.vo.rehab.SportRehabExecRecordsRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 运动康复服务
 */
@RestController
@Tag(name = "运动康复服务")
@RequestMapping("/sportRehab")
@EnableCommonResponseAdvice
public class WebSportRehabController {

    @Resource
    private SportRehabFeign sportRehabFeign;

    @Resource
    private SportRehabSurveySnapshotFeign sportRehabSurveySnapshotFeign;

    @Resource
    private SportRehabPlanItemExecuteFeign sportRehabPlanItemExecuteFeign;

    @Resource
    private EventTaskFeign eventTaskFeign;

    @Resource
    private IdServiceImpl idService;

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/getCurrentPlan")
    @Operation(summary = "获取当前康复计划", description = "此方法是用来获取病人的当前康复计划")
    public SportRehabPlanDetailRespVO getCurrentPlan(@Valid @RequestBody PatientIdVO patientIdVO) {
        return sportRehabFeign.getCurrentPlan(patientIdVO.getPatientId());
    }

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/getHistoryPlans")
    @Operation(summary = "获取历史康复计划", description = "此方法是用来获取病人的历史康复计划")
    public List<SportRehabPlanDetailRespVO> getHistoryPlans(@Valid @RequestBody HistoryPlanReqVO reqVO) {
        return sportRehabFeign.getHistoryPlans(reqVO);
    }

    @CheckDoctorPatientPermission
    @PostMapping("/extendPlan")
    @Operation(summary = "延长康复计划", description = "此方法是用来延长病人的康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "延长康复计划",module = "运动康复")
    public void extendPlan(@Valid @RequestBody ExtendPlanReqVO reqVO) {
        sportRehabFeign.extendPlan(reqVO);

        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString())
                .setBizId(reqVO.getPlanId().toString())
                .setBizType(EventTaskType.SPORTS_REHAB_PLAN_CHANGE);
        eventTaskAddVO.setUserId(AuthContextHolder.getInstance().getContext().getId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @CheckDoctorPatientPermission
    @PostMapping("/saveOrUpdatePlan")
    @Operation(summary = "保存或更新康复计划", description = "此方法是用来保存或更新病人的康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "保存或更新康复计划",module = "运动康复")
    public void saveOrUpdatePlan(@Valid @RequestBody SportRehabPlanSaveOrUpdateReqVO reqVO) {
        sportRehabFeign.saveOrUpdate(reqVO);
    }

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/surveySnapshot/list")
    @Operation(summary = "获取问卷快照列表", description = "此方法是用来获取病人的问卷快照列表")
    public List<SportRehabSurveySnapshotVO> getSurveySnapshots(@Valid @RequestBody SurveySnapshotListReqVO reqVO) {
        return sportRehabSurveySnapshotFeign.list(reqVO);
    }
    @PostMapping("/surveySnapshot/getById")
    @Operation(summary = "获取问卷快照详情")
    public SportRehabSurveySnapshotVO getSurveySnapshotById(@RequestBody IdReqVO idReqVO){
        return sportRehabSurveySnapshotFeign.details(idReqVO.getId());
    }
    @CheckDoctorPatientPermission
    @PostMapping("/surveySnapshot/send")
    @Operation(summary = "发送问卷快照", description = "此方法是用来发送病人的问卷快照")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "发送问卷快照",module = "运动康复")
    public void sendSurveySnapshots(@Valid @RequestBody RehabSurveySnapshotSendReqVO reqVO) {
        sportRehabSurveySnapshotFeign.send(reqVO);
    }

//    @PostMapping("/surveySnapshot/complete")
//    @Operation(summary = "填写问卷快照", description = "此方法是用来填写病人的问卷快照")
//    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "填写问卷快照",module = "运动康复")
//    public void completeSurveySnapshots(@Valid @RequestBody RehabSurveySnapshotCompleteReqVO reqVO) {
//        sportRehabSurveySnapshotFeign.complete(reqVO);
//    }

    @CheckDoctorPatientPermission
    @PostMapping("/surveySnapshot/saveOrUpdate")
    @Operation(summary = "保存或更新问卷快照", description = "此方法是用来保存或更新病人的问卷快照")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "保存或更新问卷快照",module = "运动康复")
    public void saveOrUpdateSurveySnapshots(@Valid @RequestBody SurveySnapshotSaveOrUpdateReqVO reqVO) {
        if (reqVO.getId() == null) {
            Long id = idService.nextId();
            reqVO.setId(id);
        }
        sportRehabSurveySnapshotFeign.saveOrUpdate(reqVO);

        SportRehabPlanSaveOrUpdateReqVO basePlanReq = sportRehabFeign.getBasePlanReq(reqVO.getPatientId());
        if (basePlanReq != null) {
            EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(reqVO.getPatientId())
                    .setRequestId(UUID.randomUUID().toString())
                    .setExecuteTime(LocalDateTime.now().plusMinutes(5))
                    .setBizId(reqVO.getPatientId().toString())
                    .setBizType(EventTaskType.SPORTS_REHAB_BASE_PLAN);
            eventTaskFeign.addEventTask(eventTaskAddVO);
        }

        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setRequestId(reqVO.getId().toString())
                .setEventTime(LocalDateTime.now())
                .setBizType(EventTaskType.PATIENT_FINISH_SPORTS_FORM)
                .setUserId(AuthContextHolder.getInstance().getContext().getId())
                .setPatientId(reqVO.getPatientId())
                .setBizId(reqVO.getId().toString());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @CheckDoctorPatientPermission
    @PostMapping("/surveySnapshot/batchComplete")
    @Operation(summary = "批量填写问卷快照", description = "此方法是用来批量填写病人的问卷快照")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "批量填写问卷快照",module = "运动康复")
    public void batchCompleteSurveySnapshots(@Valid @RequestBody RehabSurveySnapshotsBatchSaveReqVO reqVO) {
        sportRehabSurveySnapshotFeign.batchComplete(reqVO);
    }

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/planItemExecute/weeklyStat")
    @Operation(summary = "获取康复计划执行周统计", description = "此方法是用来获取康复计划执行周统计")
    public RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(@RequestBody ItemExecStatReqVO reqVO) {
        return sportRehabPlanItemExecuteFeign.getWeeklyStatistics(reqVO);
    }

    @PostMapping("/planItemExecute/queryExecuteRecordsByTimeRange")
    @Operation(summary = "获取康复计划执行记录时间范围查询", description = "此方法是用来获取康复计划执行记录时间范围查询")
    @CheckDoctorPatientPermission(needMember = false)
    public List<SportRehabExecRecordsRespVO> queryExecuteRecords(
            @Valid @RequestBody RehabPlanExecuteTimeRangeQueryReqVO reqVO) {
        Map<String, List<SportRehabPlanExecuteRecordRespVO>> map = sportRehabPlanItemExecuteFeign
                .queryExecuteRecordsByTimeRange(reqVO);
        return map.entrySet().stream()
                .map(entry -> new SportRehabExecRecordsRespVO()
                        .setDate(entry.getKey())
                        .setRecords(entry.getValue()))
                .collect(Collectors.toList());
    }

    @PostMapping("/planItemExecute/update")
    @Operation(summary = "更新康复计划执行记录", description = "此方法是用来更新康复计划执行记录")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "更新康复计划执行记录",module = "运动康复")
    public void updateExecuteRecord(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        sportRehabPlanItemExecuteFeign.updateExecuteRecord(reqVO);
    }

    @PostMapping("/planItemExecute/execute")
    @Operation(summary = "执行康复计划", description = "此方法是用来执行康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "执行康复计划",module = "运动康复")
    public void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        sportRehabFeign.execute(reqVO);
    }

    @PostMapping("/plan/reEvaluate")
    @CheckDoctorPatientPermission
    @Operation(summary = "重新评估康复计划", description = "此方法是用来重新评估病人的康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "重新评估康复计划",module = "运动康复")
    public void reEvaluate(@Valid @RequestBody PatientIdVO patientIdVO) {
        sportRehabFeign.reEvaluate(patientIdVO.getPatientId());
    }

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/plan/getDraftPlan")
    @Operation(summary = "获取草稿康复计划", description = "此方法是用来获取病人的草稿康复计划")
    public SportRehabPlanDetailRespVO getDraftPlan(@Valid @RequestBody PatientIdVO patientIdVO) {
        return sportRehabFeign.getDraftPlan(patientIdVO.getPatientId());
    }

    @PostMapping("/plan/getFormAndSnapshots")
    @CheckDoctorPatientPermission(needMember = false)
    @Operation(summary = "获取当前评估问卷及评估状态", description = "此方法是用来获取当前评估问卷及评估状态")
    public List<FormAndSnapshotRespVO> getFormAndSnapshots(@Valid @RequestBody FormAndSnapshotReqVO reqVO) {
        return sportRehabFeign.getFormAndSnapshots(reqVO);
    }

    @PostMapping("/plan/getPlanById")
    @CheckDoctorPatientPermission(needMember = false)
    @Operation(summary = "获取方案详情", description = "此方法是用来获取方案详情")
    public SportRehabPlanDetailRespVO getPlanById(@Valid @RequestBody PatientIdAndPlanIdVO reqVO) {
        return sportRehabFeign.getPlanById(reqVO);
    }
}