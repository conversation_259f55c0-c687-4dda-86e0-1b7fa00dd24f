package com.ykl.med.web.controller.medical;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.medical.api.MedicalAdviceFeign;
import com.ykl.med.medical.vo.medicalAdvice.*;
import com.ykl.med.mix.api.medical.MixMedicalAdviceFeign;
import com.ykl.med.mix.vo.MixMedicalAdviceConsultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 医嘱（处方）
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
@Tag(name = "医嘱（处方）")
@RestController
@RequestMapping("/medicalAdvice")
@EnableCommonResponseAdvice
public class MedicalAdviceController {

    @Resource
    private MedicalAdviceFeign medicalAdviceFeign;
    @Resource
    private MixMedicalAdviceFeign mixMedicalAdviceFeign;

    @Operation(summary = "医嘱列表-医嘱管理")
    @PostMapping("/page/web")
    public PageResult<MedicalAdviceWebPageVO> pageWeb(@Valid @RequestBody QueryMedicalAdviceWebPageReqVO param) {
        return medicalAdviceFeign.pageWeb(param);
    }

    @Operation(summary = "医嘱列表-详情")
    @PostMapping("/details")
    public MixMedicalAdviceConsultVO details(@RequestParam(name = "medicalAdviceId") Long medicalAdviceId) {
        return mixMedicalAdviceFeign.detailsConsult(medicalAdviceId);
    }

    @Operation(summary = "医嘱药品列表-药品导入")
    @PostMapping("/page/item")
    public PageResult<MedicalAdviceItemPageVO> pageItem(@Valid @RequestBody QueryMedicalAdviceItemPageReqVO param) {
        return medicalAdviceFeign.pageItem(param);
    }

    @Operation(summary = "创建医嘱")
    @PostMapping("/create")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "创建医嘱数据",module = "用药管理")
    public Long create(@Valid @RequestBody CreateMedicalAdviceReqVO param) {
        param.setDoctorId(AuthContextHolder.getInstance().getContext().getId());
        return mixMedicalAdviceFeign.create(param);
    }

    @Operation(summary = "作废")
    @PostMapping("/discard")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "作废医嘱数据",module = "用药管理")
    public void discard(@Valid @RequestBody DiscardMedicalAdviceReqVO param) {
        mixMedicalAdviceFeign.discard(param);
    }


    @Operation(summary = "医嘱列表-问诊id")
    @PostMapping("/getByConsult")
    public List<PrescriptionVO> getByConsult(@RequestParam(name = "consultId") Long consultId) {
        return medicalAdviceFeign.getByConsult(new QueryMedicalAdviceConsultReqVO().setConsultId(consultId));
    }



}
