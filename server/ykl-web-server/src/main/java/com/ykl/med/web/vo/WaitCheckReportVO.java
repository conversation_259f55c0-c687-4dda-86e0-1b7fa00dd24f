package com.ykl.med.web.vo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "待审核报表对象")
public class WaitCheckReportVO {

    @Schema(description = "报表类型", defaultValue = "报表名称")
    private WaitCheckReportType reportType;

    @Schema(description = "主键", defaultValue = "1")
    private Long id;
    @Schema(description = "报表名称", defaultValue = "报表名称")
    private String reportName;

    @TimestampConvert
    @Schema(description = "提交时间", defaultValue = "125562332434")
    private LocalDateTime submitTime;

    @Schema(description = "状态（参照各类型的状态）", defaultValue = "待审核")
    private String status;

    @Schema(description = "病种", defaultValue = "病种")
    private String diseases;

    @Schema(description = "病种阶段", defaultValue = "病种阶段")
    private String stage;

    @Schema(description = "患者本人手机号", defaultValue = "18109074912")
    private String contactPhone;

    @Stringify
    @Schema(description = "患者码", defaultValue = "100043")
    private Long patientId;

    @Schema(description = "名字", defaultValue = "患者名字")
    private String name;

    @Schema(description = "性别", defaultValue = "male")
    private String sex;

    @Schema(description = "年龄", defaultValue = "15")
    private Integer age;

    @AllArgsConstructor
    public enum WaitCheckReportType {
        RECORD("病历档案确认"),
        INTELLIGENT_REPORT("评估报告审核");
        private final String desc;
    }
}
