package com.ykl.med.web.config;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.user.vo.UserAuthorityVO;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * AuthorityFeignInterceptor
 *
 * <AUTHOR>
 */
@Component
public class AuthorityFeignInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        UserAuthorityVO authorityContext = AuthContextHolder.getInstance().getContext();
        if (authorityContext != null) {
            requestTemplate.header("X-Soa-Current-User-Id", String.valueOf(authorityContext.getId()));
            Long medicalTeamId = authorityContext.getMedicalTeamId();
            if (medicalTeamId != null) {
                requestTemplate.header("X-Soa-Current-Medical-Team-Id", String.valueOf(medicalTeamId));
            }
            addMedicalTeamIdsHeader(requestTemplate, authorityContext.getMedicalTeamIds());
        }
    }

    private void addMedicalTeamIdsHeader(RequestTemplate requestTemplate, Set<String> medicalTeamIds) {
        if (!CollectionUtils.isEmpty(medicalTeamIds)) {
            String medicalTeamIdsStr = String.join(",", medicalTeamIds);
            requestTemplate.header("X-Soa-Current-Medical-Team-Ids", medicalTeamIdsStr);
        }
    }
}
