package com.ykl.med.web.service;

import com.ykl.med.followup.constants.FollowupErrorCodeConstants;
import com.ykl.med.followup.enums.FollowupTimeModeEnums;
import com.ykl.med.followup.enums.FollowupTimeUnitEnums;
import com.ykl.med.followup.enums.FollowupTrackTypeEnum;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.masterdata.api.FormManageFeign;
import com.ykl.med.masterdata.enums.FromManageTypeEnum;
import com.ykl.med.masterdata.vo.FormManageVO;
import com.ykl.med.masterdata.vo.req.FormManageListReqVO;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.web.vo.FollowupTrackItemTemplateVO;
import com.ykl.med.web.vo.FollowupTrackTemplateVO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */

@Service
@RequiredArgsConstructor
public class FollowupTrackService {

    private final PatientFeign patientFeign;
    private final FormManageFeign formManageFeign;
    private final static String FOLLOWUP_NAME_PREFIX = "入组";

    public List<FollowupTrackTemplateVO> template(Long patientId) {
        if (patientId == null) {
            return new ArrayList<>();
        }
        PatientVO patientVO = patientFeign.getPatientById(patientId);
        if (patientVO == null || patientVO.getMemberVersionTime() == null) {
            return new ArrayList<>();
        }
        List<FormManageVO> formManageVOS = formManageFeign.list(new FormManageListReqVO().setType(FromManageTypeEnum.FOLLOWUP_TRACK).setStatus(true));
        LocalDate nowDay = DateTimeUtils.getNowDay();
        LocalDate memberVersionTime = patientVO.getMemberVersionTime().toLocalDate();
        List<TemplateConfig> configs = Arrays.asList(
                new TemplateConfig(1, FollowupTimeUnitEnums.WEEK),
                new TemplateConfig(2, FollowupTimeUnitEnums.WEEK),
                new TemplateConfig(4, FollowupTimeUnitEnums.WEEK),
                new TemplateConfig(8, FollowupTimeUnitEnums.WEEK),
                new TemplateConfig(12, FollowupTimeUnitEnums.WEEK),
                new TemplateConfig(6, FollowupTimeUnitEnums.MONTH),
                new TemplateConfig(9, FollowupTimeUnitEnums.MONTH),
                new TemplateConfig(12, FollowupTimeUnitEnums.MONTH));
        return configs.stream()
                .map(config -> {
                    FollowupTrackTemplateVO followupTrackTemplateVO = new FollowupTrackTemplateVO();
                    followupTrackTemplateVO.setFollowupName(FOLLOWUP_NAME_PREFIX + config.timeValue + config.timeUnit.getDesc());
                    followupTrackTemplateVO.setFollowupMode(FollowupTrackTypeEnum.followup_video.name());
                    followupTrackTemplateVO.setFollowupTimeMode(FollowupTimeModeEnums.JOIN_MEMBER.getCode());
                    followupTrackTemplateVO.setFollowupTimeLimit(config.timeValue);
                    followupTrackTemplateVO.setFollowupTimeLimitUnit(config.timeUnit.getCode());
                    // 计算随访日期
                    LocalDate followupTime = execFollowupTime(memberVersionTime, config.timeValue, config.getTimeUnit());
                    followupTrackTemplateVO.setFollowupTime(followupTime);
                    followupTrackTemplateVO.setItems(createItems(config, formManageVOS));
                    return followupTrackTemplateVO;
                })
                .filter(e -> e.getFollowupTime().equals(nowDay) || e.getFollowupTime().isAfter(nowDay))
                .collect(Collectors.toList());
    }


    private static LocalDate execFollowupTime(LocalDate memberVersionTime, Integer followupTimeLimit, FollowupTimeUnitEnums unitEnums) {
        switch (unitEnums) {
            case DAY:
                return memberVersionTime.plusDays(followupTimeLimit);
            case WEEK:
                return memberVersionTime.plusWeeks(followupTimeLimit);
            case MONTH:
                return memberVersionTime.plusMonths(followupTimeLimit);
            case YEAR:
                return memberVersionTime.plusYears(followupTimeLimit);
            default:
                throw new ServiceException(FollowupErrorCodeConstants.FOLLOWUP_TIME_UNIT_ERROR);
        }
    }

    private static List<FollowupTrackItemTemplateVO> createItems(TemplateConfig config, List<FormManageVO> formManageVOS) {
        List<FollowupTrackItemTemplateVO> result = new ArrayList<>();
        for (FormManageVO formManageVO : formManageVOS) {
            if (formManageVO.getName().contains("评价反馈")) {
                if ((config.timeValue == 4 && config.timeUnit.equals(FollowupTimeUnitEnums.WEEK))
                        || (config.timeValue == 12 && config.timeUnit.equals(FollowupTimeUnitEnums.WEEK))) { // 四周和12周需要评价反馈
                    FollowupTrackItemTemplateVO item = new FollowupTrackItemTemplateVO();
                    item.setFormId(formManageVO.getId());
                    item.setItemType(2);
                    item.setItemName(formManageVO.getName());
                    item.setItemContent(formManageVO.getRemark());
                    result.add(item);
                }
            } else {
                FollowupTrackItemTemplateVO item = new FollowupTrackItemTemplateVO();
                item.setFormId(formManageVO.getId());
                item.setItemType(2);
                item.setItemName(formManageVO.getName());
                item.setItemContent(formManageVO.getRemark());
                result.add(item);
            }
        }

        FollowupTrackItemTemplateVO item_2 = new FollowupTrackItemTemplateVO();
        item_2.setItemType(1);
        item_2.setItemName("注意事项");
        result.add(item_2);
        return result;
    }


    @Data
    static class TemplateConfig {
        private int timeValue;
        private FollowupTimeUnitEnums timeUnit;

        public TemplateConfig(int timeValue, FollowupTimeUnitEnums timeUnit) {
            this.timeValue = timeValue;
            this.timeUnit = timeUnit;
        }
    }
}
