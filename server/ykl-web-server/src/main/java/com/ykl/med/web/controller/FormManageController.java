package com.ykl.med.web.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.IdListReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.api.FormManageFeign;
import com.ykl.med.masterdata.vo.FormManageVO;
import com.ykl.med.masterdata.vo.req.FormManageListReqVO;
import com.ykl.med.masterdata.vo.req.FormManageSaveReqVO;
import com.ykl.med.masterdata.vo.req.FormManageUpdateStatusReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 表单
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/form/manage")
@EnableCommonResponseAdvice
@Tag(name = "新表单")
public class FormManageController {

    private final FormManageFeign formManageService;

    @PostMapping("/list")
    @Operation(summary = "列表")
    public List<FormManageVO> list(@Valid @RequestBody FormManageListReqVO reqVO) {
        return formManageService.list(reqVO);
    }

    @PostMapping("/detail")
    @Operation(summary = "详情")
    public FormManageVO detail(@RequestParam("id") Long id) {
        return formManageService.detail(id);
    }

}
