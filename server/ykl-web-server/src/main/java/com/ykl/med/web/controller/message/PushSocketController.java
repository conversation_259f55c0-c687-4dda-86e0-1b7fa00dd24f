package com.ykl.med.web.controller.message;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.user.vo.UserAuthorityVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "推送websocket服务")
@RestController
@RequestMapping("/pushSocket")
@Slf4j
@EnableCommonResponseAdvice
public class PushSocketController {
    @Resource
    private PushSocketFeign pushSocketFeign;

    @PostMapping("/sendScanNotice")
    @Operation(summary = "发送扫码通知")
    public void sendScanNotice(@RequestBody IdReqVO idReqVO) {
        UserAuthorityVO userAuthorityVO = AuthContextHolder.getInstance().getContext();
        pushSocketFeign.sendScanNotice(idReqVO.getId(), userAuthorityVO.getMedicalTeamId());
    }

}
