package com.ykl.med.web.vo.rehab;

import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanExecuteRecordRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(name = "SportRehabExecRecordsRespVO", description = "运动康复执行记录响应VO")
public class SportRehabExecRecordsRespVO implements Serializable {

    private static final long serialVersionUID = 7058031353218766534L;

    @Schema(description = "日期", example = "2021-01-01")
    private String date;

    @Schema(description = "运动康复计划执行记录", example = "[]")
    private List<SportRehabPlanExecuteRecordRespVO> records;
}
