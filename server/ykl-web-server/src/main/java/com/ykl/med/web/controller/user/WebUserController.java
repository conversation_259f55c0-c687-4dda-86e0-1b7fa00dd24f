package com.ykl.med.web.controller.user;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.doctors.api.MedicalTeamFeign;
import com.ykl.med.doctors.entity.vo.MedicalTeamSimpleRespVO;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.mix.api.user.MixUserFeign;
import com.ykl.med.mix.vo.UserInfoVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.enums.UserType;
import com.ykl.med.user.vo.req.LoginOrRegisterReqVO;
import com.ykl.med.user.vo.req.MyPasswordUpdateReqVO;
import com.ykl.med.user.vo.req.SimpleUserPageReqVO;
import com.ykl.med.user.vo.req.UserBaseInfoUpdateReqVO;
import com.ykl.med.user.vo.resp.LoginOrRegisterRespVO;
import com.ykl.med.web.config.YklAuthorityClientConfig;
import com.ykl.med.web.hander.ResponseCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@Tag(name = "用户服务")
@RequestMapping
@RequiredArgsConstructor
@EnableCommonResponseAdvice
public class WebUserController {

    @Resource
    private MedicalTeamFeign medicalTeamFeign;

    @Resource
    private UserFeign userFeign;

    @Resource
    private MixUserFeign mixUserFeign;

    private final YklAuthorityClientConfig yklAuthorityClientConfig;

    @GetMapping("/user/listMetaTeams")
    @Operation(summary = "列出全部叶子结点专家组")
    public List<MedicalTeamSimpleRespVO> listMetaTeams() {
        return medicalTeamFeign.listMetaTeams();
    }

    @GetMapping("/user/listMetaTeamUsers")
    @Operation(summary = "列出叶子节点专家组下的用户")
    public List<UserInfoVO> listMetaTeamUsers(
            @RequestParam(value = "medicalTeamId", required = false) Long medicalTeamId) {
        SimpleUserPageReqVO simpleUserPageReqVO = new SimpleUserPageReqVO();
        simpleUserPageReqVO.setMedicalTeamId(medicalTeamId == null ? null : medicalTeamId.toString());
        simpleUserPageReqVO.setPageSize(9999);
        return mixUserFeign.page(simpleUserPageReqVO).getList();
    }

    @PostMapping("/user/login")
    @Operation(summary = "登录")
    public LoginOrRegisterRespVO login(HttpServletRequest request, @RequestBody LoginOrRegisterReqVO reqVO) {
        String ip = request.getHeader("x-forwarded-for");
        reqVO.setIp(ip);
        reqVO.setUserType(UserType.DOCTOR);
        reqVO.setClientId(yklAuthorityClientConfig.getId());
        reqVO.setClientSecret(yklAuthorityClientConfig.getSecret());

        LoginOrRegisterRespVO loginOrRegisterRespVO = mixUserFeign.loginOrRegister(reqVO);

        /**
         * 判断账号是否绑定医生以及有医疗组
         * */
        if (loginOrRegisterRespVO != null
                && loginOrRegisterRespVO.getUserInfo() != null
                && StringUtils.isNotBlank(loginOrRegisterRespVO.getUserInfo().getId())) {
            // 获取医生信息
            UserInfoVO userSimpleVO = mixUserFeign.info(new IdReqVO().setId(Long.valueOf(loginOrRegisterRespVO.getUserInfo().getId())));
            if (userSimpleVO == null) {
                throw new ServiceException(ResponseCode.USER_IS_NOT_EXIST.getCode(), ResponseCode.USER_IS_NOT_EXIST.getMessage());
            }

            // 没有绑定医疗人员
            if (StringUtils.isBlank(userSimpleVO.getPersonnelId())) {
                throw new ServiceException(ResponseCode.USER_IS_NOT_BIND_PERSONNEL.getCode(), ResponseCode.USER_IS_NOT_BIND_PERSONNEL.getMessage());
            }

            // 没有绑定医疗组
            if (userSimpleVO.getMedicalTeamId() == null) {
                throw new ServiceException(ResponseCode.DOCTOR_IS_NOT_BIND_MEDICAL_TEAM.getCode(),
                        ResponseCode.DOCTOR_IS_NOT_BIND_MEDICAL_TEAM.getMessage());
            }
        }

        return loginOrRegisterRespVO;
    }

    @PostMapping("/user/info")
    @Operation(summary = "获取用户信息")
    public UserInfoVO info() {
        IdReqVO idReqVO = new IdReqVO();
        idReqVO.setId(AuthContextHolder.getInstance().getContext().getId());
        return mixUserFeign.info(idReqVO);
    }

    @PostMapping("/user/updateMyPassword")
    @Operation(summary = "更新用户密码")
    public void updateMyPassword(@Valid @RequestBody MyPasswordUpdateReqVO reqVO) {
        userFeign.updateMyPassword(reqVO);
    }

    @PostMapping("/user/updateBaseInfo")
    @Operation(summary = "更新用户信息")
    public void update(@RequestBody UserBaseInfoUpdateReqVO userBaseInfoUpdateReqVO) {
        // 只能修改用户自己的信息
        userBaseInfoUpdateReqVO.setId(AuthContextHolder.getInstance().getContext().getId());
        userFeign.update(userBaseInfoUpdateReqVO);
    }

}
