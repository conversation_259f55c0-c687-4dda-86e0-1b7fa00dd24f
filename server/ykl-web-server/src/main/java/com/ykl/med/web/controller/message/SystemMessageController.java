package com.ykl.med.web.controller.message;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.push.api.SystemMessageFeign;
import com.ykl.med.push.vo.sys.SystemMessageCountVO;
import com.ykl.med.push.vo.sys.SystemMessageQueryVO;
import com.ykl.med.push.vo.sys.SystemMessageReadReqVO;
import com.ykl.med.push.vo.sys.SystemMessageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "系统消息服务")
@RestController
@RequestMapping("/systemMessage")
@Slf4j
@EnableCommonResponseAdvice
public class SystemMessageController {
    @Resource
    private SystemMessageFeign systemMessageFeign;

    @PostMapping("/querySystemMessage")
    @Operation(summary = "查询系统消息")
    public PageResult<SystemMessageVO> querySystemMessage(@RequestBody SystemMessageQueryVO queryVO) {
        return systemMessageFeign.querySystemMessage(queryVO);
    }

    @PostMapping("/readMessage")
    @Operation(summary = "读取消息")
    public void readMessage(@RequestBody SystemMessageReadReqVO reqVO) {
        systemMessageFeign.readMessage(reqVO);
    }

    @PostMapping("/getMessageCount")
    @Operation(summary = "获取未读消息数量")
    public SystemMessageCountVO getMessageCount() {
        return systemMessageFeign.getMessageCount(AuthContextHolder.getInstance().getContext().getId());
    }
}
