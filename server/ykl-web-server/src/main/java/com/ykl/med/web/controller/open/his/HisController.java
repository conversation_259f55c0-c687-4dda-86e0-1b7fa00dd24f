package com.ykl.med.web.controller.open.his;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.ykl.med.framework.common.util.AesUtils;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.medical.vo.medicalAdvice.HisDispenseCallbackReqVO;
import com.ykl.med.mix.api.medical.MixMedicalAdviceFeign;
import com.ykl.med.web.vo.param.HisDispenseCallbackParam;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/27
 */
@Slf4j
@Tag(name = "His回调")
@RestController
@RequestMapping("/open")
public class HisController {

    @Resource
    private MixMedicalAdviceFeign mixMedicalAdviceFeign;

    @Value("${his.open.signKey}")
    private String signKey;

    /**
     * 发药回调(HIS调用互联网医院接口)
     * 发药完成后通知互联网医院系统
     */
    @PostMapping("/his/dispense/callback")
    public void dispenseCallback(@Valid @RequestBody HisDispenseCallbackParam param) {
        log.info("his_dispense_callback_request...{}", JSONObject.toJSON(param));
        try {
            String body = param.getBody();
            String sign = param.getSign();
            String integritySign = DigestUtils.md5Hex(body + signKey);
            log.info("参数加密结果：{}", integritySign);
            if (!ObjectUtil.equal(integritySign, sign)) {
                throw new ServiceException(60000, "签名错误");
            }
            String decrypt = AesUtils.decrypt(body, signKey);
            if (!ObjectUtil.equal(integritySign, sign)) {
                throw new ServiceException(60000, "body 解密失败");
            }

            JSONObject jsonObject = JSONObject.parseObject(decrypt);
            log.info("body 解密结果：{}", jsonObject);
            Long visitId;
            Long patientId;
            LocalDateTime sendTime;
            String sendUser;
            List<Long> orderIds;
            if (jsonObject.containsKey("timestamp")) {
                long currentTimeMillis = System.currentTimeMillis();
                long timestamp = jsonObject.getLongValue("timestamp");
                if (currentTimeMillis - timestamp > (1000 * 60)) {
                    throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "timestamp time out");
                }
            } else {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "timestamp can not null");
            }
            if (jsonObject.containsKey("visitId")) {
                visitId = jsonObject.getLong("visitId");
            } else {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "visitId can not null");
            }
            if (jsonObject.containsKey("patientId")) {
                patientId = jsonObject.getLong("patientId");
            } else {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "patientId can not null");
            }
            if (jsonObject.containsKey("sendTime")) {
                sendTime = DateTimeUtils.convertStringToDateTime(jsonObject.getString("sendTime"));
            } else {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "sendTime can not null");
            }
            if (jsonObject.containsKey("sendUser")) {
                sendUser = jsonObject.getString("sendUser");
            } else {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "sendUser can not null");
            }
            if (jsonObject.containsKey("orderIds")) {
                orderIds = jsonObject.getJSONArray("orderIds").toJavaList(Long.class);
            } else {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "orderIds can not null");
            }
            HisDispenseCallbackReqVO reqVO = new HisDispenseCallbackReqVO();
            reqVO.setVisitId(visitId);
            reqVO.setPatientId(patientId);
            reqVO.setSendTime(sendTime);
            reqVO.setSendUser(sendUser);
            reqVO.setOrderIds(orderIds);
            mixMedicalAdviceFeign.dispenseCallback(reqVO);
        } catch (Exception e) {
            log.info("his_dispense_callback_error...", e);
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

}
