package com.ykl.med.web.controller.edu;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.edu.api.EduArticleFeign;
import com.ykl.med.edu.vo.req.EduArticleAdminQueryVO;
import com.ykl.med.edu.vo.resp.EduArticleAdminDetailVO;
import com.ykl.med.edu.vo.resp.EduArticleAdminListVO;
import com.ykl.med.edu.vo.resp.EduArticleAppDetailVO;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.tag.api.RecommendFeign;
import com.ykl.med.tag.entity.dto.BizTagItemQueryDTO;
import com.ykl.med.tag.entity.enums.RecommendBizCategories;
import com.ykl.med.tag.entity.vo.BizTagItemVO;
import com.ykl.med.tag.entity.vo.TagItemVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/eduArticle")
@Tag(name = "患教文章")
@EnableCommonResponseAdvice
public class EduArticleController {
    @Resource
    private EduArticleFeign eduArticleFeign;
    @Resource
    private RecommendFeign recommendFeign;


    @PostMapping("/queryEduArticle")
    @Operation(summary = "查询患教文章")
    public PageResult<EduArticleAdminListVO> queryEduArticle(@RequestBody EduArticleAdminQueryVO queryVO) {
        if (CollectionUtils.isNotEmpty(queryVO.getTagIds())) {
            BizTagItemQueryDTO bizTagItemQueryDTO = new BizTagItemQueryDTO();
            bizTagItemQueryDTO.setPageNo(1);
            bizTagItemQueryDTO.setPageSize(9999);
            bizTagItemQueryDTO.setTagIds(queryVO.getTagIds().stream().map(String::valueOf).collect(Collectors.toList()));
            bizTagItemQueryDTO.setBizCategory(RecommendBizCategories.EDU.getValue());
            PageResult<BizTagItemVO> bizTagItemVOPageResult = recommendFeign.queryBizTagItems(bizTagItemQueryDTO);
            if (CollectionUtils.isEmpty(bizTagItemVOPageResult.getList())) {
                return new PageResult<>();
            }
            List<String> eduArticleIdsByString = bizTagItemVOPageResult.getList().get(0).getItemIds().stream().map(TagItemVO::getId).
                    filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<Long> eduArticleIds = eduArticleIdsByString.stream().map(Long::valueOf).collect(Collectors.toList());
            queryVO.setEduArticleIds(eduArticleIds);
        }
        return eduArticleFeign.queryEduArticle(queryVO);
    }


    @PostMapping("/queryEduArticleDetail")
    @Operation(summary = "查询患教文章详情")
    public EduArticleAdminDetailVO queryEduArticleDetail(@RequestBody IdReqVO reqVO) {
        return eduArticleFeign.queryEduArticleDetail(reqVO.getId());
    }


    @PostMapping("/getAppDetailPreview")
    @Operation(summary = "预览")
    public EduArticleAppDetailVO getAppDetailPreview(@RequestBody IdReqVO reqVO) {
        return eduArticleFeign.getAppDetailPreview(reqVO.getId());
    }

}