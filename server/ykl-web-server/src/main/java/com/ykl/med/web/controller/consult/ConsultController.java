package com.ykl.med.web.controller.consult;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.mix.api.consult.MixConsulFeign;
import com.ykl.med.mix.vo.consult.*;
import com.ykl.med.shift.api.ConsultFeign;
import com.ykl.med.shift.vo.req.*;
import com.ykl.med.shift.vo.resp.ConsultConclusionVO;
import com.ykl.med.shift.vo.resp.ConsultDoctorGroupVO;
import com.ykl.med.shift.vo.resp.ConsultDoctorPageVO;
import com.ykl.med.shift.vo.resp.ConsultPatientPageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 线上问诊
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
@Tag(name = "线上问诊")
@RestController
@RequestMapping("/consult")
@EnableCommonResponseAdvice
public class ConsultController {

    @Resource
    private ConsultFeign consultFeign;
    @Resource
    private MixConsulFeign mixConsulFeign;


    @Operation(summary = "线上门诊(当前用户 所有的问诊列表)")
    @PostMapping("/list/doctor")
    public List<ConsultDoctorGroupVO> listDoctor(@Valid @RequestBody QueryConsultListDoctorReqVO param) {
        param.setDoctorId(AuthContextHolder.getInstance().getContext().getId());
        return mixConsulFeign.listDoctor(param);
    }

    @Operation(summary = "线上门诊 - 待接诊列表")
    @PostMapping("/list/wait")
    public PageResult<ConsultDoctorPageVO> listWait(@Valid @RequestBody QueryConsultWaitPageReqVO param) {
        param.setDoctorId(AuthContextHolder.getInstance().getContext().getId());
        return mixConsulFeign.listWait(param);
    }

    @Operation(summary = "问诊-列表(医护端：患者详情：线上门诊)")
    @PostMapping("/list/patient")
    public List<ConsultPatientPageVO> listPatient(@Valid @RequestBody QueryConsultListPatientReqVO param) {
        return consultFeign.listPatient(param);
    }

    @Operation(summary = "线上门诊-详情")
    @PostMapping("/details")
    public MixConsultVO details(@RequestParam(name = "consultId") Long consultId) {
        return mixConsulFeign.details(consultId);
    }


    @Operation(summary = "患者电子病历")
    @PostMapping("/medicalRecords")
    public MixConsultMedicalRecordsVO medicalRecords(@Valid @RequestBody QueryMedicalRecordReqVO param) {
        return mixConsulFeign.medicalRecords(param);
    }


    @Operation(summary = "问诊-问诊小结详情")
    @PostMapping("/conclusion")
    public ConsultConclusionVO conclusion(@RequestParam(name = "consultId") Long consultId) {
        return consultFeign.conclusion(consultId);
    }


    @Operation(summary = "问诊-接诊")
    @PostMapping("/start")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "问诊-接诊", module = "线上门诊")
    public void start(@Valid @RequestBody StartConclusionReqVO param) {
        param.setUserId(AuthContextHolder.getInstance().getContext().getId());
        mixConsulFeign.start(param);
    }


    @Operation(summary = "问诊-完成问诊")
    @PostMapping("/finish")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "问诊-完成问诊", module = "线上门诊")
    public void finish(@Valid @RequestBody FinishConclusionReqVO param) {
        param.setUserId(AuthContextHolder.getInstance().getContext().getId());
        mixConsulFeign.finish(param);
    }


    @Operation(summary = "问诊-保存问诊小结")
    @PostMapping("/saveConclusion")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "问诊-保存问诊小结", module = "线上门诊")
    public void saveConclusion(@Valid @RequestBody SaveConclusionReqVO param) {
        consultFeign.saveConclusion(param);
    }


    @Operation(summary = "问诊-发起视频")
    @PostMapping("/startVideo")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "问诊-发起视频", module = "线上门诊")
    public RoomCreateRespVO startVideo(@RequestParam(name = "consultId") Long consultId) {
        return mixConsulFeign.startVideo(AuthContextHolder.getInstance().getContext().getId(), consultId);
    }

    @Operation(summary = "问诊-保存视频")
    @PostMapping("/saveVideo")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "问诊-保存视频", module = "线上门诊")
    public String saveVideo(@Valid @RequestBody SaveVideoReqVO param) {
        return consultFeign.saveVideo(param);
    }


    @Operation(summary = "问诊-结束视频")
    @PostMapping("/closeVideo")
    public void closeVideo(@RequestParam(name = "consultId") Long consultId,
                           @RequestParam(name = "userId") Long userId) {
        mixConsulFeign.closeVideo(consultId, true, userId);
    }


    @Operation(summary = "线下问诊-列表")
    @PostMapping("/offline/page")
    public List<ConsultOfflineVO> offlineList(@Valid @RequestBody ConsultOfflineReqVO param) {
        return mixConsulFeign.offlineList(param);
    }

}
