package com.ykl.med.web.controller.message;

import cn.hutool.core.collection.CollectionUtil;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.mix.api.push.MixEarlyWarnFeign;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientUserVO;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.patient.vo.patient.PatientQueryVO;
import com.ykl.med.push.api.EarlyWarnFeign;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.push.enums.EarlyWarnScope;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.push.vo.event.EventTaskCancelVO;
import com.ykl.med.push.vo.warn.*;
import com.ykl.med.records.api.BasicReportFeign;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.user.vo.UserAuthorityVO;
import com.ykl.med.web.hander.CheckDoctorPatientPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Tag(name = "预警")
@RestController
@RequestMapping("earlyWarn")
@Slf4j
@EnableCommonResponseAdvice
public class EarlyWarnController {

    @Resource
    private EarlyWarnFeign earlyWarnFeign;
    @Resource
    private PatientFeign patientFeign;
    @Resource
    private MixEarlyWarnFeign mixEarlyWarnFeign;
    @Resource
    private BasicReportFeign basicReportFeign;
    @Resource
    private PushSocketFeign pushSocketFeign;
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private EventTaskFeign eventTaskFeign;

    @PostMapping("/addEarlyWarn")
    @Operation(summary = "添加预警")
    @CheckDoctorPatientPermission
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "添加预警",module = "预警管理")
    public void addEarlyWarn(@RequestBody EarlyWarnReqVO reqVO) {
        UserAuthorityVO authorityContext = AuthContextHolder.getInstance().getContext();
        reqVO.setDoctorId(authorityContext.getId());
        Long earlyId = earlyWarnFeign.addEarlyWarn(reqVO);

        if (reqVO.getWarnScope() == EarlyWarnScope.PATIENT) {
            mixEarlyWarnFeign.sendEarlyWarnMessage(earlyId, reqVO.getDoctorId());
        } else {
            eventTaskFeign.addEventTask(new EventTaskAddVO()
                    .setUserId(reqVO.getDoctorId())
                    .setPatientId(reqVO.getPatientId())
                    .setRequestId(earlyId.toString())
                    .setEventTime(reqVO.getEventTime() == null ? LocalDateTime.now() : reqVO.getEventTime())
                    .setBizId(earlyId.toString())
                    .setBizType(EventTaskType.PENDING_WARN));
        }

        List<PatientUserVO> patientUsers = patientUserFeign.getFamilyByPatientId(reqVO.getPatientId());
        for (PatientUserVO patientUser : patientUsers) {
            pushSocketFeign.sendWarnMessage(patientUser.getUserId());
        }
    }

    @PostMapping("/send")
    @Operation(summary = "发送预警干预方案")
    @CheckDoctorPatientPermission
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "发送预警干预方案",module = "预警管理")
    public void send(@RequestBody EarlyWarnSendReqVO reqVO) {
        earlyWarnFeign.send(reqVO);
        mixEarlyWarnFeign.sendEarlyWarnMessage(reqVO.getEarlyWarnId(), reqVO.getCurrentUserId());
        if (reqVO.getFinish()) {
            eventTaskFeign.cancel(new EventTaskCancelVO()
                    .setBizId(reqVO.getEarlyWarnId().toString())
                    .setBizType(EventTaskType.PENDING_WARN));
        }
    }

    @PostMapping("/doctorEarlyWarnList")
    @Operation(summary = "医生预警列表")
    @CheckDoctorPatientPermission(needMember = false)
    public PageResult<EarlyWarnDoctorListVO> doctorEarlyWarnList(@RequestBody EarlyWarnDoctorQueryVO reqVO) {
        return earlyWarnFeign.doctorEarlyWarnList(reqVO);
    }

    @PostMapping("/getEarlyWarnWebById")
    @Operation(summary = "预警详情")
    public EarlyWarnDetailVO getEarlyWarnWebById(@RequestBody IdReqVO reqVO) {
        EarlyWarnDetailVO earlyWarnDetailVO = earlyWarnFeign.getEarlyWarnWebById(reqVO.getId());
        return earlyWarnDetailVO.setItems(mixEarlyWarnFeign.buildEarlyWarnItemDetailVO(earlyWarnDetailVO.getItems()));
    }

    @PostMapping("/doctorFinish")
    @Operation(summary = "医生完成预警")
    @CheckDoctorPatientPermission
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "医生完成预警",module = "预警管理")
    public void doctorFinish(@RequestBody EarlyWarnDoctorFinishReqVO reqVO) {
        earlyWarnFeign.doctorFinish(reqVO);
        reqVO.getEarlyWarnIds().forEach((Long earlyWarnId) -> eventTaskFeign.cancel(new EventTaskCancelVO()
                .setBizId(earlyWarnId.toString())
                .setBizType(EventTaskType.PENDING_WARN)));
    }

    @PostMapping("/getEarlyWarnDoctorMessagePage")
    @Operation(summary = "医生预警消息列表(预警待处理)")
    public PageResult<EarlyWarnDoctorMessageVO> getEarlyWarnDoctorMessagePage(@RequestBody EarlyWarnDoctorMessageQueryVO queryVO) {
        PageResult<EarlyWarnDoctorMessageVO> pageResult = earlyWarnFeign.getEarlyWarnDoctorMessagePage(queryVO);
        if (pageResult != null && CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<Long> patientIds = pageResult.getList().stream().map(EarlyWarnDoctorMessageVO::getPatientId).distinct().collect(Collectors.toList());
            List<PatientBaseVO> patientBaseVOS = patientFeign.queryPatient(new PatientQueryVO().setIds(patientIds));
            Map<Long, PatientBaseVO> patientBaseVOMap = patientBaseVOS.stream().collect(Collectors.toMap(PatientBaseVO::getPatientId, patientBaseVO -> patientBaseVO));
            pageResult.getList().forEach((EarlyWarnDoctorMessageVO earlyWarnDoctorMessageVO) -> BeanUtils.copyProperties(patientBaseVOMap.get(earlyWarnDoctorMessageVO.getPatientId()), earlyWarnDoctorMessageVO));
            List<BasicReportVO> basicReportVOS = basicReportFeign.listByPatientIds(patientIds);
            if (CollectionUtil.isNotEmpty(basicReportVOS)) {
                Map<Long, BasicReportVO> basicReportVOMap = basicReportVOS.stream().collect(Collectors.toMap(BasicReportVO::getPatientId, Function.identity()));
                for (EarlyWarnDoctorMessageVO earlyWarnDoctorMessageVO : pageResult.getList()){
                    BasicReportVO basicReportVO = basicReportVOMap.get(earlyWarnDoctorMessageVO.getPatientId());
                    if (Objects.nonNull(basicReportVO)) {
                        earlyWarnDoctorMessageVO.setDiseases(basicReportVO.getDiseaseName());
                        earlyWarnDoctorMessageVO.setStage(basicReportVO.getStage());
                    }
                }
            }
            return pageResult;
        } else {
            return pageResult;
        }
    }
}