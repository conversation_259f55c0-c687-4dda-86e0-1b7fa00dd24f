package com.ykl.med.web.controller.product;

import com.alibaba.fastjson.JSONArray;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.enums.ProductType;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.product.constants.ProductErrorCodeConstants;
import com.ykl.med.product.feign.ProductTreatmentProjectTypeFeign;
import com.ykl.med.product.feign.SkuFeign;
import com.ykl.med.product.vo.req.SkuQueryVO;
import com.ykl.med.product.vo.resp.PrescriptionDrugSkuDetailVO;
import com.ykl.med.product.vo.resp.SkuDetailVO;
import com.ykl.med.product.vo.treatment.ProductTreatmentProjectTypeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Tag(name = "商品SKU服务")
@RestController
@RequestMapping("/sku")
@Slf4j
@EnableCommonResponseAdvice
public class SkuController {
    @Resource
    private SkuFeign skuFeign;
    @Resource
    private ProductTreatmentProjectTypeFeign productTreatmentProjectTypeFeign;
    @Resource
    private CommonConfigFeign commonConfigFeign;

    @PostMapping("/queryPrescriptionDrugSkuDetail")
    @Operation(summary = "查询处方药sku详情")
    public PageResult<PrescriptionDrugSkuDetailVO> queryPrescriptionDrugSkuDetail(@RequestBody SkuQueryVO queryVO) {
        if (StringUtils.isNotEmpty(queryVO.getWords())) {
            String prescriptionDrugDisable = commonConfigFeign.getCommonConfigValueByKey("PRESCRIPTION_DRUG_DISABLE");
            JSONArray jsonArray = JSONArray.parseArray(prescriptionDrugDisable);
            for (Object o : jsonArray) {
                if (o.toString().equals(queryVO.getWords())) {
                    throw new ServiceException(ProductErrorCodeConstants.PRESCRIPTION_DRUG_DISABLE);
                }
            }
        }
        return skuFeign.queryPrescriptionDrugSkuDetail(queryVO);
    }

    @PostMapping("/queryTreatmentSkuDetail")
    @Operation(summary = "查询诊疗项目sku详情")
    public PageResult<SkuDetailVO> queryTreatmentSkuDetail(@RequestBody SkuQueryVO queryVO) {
        Set<String> medicalTeamIds = AuthContextHolder.getInstance().getContext().getMedicalTeamIds();
        List<Long> skuOutBizIds = medicalTeamIds.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        queryVO.setType(ProductType.TREATMENT_PROJECT);
        queryVO.setSkuOutBizIds(skuOutBizIds);
        return skuFeign.querySkuDetail(queryVO);
    }

    @PostMapping("/getAllProductTreatmentProjectType")
    @Operation(summary = "获取商品诊疗项目类别")
    public List<ProductTreatmentProjectTypeVO> getAllProductTreatmentProjectType() {
        return productTreatmentProjectTypeFeign.getAll();
    }
}
