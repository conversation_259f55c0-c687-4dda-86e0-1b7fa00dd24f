package com.ykl.med.web.controller.rehab;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.rehab.api.FuncRehabFeign;
import com.ykl.med.rehab.api.FuncRehabPlanItemExecuteFeign;
import com.ykl.med.rehab.api.FuncRehabSurveySnapshotFeign;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.func.FuncRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.FormAndSnapshotRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanExecuteRecordRespVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabSurveySnapshotVO;
import com.ykl.med.web.hander.CheckDoctorPatientPermission;
import com.ykl.med.web.vo.PatientIdVO;
import com.ykl.med.web.vo.rehab.FuncRehabExecRecordsRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 功能康复服务
 */
@RestController
@Tag(name = "功能康复服务")
@RequestMapping("/funcRehab")
@EnableCommonResponseAdvice
public class WebFuncRehabController {

    @Resource
    private FuncRehabFeign funcRehabFeign;

    @Resource
    private FuncRehabSurveySnapshotFeign funcRehabSurveySnapshotFeign;

    @Resource
    private FuncRehabPlanItemExecuteFeign funcRehabPlanItemExecuteFeign;

    @Resource
    private EventTaskFeign eventTaskFeign;

    @Resource
    private IdServiceImpl idService;

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/getCurrentPlan")
    @Operation(summary = "获取当前康复计划", description = "此方法是用来获取病人的当前康复计划")
    public FuncRehabPlanDetailRespVO getCurrentPlan(@Valid @RequestBody PatientIdVO patientIdVO) {
        return funcRehabFeign.getCurrentPlan(patientIdVO.getPatientId());
    }

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/getHistoryPlans")
    @Operation(summary = "获取历史康复计划", description = "此方法是用来获取病人的历史康复计划")
    public List<FuncRehabPlanDetailRespVO> getHistoryPlans(@Valid @RequestBody HistoryPlanReqVO reqVO) {
        return funcRehabFeign.getHistoryPlans(reqVO);
    }

    @CheckDoctorPatientPermission
    @PostMapping("/extendPlan")
    @Operation(summary = "延长康复计划", description = "此方法是用来延长病人的康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "延长康复计划", module = "功能康复")
    public void extendPlan(@Valid @RequestBody ExtendPlanReqVO reqVO) {
        funcRehabFeign.extendPlan(reqVO);
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString())
                .setExecuteTime(LocalDateTime.now())
                .setBizId(reqVO.getPlanId().toString())
                .setBizType(EventTaskType.FUNC_REHAB_PLAN_CHANGE);
        eventTaskAddVO.setUserId(AuthContextHolder.getInstance().getContext().getId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @CheckDoctorPatientPermission
    @PostMapping("/saveOrUpdatePlan")
    @Operation(summary = "保存或更新康复计划", description = "此方法是用来保存或更新病人的康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "保存或更新康复计划", module = "功能康复")
    public void saveOrUpdatePlan(@Valid @RequestBody FuncRehabPlanSaveOrUpdateReqVO reqVO) {
        saveOrUpdate(reqVO);
    }

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/surveySnapshot/list")
    @Operation(summary = "获取问卷快照列表", description = "此方法是用来获取病人的问卷快照列表")
    public List<FuncRehabSurveySnapshotVO> getSurveySnapshots(@Valid @RequestBody SurveySnapshotListReqVO reqVO) {
        return funcRehabSurveySnapshotFeign.list(reqVO);
    }

    @PostMapping("/surveySnapshot/getById")
    @Operation(summary = "获取问卷快照详情")
    public FuncRehabSurveySnapshotVO getSurveySnapshotById(@RequestBody IdReqVO idReqVO) {
        return funcRehabSurveySnapshotFeign.details(idReqVO.getId());
    }

//    @PostMapping("/surveySnapshot/complete")
//    @Operation(summary = "填写问卷快照", description = "此方法是用来填写病人的问卷快照")
//    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "填写问卷快照", module = "功能康复")
//    public void completeSurveySnapshots(@Valid @RequestBody RehabSurveySnapshotCompleteReqVO reqVO) {
//        funcRehabSurveySnapshotFeign.complete(reqVO);
//
//
//    }

    @CheckDoctorPatientPermission
    @PostMapping("/surveySnapshot/saveOrUpdate")
    @Operation(summary = "保存或更新问卷快照", description = "此方法是用来保存或更新病人的问卷快照")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "保存或更新问卷快照", module = "功能康复")
    public void saveOrUpdateSurveySnapshots(@Valid @RequestBody SurveySnapshotSaveOrUpdateReqVO reqVO) {
        if (reqVO.getId() == null) {
            Long id = idService.nextId();
            reqVO.setId(id);
        }
        funcRehabSurveySnapshotFeign.saveOrUpdate(reqVO);

        FuncRehabPlanSaveOrUpdateReqVO basePlanReq = funcRehabFeign.getBasePlanReq(reqVO.getPatientId());
        if (basePlanReq != null) {
            this.saveOrUpdate(basePlanReq);
        }

        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setRequestId(reqVO.getId().toString())
                .setBizType(EventTaskType.PATIENT_FINISH_FUNCTIONAL_FORM)
                .setUserId(AuthContextHolder.getInstance().getContext().getId())
                .setPatientId(reqVO.getPatientId())
                .setBizId(reqVO.getId().toString());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @CheckDoctorPatientPermission
    @PostMapping("/surveySnapshot/batchComplete")
    @Operation(summary = "批量填写问卷快照", description = "此方法是用来批量填写病人的问卷快照")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "批量填写问卷快照", module = "功能康复")
    public void batchCompleteSurveySnapshots(@Valid @RequestBody RehabSurveySnapshotsBatchSaveReqVO reqVO) {
        funcRehabSurveySnapshotFeign.batchComplete(reqVO);
    }

    @CheckDoctorPatientPermission
    @PostMapping("/surveySnapshot/send")
    @Operation(summary = "发送问卷快照", description = "此方法是用来发送病人的问卷快照")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "发送问卷快照", module = "功能康复")
    public void sendSurveySnapshots(@Valid @RequestBody RehabSurveySnapshotSendReqVO reqVO) {
        funcRehabSurveySnapshotFeign.send(reqVO);
    }

    @PostMapping("/planItemExecute/weeklyStat")
    @Operation(summary = "获取康复计划执行记录周统计", description = "此方法是用来获取病人的康复计划执行记录周统计")
    @CheckDoctorPatientPermission(needMember = false)
    public RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(@RequestBody ItemExecStatReqVO reqVO) {
        return funcRehabPlanItemExecuteFeign.getWeeklyStatistics(reqVO);
    }

    @PostMapping("/planItemExecute/queryExecuteRecordsByTimeRange")
    @Operation(summary = "获取康复计划执行记录", description = "此方法是用来获取病人的康复计划执行记录")
    @CheckDoctorPatientPermission(needMember = false)
    public List<FuncRehabExecRecordsRespVO> queryExecuteRecords(
            @Valid @RequestBody RehabPlanExecuteTimeRangeQueryReqVO reqVO) {
        Map<String, List<FuncRehabPlanExecuteRecordRespVO>> map = funcRehabPlanItemExecuteFeign
                .queryExecuteRecordsByTimeRange(reqVO);
        return map.entrySet().stream()
                .map(entry -> new FuncRehabExecRecordsRespVO()
                        .setDate(entry.getKey())
                        .setRecords(entry.getValue()))
                .collect(Collectors.toList());
    }

    @PostMapping("/planItemExecute/update")
    @Operation(summary = "更新康复计划执行记录", description = "此方法是用来更新病人的康复计划执行记录")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "更新康复计划执行记录", module = "功能康复")
    public void updateExecuteRecord(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        funcRehabPlanItemExecuteFeign.updateExecuteRecord(reqVO);
    }

    @PostMapping("/planItemExecute/execute")
    @Operation(summary = "执行康复计划", description = "此方法是用来执行病人的康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "执行康复计划", module = "功能康复")
    public void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        funcRehabFeign.execute(reqVO);
    }

    @PostMapping("/plan/reEvaluate")
    @Operation(summary = "重新评估康复计划", description = "此方法是用来重新评估病人的康复计划")
    @CheckDoctorPatientPermission
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "重新评估康复计划", module = "功能康复")
    public void reEvaluate(@Valid @RequestBody PatientIdVO patientIdVO) {
        funcRehabFeign.reEvaluate(patientIdVO.getPatientId());
    }

    @PostMapping("/plan/getDraftPlan")
    @Operation(summary = "查看康复方案草稿", description = "此方法是用来查看病人的康复方案草稿")
    @CheckDoctorPatientPermission(needMember = false)
    public FuncRehabPlanDetailRespVO getDraftPlan(@Valid @RequestBody PatientIdVO patientIdVO) {
        return funcRehabFeign.getDraftPlan(patientIdVO.getPatientId());
    }

    @PostMapping("/plan/getFormAndSnapshots")
    @Operation(summary = "获取当前评估问卷及评估状态", description = "此方法是用来获取当前评估问卷及评估状态")
    @CheckDoctorPatientPermission(needMember = false)
    public List<FormAndSnapshotRespVO> getFormAndSnapshots(@Valid @RequestBody FormAndSnapshotReqVO reqVO) {
        return funcRehabFeign.getFormAndSnapshots(reqVO);
    }

    @PostMapping("/plan/getPlanById")
    @Operation(summary = "获取方案详情", description = "此方法是用来获取方案详情")
    @CheckDoctorPatientPermission(needMember = false)
    public FuncRehabPlanDetailRespVO getPlanById(@Valid @RequestBody PatientIdAndPlanIdVO reqVO) {
        return funcRehabFeign.getPlanById(reqVO);
    }


    public void saveOrUpdate(FuncRehabPlanSaveOrUpdateReqVO reqVO) {
        Long id = funcRehabFeign.saveOrUpdate(reqVO);

        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString())
                .setEventTime(LocalDateTime.now())
                .setExecuteTime(LocalDateTime.now())
                .setBizId(id.toString())
                .setBizType(EventTaskType.FUNC_REHAB_PLAN_CHANGE);
        eventTaskAddVO.setUserId(AuthContextHolder.getInstance().getContext().getId());
        eventTaskFeign.addEventTask(eventTaskAddVO);


    }
}
