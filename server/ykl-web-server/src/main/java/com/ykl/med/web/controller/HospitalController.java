package com.ykl.med.web.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.doctors.api.HospitalFeign;
import com.ykl.med.doctors.entity.dto.HospitalQueryDTO;
import com.ykl.med.doctors.entity.vo.HospitalVO;
import com.ykl.med.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "医院目录服务")
@RestController
@RequestMapping("/hospital")
@Validated
@EnableCommonResponseAdvice
public class HospitalController {

    @Resource
    private HospitalFeign apiFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<HospitalVO> query(@RequestBody HospitalQueryDTO requestBody){
        requestBody.setStatus("ENABLE");
        return apiFeign.crud("GET", requestBody);
    }
}
