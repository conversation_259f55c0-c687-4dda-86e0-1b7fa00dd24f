package com.ykl.med.web.hander;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 验证医生有没有权限操作患者
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CheckDoctorPatientPermission {
    /**
     * 患者id的参数名
     */
    String name() default "patientId";

    /**
     * 目前支持，long,string,collection
     */
    Class<?> type() default Long.class;

    /**
     * 是否自定义获取患者Id的方法
     */
    boolean customizeMethod() default false;

    /**
     * 目前支持返回参数为，long,string,collection
     */
    String methodName() default "getPatientIds";

    /**
     * 是否需要会员
     */
    boolean needMember() default true;
}
