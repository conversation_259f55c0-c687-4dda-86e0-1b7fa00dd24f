package com.ykl.med.web.controller.symptom;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.medical.api.MedicalOrderFeign;
import com.ykl.med.medical.enums.MedicalOrderStatus;
import com.ykl.med.medical.vo.order.MedicalOrderListAppVO;
import com.ykl.med.medical.vo.order.MedicalOrderQueryAppVO;
import com.ykl.med.mix.api.symtom.MixIntelligentReportFeign;
import com.ykl.med.patient.api.PatientDoctorFeign;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.PatientUserDetailVO;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.patient.vo.patient.PatientDoctorQueryVO;
import com.ykl.med.patient.vo.patient.PatientQueryVO;
import com.ykl.med.records.api.BasicReportFeign;
import com.ykl.med.records.api.ReportFeign;
import com.ykl.med.records.enums.RecognitionStatus;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.records.vo.req.ReportListReqVO;
import com.ykl.med.records.vo.resp.ReportListRespVO;
import com.ykl.med.rehab.api.NutritionalExecuteFeign;
import com.ykl.med.rehab.api.NutritionalFeign;
import com.ykl.med.rehab.vo.req.nutritional.QueryExecDayReportReqVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalExecDayReportVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPlanVO;
import com.ykl.med.symptoms.api.report.IntelligentReportFeign;
import com.ykl.med.symptoms.enums.IntelligentReportStatus;
import com.ykl.med.symptoms.enums.IntelligentReportType;
import com.ykl.med.symptoms.vo.report.*;
import com.ykl.med.web.service.DoctorRoleService;
import com.ykl.med.web.vo.WaitCheckReportVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Tag(name = "智能报告服务")
@RestController
@RequestMapping("/intelligentReport")
@Slf4j
@EnableCommonResponseAdvice
public class IntelligentReportController {
    @Resource
    private IntelligentReportFeign intelligentReportFeign;
    @Resource
    private MixIntelligentReportFeign mixIntelligentReportFeign;
    @Resource
    private PatientDoctorFeign patientDoctorFeign;
    @Resource
    private ReportFeign reportFeign;
    @Resource
    private PatientFeign patientFeign;
    @Resource
    private BasicReportFeign basicReportFeign;
    @Resource
    private MedicalOrderFeign medicalOrderFeign;
    @Resource
    private NutritionalFeign nutritionalFeign;
    @Resource
    private NutritionalExecuteFeign nutritionalExecuteFeign;
    @Resource
    private DoctorRoleService doctorRoleService;

    @Operation(summary = "添加报告")
    @PostMapping("/add")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "添加症状报告", module = "症状管理")
    public void add(@RequestBody IntelligentReportAddVO addVO) {
        if (addVO.getType() == IntelligentReportType.MEDICATION_INSTRUCTIONS) {
            MedicalOrderQueryAppVO queryVO = new MedicalOrderQueryAppVO().setPatientId(addVO.getPatientId()).setStatus(MedicalOrderStatus.ENABLE);
            queryVO.setPageSize(999);
            PageResult<MedicalOrderListAppVO> medicalOrderList = medicalOrderFeign.queryApp(queryVO);
            if (medicalOrderList == null || CollectionUtils.isEmpty(medicalOrderList.getList())) {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "该患者暂无用药信息");
            }
        } else if (addVO.getType() == IntelligentReportType.DIETARY_ANALYSIS_REPORT) {
            QueryExecDayReportReqVO execDayReportReqVO = new QueryExecDayReportReqVO();
            execDayReportReqVO.setPatientId(addVO.getPatientId());
            execDayReportReqVO.setPageNo(1);
            execDayReportReqVO.setPageSize(999);
            execDayReportReqVO.setStartTime(addVO.getStartDate().atStartOfDay());
            execDayReportReqVO.setEndTime(addVO.getEndDate().atTime(23, 59, 59));
            PageResult<NutritionalExecDayReportVO> pageResult = nutritionalExecuteFeign.queryExecDayPage(execDayReportReqVO);
            if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "该患者暂无打卡记录");
            }
        } else if (addVO.getType() == IntelligentReportType.NUTRITIONAL_REPORT) {
            NutritionalPlanVO nutritionalPlanVO = nutritionalFeign.current(addVO.getPatientId());
            if (nutritionalPlanVO == null) {
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "该患者暂无方案");
            }
        }
        intelligentReportFeign.add(addVO);
    }


    @Operation(summary = "更改报告、提交报告")
    @PostMapping("/update")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "更改症状报告、提交症状报告", module = "症状管理")
    public void update(@RequestBody IntelligentReportSubmitReqVO reqVO) {
        intelligentReportFeign.update(reqVO);
    }

    @Operation(summary = "查询分页")
    @PostMapping("/getPage")
    public PageResult<IntelligentReportListVO> getPage(@RequestBody IntelligentReportQueryVO reqVO) {
        return intelligentReportFeign.getPage(reqVO);
    }

    @Operation(summary = "查询报告详情")
    @PostMapping("/getDetailById")
    public IntelligentReportDetailVO getDetailById(@RequestBody IdReqVO reqVO) {
        return intelligentReportFeign.getDetailById(reqVO.getId());
    }

    @Operation(summary = "症状报告重新生成综述")
    @PostMapping("/aiSymptomReportSummary")
    public String aiSymptomReportSummary(@RequestBody IntelligentReportSummaryReqVO vo) {
        return mixIntelligentReportFeign.aiSymptomReportSummary(vo);
    }

    @PostMapping("/aiNutritionOnlySummary")
    @Operation(summary = "营养报告重新生成综述")
    public String aiNutritionOnlySummary(@RequestBody IntelligentReportSummaryReqVO reqVO) {
        return mixIntelligentReportFeign.aiNutritionOnlySummary(reqVO);
    }

    @PostMapping("/aiNutritionIntakeAssessmentOnlySummary")
    @Operation(summary = "饮食分析重新生成综述")
    public String aiNutritionIntakeAssessmentOnlySummary(@RequestBody IdReqVO idReqVO) {
        return mixIntelligentReportFeign.aiNutritionIntakeAssessmentOnlySummary(idReqVO.getId());
    }

    @PostMapping("/getNutritionExecDayByReport")
    @Operation(summary = "查询营养方案饮食打卡")
    public PageResult<NutritionalExecDayReportVO> queryExecDayList(@RequestBody IdReqVO reqVO) {
        IntelligentReportVO vo = intelligentReportFeign.getById(reqVO.getId());
        QueryExecDayReportReqVO execDayReportReqVO = new QueryExecDayReportReqVO();
        execDayReportReqVO.setPatientId(vo.getPatientId());
        execDayReportReqVO.setPageNo(1);
        execDayReportReqVO.setPageSize(999);
        execDayReportReqVO.setStartTime(vo.getStartDate().atStartOfDay());
        execDayReportReqVO.setEndTime(vo.getEndDate().atTime(23, 59, 59));
        return nutritionalExecuteFeign.queryExecDayPage(execDayReportReqVO);
    }

    @Operation(summary = "查询待审核报告")
    @PostMapping("/getWaitCheckReport")
    public List<WaitCheckReportVO> getWaitCheckReport() {
        PatientDoctorQueryVO patientDoctorQueryVO = new PatientDoctorQueryVO();
        patientDoctorQueryVO.setCurrentUserId(AuthContextHolder.getInstance().getContext().getId());
        patientDoctorQueryVO.setPageSize(9999);
        PageResult<PatientUserDetailVO> pageResult = patientDoctorFeign.getPatientUserByDoctor(patientDoctorQueryVO);
        List<PatientUserDetailVO> list = pageResult.getList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<Long> patientIds = list.stream().map(PatientUserDetailVO::getPatientId).collect(Collectors.toList());

        IntelligentReportQueryVO intelligentReportQueryVO = new IntelligentReportQueryVO();
        intelligentReportQueryVO.setStatusList(Lists.newArrayList(IntelligentReportStatus.REPORT_GENERATED, IntelligentReportStatus.DIGITAL_DOCTOR_GENERATED));
        if (doctorRoleService.isNutritionalDoctor()) {
            intelligentReportQueryVO.setTypes(Lists.newArrayList(IntelligentReportType.NUTRITIONAL_REPORT,
                    IntelligentReportType.DIETARY_ANALYSIS_REPORT));
        } else {
            //剔除营养报告
            intelligentReportQueryVO.setTypes(Lists.newArrayList(IntelligentReportType.SYMPTOM_REPORT,
                    IntelligentReportType.INTERPRETATION_OF_IMAGE_REPORT,
                    IntelligentReportType.INTERPRETATION_OF_INSPECTION_REPORT,
                    IntelligentReportType.MEDICATION_INSTRUCTIONS
            ));
            intelligentReportQueryVO.setPatientIds(patientIds);
        }
        List<IntelligentReportVO> intelligentReportVOS = intelligentReportFeign.queryList(intelligentReportQueryVO);

        ReportListReqVO reportListReqVO = new ReportListReqVO();
        reportListReqVO.setPatientIds(patientIds);
        reportListReqVO.setRecognitionStatus(RecognitionStatus.WAITING_CONFIRM);
        List<ReportListRespVO> reportListRespVOS = reportFeign.list(reportListReqVO);

        List<WaitCheckReportVO> waitCheckReportVOS = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(intelligentReportVOS)) {
            for (IntelligentReportVO intelligentReportVO : intelligentReportVOS) {
                WaitCheckReportVO waitCheckReportVO = new WaitCheckReportVO();
                waitCheckReportVO.setReportType(WaitCheckReportVO.WaitCheckReportType.INTELLIGENT_REPORT);
                waitCheckReportVO.setId(intelligentReportVO.getId());
                waitCheckReportVO.setReportName(intelligentReportVO.getName());
                waitCheckReportVO.setStatus(intelligentReportVO.getStatus().name());
                waitCheckReportVO.setSubmitTime(intelligentReportVO.getCreateTime());
                waitCheckReportVO.setPatientId(intelligentReportVO.getPatientId());
                waitCheckReportVOS.add(waitCheckReportVO);
            }
        }

        if (!CollectionUtils.isEmpty(reportListRespVOS)) {
            for (ReportListRespVO reportListRespVO : reportListRespVOS) {
                WaitCheckReportVO waitCheckReportVO = new WaitCheckReportVO();
                waitCheckReportVO.setReportType(WaitCheckReportVO.WaitCheckReportType.RECORD);
                waitCheckReportVO.setId(reportListRespVO.getId());
                waitCheckReportVO.setReportName(reportListRespVO.getSubCategoryName());
                waitCheckReportVO.setStatus(reportListRespVO.getRecognitionStatus().name());
                waitCheckReportVO.setSubmitTime(reportListRespVO.getUploadTime());
                waitCheckReportVO.setPatientId(reportListRespVO.getPatientId());
                waitCheckReportVOS.add(waitCheckReportVO);
            }
        }
        List<Long> patientIdReports = waitCheckReportVOS.stream().map(WaitCheckReportVO::getPatientId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(patientIdReports)) {
            return waitCheckReportVOS;
        }
        PatientQueryVO patientQueryVO = new PatientQueryVO();
        patientQueryVO.setIds(patientIdReports);
        List<PatientBaseVO> patientBaseVOList = patientFeign.queryPatient(patientQueryVO);
        Map<Long, PatientBaseVO> patientBaseVOMap = patientBaseVOList.stream().collect(Collectors.toMap(PatientBaseVO::getPatientId, Function.identity(), (key1, key2) -> key2));
        List<BasicReportVO> basicReportVOS = basicReportFeign.listByPatientIds(patientIds);
        Map<Long, BasicReportVO> basicReportVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(basicReportVOS)) {
            basicReportVOMap = basicReportVOS.stream().collect(Collectors.toMap(BasicReportVO::getPatientId, Function.identity()));
        }
        for (WaitCheckReportVO waitCheckReportVO : waitCheckReportVOS) {
            PatientBaseVO patientBaseVO = patientBaseVOMap.get(waitCheckReportVO.getPatientId());
            if (Objects.nonNull(patientBaseVO)) {
                waitCheckReportVO.setName(patientBaseVO.getName());
                waitCheckReportVO.setSex(patientBaseVO.getSex());
                waitCheckReportVO.setAge(patientBaseVO.getAge());
                waitCheckReportVO.setContactPhone(patientBaseVO.getContactPhone());
            }
            if (basicReportVOMap.containsKey(waitCheckReportVO.getPatientId())) {
                waitCheckReportVO.setDiseases(basicReportVOMap.get(waitCheckReportVO.getPatientId()).getDiseaseName());
                waitCheckReportVO.setStage(basicReportVOMap.get(waitCheckReportVO.getPatientId()).getStage());
            }
        }
        return waitCheckReportVOS.stream().sorted(Comparator.comparing(WaitCheckReportVO::getSubmitTime).reversed()).collect(Collectors.toList());
    }
}
