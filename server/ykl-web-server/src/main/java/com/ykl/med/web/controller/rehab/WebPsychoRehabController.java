package com.ykl.med.web.controller.rehab;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.vo.form.PatientFormQueryVO;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.mix.api.rehab.MixPsychoRehabFeign;
import com.ykl.med.mix.vo.rehab.psycho.MixPsychoRehabPlanDetailRespVO;
import com.ykl.med.mix.vo.rehab.psycho.MixPsychoRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.records.api.BasicReportFeign;
import com.ykl.med.records.vo.BasicReportVO;
import com.ykl.med.rehab.api.PsychoRehabFeign;
import com.ykl.med.rehab.api.PsychoRehabOperateLogFeign;
import com.ykl.med.rehab.api.PsychoRehabPlanItemExecuteFeign;
import com.ykl.med.rehab.api.PsychologyFormFeign;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.psycho.*;
import com.ykl.med.shift.api.ConsultFeign;
import com.ykl.med.framework.common.enums.ConsultChatTypeEnum;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.vo.req.CreateByDirectlyReqVO;
import com.ykl.med.shift.vo.req.UpdateConsultVO;
import com.ykl.med.shift.vo.resp.ConsultVO;
import com.ykl.med.web.hander.CheckDoctorPatientPermission;
import com.ykl.med.web.vo.PatientIdVO;
import com.ykl.med.web.vo.rehab.PsychoRehabExecRecordsRespVO;
import com.ykl.med.web.vo.rehab.WebPsychologyFormResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 心理康复服务
 */
@RestController
@Tag(name = "心理康复服务")
@RequestMapping("/psychoRehab")
@EnableCommonResponseAdvice
public class WebPsychoRehabController {

    @Resource
    private PsychoRehabFeign psychoRehabFeign;

    @Resource
    private PsychoRehabPlanItemExecuteFeign psychoRehabPlanItemExecuteFeign;

    @Resource
    private EventTaskFeign eventTaskFeign;

    @Resource
    private PatientFormFeign patientFormFeign;

    @Resource
    private ConsultFeign consultFeign;

    @Resource
    private BasicReportFeign basicReportFeign;

    @Resource
    private PsychologyFormFeign psychologyFormFeign;

    @Resource
    private MixPsychoRehabFeign mixPsychoRehabFeign;

    @Resource
    private PsychoRehabOperateLogFeign psychoRehabOperateLogFeign;

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/getCurrentPlan")
    @Operation(summary = "获取当前康复计划", description = "此方法是用来获取病人的当前康复计划")
    public MixPsychoRehabPlanDetailRespVO getCurrentPlan(@Valid @RequestBody PatientIdVO patientIdVO) {
        return mixPsychoRehabFeign.getCurrentPlan(patientIdVO.getPatientId());
    }

    @PostMapping("/getRecommend")
    @Operation(summary = "获取当前心理康复推荐", description = "此方法是用根据填的量表来推东西")
    public PsychoRehabRecommendVO getRecommend(@RequestBody PatientIdVO patientIdVO) {
        BasicReportVO basicReportVO = basicReportFeign.getByPatientId(patientIdVO.getPatientId());
        String stage = basicReportVO == null ? "" : basicReportVO.getStage();
        return psychologyFormFeign.getRecommend(patientIdVO.getPatientId(), stage);
    }

    @PostMapping("/getFormResult")
    @Operation(summary = "获取评估结果", description = "此方法是用来获取评估结果")
    public WebPsychologyFormResultVO getFormResult(@RequestBody PatientIdVO patientIdVO) {
        BasicReportVO basicReportVO = basicReportFeign.getByPatientId(patientIdVO.getPatientId());
        String stage = basicReportVO == null ? "" : basicReportVO.getStage();
        PsychologyFormResultVO result = psychologyFormFeign.getFormResult(patientIdVO.getPatientId(), stage);
        WebPsychologyFormResultVO webResult = CopyPropertiesUtil.normalCopyProperties(result, WebPsychologyFormResultVO.class);
        List<ConsultVO> consults = consultFeign.listBizId(result.getPlanId());
        if (CollectionUtils.isNotEmpty(consults)) {
            webResult.setConsult(consults.get(0));
            if (webResult.getFlowStatus() == PsychologyFormResultVO.PsychologyFlowStatus.NEED_CONSULTATION) {
                if (webResult.getConsult().getConsultState() == ConsultStateEnum.STARTING
                        || webResult.getConsult().getConsultState() == ConsultStateEnum.WAIT) {
                    webResult.setFlowStatus(PsychologyFormResultVO.PsychologyFlowStatus.CONSULTATION);
                } else {
                    webResult.setFlowStatus(PsychologyFormResultVO.PsychologyFlowStatus.COMPLETED);
                }
            }
            List<PatientFormVO> patientForms = patientFormFeign.query(new PatientFormQueryVO()
                    .setBizId(webResult.getConsult().getId())
                    .setType(PatientFormBizType.CONSULT));
            webResult.setConsultForms(patientForms);
        }

        PsychoRehabPlanDetailRespVO currentPlan = psychoRehabFeign.getCurrentPlan(patientIdVO.getPatientId());
        if (currentPlan != null) {
            if (currentPlan.getStatus() == CommonStatusEnum.ENABLE) {
                webResult.setFlowStatus(PsychologyFormResultVO.PsychologyFlowStatus.PLAN_GENERATED);
            } else if (currentPlan.getStatus() == CommonStatusEnum.DISABLE) {
                webResult.setFlowStatus(PsychologyFormResultVO.PsychologyFlowStatus.DRAFT);
            }
        }
        return webResult;
    }

    @CheckDoctorPatientPermission(needMember = false)
    @PostMapping("/getHistoryPlans")
    @Operation(summary = "获取历史康复计划", description = "此方法是用来获取病人的历史康复计划")
    public List<PsychoRehabPlanVO> getHistoryPlans(@Valid @RequestBody HistoryPlanReqVO reqVO) {
        return psychoRehabFeign.getHistoryPlans(reqVO);
    }

    @CheckDoctorPatientPermission()
    @PostMapping("/extendPlan")
    @Operation(summary = "延长康复计划", description = "此方法是用来延长病人的康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "保存或更新康复计划", module = "心理康复")
    public void extendPlan(@Valid @RequestBody ExtendPlanReqVO reqVO) {
        MixPsychoRehabPlanDetailRespVO oldPlan = mixPsychoRehabFeign.getPlanById(new PatientIdAndPlanIdVO().setPlanId(reqVO.getPlanId()).setPatientId(reqVO.getPatientId()));

        psychoRehabFeign.extendPlan(reqVO);

        // 添加事件任务
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString())
                .setEventTime(LocalDateTime.now())
                .setExecuteTime(LocalDateTime.now())
                .setBizId(reqVO.getPlanId().toString())
                .setBizType(EventTaskType.PSYCHO_REHAB_PLAN_CHANGE);
        JSONObject extJson = new JSONObject();
        extJson.put("oldPlan", oldPlan);
        eventTaskAddVO.setExtJson(extJson);
        eventTaskAddVO.setUserId(AuthContextHolder.getInstance().getContext().getId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @PostMapping("/addFirstConsult")
    @Operation(summary = "添加第一次的预约问诊", description = "此方法是用来添加初诊")
    public void addFirstConsult(@RequestBody CreateByDirectlyReqVO param) {
        param.setDoctorId(AuthContextHolder.getInstance().getContext().getId());
        param.setChatType(ConsultChatTypeEnum.VIDEO_CHAT);
        param.setConsultState(ConsultStateEnum.WAIT);
        mixPsychoRehabFeign.addFirstConsult(param);
    }


    @PostMapping("/updateConsult")
    @Operation(summary = "修改问诊或者终止问诊")
    public void updateConsult(@RequestBody UpdateConsultVO updateConsultVO) {
        mixPsychoRehabFeign.updateConsult(updateConsultVO, AuthContextHolder.getInstance().getContext().getId());
    }

    @PostMapping("/saveOrUpdatePlan")
    @Operation(summary = "保存或更新康复计划", description = "此方法是用来保存或更新病人的康复计划")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "保存或更新康复计划", module = "心理康复")
    public void saveOrUpdatePlan(@Valid @RequestBody MixPsychoRehabPlanSaveOrUpdateReqVO reqVO) {
        mixPsychoRehabFeign.saveOrUpdate(reqVO);
    }


    @PostMapping("/planItemExecute/weeklyStat")
    @Operation(summary = "获取康复计划执行记录周统计", description = "此方法是用来获取病人的康复计划执行记录周统计")
    @CheckDoctorPatientPermission(needMember = false)
    public RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(@RequestBody ItemExecStatReqVO reqVO) {
        return psychoRehabPlanItemExecuteFeign.getWeeklyStatistics(reqVO);
    }

    @PostMapping("/planItemExecute/queryExecuteRecordsByTimeRange")
    @Operation(summary = "获取康复计划执行记录", description = "此方法是用来获取病人的康复计划执行记录")
    @CheckDoctorPatientPermission(needMember = false)
    public List<PsychoRehabExecRecordsRespVO> queryExecuteRecords(
            @Valid @RequestBody RehabPlanExecuteTimeRangeQueryReqVO reqVO) {
        Map<String, List<PsychoRehabPlanExecuteRecordRespVO>> map = psychoRehabPlanItemExecuteFeign
                .queryExecuteRecordsByTimeRange(reqVO);
        return map.entrySet().stream()
                .map(entry -> new PsychoRehabExecRecordsRespVO()
                        .setDate(entry.getKey())
                        .setRecords(entry.getValue()))
                .collect(Collectors.toList());
    }

    @PostMapping("/planItemExecute/update")
    @Operation(summary = "更新康复计划执行记录", description = "此方法是用来更新病人的康复计划执行记录")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "更新康复计划执行记录", module = "心理康复")
    public void updateExecuteRecord(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        psychoRehabPlanItemExecuteFeign.updateExecuteRecord(reqVO);
    }

    @PostMapping("/planItemExecute/execute")
    @Operation(summary = "执行康复计划", description = "此方法是用来执行病人的康复计划")
    @CheckDoctorPatientPermission()
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "执行康复计划", module = "心理康复")
    public void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        psychoRehabFeign.execute(reqVO);
    }


    @PostMapping("/plan/getPlanById")
    @Operation(summary = "获取方案详情", description = "此方法是用来获取方案详情")
    @CheckDoctorPatientPermission(needMember = false)
    public MixPsychoRehabPlanDetailRespVO getPlanById(@Valid @RequestBody PatientIdAndPlanIdVO reqVO) {
        return mixPsychoRehabFeign.getPlanById(reqVO);
    }

    @PostMapping("/plan/getLastConsultForm")
    @Operation(summary = "获取上一个已经填写的咨询记录表")
    public PatientFormVO getLastConsultForm(@RequestBody PatientIdAndPlanIdVO reqVO) {
        return mixPsychoRehabFeign.getLastConsultForm(reqVO);
    }


    @Operation(summary = "分页查询心理操作日志")
    @PostMapping("/plan/operateLogPage")
    public PageResult<PsychoRehabOperateLogVO> page(@RequestBody PsychoRehabOperateLogQueryVO queryVO) {
        return psychoRehabOperateLogFeign.page(queryVO);
    }
}