package com.ykl.med.web.controller.stat;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.mix.api.stat.MixPatientStatFeign;
import com.ykl.med.mix.vo.stat.MixPatientStatVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Tag(name = "患者统计服务")
@RestController
@RequestMapping("/patientStat")
@Slf4j
@EnableCommonResponseAdvice
public class WebPatientStatController {
    @Resource
    private MixPatientStatFeign mixPatientStatFeign;

    @PostMapping("/getStatByPatientId")
    @Operation(summary = "获取患者自我管理统计")
    public MixPatientStatVO getStatByPatientId(@RequestBody IdReqVO idReqVO) {
        return mixPatientStatFeign.getStatByPatientId(idReqVO.getId());
    }
}
