package com.ykl.med.web.controller.masterdata;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.vo.form.*;
import com.ykl.med.mix.api.MixPatientFormFeign;
import com.ykl.med.push.constans.MessageConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Tag(name = "患者量表")
@RestController
@RequestMapping("/patientForm")
@Slf4j
@EnableCommonResponseAdvice
public class PatientFormController {
    @Resource
    private PatientFormFeign patientFormFeign;
    @Resource
    private MixPatientFormFeign mixPatientFormFeign;

    @PostMapping("/write")
    @Operation(summary = "填写量表")
    public void write(@RequestBody PatientFormWriteReqVO reqVO) {
        mixPatientFormFeign.write(reqVO);
    }

    @PostMapping("/aiNutritionQuestionnaireSummary")
    @Operation(summary = "营养问卷总结")
    public String aiNutritionQuestionnaireSummary(@RequestBody PatientFormWriteReqVO reqVO){
        return mixPatientFormFeign.aiNutritionQuestionnaireSummary(reqVO);
    }

    @PostMapping("/send")
    @Operation(summary = "发送量表")
    public void send(@RequestBody IdReqVO idReqVO) {
        //目前量表都从系统发
        mixPatientFormFeign.send(idReqVO.getId(), MessageConstants.SYSTEM_USER);
    }

    @PostMapping("/draft")
    @Operation(summary = "保存量表填写草稿")
    public void draft(@RequestBody PatientFormWriteReqVO reqVO) {
        patientFormFeign.draft(reqVO);
    }

    @PostMapping("/query")
    @Operation(summary = "查询量表")
    public List<PatientFormVO> query(@RequestBody PatientFormQueryVO queryVO) {
        return patientFormFeign.query(queryVO);
    }

    @PostMapping("/getById")
    @Operation(summary = "查询量表详情")
    public PatientFormDetailVO getById(@RequestBody IdReqVO reqVO) {
        return patientFormFeign.getDetailById(reqVO.getId());
    }

    @PostMapping("/delete")
    @Operation(summary = "删除量表")
    public void delete(@RequestBody PatientFormDeleteReqVO reqVO) {
        patientFormFeign.delete(reqVO);
    }

}
