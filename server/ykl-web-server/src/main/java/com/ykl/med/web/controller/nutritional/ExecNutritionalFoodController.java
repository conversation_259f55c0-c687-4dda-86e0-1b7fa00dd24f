//package com.ykl.med.web.controller.nutritional;
//
//import com.ykl.med.base.handler.EnableCommonResponseAdvice;
//import com.ykl.med.constants.LogConstants;
//import com.ykl.med.framework.common.exception.ServiceException;
//import com.ykl.med.framework.common.pojo.PageResult;
//import com.ykl.med.framework.common.util.date.DateUtils;
//import com.ykl.med.interfaces.OperateLog;
//import com.ykl.med.nutritional.api.ExecNutritionalFoodFeign;
//import com.ykl.med.nutritional.api.NutritionalFeign;
//import com.ykl.med.nutritional.entity.dto.ExecNutritionalFoodAddDTO;
//import com.ykl.med.nutritional.entity.dto.ExecNutritionalFoodQueryDTO;
//import com.ykl.med.nutritional.entity.dto.NutritionalQueryDTO;
//import com.ykl.med.nutritional.entity.vo.ExecNutritionalFoodLogVO;
//import com.ykl.med.nutritional.entity.vo.ExecNutritionalFoodVO;
//import com.ykl.med.nutritional.entity.vo.NutritionalVO;
//import com.ykl.med.web.hander.CheckDoctorPatientPermission;
//import com.ykl.med.web.hander.ResponseCode;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Tag(name = "营养方案食物日志服务")
//@RestController
//@RequestMapping("/execNutritionalFood")
//@Validated
//@EnableCommonResponseAdvice
//public class ExecNutritionalFoodController {
//
//    @Resource
//    private ExecNutritionalFoodFeign apiFeign;
//
//    @Resource
//    private NutritionalFeign nutritionalFeign;
//
//    @PostMapping("/query")
//    @Operation(summary = "查询列表", description = "返回执行计划")
//    @CheckDoctorPatientPermission(needMember = false) // 患者与医生操作权限判断,默认捕获patientId参数
//    public PageResult<ExecNutritionalFoodLogVO> query(@RequestBody ExecNutritionalFoodQueryDTO requestBody) {
//
//        // 初始化返回参数
//        PageResult<ExecNutritionalFoodLogVO> pageResult = new PageResult<>();
//        pageResult.setTotal(0L);
//        pageResult.setList(new ArrayList<>());
//
//        // 不能为空的字段: 营养方案ID, 查询时间
//        if ( requestBody.getNutritionalId() == null || requestBody.getNutritionalId().isEmpty() ){
//            throw new ServiceException(ResponseCode.PARAMS_NOT_NULL.getCode(),"nutritionalId:"+ResponseCode.PARAMS_NOT_NULL.getMessage());
//        }
//
//        // 开始时间
//        String startTime;
//        // 结束时间
//        String endTime;
//
//        if ( requestBody.getExecTimeFirst() != null ){
//            startTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(requestBody.getExecTimeFirst(),false) ,0);
//            if ( startTime == null ){
//                throw new ServiceException(ResponseCode.PARAMS_FROM_ERROR.getCode(),"createTimeFirst:"+ResponseCode.PARAMS_FROM_ERROR.getMessage());
//            }
//        } else {
//            startTime = DateUtils.convertLocalDataTimeToStartOfDay(LocalDateTime.now().minusDays(6), 0);
//        }
//        if ( requestBody.getExecTimeSecond() != null ){
//            endTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(requestBody.getExecTimeSecond(),false) ,1);
//            if ( endTime == null ){
//                throw new ServiceException(ResponseCode.PARAMS_FROM_ERROR.getCode(),"createTimeFirst:"+ResponseCode.PARAMS_FROM_ERROR.getMessage());
//            }
//        } else {
//            endTime = DateUtils.convertLocalDataTimeToStartOfDay(LocalDateTime.now(), 1);
//        }
//
//        requestBody.setExecTime(startTime + "," + endTime);
//
//        /**
//         * 获取营养康复计划数据
//         * */
//        NutritionalQueryDTO nutritionalQueryDTO = new NutritionalQueryDTO();
//        nutritionalQueryDTO.setPatientId(requestBody.getPatientId());
//        nutritionalQueryDTO.setId(requestBody.getNutritionalId());
//        PageResult<NutritionalVO> nutritionalVOPageResult = nutritionalFeign.crud("GET",nutritionalQueryDTO);
//        // 未找到营养康复计划
//        if ( nutritionalVOPageResult.getList() == null || nutritionalVOPageResult.getList().size() == 0 ){
//            return pageResult;
//        }
//
//        /**
//         * 获取食物日志数据
//         * */
//        ExecNutritionalFoodQueryDTO execNutritionalFood = new ExecNutritionalFoodQueryDTO();
//        execNutritionalFood.setExecTime(requestBody.getExecTime());
//        execNutritionalFood.setPatientId(requestBody.getPatientId());
//        execNutritionalFood.setNutritionalId(requestBody.getNutritionalId());
//        PageResult<ExecNutritionalFoodVO> execNutritionalFoodVOPageResult =
//                apiFeign.crud("GET", execNutritionalFood);
//        // 为空:返回
//        if ( execNutritionalFoodVOPageResult.getList() == null ){
//            return pageResult;
//        }
//
//        /**
//         * 组装数据
//         * */
//        // 同一日期下的数据
//        Map<String, ExecNutritionalFoodLogVO> execNutritionalFoodLogVOMap = new HashMap<>();
//        for ( ExecNutritionalFoodVO execNutritionalFoodVO : execNutritionalFoodVOPageResult.getList() )
//        {
//            String execTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(execNutritionalFoodVO.getExecTime(),true),2);
//            if ( execTime == null ){
//                continue;
//            }
//            String[] str = execTime.split(" ");
//            if ( str.length < 1 ){
//                continue;
//            }
//            String dateTime = str[0].trim();
//            if ( dateTime.isEmpty() ){
//                continue;
//            }
//            // 餐别
//            String dinnerClass1 = execNutritionalFoodVO.getDinnerClass().trim();
//            if ( dinnerClass1.isEmpty() ){
//                continue;
//            }
//
//            // 日期为空:初始化
//            if ( !execNutritionalFoodLogVOMap.containsKey(dateTime) ){
//                execNutritionalFoodLogVOMap.put(dateTime,new ExecNutritionalFoodLogVO());
//                execNutritionalFoodLogVOMap.get(dateTime).setDateTime(dateTime);
//            }
//
//            // 没有餐别:初始化
//            if ( !execNutritionalFoodLogVOMap.get(dateTime).getExecNutritionalFoodMap().containsKey(dinnerClass1) ){
//                execNutritionalFoodLogVOMap.get(dateTime).getExecNutritionalFoodMap().put(dinnerClass1, new ArrayList<>());
//            }
//
//            // 添加数据到map
//            execNutritionalFoodLogVOMap.get(dateTime).getExecNutritionalFoodMap().get(dinnerClass1).add(execNutritionalFoodVO);
//        }
//
//        List<ExecNutritionalFoodLogVO> execNutritionalFoodLogVOList = new ArrayList<>();
//        for ( Map.Entry<String, ExecNutritionalFoodLogVO> execNutritionalFoodLogVOEntry : execNutritionalFoodLogVOMap.entrySet() ) {
//            execNutritionalFoodLogVOList.add(execNutritionalFoodLogVOEntry.getValue());
//        }
//
//        pageResult.setList(execNutritionalFoodLogVOList);
//        pageResult.setTotal((long)execNutritionalFoodLogVOList.size());
//
//        return pageResult;
//    }
//
//    @PostMapping("/queryFoodLog")
//    @Operation(summary = "查询日志", description = "")
//    @CheckDoctorPatientPermission(needMember = false) // 患者与医生操作权限判断,默认捕获patientId参数
//    public PageResult<ExecNutritionalFoodVO> getFoodLog(@RequestBody ExecNutritionalFoodQueryDTO requestBody) {
//
//        // 初始化返回参数
//        PageResult<ExecNutritionalFoodVO> pageResult = new PageResult<>();
//        pageResult.setTotal(0L);
//        pageResult.setList(new ArrayList<>());
//
//        List<String> execTimeList = new ArrayList<>();
//        execTimeList.add(requestBody.getExecTimeFirst());
//        execTimeList.add(requestBody.getExecTimeSecond());
//        requestBody.setExecTime(this.dateRangeListToJoinString(execTimeList, requestBody.getExecTimeSort()));
//
//        /**
//         * 获取食物日志数据
//         * */
//        pageResult = apiFeign.crud("GET", requestBody);
//
//        return pageResult;
//    }
//
//    @PostMapping("/update")
//    @Operation(summary = "更新信息", description = "")
//    @CheckDoctorPatientPermission() // 患者与医生操作权限判断,默认捕获patientId参数
//    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "添加执行计划",module = "营养康复")
//    public void update(@RequestBody ExecNutritionalFoodAddDTO requestBody) {
//
//        ExecNutritionalFoodAddDTO execNutritionalFoodAddDTO = new ExecNutritionalFoodAddDTO();
//        execNutritionalFoodAddDTO.setId(requestBody.getId());
//        execNutritionalFoodAddDTO.setPatientId(requestBody.getPatientId());
//        execNutritionalFoodAddDTO.setNutritionalId(requestBody.getNutritionalId());
//        execNutritionalFoodAddDTO.setExecTime(requestBody.getExecTime());
//        execNutritionalFoodAddDTO.setQuantity(requestBody.getQuantity());
//
//        apiFeign.crud("PUT", execNutritionalFoodAddDTO);
//    }
//
//    /**
//     * 时间范围逗号拼接
//     * dateRangeList: 时间范围
//     * sortString: 排序字符, ASC正序, DESC倒序
//     * */
//    private String dateRangeListToJoinString(List<String> dateRangeList, String sortString){
//
//        StringBuilder dateString = new StringBuilder();
//
//        if ( dateRangeList != null ) {
//            for (String dateStr : dateRangeList) {
//                // 过滤掉错误值
//                if ( StringUtils.isNotBlank(dateStr) ){
//                    dateStr = DateUtils.convertLocalDataTimeToStartOfDay(
//                            DateUtils.convertToLocalDateTime(dateStr, true), 2);
//                }
//                // 有时间
//                if ( StringUtils.isNotBlank(dateStr)  ) {
//                    if (dateString.length() == 0) {
//                        dateString.append(dateStr);
//                    } else {
//                        dateString.append(",").append(dateStr);
//                    }
//                } else { // 为空
//                    if (dateString.length() == 0) { // 设置最小值
//                        dateString.append("2000-01-01 00:00:00");
//                    } else { //设置最大值
//                        dateString.append(",").append("9999-01-01 00:00:00");
//                    }
//                }
//            }
//        }
//
//        /**
//         * 排序
//         * */
//        if ( sortString != null ){
//            if ( sortString.equals("ASC") || sortString.equals("0") ){
//                sortString = "SORT:ASC";
//            } else if ( sortString.equals("DESC") || sortString.equals("1") ){
//                sortString = "SORT:DESC";
//            } else {
//                sortString = "";
//            }
//
//            if ( !sortString.isEmpty() ){
//                if ( dateString.length() == 0 ){
//                    dateString.append(sortString);
//                } else {
//                    dateString.append(",").append(sortString);
//                }
//            }
//        }
//
//        if ( dateString.length() == 0 ){
//            return null;
//        }
//
//        return dateString.toString();
//    }
//
//}
