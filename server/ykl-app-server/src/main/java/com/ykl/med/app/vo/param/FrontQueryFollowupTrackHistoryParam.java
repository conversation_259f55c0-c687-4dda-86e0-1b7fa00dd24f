package com.ykl.med.app.vo.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */
@Data
public class FrontQueryFollowupTrackHistoryParam {

    @Schema(description = "随访方式(字典表)")
    private String followupMode;

    @Schema(description = "开始时间(yyyy-MM-dd)")
    private String followupTimeStart;

    @Schema(description = "结束时间(yyyy-MM-dd)")
    private String followupTimeEnd;

    @Schema(description = "排序类型(0-按照后台设置的顺序、1-随访时间正序、2-随访时间倒叙)")
    private Integer sortType = 0;

}
