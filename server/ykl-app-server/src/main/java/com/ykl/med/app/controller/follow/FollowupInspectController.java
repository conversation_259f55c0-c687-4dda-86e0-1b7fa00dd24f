package com.ykl.med.app.controller.follow;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.followup.api.FollowupInspectFeign;
import com.ykl.med.followup.entity.param.QueryFollowupInspectInfoParam;
import com.ykl.med.followup.entity.vo.FollowupInspectVO;
import com.ykl.med.mix.api.follow.MixFollowupFeign;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 检查随访
 *
 * <AUTHOR>
 * @since 2024/8/14
 */
@Tag(name = "检查随访")
@RestController
@RequestMapping("/followup/inspect")
@EnableCommonResponseAdvice
public class FollowupInspectController {

    @Resource
    private MixFollowupFeign mixFollowupFeign;
    @Resource
    private FollowupInspectFeign followupInspectFeign;


    /**
     * 随访详情
     *
     * @param param QueryFollowupInspectInfoParam
     * @return FollowupInspectVO
     */
    @PostMapping("/info")
    @Operation(summary = "随访详情")
    public FollowupInspectVO info(@Valid @RequestBody QueryFollowupInspectInfoParam param) {
        return mixFollowupFeign.info(param);
    }

}
