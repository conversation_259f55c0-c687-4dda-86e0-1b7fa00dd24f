package com.ykl.med.app.controller;

import com.ykl.med.app.service.PatientService;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.doctors.api.DoctorFeign;
import com.ykl.med.doctors.entity.vo.DoctorListVO;
import com.ykl.med.doctors.entity.vo.DoctorVO;
import com.ykl.med.mix.api.patient.MixPatientDoctorFeign;
import com.ykl.med.mix.vo.patient.QueryDoctorByPatientMedicalTeamReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/18
 */

@Tag(name = "医生相关")
@RestController
@RequestMapping("/doctor")
@EnableCommonResponseAdvice
public class AppDoctorController {

    @Resource
    private PatientService patientService;
    @Resource
    private DoctorFeign doctorFeign;
    @Resource
    private MixPatientDoctorFeign mixPatientDoctorFeign;


    @PostMapping("/getById")
    @Operation(summary = "医生详情")
    public DoctorVO getById(@RequestParam(name = "id") Long id) {
        return doctorFeign.getById(id);
    }

    @PostMapping("/getDoctorsInPatientMedicalGroup")
    @Operation(summary = "根据患者的医疗团队id查找医生列表")
    public List<DoctorListVO> getDoctorsInPatientMedicalGroup(@RequestBody QueryDoctorByPatientMedicalTeamReqVO reqVO) {
        return mixPatientDoctorFeign.getDoctorsInPatientMedicalGroup(reqVO.setPatientId(patientService.getCurrentPatientId()));
    }

    @PostMapping("/getPatientMedicalTeam")
    @Operation(summary = "获取患者的医疗团队")
    public List<DoctorListVO> getPatientMedicalTeam(@RequestBody QueryDoctorByPatientMedicalTeamReqVO reqVO) {
        return mixPatientDoctorFeign.getPatientMedicalTeam(reqVO.setPatientId(patientService.getCurrentPatientId()));
    }



}
