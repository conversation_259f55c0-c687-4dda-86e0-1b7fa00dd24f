package com.ykl.med.app.controller.rehab;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.masterdata.api.DishFeign;
import com.ykl.med.masterdata.vo.req.recipe.*;
import com.ykl.med.masterdata.vo.resp.recipe.CalculateNutritionVO;
import com.ykl.med.masterdata.vo.resp.recipe.DishListVO;
import com.ykl.med.masterdata.vo.resp.recipe.DishVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2024/6/25
 */
@Tag(name = "菜品")
@RestController
@RequestMapping("/dish")
@EnableCommonResponseAdvice
public class DishController {

    @Resource
    private DishFeign dishFeign;

    /**
     * 菜品列表
     *
     * @param param {@link DishQueryReqVO}
     * @return PageResult<DishListVO>
     */
    @PostMapping("/page")
    @Operation(summary = "菜品列表")
    public PageResult<DishListVO> page(@Valid @RequestBody DishQueryReqVO param) {
        return dishFeign.page(param);
    }


    /**
     * 菜品详情
     *
     * @param param DishQueryByIdReqVO
     * @return DishVO
     */
    @PostMapping("/info")
    @Operation(summary = "菜品详情")
    public DishVO info(@Valid @RequestBody DishQueryByIdReqVO param) {
        return dishFeign.info(param);
    }



}
