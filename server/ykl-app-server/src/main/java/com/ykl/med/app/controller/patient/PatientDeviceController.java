package com.ykl.med.app.controller.patient;

import com.ykl.med.app.service.PatientService;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.patient.api.PatientDeviceFeign;
import com.ykl.med.patient.vo.AddPatientDeviceReqVO;
import com.ykl.med.patient.vo.PatientDeviceVO;
import com.ykl.med.patient.vo.QueryPatientDeviceReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/3
 */
@Tag(name = "绑定设备")
@RestController
@RequestMapping("/patient/device")
@EnableCommonResponseAdvice
public class PatientDeviceController {

    @Resource
    private PatientDeviceFeign patientDeviceFeign;
    @Resource
    private PatientService patientService;

    @PostMapping("/list")
    @Operation(summary = "列表")
    public List<PatientDeviceVO> list(@RequestBody QueryPatientDeviceReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        return patientDeviceFeign.list(reqVO);
    }

    @PostMapping("/add")
    @Operation(summary = "添加")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "添加患者设备", module = "患者管理")
    public void add(@Valid @RequestBody AddPatientDeviceReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        patientDeviceFeign.add(reqVO);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除患者设备", module = "患者管理")
    public void delete(@Valid @RequestBody IdReqVO reqVO) {
        patientDeviceFeign.delete(reqVO);
    }


}
