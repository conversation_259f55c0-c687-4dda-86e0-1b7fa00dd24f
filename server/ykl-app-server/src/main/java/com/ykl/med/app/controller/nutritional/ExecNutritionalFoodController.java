//package com.ykl.med.app.controller.nutritional;
//
//import com.ykl.med.app.handler.ResponseCode;
//import com.ykl.med.base.handler.EnableCommonResponseAdvice;
//import com.ykl.med.constants.LogConstants;
//import com.ykl.med.framework.common.enums.FoodCategories;
//import com.ykl.med.framework.common.exception.ServiceException;
//import com.ykl.med.framework.common.pojo.PageResult;
//import com.ykl.med.framework.common.util.date.DateUtils;
//import com.ykl.med.interfaces.OperateLog;
//import com.ykl.med.nutritional.api.ExecNutritionalFeign;
//import com.ykl.med.nutritional.api.ExecNutritionalFoodFeign;
//import com.ykl.med.nutritional.api.NutritionalFeign;
//import com.ykl.med.nutritional.entity.dto.*;
//import com.ykl.med.nutritional.entity.vo.ExecNutritionalFoodLogVO;
//import com.ykl.med.nutritional.entity.vo.ExecNutritionalFoodVO;
//import com.ykl.med.nutritional.entity.vo.ExecNutritionalVO;
//import com.ykl.med.nutritional.entity.vo.NutritionalVO;
//import com.ykl.med.patient.api.PatientUserFeign;
//import com.ykl.med.patient.vo.PatientVO;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.lang.reflect.Field;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Tag(name = "营养方案食物日志服务")
//@RestController
//@RequestMapping("/execNutritionalFood")
//@Validated
//@EnableCommonResponseAdvice
//public class ExecNutritionalFoodController {
//
//    @Resource
//    private ExecNutritionalFoodFeign apiFeign;
//
//    @Resource
//    private NutritionalFeign nutritionalFeign;
//
//    @Resource
//    private ExecNutritionalFeign execNutritionalFeign;
//
//    @Resource
//    private PatientUserFeign patientUserFeign;
//
//    @PostMapping("/query")
//    @Operation(summary = "查询列表", description = "返回执行计划")
//    public PageResult<ExecNutritionalFoodLogVO> query(@RequestBody ExecNutritionalFoodQueryDTO requestBody) {
//
//        // 初始化返回参数
//        PageResult<ExecNutritionalFoodLogVO> pageResult = new PageResult<>();
//        pageResult.setTotal(0L);
//        pageResult.setList(new ArrayList<>());
//
//        // 不能为空的字段: 营养方案ID, 查询时间
//        if ( requestBody.getNutritionalId() == null || requestBody.getNutritionalId().isEmpty() ){
//            throw new ServiceException(ResponseCode.PARAMS_NOT_NULL.getCode(),"nutritionalId:"+ResponseCode.PARAMS_NOT_NULL.getMessage());
//        }
//
//        // 开始时间
//        String startTime;
//        // 结束时间
//        String endTime;
//
//        if ( requestBody.getExecTimeFirst() != null ){
//            startTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(requestBody.getExecTimeFirst(),false) ,0);
//            if ( startTime == null ){
//                throw new ServiceException(ResponseCode.PARAMS_FROM_ERROR.getCode(),"createTimeFirst:"+ResponseCode.PARAMS_FROM_ERROR.getMessage());
//            }
//        } else {
//            startTime = DateUtils.convertLocalDataTimeToStartOfDay(LocalDateTime.now().minusDays(6), 0);
//        }
//        if ( requestBody.getExecTimeSecond() != null ){
//            endTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(requestBody.getExecTimeSecond(),false) ,1);
//            if ( endTime == null ){
//                throw new ServiceException(ResponseCode.PARAMS_FROM_ERROR.getCode(),"createTimeFirst:"+ResponseCode.PARAMS_FROM_ERROR.getMessage());
//            }
//        } else {
//            endTime = DateUtils.convertLocalDataTimeToStartOfDay(LocalDateTime.now(), 1);
//        }
//
//        requestBody.setExecTime(startTime + "," + endTime);
//
//        // 获取患者ID
//        Long patientId = this.getPatientId(requestBody.getCurrentUserId());
//        if ( patientId == -1 ) {
//            throw new ServiceException(ResponseCode.GET_PATIENT_ID_FAIL.getCode(),ResponseCode.GET_PATIENT_ID_FAIL.getMessage());
//        }
//        requestBody.setPatientId(patientId.toString());
//
//        /**
//         * 获取营养康复计划数据
//         * */
//        NutritionalQueryDTO nutritionalQueryDTO = new NutritionalQueryDTO();
//        nutritionalQueryDTO.setPatientId(requestBody.getPatientId());
//        nutritionalQueryDTO.setId(requestBody.getNutritionalId());
//        PageResult<NutritionalVO> nutritionalVOPageResult = nutritionalFeign.crud("GET",nutritionalQueryDTO);
//        // 未找到营养康复计划
//        if ( nutritionalVOPageResult.getList() == null || nutritionalVOPageResult.getList().size() == 0 ){
//            return pageResult;
//        }
//
//        /**
//         * 获取食物日志数据
//         * */
//        ExecNutritionalFoodQueryDTO execNutritionalFood = new ExecNutritionalFoodQueryDTO();
//        execNutritionalFood.setExecTime(requestBody.getExecTime());
//        execNutritionalFood.setPatientId(requestBody.getPatientId());
//        execNutritionalFood.setNutritionalId(requestBody.getNutritionalId());
//        PageResult<ExecNutritionalFoodVO> execNutritionalFoodVOPageResult = apiFeign.crud("GET", execNutritionalFood);
//        // 为空:返回
//        if ( execNutritionalFoodVOPageResult.getList() == null ){
//            return pageResult;
//        }
//
//        /**
//         * 组装数据
//         * */
//        // 同一日期下的数据
//        Map<String, ExecNutritionalFoodLogVO> execNutritionalFoodLogVOMap = new HashMap<>();
//        for ( ExecNutritionalFoodVO execNutritionalFoodVO : execNutritionalFoodVOPageResult.getList() )
//        {
//            String execTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(execNutritionalFoodVO.getExecTime(),true),2);
//            if ( execTime == null ){
//                continue;
//            }
//            String[] str = execTime.split(" ");
//            if ( str.length < 1 ){
//                continue;
//            }
//            String dateTime = str[0].trim();
//            if ( dateTime.isEmpty() ){
//                continue;
//            }
//            // 餐别
//            String dinnerClass1 = execNutritionalFoodVO.getDinnerClass().trim();
//            if ( dinnerClass1.isEmpty() ){
//                continue;
//            }
//
//            // 日期为空:初始化
//            if ( !execNutritionalFoodLogVOMap.containsKey(dateTime) ){
//                execNutritionalFoodLogVOMap.put(dateTime,new ExecNutritionalFoodLogVO());
//                execNutritionalFoodLogVOMap.get(dateTime).setDateTime(dateTime);
//            }
//
//            // 没有餐别:初始化
//            if ( !execNutritionalFoodLogVOMap.get(dateTime).getExecNutritionalFoodMap().containsKey(dinnerClass1) ){
//                execNutritionalFoodLogVOMap.get(dateTime).getExecNutritionalFoodMap().put(dinnerClass1, new ArrayList<>());
//            }
//
//            // 添加数据到map
//            execNutritionalFoodLogVOMap.get(dateTime).getExecNutritionalFoodMap().get(dinnerClass1).add(execNutritionalFoodVO);
//        }
//
//        List<ExecNutritionalFoodLogVO> execNutritionalFoodLogVOList = new ArrayList<>();
//        for ( Map.Entry<String, ExecNutritionalFoodLogVO> execNutritionalFoodLogVOEntry : execNutritionalFoodLogVOMap.entrySet() ) {
//            execNutritionalFoodLogVOList.add(execNutritionalFoodLogVOEntry.getValue());
//        }
//
//        pageResult.setList(execNutritionalFoodLogVOList);
//        pageResult.setTotal((long)execNutritionalFoodLogVOList.size());
//
//        return pageResult;
//    }
//
//    @PostMapping("/queryFoodLog")
//    @Operation(summary = "查询日志", description = "")
//    public PageResult<ExecNutritionalFoodVO> getFoodLog(@RequestBody ExecNutritionalFoodQueryDTO requestBody) {
//
//        // 初始化返回参数
//        PageResult<ExecNutritionalFoodVO> pageResult = new PageResult<>();
//        pageResult.setTotal(0L);
//        pageResult.setList(new ArrayList<>());
//
//        // 不能为空的字段: 营养方案ID, 查询时间
//        if ( requestBody.getNutritionalId() == null || requestBody.getNutritionalId().isEmpty() ){
//            throw new ServiceException(ResponseCode.PARAMS_NOT_NULL.getCode(),"nutritionalId:"+ResponseCode.PARAMS_NOT_NULL.getMessage());
//        }
//
//        // 开始时间
//        String startTime;
//        // 结束时间
//        String endTime;
//
//        if ( requestBody.getExecTimeFirst() != null ){
//            startTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(requestBody.getExecTimeFirst(),false) ,0);
//            if ( startTime == null ){
//                throw new ServiceException(ResponseCode.PARAMS_FROM_ERROR.getCode(),"createTimeFirst:"+ResponseCode.PARAMS_FROM_ERROR.getMessage());
//            }
//        } else {
//            startTime = DateUtils.convertLocalDataTimeToStartOfDay(LocalDateTime.now().minusDays(6), 0);
//        }
//        if ( requestBody.getExecTimeSecond() != null ){
//            endTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(requestBody.getExecTimeSecond(),false) ,1);
//            if ( endTime == null ){
//                throw new ServiceException(ResponseCode.PARAMS_FROM_ERROR.getCode(),"createTimeFirst:"+ResponseCode.PARAMS_FROM_ERROR.getMessage());
//            }
//        } else {
//            endTime = DateUtils.convertLocalDataTimeToStartOfDay(LocalDateTime.now(), 1);
//        }
//
//        requestBody.setExecTime(startTime + "," + endTime);
//
//        // 获取患者ID
//        Long patientId = this.getPatientId(requestBody.getCurrentUserId());
//        if ( patientId == -1 ) {
//            throw new ServiceException(ResponseCode.GET_PATIENT_ID_FAIL.getCode(),ResponseCode.GET_PATIENT_ID_FAIL.getMessage());
//        }
//        requestBody.setPatientId(patientId.toString());
//
//        /**
//         * 获取营养康复计划数据
//         * */
//        NutritionalQueryDTO nutritionalQueryDTO = new NutritionalQueryDTO();
//        nutritionalQueryDTO.setPatientId(requestBody.getPatientId());
//        nutritionalQueryDTO.setId(requestBody.getNutritionalId());
//        PageResult<NutritionalVO> nutritionalVOPageResult = nutritionalFeign.crud("GET",nutritionalQueryDTO);
//        // 未找到营养康复计划
//        if ( nutritionalVOPageResult.getList() == null || nutritionalVOPageResult.getList().size() == 0 ){
//            return pageResult;
//        }
//
//        /**
//         * 获取食物日志数据
//         * */
//        pageResult = apiFeign.crud("GET", requestBody);
//
//        return pageResult;
//    }
//
//    @PostMapping("/update")
//    @Operation(summary = "更新信息", description = "")
//    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改营养方案食物日志", module = "营养康复")
//    public void update(@RequestBody ExecNutritionalFoodAddDTO requestBody) {
//        // 获取患者ID
//        Long patientId = this.getPatientId(requestBody.getCurrentUserId());
//        if ( patientId == -1 ) {
//            throw new ServiceException(ResponseCode.PATIENT_NOT_AUTHORITY.getCode(),ResponseCode.PATIENT_NOT_AUTHORITY.getMessage());
//        }
//        requestBody.setPatientId(patientId.toString());
//
//        ExecNutritionalFoodAddDTO execNutritionalFoodAddDTO = new ExecNutritionalFoodAddDTO();
//        execNutritionalFoodAddDTO.setId(requestBody.getId());
//        execNutritionalFoodAddDTO.setPatientId(requestBody.getPatientId());
//        execNutritionalFoodAddDTO.setNutritionalId(requestBody.getNutritionalId());
//        execNutritionalFoodAddDTO.setExecTime(requestBody.getExecTime());
//        execNutritionalFoodAddDTO.setQuantity(requestBody.getQuantity());
//
//        apiFeign.crud("PUT", execNutritionalFoodAddDTO);
//    }
//
//    @PostMapping("/delete")
//    @Operation(summary = "删除数据", description = "")
//    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除营养方案食物日志", module = "营养康复")
//    public void update(@RequestBody DeleteDTO requestBody) {
//
//        // 获取患者ID
//        Long patientId = this.getPatientId(requestBody.getCurrentUserId());
//        if ( patientId == -1 ) {
//            throw new ServiceException(ResponseCode.PATIENT_NOT_AUTHORITY.getCode(),ResponseCode.PATIENT_NOT_AUTHORITY.getMessage());
//        }
//        requestBody.setPatientId(patientId.toString());
//
//        /**
//         * 取出食物打卡数据
//         * */
//        String ids = requestBody.getId();
//        if ( ids == null ){
//            ids = String.join(",",requestBody.getIds());
//            requestBody.setId(ids);
//        }
//        String[] idStr = ids.split(",");
//        int idCount = idStr.length;
//        ExecNutritionalFoodQueryDTO execNutritionalFoodQueryDTO = new ExecNutritionalFoodQueryDTO();
//        execNutritionalFoodQueryDTO.setId(ids);
//        execNutritionalFoodQueryDTO.setPatientId(requestBody.getPatientId());
//        PageResult<ExecNutritionalFoodVO> execNutritionalFoodVOPageResult = apiFeign.crud("GET", execNutritionalFoodQueryDTO);
//        if ( execNutritionalFoodVOPageResult.getList() == null || execNutritionalFoodVOPageResult.getList().size() != idCount ){
//            throw new ServiceException(ResponseCode.DATA_IS_NOT_EXIST.getCode(),ResponseCode.DATA_IS_NOT_EXIST.getMessage());
//        }
//
//        /**
//         * 回写食物打卡数据
//         * */
//        for ( ExecNutritionalFoodVO execNutritionalFoodVO : execNutritionalFoodVOPageResult.getList() ){
//            /**
//             * 读出那天的打卡数据
//             * */
//            String startTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(execNutritionalFoodVO.getExecTime(),true),0);
//            String endTime = DateUtils.convertLocalDataTimeToStartOfDay(
//                    DateUtils.convertToLocalDateTime(execNutritionalFoodVO.getExecTime(),true),1);
//            // 创建查询对象
//            ExecNutritionalQueryDTO execNutritionalQueryDTO = new ExecNutritionalQueryDTO();
//            execNutritionalQueryDTO.setPatientId(requestBody.getPatientId());
//            execNutritionalQueryDTO.setCreateTime(startTime+","+endTime);// 当天的打卡记录
//            PageResult<ExecNutritionalVO> execNutritionalVOPageResult = execNutritionalFeign.crud("GET", execNutritionalQueryDTO);
//            if ( execNutritionalVOPageResult.getList() == null ){
//                continue;
//            }
//
//            /**
//             * 营养打卡数据
//             * */
//            ExecNutritionalVO execNutritionalVO = execNutritionalVOPageResult.getList().get(0);
//
//            // 膳食类型
//            String foodType = execNutritionalFoodVO.getFoodType();
//
//            /**
//             * 减掉已增的数据
//             * */
//            // 减掉膳食数据
//            FoodCategories foodCategories = FoodCategories.fromValue(foodType);
//            if ( foodCategories != null ) {
//                try {
//                    // 通过类型的字段名,获取字段
//                    Field field = execNutritionalVO.getClass().getDeclaredField(foodCategories.getName());
//                    if ( field != null
//                            && execNutritionalFoodVO.getQuantity() != null
//                            && Integer.parseInt(execNutritionalFoodVO.getQuantity()) > 0 ){
//                        field.setAccessible(true);
//
//                        if ( field.get(execNutritionalVO) != null && !field.get(execNutritionalVO).toString().isEmpty() ){
//                            field.set(execNutritionalVO,
//                                    Integer.parseInt(field.get(execNutritionalVO).toString())
//                                            - Integer.parseInt(execNutritionalFoodVO.getQuantity())
//                            );
//                        }
//                    }
//                } catch (Exception ignored){}
//            }
//
//            // 简单营养素
//            if ( execNutritionalFoodVO.getKilocalorie() != null
//                    && execNutritionalVO.getKilocalorie() > Integer.parseInt(execNutritionalFoodVO.getKilocalorie())){
//                execNutritionalVO.setKilocalorie(execNutritionalVO.getKilocalorie() - Integer.parseInt(execNutritionalFoodVO.getKilocalorie()));
//            }
//            if ( execNutritionalFoodVO.getCarbohydrate() != null
//                    && execNutritionalVO.getCarbohydrate() > Integer.parseInt(execNutritionalFoodVO.getCarbohydrate())){
//                execNutritionalVO.setCarbohydrate(execNutritionalVO.getCarbohydrate() - Integer.parseInt(execNutritionalFoodVO.getCarbohydrate()));
//            }
//            if ( execNutritionalFoodVO.getProtein() != null
//                    && execNutritionalVO.getProtein() > Integer.parseInt(execNutritionalFoodVO.getProtein())){
//                execNutritionalVO.setProtein(execNutritionalVO.getProtein() - Integer.parseInt(execNutritionalFoodVO.getProtein()));
//            }
//            if ( execNutritionalFoodVO.getFat() != null
//                    && execNutritionalVO.getFat() > Integer.parseInt(execNutritionalFoodVO.getFat())){
//                execNutritionalVO.setFat(execNutritionalVO.getFat() - Integer.parseInt(execNutritionalFoodVO.getFat()));
//            }
//            if ( execNutritionalFoodVO.getWater() != null
//                    && execNutritionalVO.getWater() > Integer.parseInt(execNutritionalFoodVO.getWater())){
//                execNutritionalVO.setWater(execNutritionalVO.getWater() - Integer.parseInt(execNutritionalFoodVO.getWater()));
//            }
//
//            // 防止删除失败后,重试
//            execNutritionalVO.setRemark(execNutritionalVO.getRemark()+";删除打卡记录:"+execNutritionalFoodVO.getId());
//
//            /**
//             * 写数据到打开记录
//             * */
//            execNutritionalFeign.crud("PUT", execNutritionalVO);
//        }
//
//        apiFeign.crud("DELETE", requestBody);
//    }
//
//    /**
//     * 获取患者ID
//     * */
//    public Long getPatientId(Long userId) {
//        PatientVO patientVO = patientUserFeign.getPatientByUserId(userId);
//        if ( (patientVO == null) || patientVO.getPatientId() == null || patientVO.getPatientId() < 1 ){
//            return -1L;
//        }
//
//        return patientVO.getPatientId();
//    }
//
//}
