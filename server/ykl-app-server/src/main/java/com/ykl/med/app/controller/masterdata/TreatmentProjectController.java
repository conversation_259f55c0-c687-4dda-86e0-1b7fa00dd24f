package com.ykl.med.app.controller.masterdata;


import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.masterdata.api.treatment.TreatmentProjectFeign;
import com.ykl.med.masterdata.vo.TreatmentProjectVO;
import com.ykl.med.masterdata.vo.req.TreatmentProjectQueryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Treatment Project Controller.
 */
@Tag(name = "治疗项目")
@RestController
@RequestMapping("/treatmentProject")
@EnableCommonResponseAdvice
public class TreatmentProjectController {

    @Resource
    private TreatmentProjectFeign TreatmentProjectFeign;

    @PostMapping("/queryTreatmentProject")
    @Operation(summary = "查询治疗项目")
    public List<TreatmentProjectVO> queryTreatmentProject(@RequestBody TreatmentProjectQueryVO queryVO) {
        return TreatmentProjectFeign.queryTreatmentProject(queryVO);
    }

    @PostMapping("/getById")
    @Operation(summary = "根据Id查询治疗项目")
    public TreatmentProjectVO getById(@RequestBody IdReqVO reqVO) {
        return TreatmentProjectFeign.getById(reqVO.getId());
    }


}