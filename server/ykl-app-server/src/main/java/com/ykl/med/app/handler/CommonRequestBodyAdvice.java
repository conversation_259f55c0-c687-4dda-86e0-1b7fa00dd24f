package com.ykl.med.app.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.CommonRequestUtils;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.enums.OperateLogSystemType;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.interfaces.AutoBuildPatientId;
import com.ykl.med.framework.common.interfaces.AutoBuildPatientMedicalTeamId;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.interfaces.OperateLogFeign;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.constants.ConfigErrorCodeConstants;
import com.ykl.med.patient.api.MemberVersionFeign;
import com.ykl.med.patient.constants.PatientErrorCodeConstants;
import com.ykl.med.patient.enums.MemberVersionPurviewType;
import com.ykl.med.patient.vo.member.PatientMemberVersionVO;
import com.ykl.med.user.vo.UserAuthorityVO;
import com.ykl.med.util.OperateLogUtil;
import com.ykl.med.vo.OperateLogAddVO;
import com.ykl.med.wechat.sender.WechatRobot;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@ControllerAdvice
public class CommonRequestBodyAdvice implements RequestBodyAdvice {
    @Resource
    private HttpServletRequest request;
    @Resource
    private HttpServletResponse response;
    @Resource
    private MemberVersionFeign memberVersionFeign;
    @Resource
    private CommonConfigFeign commonConfigFeign;
    @Resource
    private OperateLogFeign operateLogFeign;
    @Resource
    private IdServiceImpl idService;
    @Resource
    private WechatRobot wechatRobot;

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        return inputMessage;
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        Class<?> clazz = parameter.getParameterType();
        handleBody(body, parameter, clazz);
        CommonRequestUtils.buildCommonHeader(request, inputMessage.getHeaders(), body, clazz);
        checkVersionLimit(inputMessage.getHeaders(), Objects.requireNonNull(parameter.getMethod(), "method is null"));
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        handleBody(body, parameter, null);
        CommonRequestUtils.buildCommonHeader(request, inputMessage.getHeaders(), body, null);
        return body;
    }

    /**
     * 处理体方法。该方法通过解析方法参数操作日志，获取用户授权信息，并构建相对应的请求。该方法同样会检查是否需要病人信息，并进行适当处理。
     *
     * @param body      请求报文体对象
     * @param parameter 方法参数对象
     * @param clazz     类实例对象
     */
    private void handleBody(Object body, MethodParameter parameter, Class<?> clazz) {
        UserAuthorityVO userAuthorityVO = AuthContextHolder.getInstance().getContext();
        Long userId = userAuthorityVO == null ? null : userAuthorityVO.getId();
        PatientMemberVersionVO patientMemberVersionVO = null;
        if (userId != null) {
            patientMemberVersionVO = memberVersionFeign.getMemberVersionByUserId(userId);
        }
        String relation = patientMemberVersionVO == null ? null : patientMemberVersionVO.getRelation();
        Long patientId = patientMemberVersionVO == null ? null : patientMemberVersionVO.getPatientId();
        String patientName = patientMemberVersionVO == null ? null : patientMemberVersionVO.getPatientName();
        this.operateLog(operateLogFeign, parameter.getMethod(), JSONObject.toJSONString(body), patientId, patientName, relation);
        CommonRequestUtils.buildUserId(body, userId, clazz);
        if (checkNeedPatient(parameter, clazz)) {
            Long medicalTeamId = patientMemberVersionVO == null ? null : patientMemberVersionVO.getMedicalTeamId();
            this.checkPatientMemberVersionPermission(Objects.requireNonNull(parameter.getMethod()), patientMemberVersionVO);
            CommonRequestUtils.buildCurrentPatientId(body, patientId, clazz);
            CommonRequestUtils.buildCurrentPatientMedicalTeamId(body, medicalTeamId, clazz);
        }
    }

    /**
     * 检查版本限制。此方法从Http头部获取应用版本，并确保它满足方法的版本限制。如果不满足，将抛出异常。
     *
     * @param headers HTTP头信息对象
     * @param method  方法对象
     */
    public void checkVersionLimit(HttpHeaders headers, Method method) {
        VersionLimit versionLimit = method.getAnnotation(VersionLimit.class);
        if (versionLimit == null) {
            return;
        }
        String version = headers.getFirst(CommonRequestUtils.APP_VERSION);
        AssertUtils.isTrue(StringUtils.isNotEmpty(version), ConfigErrorCodeConstants.VERSION_LIMIT);
        AssertUtils.isTrue(StringUtils.isEmpty(versionLimit.minVersion()) || version.compareTo(versionLimit.minVersion()) >= 0, ConfigErrorCodeConstants.VERSION_LIMIT);
        AssertUtils.isTrue(StringUtils.isEmpty(versionLimit.maxVersion()) || version.compareTo(versionLimit.maxVersion()) <= 0, ConfigErrorCodeConstants.VERSION_LIMIT);
        this.checkExactOrExcludeVersions(version, versionLimit.exactVersions(), true);
        this.checkExactOrExcludeVersions(version, versionLimit.excludeVersions(), false);
    }

    /**
     * 检查患者会员版本权限。此方法获取方法上的权限注解，确保病人会员版本包含所需的权限。如果不满足，将抛出异常。
     *
     * @param method                 方法对象
     * @param patientMemberVersionVO 病人会员版本值对象
     */
    public void checkPatientMemberVersionPermission(Method method, PatientMemberVersionVO patientMemberVersionVO) {
        CheckPatientMemberVersionPermission checkPatientMemberVersionPermission = method.getAnnotation(CheckPatientMemberVersionPermission.class);
        if (checkPatientMemberVersionPermission == null) {
            return;
        }
        MemberVersionPurviewType[] purviewTypes = checkPatientMemberVersionPermission.value();
        JSONObject jsonObject = commonConfigFeign.getCommonConfigValueJsonByKey("non_member_purview");
        if (jsonObject != null) {
            JSONArray jsonArray = jsonObject.getJSONArray("nonMemberPurviewList");
            Set<MemberVersionPurviewType> nonMemberPurviewList = new HashSet<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                nonMemberPurviewList.add(MemberVersionPurviewType.valueOf(jsonArray.getString(i)));
            }
            if (nonMemberPurviewList.containsAll(Lists.newArrayList(purviewTypes))) {
                return;
            }
        }
        AssertUtils.notNull(patientMemberVersionVO, PatientErrorCodeConstants.PATIENT_NOT_EXISTS);
        AssertUtils.notNull(patientMemberVersionVO.getPatientId(), PatientErrorCodeConstants.PATIENT_NOT_EXISTS);
        AssertUtils.notNull(patientMemberVersionVO.getMemberVersion(), PatientErrorCodeConstants.NO_MEMBER_VERSION_PERMISSIONS);
        List<MemberVersionPurviewType> purviewTypeSet = patientMemberVersionVO.getMemberVersion().getPurviewList();
        AssertUtils.isTrue(new HashSet<>(purviewTypeSet).containsAll(Lists.newArrayList(purviewTypes)), PatientErrorCodeConstants.NO_MEMBER_VERSION_PERMISSIONS);
    }

    /**
     * 记录操作日志。此方法获取方法上的操作日志注解，并将相关信息保存到一个新的OperateLogVO对象中，最后将该对象存储下来。
     *
     * @param method  方法对象
     * @param content 日志内容字符串
     */
    public void operateLog(OperateLogFeign operateLogFeign, Method method, String content, Long patientId, String patientName, String relation) {
        try {
            OperateLog operateLog = method.getAnnotation(OperateLog.class);
            if (operateLog != null) {
                //记录操作日志
                OperateLogAddVO operateLogVO = new OperateLogAddVO();
                UserAuthorityVO userAuthorityVO = AuthContextHolder.getInstance().getContext();
                operateLogVO.setUserName(patientName);
                operateLogVO.setUserId(userAuthorityVO.getId());
                operateLogVO.setUserRelation(relation);
                //Id 在AppInterceptor已经设置过了，所以直接从header中获取
                String operateLogIdStr = response.getHeader(OperateLogUtil.OPERATE_LOG_ID_HEADER);
                if (StringUtils.isEmpty(operateLogIdStr)) {
                    return;
                }
                operateLogVO.setId(Long.valueOf(operateLogIdStr));
                operateLogVO.setSystemType(OperateLogSystemType.APP);
                operateLogVO.setCreateTime(LocalDateTime.now());
                operateLogVO.setEventType(operateLog.eventType().getDesc());
                if (StringUtils.isNotEmpty(operateLog.module()) && !LogConstants.logModules.contains(operateLog.module())) {
                    throw new RuntimeException("模块填写错误:" + operateLog.module());
                }
                operateLogVO.setModule(operateLog.module());
                operateLogVO.setEventContent(OperateLogUtil.getEventContent(operateLog.eventContent(), content));
                operateLogVO.setUrl(request.getRequestURL().toString() + (request.getQueryString() != null ? "?" + request.getQueryString() : ""));
                operateLogVO.setIp(CommonRequestUtils.getClientIP(request));
                operateLogVO.setArgsAsJson(content);
                operateLogVO.setPatientId(patientId);
                operateLogVO.setPatientName(patientName);
                operateLogFeign.saveOperateLog(operateLogVO);
            }
        } catch (Exception e) {
            log.error("记录操作日志异常,{}", method.getDeclaringClass().getName() + "." + method.getName(), e);
            wechatRobot.sendMsg("记录操作日志异常:" + method.getDeclaringClass().getName() + "." + method.getName() + "," + e.getMessage());
        }
    }


    /**
     * 检查是否需要病人信息。此方法根据自动构建病人医疗队伍ID，自动构建病人ID以及检查病人会员版本权限注解来判断是否需要病人信息。
     *
     * @param parameter 方法参数对象
     * @param clazz     类对象
     * @return boolean 表示是否需要病人信息
     */
    private boolean checkNeedPatient(MethodParameter parameter, Class<?> clazz) {
        if (clazz != null && AutoBuildPatientMedicalTeamId.class.isAssignableFrom(clazz)) {
            return true;
        }
        if (clazz != null && AutoBuildPatientId.class.isAssignableFrom(clazz)) {
            return true;
        }
        return parameter.getMethodAnnotation(CheckPatientMemberVersionPermission.class) != null;
    }

    /**
     * 检查当前版本是否符合准确或排除版本。此方法比较当前版本是否在准确版本或排除版本中，如果是，将抛出异常。
     *
     * @param currentVersion 当前版本号字符串
     * @param versions       版本数组
     * @param isExact        是否为准确版本
     */
    private void checkExactOrExcludeVersions(String currentVersion, String[] versions, boolean isExact) {
        if (versions != null && versions.length > 0) {
            boolean flag = Arrays.asList(versions).contains(currentVersion);
            AssertUtils.isTrue(isExact == flag, ConfigErrorCodeConstants.VERSION_LIMIT);
        }
    }

}