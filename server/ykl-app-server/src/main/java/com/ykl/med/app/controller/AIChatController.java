package com.ykl.med.app.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.push.api.AIChatFeign;
import com.ykl.med.push.vo.ai.AIChatAddVO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "AI会话记录接口")
@RestController
@RequestMapping("/aiChat")
@EnableCommonResponseAdvice
public class AIChatController {
    @Resource
    private AIChatFeign aiChatFeign;


    @PostMapping("/save")
    @Schema(description = "保存AI会话")
    public void save(@RequestBody AIChatAddVO addVO) {
        aiChatFeign.save(addVO);
    }
}
