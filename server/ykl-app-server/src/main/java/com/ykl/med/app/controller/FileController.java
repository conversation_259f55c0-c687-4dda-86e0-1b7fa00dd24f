package com.ykl.med.app.controller;

import com.ykl.med.app.constants.AppConstants;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.file.api.FileFeign;
import com.ykl.med.file.vo.FileUploadRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件管理
 *
 * <AUTHOR>
 */
@Tag(name = "文件管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/file")
@EnableCommonResponseAdvice
public class FileController {

    private final FileFeign fileFeign;

    @PostMapping("/uploadSingle")
    public FileUploadRespVO uploadSingle(@RequestParam("file") MultipartFile file) {
        return fileFeign.uploadSingle(AppConstants.APP_MINIO_BUCKET,
                AppConstants.APP_MINIO_PATH + "/" + AuthContextHolder.getInstance().getContext().getId(), file,
                null,null);
    }

    @GetMapping("/getFileInfo")
    @Operation(summary = "获取文件信息", description = "返回文件信息")
    public FileUploadRespVO getFileInfo(@RequestParam("path") String path) {
        return fileFeign.getFileInfo(AppConstants.APP_MINIO_BUCKET, path);
    }

    @PostMapping("/upload")
    @Operation(summary = "文件上传", description = "返回文件上传结果")
    public List<FileUploadRespVO> upload(@RequestParam("files") MultipartFile[] files) {
        return fileFeign.upload(AppConstants.APP_MINIO_BUCKET,
                AppConstants.APP_MINIO_PATH + "/" + AuthContextHolder.getInstance().getContext().getId(), files, null);
    }
}
