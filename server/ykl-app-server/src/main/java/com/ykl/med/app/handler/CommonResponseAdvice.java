package com.ykl.med.app.handler;

import com.alibaba.fastjson2.JSON;
import com.ykl.med.base.handler.CommonResponseUtils;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.CommonResult;
import com.ykl.med.interfaces.OperateLogFeign;
import com.ykl.med.util.OperateLogUtil;
import com.ykl.med.wechat.sender.WechatRobot;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;
import java.lang.reflect.Method;

@Slf4j
@ControllerAdvice
public class CommonResponseAdvice implements ResponseBodyAdvice<Object> {

    @Resource
    private OperateLogFeign operateLogFeign;
    @Resource
    private WechatRobot wechatRobot;

    /**
     * 适用于哪些方法
     */
    @Override
    public boolean supports(
            MethodParameter methodParameter,
            Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    /**
     * 返回前执行
     */
    @Override
    public Object beforeBodyWrite(
            Object body,
            MethodParameter methodParameter,
            MediaType selectedContentType,
            Class<? extends HttpMessageConverter<?>> selectedConverterType,
            ServerHttpRequest request,
            ServerHttpResponse response) {
        Class clazz = methodParameter.getParameterType();
        Object result = body;
        if (clazz.equals(CommonResult.class)) {
            result = body;
        } else if (methodParameter.getDeclaringClass().getAnnotation(EnableCommonResponseAdvice.class) != null) {
            result = CommonResponseUtils.convert(body, methodParameter);
        }
        operateLog(operateLogFeign, methodParameter.getMethod(), JSON.toJSONString(result), response);
        return result;
    }

    public void operateLog(OperateLogFeign operateLogFeign, Method method, String content, ServerHttpResponse response) {
        try {
            String operateLogIdStr = response.getHeaders().getFirst(OperateLogUtil.OPERATE_LOG_ID_HEADER);
            if (StringUtils.isNotEmpty(operateLogIdStr)) {
                operateLogFeign.setOperateLogResult(Long.parseLong(operateLogIdStr), content);
            }
        } catch (Exception e) {
            log.error("记录接口响应操作日志异常,{}", method.getDeclaringClass().getName() + "." + method.getName(), e);
            wechatRobot.sendMsg("记录操作日志异常:" + method.getDeclaringClass().getName() + "." + method.getName() + e.getMessage());
        }
    }

}