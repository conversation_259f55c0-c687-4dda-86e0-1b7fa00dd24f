package com.ykl.med.app.controller.masterdata;


import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.masterdata.api.treatment.TreatmentSubTypeExaminationTestFeign;
import com.ykl.med.masterdata.vo.ExaminationTestProjectVO;
import com.ykl.med.masterdata.vo.TreatmentSubTypeExaminationTestVO;
import com.ykl.med.masterdata.vo.req.TreatmentSubTypeExaminationTestQueryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/treatmentSubTypeExaminationTest")
@Tag(name = "治疗子类型检查检验项目")
@EnableCommonResponseAdvice
public class TreatmentSubTypeExaminationTestController {

    @Resource
    private TreatmentSubTypeExaminationTestFeign treatmentSubTypeExaminationTestFeign;

    @PostMapping("/query")
    @Operation(summary = "查询治疗子类型检测数据")
    public List<TreatmentSubTypeExaminationTestVO> query(@RequestBody TreatmentSubTypeExaminationTestQueryVO queryVO) {
        return treatmentSubTypeExaminationTestFeign.query(queryVO);
    }

    @PostMapping("/getExaminationTestBySubType")
    @Operation(summary = "根据子类型查询检查检验项目")
    public List<ExaminationTestProjectVO> getExaminationTestBySubType(@RequestBody TreatmentSubTypeExaminationTestQueryVO queryVO) {
        return treatmentSubTypeExaminationTestFeign.getExaminationTestBySubType(queryVO.getSubType());
    }
}