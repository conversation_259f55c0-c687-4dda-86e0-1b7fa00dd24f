package com.ykl.med.app.controller.message;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.ykl.med.app.vo.ChatIdBatchVO;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.enums.MemberServicePackageType;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.mix.api.push.MixChatFeign;
import com.ykl.med.mix.vo.push.MixChatUserInfoVO;
import com.ykl.med.patient.api.MemberVersionFeign;
import com.ykl.med.patient.api.PatientBalanceFeign;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.constants.PatientErrorCodeConstants;
import com.ykl.med.patient.enums.PatientBalanceLogType;
import com.ykl.med.patient.vo.balance.PatientBalanceChangeReqVO;
import com.ykl.med.patient.vo.balance.PatientBalanceVO;
import com.ykl.med.patient.vo.member.PatientMemberVersionVO;
import com.ykl.med.patient.vo.patient.PatientSimpleVO;
import com.ykl.med.push.api.ChatFeign;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.UserChatFeign;
import com.ykl.med.push.enums.ChatType;
import com.ykl.med.push.enums.MessageResourceType;
import com.ykl.med.push.vo.message.*;
import com.ykl.med.shift.api.ConsultFeign;
import com.ykl.med.shift.vo.resp.ConsultVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "消息服务")
@RestController
@RequestMapping("/message")
@Slf4j
@EnableCommonResponseAdvice
public class MessageController {
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private PatientBalanceFeign patientBalanceFeign;
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private MemberVersionFeign memberVersionFeign;
    @Resource
    private ChatFeign chatFeign;
    @Resource
    private UserChatFeign userChatFeign;
    @Resource
    private ConsultFeign consultFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private MixChatFeign mixChatService;

    @PostMapping("/createPersonChat")
    @Operation(summary = "创建会话", description = "返回会话ID")
    public Long createPersonChat(@RequestBody ChatCreateReqVO reqVO) {
        return chatFeign.createPersonChat(reqVO);
    }

    @PostMapping("/createLiveChat")
    @Operation(summary = "创建直播临时会话", description = "返回会话ID")
    public Long createLiveChat(@RequestBody ChatCreateReqVO reqVO) {
        ConsultVO consultVO = consultFeign.details(Long.valueOf(reqVO.getCode()));
        ChatVO chatVO = chatFeign.getLiveChat(reqVO.getCode());
        if (chatVO == null) {
            Long doctorId = consultVO.getDoctorId();
            UserSimpleVO doctor = userFeign.getByUserId(doctorId);
            reqVO.setName(doctor.getName());
            reqVO.setLogo(doctor.getAvatar());
            return chatFeign.createLiveChat(reqVO);
        } else {
            userChatFeign.addChatUser(chatVO.getId(), reqVO.getCurrentUserId());
            return chatVO.getId();
        }
    }

    @PostMapping("/sendMsg")
    @Operation(summary = "发送消息", description = "返回消息id")
    public MessageVO sendMsg(@RequestBody MessageSendReqVO reqVO) {
        PatientMemberVersionVO patientMemberVersionVO = memberVersionFeign.getMemberVersionByUserId(reqVO.getCurrentUserId());
        AssertUtils.notNull(patientMemberVersionVO, PatientErrorCodeConstants.PATIENT_NOT_EXISTS);
        AssertUtils.notNull(patientMemberVersionVO.getMemberVersion(), PatientErrorCodeConstants.MEMBER_VERSION_NOT_EXISTS);

        PatientSimpleVO patientSimpleVO = patientUserFeign.getPatientSimpleByUserId(reqVO.getCurrentUserId());
        if (patientSimpleVO == null || patientSimpleVO.getMedicalTeamId() == null) {
            throw new ServiceException(PatientErrorCodeConstants.PATIENT_IS_NOT_MEMBER);
        }
        PatientBalanceVO patientBalanceVO = patientBalanceFeign.getPatientBalance(patientSimpleVO.getPatientId(), MemberServicePackageType.EXPERT_ADVICE, patientSimpleVO.getMedicalTeamId());
        AssertUtils.isTrue(patientBalanceVO != null && patientBalanceVO.getBalance() > 0, PatientErrorCodeConstants.INSUFFICIENT_PATIENT_BALANCE);
        reqVO.setResourceType(MessageResourceType.APP);
        MessageVO messageVO = messageFeign.sendMsg(reqVO);
        if (messageVO != null && messageVO.getId() != null) {
            //直播临时会话不扣费
            ChatVO chatVO = chatFeign.getChat(messageVO.getChatId());
            if (chatVO.getType() == ChatType.LIVE) {
                return messageVO;
            }
            PatientBalanceChangeReqVO changeReqVO = new PatientBalanceChangeReqVO();
            changeReqVO.setUserId(reqVO.getCurrentUserId());
            changeReqVO.setChangeBalance(1);
            changeReqVO.setServicePackageId(patientSimpleVO.getMedicalTeamId());
            changeReqVO.setOutTradeNo(messageVO.getId().toString());
            changeReqVO.setServicePackageType(MemberServicePackageType.EXPERT_ADVICE);
            changeReqVO.setType(PatientBalanceLogType.CHAT_DEDUCT);
            changeReqVO.setPatientId(patientSimpleVO.getPatientId());
            patientBalanceFeign.changeBalance(changeReqVO);
        }
        return messageVO;
    }

    @PostMapping("/queryMessage")
    @Operation(summary = "查询消息")
    public MessageQueryRespVO queryMessage(@RequestBody MessageQueryReqVO reqVO) {
        return messageFeign.queryMessage(reqVO);
    }


    @PostMapping("/getUpdateChat")
    @Operation(summary = "查询会话信息")
    public List<ChatVO> getUpdateChat(@RequestBody ChatQueryReqVO reqVO) {
        reqVO.setChatTypeList(Lists.newArrayList(ChatType.LIVE, ChatType.PERSON, ChatType.SYSTEM));
        if (reqVO.getMaxId() > 0) {
            return messageFeign.getUpdateChat(reqVO);
        } else {
            return messageFeign.getAllChat(reqVO);
        }
    }

    @PostMapping("/getChat")
    @Operation(summary = "查询会话详情")
    public List<ChatDetailVO> getChat(@RequestBody ChatIdBatchVO batchVO) {
        List<Long> list = batchVO.getChatIds().stream().map(Long::valueOf).collect(Collectors.toList());
        return chatFeign.getChatDetail(list, AuthContextHolder.getInstance().getContext().getId());
    }


    @PostMapping("/selectUnread")
    @Operation(summary = "查询用户未读会话")
    public List<Long> selectUnread() {
        return userChatFeign.selectUnread(AuthContextHolder.getInstance().getContext().getId(), Lists.newArrayList(ChatType.LIVE, ChatType.PERSON));
    }


    @PostMapping("/unreadConfirm")
    @Operation(summary = "标记用户已读会话")
    public void unreadConfirm(@RequestBody ChatReadVO chatReadVO) {
        userChatFeign.unreadConfirm(chatReadVO.getCurrentUserId(), chatReadVO.getChatId(), chatReadVO.getReadMaxId());
    }

    @PostMapping("/getUnreadCount")
    @Operation(summary = "查询用户未读消息数量")
    public Long getUnreadCount() {
        return messageFeign.getUnreadCount(AuthContextHolder.getInstance().getContext().getId());
    }


    @PostMapping("/getChatUserInfoByUserIds")
    @Operation(summary = "获取会话中的用户信息")
    public List<MixChatUserInfoVO> getChatUserInfoByUserIds(@RequestBody IdListReqVO idListReqVO) {
        return mixChatService.getChatUserInfoByUserIds(idListReqVO);
    }

}
