package com.ykl.med.app.controller.nutritional;

import com.ykl.med.app.handler.ResponseCode;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.mix.api.recommend.MixRecommendFeign;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.tag.api.RuleClassFeign;
import com.ykl.med.tag.entity.dto.RecommendQueryDTO;
import com.ykl.med.tag.entity.dto.RuleClassQueryDTO;
import com.ykl.med.tag.entity.vo.RecommendVO;
import com.ykl.med.tag.entity.vo.RuleClassVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Tag(name = "推荐服务")
@RestController
@RequestMapping("/recommend")
@Validated
@EnableCommonResponseAdvice
public class RecommendController {

    @Resource
    private MixRecommendFeign mixRecommendFeign;

    @Resource
    private RuleClassFeign ruleClassFeign;

    @Resource
    private PatientUserFeign patientUserFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<RecommendVO> query(@RequestBody RecommendQueryDTO requestBody){
        // 获取患者ID
        Long patientId = this.getPatientId(requestBody.getCurrentUserId());
        if ( patientId == -1 ) {
            throw new ServiceException(ResponseCode.PATIENT_NOT_EXIST.getCode(),ResponseCode.PATIENT_NOT_EXIST.getMessage());
        }
        requestBody.setPatientId(String.valueOf(patientId));

        // 推荐规则业务类型列表
        List<String> bizCategory = new ArrayList<>();
        bizCategory.add("EDU");// 只返回推荐的患教数据
        requestBody.setBizCategory(bizCategory);

        return mixRecommendFeign.query(requestBody);
    }

    @PostMapping("/queryRuleClass")
    @Operation(summary = "查询规则类别列表", description = "返回列表数据")
    public PageResult<RuleClassVO> queryRuleClass(@RequestBody RuleClassQueryDTO requestBody){
        // 获取患者ID
        Long patientId = this.getPatientId(requestBody.getCurrentUserId());
        if ( patientId == -1 ) {
            throw new ServiceException(ResponseCode.PATIENT_NOT_EXIST.getCode(),ResponseCode.PATIENT_NOT_EXIST.getMessage());
        }

        return ruleClassFeign.query(requestBody);
    }

    /**
     * 获取患者ID
     * */
    public Long getPatientId(Long userId) {
        PatientVO patientVO = patientUserFeign.getPatientByUserId(userId);
        if ( (patientVO == null) || patientVO.getPatientId() == null || patientVO.getPatientId() < 1 ){
            return -1L;
        }

        return patientVO.getPatientId();
    }
}
