package com.ykl.med.app.controller.symptom;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.app.handler.CheckPatientMemberVersionPermission;
import com.ykl.med.app.service.PatientService;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.patient.enums.MemberVersionPurviewType;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskOperationType;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.symptoms.api.records.SymptomFeign;
import com.ykl.med.symptoms.api.records.SymptomRecordsFeign;
import com.ykl.med.symptoms.api.scale.ExecSymptomScaleFeign;
import com.ykl.med.symptoms.api.scale.SymptomScaleFeign;
import com.ykl.med.symptoms.vo.records.*;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleAddVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleQueryVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleVO;
import com.ykl.med.symptoms.vo.scale.SymptomScaleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Tag(name = "症状管理服务")
@RestController
@RequestMapping("/symptom")
@Slf4j
@EnableCommonResponseAdvice
public class SymptomController {

    @Resource
    private SymptomFeign symptomFeign;

    @Resource
    private SymptomScaleFeign symptomScaleFeign;

    @Resource
    private ExecSymptomScaleFeign execSymptomScaleFeign;

    @Resource
    private SymptomRecordsFeign symptomRecordsFeign;

    @Resource
    private PatientService patientService;

    @Resource
    private EventTaskFeign eventTaskFeign;

    @PostMapping("/query")
    @Operation(summary = "查询症状列表(V1.2)", description = "症状名称重新取")
    @CheckPatientMemberVersionPermission(MemberVersionPurviewType.SYMPTOM_MANAGEMENT)
    public PageResult<SymptomVO> query(@RequestBody SymptomQueryVO queryVO) {
        queryVO.setPatientId(patientService.getCurrentPatientId());
        return symptomFeign.query(queryVO);
    }

    @PostMapping("/add")
    @Operation(summary = "增加症状数据(V1.2)", description = "请求的数据来源很多变了")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "增加症状数据",module = "症状管理")
    public void add(@RequestBody SymptomAddVO addVO) {
        addVO.setPatientId(patientService.getCurrentPatientId());
        symptomFeign.add(addVO);
    }

    @PostMapping("/getById")
    @Operation(summary = "查询症状详情")
    public SymptomVO getById(@RequestBody IdReqVO idReqVO) {
        return symptomFeign.getById(idReqVO.getId());
    }

    @PostMapping("/update")
    @Operation(summary = "症状消失")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "症状消失",module = "症状管理")
    public void update(@RequestBody IdReqVO requestBody) {
        symptomFeign.stopSymptom(requestBody.getId());

        SymptomVO symptomVO = symptomFeign.getById(requestBody.getId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("changeBiz", symptomVO.getSymptomName());
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setExtJson(jsonObject)
                .setPatientId(patientService.getCurrentPatientId())
                .setEventTime(LocalDateTime.now())
                .setOperation(EventTaskOperationType.DELETE)
                .setBizType(EventTaskType.PATIENT_SYMPTOM_CHANGE)
                .setRequestId(UUID.randomUUID().toString())
                .setBizId(symptomVO.getId().toString())
                .setUserId(AuthContextHolder.getInstance().getContext().getId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @PostMapping("/querySymptomRecords")
    @Operation(summary = "查询症状记录(V1.2)")
    public PageResult<SymptomRecordsVO> querySymptomRecords(@RequestBody SymptomRecordsQueryVO queryVO) {
        return symptomRecordsFeign.query(queryVO);
    }

    @PostMapping("/deleteSymptomRecords")
    @Operation(summary = "删除症状记录")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除症状记录",module = "症状管理")
    public void deleteSymptomRecords(@RequestBody IdReqVO idReqVO) {
        symptomRecordsFeign.delete(idReqVO.getId());
    }


    @PostMapping("/querySymptomScale")
    @Operation(summary = "查询症状量表(V1.2)", description = "返回结构有修改，主要是加了isExecuted字段，以及去除了多余的层级结构")
    public SymptomScaleVO querySymptomScale() {
        return symptomScaleFeign.getCurrentSymptomScale(patientService.getCurrentPatientId());
    }

    @PostMapping("/queryExecSymptomScale")
    @Operation(summary = "查询症状量表执行列表")
    public PageResult<ExecSymptomScaleVO> queryExecSymptomScale(@RequestBody ExecSymptomScaleQueryVO queryVO) {
        queryVO.setPatientId(patientService.getCurrentPatientId());
        return execSymptomScaleFeign.query(queryVO);
    }

    @PostMapping("/getDetailById")
    @Operation(summary = "查询症状量表执行详情")
    public ExecSymptomScaleVO getDetailById(@RequestBody IdReqVO idReqVO) {
        return execSymptomScaleFeign.getDetailById(idReqVO.getId());
    }


    @PostMapping("/updateExecSymptomScale")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "更新症状量表执行信息",module = "症状管理")
    @Operation(summary = "更新症状量表执行信息", description = "")
    public void updateExecSymptomScale(@RequestBody ExecSymptomScaleAddVO addVO) {
        addVO.setPatientId(patientService.getCurrentPatientId());
        addVO.setIsPatient(true);
        symptomScaleFeign.execSymptomScale(addVO);
    }

    @PostMapping("/updateExecSymptomScaleV2")
    @Operation(summary = "更新症状量表执行信息V2", description = "")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "更新症状量表执行信息",module = "症状管理")
    public void updateExecSymptomScaleV2(@RequestBody ExecSymptomScaleAddVO addVO) {
        addVO.setPatientId(patientService.getCurrentPatientId());
        addVO.setIsPatient(true);
        if (CollectionUtils.isNotEmpty(addVO.getSymptomAddVOS())){
            for (SymptomAddVO symptomAddVO : addVO.getSymptomAddVOS()){
                symptomAddVO.setPatientId(patientService.getCurrentPatientId());
                symptomAddVO.setCurrentUserId(AuthContextHolder.getInstance().getContext().getId());
                symptomAddVO.setStartTime(symptomAddVO.getStartTime()!=null ? symptomAddVO.getStartTime() : LocalDateTime.now());
            }
        }
        if (CollectionUtils.isNotEmpty(addVO.getExtraSymptoms())){
            for (SymptomAddVO symptomAddVO : addVO.getExtraSymptoms()){
                symptomAddVO.setPatientId(patientService.getCurrentPatientId());
                symptomAddVO.setCurrentUserId(AuthContextHolder.getInstance().getContext().getId());
                symptomAddVO.setStartTime(symptomAddVO.getStartTime()!=null ? symptomAddVO.getStartTime() : LocalDateTime.now());
            }
        }
        symptomScaleFeign.execSymptomScale(addVO);
    }


    @PostMapping("/getPainTotal")
    @Operation(summary = "查询疼痛症状列表")
    public SymptomPainTotalVO getPainTotal() {
        return symptomFeign.getPainTotal(patientService.getCurrentPatientId());
    }


    @PostMapping("/queryChart")
    @Operation(summary = "查询症状图表")
    public List<SymptomChartVO> queryChart(@RequestBody SymptomChartQueryVO symptomChartQueryVO) {
        symptomChartQueryVO.setPatientId(patientService.getCurrentPatientId());
        return symptomFeign.queryChart(symptomChartQueryVO);
    }

    @PostMapping("/queryPainRecord")
    @Operation(summary = "查询疼痛记录")
    public PageResult<SymptomPainRecordsVO> queryPainRecord(@RequestBody PageParam pageParam) {
        return symptomFeign.queryPainRecord(patientService.getCurrentPatientId(), pageParam);
    }
}
