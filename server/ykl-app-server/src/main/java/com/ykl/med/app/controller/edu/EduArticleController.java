package com.ykl.med.app.controller.edu;

import com.ykl.med.app.handler.CheckPatientMemberVersionPermission;
import com.ykl.med.app.service.PatientService;
import com.ykl.med.app.vo.AppEduArticleDetailReqVO;
import com.ykl.med.app.vo.EduArticleQueryGroupVO;
import com.ykl.med.app.vo.EduMessageUpdateReadStatusReqVO;
import com.ykl.med.app.vo.TranscribeResultVO;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.edu.api.*;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryVO;
import com.ykl.med.edu.vo.column.EduArticleColumnVO;
import com.ykl.med.edu.vo.req.EduArticleAppQueryVO;
import com.ykl.med.edu.vo.req.EduArticleMessageQueryVO;
import com.ykl.med.edu.vo.req.UserCourseRecordReqVO;
import com.ykl.med.edu.vo.resp.*;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.mix.api.edu.MixEduFeign;
import com.ykl.med.patient.api.PatientCollectionFeign;
import com.ykl.med.patient.enums.MemberVersionPurviewType;
import com.ykl.med.patient.enums.PatientCollectionType;
import com.ykl.med.patient.vo.PatientCollectionListVO;
import com.ykl.med.patient.vo.PatientCollectionQueryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/eduArticle")
@Tag(name = "患教中心")
@EnableCommonResponseAdvice
public class EduArticleController {
    @Resource
    private EduArticleFeign eduArticleFeign;
    @Resource
    private EduArticleMessageFeign eduArticleMessageFeign;
    @Resource
    private EduCategoryFeign eduCategoryFeign;
    @Resource
    private PatientCollectionFeign patientCollectionFeign;
    @Resource
    private PatientService patientService;
    @Resource
    private MixEduFeign mixEduFeign;

    @PostMapping("/queryArticle")
    @Operation(summary = "查询患教文章列表")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public PageResult<EduArticleAppListVO> queryArticle(@RequestBody EduArticleAppQueryVO queryVO) {
        return eduArticleFeign.queryArticle(queryVO);
    }


    @PostMapping("/getPatientCollection")
    @Operation(summary = "查询我的收藏患教文章列表")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public PageResult<EduArticleCollectionAppListVO> getPatientCollection(@RequestBody PatientCollectionQueryVO patientCollectionQueryVO) {
        patientCollectionQueryVO.setType(PatientCollectionType.EDU);
        PageResult<PatientCollectionListVO> pageResult = patientCollectionFeign.getPatientCollection(patientCollectionQueryVO);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
            return new PageResult<>();
        }
        Map<Long, PatientCollectionListVO> map = pageResult.getList().stream().collect(Collectors.toMap(e -> Long.valueOf(e.getOutBizId()), e -> e));
        List<Long> articleIds = pageResult.getList().stream().map(e -> Long.valueOf(e.getOutBizId())).collect(Collectors.toList());
        List<EduArticleVO> list = eduArticleFeign.getArticleList(articleIds);
        if (CollectionUtils.isEmpty(list)) {
            return new PageResult<>();
        }
        List<EduArticleCollectionAppListVO> eduArticleCollectionListVOS = new ArrayList<>();
        for (EduArticleVO eduArticleAppListVO : list) {
            EduArticleCollectionAppListVO eduArticleCollectionListVO = CopyPropertiesUtil.normalCopyProperties(eduArticleAppListVO, EduArticleCollectionAppListVO.class);
            eduArticleCollectionListVO.setCollectionTime(map.get(eduArticleAppListVO.getId()).getCreateTime());
            eduArticleCollectionListVOS.add(eduArticleCollectionListVO);
        }
        return new PageResult<>(eduArticleCollectionListVOS, pageResult.getTotal());
    }

    //这个接口是给 web 端 代理 app 端使用的，用webAndApp标记下
    @PostMapping("/getAppDetail/webAndApp")
    @Operation(summary = "查询患教文章详情")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public EduArticleAppDetailUserVO getAppDetail(@RequestBody AppEduArticleDetailReqVO reqVO) {
        eduArticleFeign.viewCountAdd(reqVO.getId());
        return eduArticleFeign.getAppDetail(reqVO.getId(), reqVO.getCurrentUserId(), reqVO.getCurrentPatientId());
    }

    //这个接口是给 web 端 代理 app 端使用的，用webAndApp标记下
    @PostMapping("/getAppDetailPreview/webAndApp")
    @Operation(summary = "预览")
    public EduArticleAppDetailVO getAppDetailPreview(@RequestBody IdReqVO reqVO) {
        eduArticleFeign.viewCountAdd(reqVO.getId());
        return eduArticleFeign.getAppDetailPreview(reqVO.getId());
    }

    @PostMapping("/courseViewCountAdd/webAndApp")
    @Operation(summary = "课程观看次数增加(废弃)")
    public void courseViewCountAdd(@RequestBody UserCourseRecordReqVO reqVO) {
    }

    @PostMapping("articleMessage/pageByPatientId")
    @Operation(summary = "我的患教、历史推送")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public PageResult<EduArticleMineAppListVO> pageByPatientId(@RequestBody EduArticleMessageQueryVO queryVO) {
        return eduArticleMessageFeign.pageByPatientId(queryVO);
    }

    @PostMapping("homePageTopEduArticle")
    @Operation(summary = "首页top患教")
    public List<HomePageTopEduCourseVO> homePageTopEduArticle() {
        return eduArticleMessageFeign.homePageTopEduArticle(patientService.getCurrentPatientId(), AuthContextHolder.getInstance().getContext().getId());
    }

    @PostMapping("updateReadStatus/webAndApp")
    @Operation(summary = "更新患教已读状态")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public void updateReadStatus(@RequestBody EduMessageUpdateReadStatusReqVO reqVO) {
        eduArticleMessageFeign.updateReadStatus(reqVO.getCurrentUserId(), reqVO.getArticleIds());
    }

    //这个接口是给 web 端 代理 app 端使用的，用webAndApp标记下
    @PostMapping("courseRecord/webAndApp")
    @Operation(summary = "保存或更新患教消息阅读记录（废弃）")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public void courseRecord(@RequestBody UserCourseRecordReqVO reqVO) {
    }

    //todo 废弃
    @PostMapping("/getArticleQueryGroup")
    @Operation(summary = "患教查询分组聚合对象")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public EduArticleQueryGroupVO getArticleQueryGroup(@RequestBody EduArticleAppQueryVO queryVO) {
        return null;
    }

    //todo 废弃
    @PostMapping("/getAllCategory")
    @Operation(summary = "获取所有分类")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public List<EduCategoryVO> getAllCategory() {
        return eduCategoryFeign.getAllCategory();
    }

    @Operation(summary = "搜索分类")
    @PostMapping("/queryArticleCategory")
    public List<EduCategoryVO> queryArticleCategory(@RequestBody EduArticleAppQueryVO queryVO) {
        return eduArticleFeign.queryArticleCategory(queryVO);
    }

    @PostMapping("/queryEduSubCategory")
    @Operation(summary = "搜索子分类")
    public List<EduSubCategoryVO> queryEduSubCategory(@RequestBody EduArticleAppQueryVO queryVO) {
        return eduArticleFeign.queryEduSubCategory(queryVO);
    }

    @PostMapping("/queryEduColumn")
    @Operation(summary = "搜索专栏")
    public List<EduArticleColumnVO> queryEduColumn(@RequestBody EduArticleAppQueryVO queryVO) {
        return eduArticleFeign.queryEduColumn(queryVO);
    }

    @PostMapping("/queryBrowseHistory")
    @Operation(summary = "历史浏览")
    @CheckPatientMemberVersionPermission(value = {MemberVersionPurviewType.HEALTH_EDUCATION})
    public PageResult<EduArticleBrowseHistoryAppListVO> queryBrowseHistory(@RequestBody PageParam pageParam) {
        return eduArticleMessageFeign.queryBrowseHistory(AuthContextHolder.getInstance().getContext().getId(), pageParam);
    }

    @PostMapping("/transcribe")
    @Operation(summary = "转录")
    public TranscribeResultVO transcribe(@RequestParam("file") MultipartFile file) {
        return new TranscribeResultVO().setResult(mixEduFeign.transcribe(file));
    }

}