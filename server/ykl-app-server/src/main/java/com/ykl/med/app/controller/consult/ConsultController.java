package com.ykl.med.app.controller.consult;

import com.ykl.med.app.service.PatientService;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.livekit.feign.LivekitFeign;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.mix.api.consult.MixConsulFeign;
import com.ykl.med.mix.vo.consult.MixConsultVO;
import com.ykl.med.shift.api.ConsultFeign;
import com.ykl.med.shift.vo.req.QueryConsultPageAppReqVO;
import com.ykl.med.shift.vo.req.SaveCommentReqVO;
import com.ykl.med.shift.vo.req.SaveDiseaseHistoryReqVO;
import com.ykl.med.shift.vo.resp.ConsultAppPageVO;
import com.ykl.med.shift.vo.resp.ConsultDiseaseHistoryVO;
import com.ykl.med.shift.vo.resp.ConsultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 线上问诊
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
@Tag(name = "线上问诊")
@RestController
@RequestMapping("/consult")
@EnableCommonResponseAdvice
public class ConsultController {

    @Resource
    private ConsultFeign consultFeign;
    @Resource
    private MixConsulFeign mixConsulFeign;
    @Resource
    private PatientService patientService;
    @Resource
    private LivekitFeign livekitFeign;

    @Operation(summary = "问诊-列表")
    @PostMapping("/page/app")
    public PageResult<ConsultAppPageVO> pageApp(@Valid @RequestBody QueryConsultPageAppReqVO param) {
        param.setPatientId(patientService.getCurrentPatientId());
        return consultFeign.pageApp(param);
    }

    @Operation(summary = "问诊-详情")
    @PostMapping("/details")
    public MixConsultVO details(@RequestParam(name = "consultId") Long consultId) {
        return mixConsulFeign.details(consultId);
    }

    @Operation(summary = "问诊-详情")
    @PostMapping("/details/orderCode")
    public ConsultVO getByOrderCode(@RequestParam(name = "orderCode") String orderCode) {
        return consultFeign.getByOrderCode(orderCode);
    }

    @Operation(summary = "问诊-病史详情")
    @GetMapping("/diseaseHistory")
    public ConsultDiseaseHistoryVO diseaseHistory(@RequestParam(name = "consultId") Long consultId) {
        return consultFeign.diseaseHistory(consultId);
    }

    @Operation(summary = "问诊-填写电子病历")
    @PostMapping("/saveDiseaseHistory")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改问诊病史信息", module = "线上门诊")
    public void saveDiseaseHistory(@Valid @RequestBody SaveDiseaseHistoryReqVO param) {
        mixConsulFeign.saveDiseaseHistory(param);
    }

    @Operation(summary = "问诊-评价")
    @PostMapping("/saveComment")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改问诊评价信息", module = "线上门诊")
    public void saveComment(@Valid @RequestBody SaveCommentReqVO param) {
        param.setPatientId(patientService.getCurrentPatientId());
        consultFeign.saveComment(param);
    }

    @Operation(summary = "获取当前用户的视频信息,没有房间信息返回null")
    @PostMapping("/livekit/getRoomInfoByUserId")
    public RoomCreateRespVO getRoomInfoByUserId() {
        return livekitFeign.getRoomInfoByUserId(AuthContextHolder.getInstance().getContext().getId());
    }

    @Operation(summary = "问诊-结束视频")
    @PostMapping("/closeVideo")
    public void closeVideo(@RequestParam(name = "consultId") Long consultId,
                           @RequestParam(name = "joinFlag") Boolean joinFlag) {
        mixConsulFeign.closeVideo(consultId, joinFlag, AuthContextHolder.getInstance().getContext().getId());
    }


}
