package com.ykl.med.app.controller.medical;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.medical.api.ExecMedicalOrderFeign;
import com.ykl.med.medical.vo.execOrder.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Tag(name = "执行用药服务")
@RestController
@RequestMapping("/execMedicalOrder")
@Slf4j
@EnableCommonResponseAdvice
public class ExecMedicalOrderController {
    @Resource
    private ExecMedicalOrderFeign execMedicalOrderFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public List<ExecMedicalOrderDetailVO> query(@RequestBody ExecMedicalOrderQueryAppVO queryAppVO) {
        ExecMedicalOrderQueryVO queryVO = new ExecMedicalOrderQueryVO();
        queryVO.setMedicalOrderIds(Lists.newArrayList(queryAppVO.getMedicalOrderId()));
        return execMedicalOrderFeign.queryDetail(queryVO);
    }

    @Operation(summary = "查询列表(分页)")
    @PostMapping("/queryPage")
    public PageResult<ExecMedicalOrderDetailVO> queryPage(@RequestBody ExecMedicalOrderQueryAppVO appVO) {
        ExecMedicalOrderQueryVO queryVO = new ExecMedicalOrderQueryVO();
        queryVO.setMedicalOrderIds(Lists.newArrayList(appVO.getMedicalOrderId()));
        queryVO.setPageNo(appVO.getPageNo());
        queryVO.setPageSize(appVO.getPageSize());
        return execMedicalOrderFeign.queryPage(queryVO);
    }

    @PostMapping("/update")
    @Operation(summary = "更新信息")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "更新用药执行记录",module = "用药管理")
    public ExecMedicalOrderDetailVO update(@RequestBody ExecMedicalOrderReqVO execMedicalOrderReqVO) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime execTime = LocalDateTime.parse(execMedicalOrderReqVO.getExecTime(), formatter);
        return execMedicalOrderFeign.exec(execMedicalOrderReqVO.getId(), execTime);
    }

    @PostMapping("/nextExec")
    @Operation(summary = "获取下一个打卡点，请求参数为用药id")
    public ExecMedicalOrderVO nextExec(@RequestParam(value = "medicalOrderId") Long medicalOrderId) {
        return execMedicalOrderFeign.nextExec(medicalOrderId);
    }
}
