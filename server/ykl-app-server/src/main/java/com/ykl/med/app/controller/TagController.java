package com.ykl.med.app.controller;

import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.tag.api.TagNewFeign;
import com.ykl.med.tag.entity.dto.TagQueryDTO;
import com.ykl.med.tag.entity.vo.TagVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "标签服务")
@RestController
@RequestMapping("/tag")
@Validated
@EnableCommonResponseAdvice
public class TagController {

    @Resource
    private TagNewFeign apiFeign;

    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<TagVO> query(@RequestBody TagQueryDTO requestBody){
        return apiFeign.query(requestBody);
    }
}
