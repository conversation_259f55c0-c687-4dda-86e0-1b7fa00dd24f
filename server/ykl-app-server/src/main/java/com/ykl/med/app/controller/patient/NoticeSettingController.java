package com.ykl.med.app.controller.patient;

import com.ykl.med.app.service.PatientService;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.user.api.NoticeSettingFeign;
import com.ykl.med.user.vo.req.UpdateNoticeSettingReqVO;
import com.ykl.med.user.vo.resp.NoticeSettingVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2024/8/28
 */
@Tag(name = "通知设置")
@RestController
@RequestMapping("/user/notice/setting")
@EnableCommonResponseAdvice
public class NoticeSettingController {

    @Resource
    private PatientService patientService;
    @Resource
    private NoticeSettingFeign noticeSettingFeign;

    @PostMapping("/getByPatientId")
    @Operation(summary = "查询")
    public NoticeSettingVO getByPatientId() {
        return noticeSettingFeign.getByPatientId(patientService.getCurrentPatientId());
    }

    @PostMapping("/update")
    @Operation(summary = "更新")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "通知设置")
    public void update(@Valid @RequestBody UpdateNoticeSettingReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        noticeSettingFeign.update(reqVO);
    }


}

