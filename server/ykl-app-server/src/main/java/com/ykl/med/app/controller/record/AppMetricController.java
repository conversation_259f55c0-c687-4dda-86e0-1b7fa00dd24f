package com.ykl.med.app.controller.record;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Sets;
import com.ykl.med.app.service.PatientService;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskOperationType;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.records.api.MetricFeign;
import com.ykl.med.records.vo.FollowedMetricStatisticsVO;
import com.ykl.med.records.vo.MetricDataVO;
import com.ykl.med.records.vo.MetricGoalVO;
import com.ykl.med.records.vo.req.*;
import com.ykl.med.records.vo.resp.MetricDefinitionRespVO;
import com.ykl.med.records.vo.resp.MetricStatRespVO;
import com.ykl.med.records.vo.resp.MultiMetricDataListRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 指标管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping
@Tag(name = "指标管理")
@RequiredArgsConstructor
@EnableCommonResponseAdvice
public class AppMetricController {

    private final MetricFeign metricFeign;

    private final PatientService patientService;

    private final EventTaskFeign eventTaskFeign;

    @PostMapping("/metric/followedDefinition")
    @Operation(summary = "查询关注的指标定义")
    public List<MetricDefinitionRespVO> followedDefinition(@RequestBody FollowedMetricDefinitionReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        return metricFeign.followedDefinition(reqVO);
    }

    @PostMapping("/metric/allDefinition")
    @Operation(summary = "查询所有指标定义")
    public List<MetricDefinitionRespVO> allDefinition(@RequestBody MetricDefinitionReqVO reqVO) {
        return metricFeign.allDefinition(reqVO);
    }

    @PostMapping("/metric/list")
    @Operation(summary = "查询指标")
    public List<MetricDataVO> listFollowed(@RequestBody MetricListReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        switch (reqVO.getType()) {
            case FOLLOWED:
                return metricFeign.listFollowed(reqVO);
            case EXCEPTION:
                return metricFeign.listException(reqVO);
            case OTHER:
                return metricFeign.listOther(reqVO);
            case RECORDED:
                return metricFeign.listRecorded(reqVO);
            case RECORDED_DAILY_MONITOR:
                return metricFeign.listRecordedDailyMonitor(reqVO);
            default:
                throw new IllegalArgumentException("不支持的指标类型");
        }
    }

    @PostMapping("/metric/setFollowedMetrics")
    @Operation(summary = "设置关注指标")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "设置关注指标",module = "病历档案")
    public void setFollowedMetrics(@RequestBody RecordsMetricsFollowedSaveReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        metricFeign.setFollowedMetrics(reqVO);
    }

    @PostMapping("/metric/record")
    @Operation(summary = "记录指标")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "记录指标",module = "病历档案")
    public void record(@RequestBody MetricRecordReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        metricFeign.record(reqVO);

        Map<String, MetricDefinitionRespVO.Metric> definitions = metricFeign.allDefinition(new MetricDefinitionReqVO()).stream()
                .map(MetricDefinitionRespVO::getMetrics).flatMap(List::stream)
                .collect(Collectors.toMap(
                        metric -> metric.getExaminationTestProjectId() + "," + metric.getSpecimen(),
                        Function.identity(),
                        (o, n) -> n)
                );

        Set<String> searchKeys = reqVO.getMetricsList().stream()
                .map(metric -> metric.getExaminationTestProjectId() + "," + metric.getSpecimen())
                .collect(Collectors.toSet());

        Set<String> metricNames = Sets.newHashSet();
        searchKeys.forEach(searchKey -> metricNames.add(definitions.get(searchKey).getName()));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("changeBiz", Joiner.on(",").join(metricNames));
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setExtJson(jsonObject)
                .setRequestId(UUID.randomUUID().toString())
                .setEventTime(LocalDateTime.now())
                .setBizType(EventTaskType.PATIENT_METRIC_CHANGE)
                .setUserId(AuthContextHolder.getInstance().getContext().getId())
                .setPatientId(patientService.getCurrentPatientId())
                .setOperation(EventTaskOperationType.ADD)
                .setBizId(String.valueOf(reqVO.getMetricsList().stream().findFirst().map(MetricRecordReqVO.Metrics::getId).orElse(0L)));
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @PostMapping("/metric/stat")
    @Operation(summary = "统计指标信息")
    public MetricStatRespVO stat(@RequestBody MetricStatReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        return metricFeign.stat(reqVO);
    }

    @PostMapping("/metric/unfollowMetric")
    @Operation(summary = "取消关注指标")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "取消关注指标",module = "病历档案")
    public void unfollowMetric(@RequestBody UnfollowMetricReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        metricFeign.unfollowMetric(reqVO);
    }

    @PostMapping("/metric/followMetric")
    @Operation(summary = "关注指标")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "关注指标",module = "病历档案")
    public void followMetric(@RequestBody PatientMetricReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        metricFeign.followMetric(reqVO);
    }

    @PostMapping("/metric/data/delete")
    @Operation(summary = "删除指标数据")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除指标数据",module = "病历档案")
    public void delete(@RequestParam("metricDataId") Long metricDataId) {
        MetricDataVO metricDataVO = metricFeign.detail(metricDataId);
        metricFeign.delete(metricDataId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("changeBiz", metricDataVO.getName());
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setExtJson(jsonObject)
                .setRequestId(UUID.randomUUID().toString())
                .setEventTime(LocalDateTime.now())
                .setBizType(EventTaskType.PATIENT_METRIC_CHANGE)
                .setUserId(AuthContextHolder.getInstance().getContext().getId())
                .setPatientId(patientService.getCurrentPatientId())
                .setOperation(EventTaskOperationType.DELETE)
                .setBizId(String.valueOf(metricDataId));
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @PostMapping("/metric/data/list")
    @Operation(summary = "查询指标数据")
    public List<MetricDataVO> listData(@RequestBody MetricDataListReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        return metricFeign.listData(reqVO);
    }

    @PostMapping("/metric/data/multiList")
    @Operation(summary = "批量查询指标数据")
    public List<MultiMetricDataListRespVO> multiListData(@RequestBody MultiMetricDataListReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        return metricFeign.multiListData(reqVO);
    }

    @PostMapping("/metric/goal/saveOrUpdate")
    @Operation(summary = "保存或更新指标目标值")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "保存或更新指标目标值",module = "病历档案")
    public void saveOrUpdateGoal(@RequestBody MetricGoalVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        metricFeign.saveOrUpdateGoal(reqVO);
    }

    @PostMapping("/metric/goal/detail")
    @Operation(summary = "查询指标目标值")
    public MetricGoalVO detailGoal(@RequestBody PatientMetricReqVO reqVO) {
        reqVO.setPatientId(patientService.getCurrentPatientId());
        return metricFeign.detailGoal(reqVO);
    }

    @PostMapping("/followed/metric/statistics")
    @Operation(summary = "关注指标统计-首页折线图")
    public List<FollowedMetricStatisticsVO> followedMetricStatistics() {
        return metricFeign.followedMetricStatistics(patientService.getCurrentPatientId());
    }
}