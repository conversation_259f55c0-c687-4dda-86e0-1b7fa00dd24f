package com.ykl.med.app.controller.message;

import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.push.api.UserComplaintFeign;
import com.ykl.med.push.vo.complaint.UserComplaintQueryVO;
import com.ykl.med.push.vo.complaint.UserComplaintReqVO;
import com.ykl.med.push.vo.complaint.UserComplaintVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/complaint")
@Tag(name = "投诉/举报/表扬")
@EnableCommonResponseAdvice
public class UserComplaintController {
    @Resource
    private UserComplaintFeign userComplaintFeign;

    @PostMapping("/add")
    @Operation(summary = "新增投诉/举报/表扬")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "新增投诉/举报/表扬")
    public void addUserComplaint(@RequestBody @Valid UserComplaintReqVO reqVO) {
        userComplaintFeign.addUserComplaint(reqVO);
    }

    @PostMapping("/queryApp")
    @Operation(summary = "查询投诉")
    public PageResult<UserComplaintVO> queryUserComplaintApp(@RequestBody @Valid UserComplaintQueryVO reqVO) {
        reqVO.setUserId(AuthContextHolder.getInstance().getContext().getId());
        return userComplaintFeign.queryUserComplaint(reqVO);
    }
}