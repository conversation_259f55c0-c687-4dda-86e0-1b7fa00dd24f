package com.ykl.med.app.controller.symptom;

import com.ykl.med.app.service.PatientService;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.symptoms.api.wound.PatientWoundCareFeign;
import com.ykl.med.symptoms.vo.wound.PatientWoundCareAddVO;
import com.ykl.med.symptoms.vo.wound.PatientWoundCareQueryVO;
import com.ykl.med.symptoms.vo.wound.PatientWoundCareVO;
import com.ykl.med.symptoms.vo.wound.PatientWoundVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "伤口护理")
@RestController
@RequestMapping("woundCare")
@EnableCommonResponseAdvice
public class PatientWoundCareController {

    @Resource
    private PatientWoundCareFeign patientWoundCareFeign;
    @Resource
    private PatientService patientService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存或更新病患伤口护理记录")
    @OperateLog(eventType = LogConstants.LogEventType.ADD_OR_MODIFY, eventContent = "保存或更新病患伤口护理记录", module = "症状管理")
    public void saveOrUpdate(@RequestBody PatientWoundCareAddVO addVO) {
        addVO.setPatientId(patientService.getCurrentPatientId());
        patientWoundCareFeign.saveOrUpdate(addVO);
    }

    @PostMapping("/query")
    @Operation(summary = "查询病患伤口护理记录")
    public PageResult<PatientWoundCareVO> query(@RequestBody PatientWoundCareQueryVO queryVO) {
        queryVO.setPatientId(patientService.getCurrentPatientId());
        return patientWoundCareFeign.query(queryVO);
    }

    @PostMapping("/getWoundList")
    @Operation(summary = "获取病患伤口列表")
    public List<PatientWoundVO> getWoundList() {
        return patientWoundCareFeign.getWoundList(patientService.getCurrentPatientId());
    }

    @PostMapping("/deleteWound")
    @Operation(summary = "删除伤口")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除伤口", module = "症状管理")
    public void deleteWound(@RequestBody IdReqVO reqVO) {
        patientWoundCareFeign.deleteWound(reqVO.getId());
    }
}
