package com.ykl.med.app.controller.rehab;

import com.ykl.med.app.service.PatientService;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.patient.PatientSimpleVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.push.api.ToDoMessageFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.rehab.api.SportRehabFeign;
import com.ykl.med.rehab.api.SportRehabSurveySnapshotFeign;
import com.ykl.med.rehab.vo.req.RehabSurveySnapshotCompleteReqVO;
import com.ykl.med.rehab.vo.req.SurveySnapshotListReqVO;
import com.ykl.med.rehab.vo.req.sport.SportRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabSurveySnapshotVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@RestController
@Tag(name = "康复管理 - 运动康复调查快照")
@RequestMapping("/sportRehab/surveySnapshot")
@EnableCommonResponseAdvice
public class AppSportRehabSurveySnapshotController {

    @Resource
    private SportRehabSurveySnapshotFeign sportRehabSurveySnapshotFeign;

    @Resource
    private PatientUserFeign patientUserFeign;

    @Resource
    private EventTaskFeign eventTaskFeign;
    @Resource
    private PatientService patientService;

    @Resource
    private ToDoMessageFeign toDoMessageFeign;

    @Resource
    private MessageFeign messageFeign;
    @Resource
    private SportRehabFeign sportRehabFeign;
    @Resource
    private PushSocketFeign pushSocketFeign;

    @PostMapping("/listAll")
    @Operation(summary = "获取线下问卷快照列表", description = "返回线下问卷快照列表")
    public List<SportRehabSurveySnapshotVO> listAll() {
        PatientSimpleVO patientSimpleVO = patientUserFeign
                .getPatientSimpleByUserId(AuthContextHolder.getInstance().getContext().getId());
        return sportRehabSurveySnapshotFeign.list(new SurveySnapshotListReqVO()
                .setPatientId(patientSimpleVO.getPatientId()));
    }

    @PostMapping("/details")
    @Operation(summary = "问卷详情")
    public SportRehabSurveySnapshotVO details(@RequestParam(name = "id") Long id) {
        return sportRehabSurveySnapshotFeign.details(id);
    }

    @PostMapping("/complete")
    @Operation(summary = "完成调查快照", description = "完成调查快照")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "运动康复调查快照完成", module = "运动康复")
    public void complete(@RequestBody RehabSurveySnapshotCompleteReqVO reqVO) {
        sportRehabSurveySnapshotFeign.complete(reqVO);
        SportRehabSurveySnapshotVO details = sportRehabSurveySnapshotFeign.details(reqVO.getId());
        SportRehabPlanSaveOrUpdateReqVO basePlanReq = sportRehabFeign.getBasePlanReq(details.getPatientId());
        if (basePlanReq != null) {
            EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(details.getPatientId())
                    .setRequestId(UUID.randomUUID().toString())
                    .setExecuteTime(LocalDateTime.now().plusMinutes(5))
                    .setBizId(details.getPatientId().toString())
                    .setBizType(EventTaskType.SPORTS_REHAB_BASE_PLAN);
            eventTaskFeign.addEventTask(eventTaskAddVO);
        }
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setRequestId(reqVO.getId().toString())
                .setEventTime(LocalDateTime.now())
                .setBizType(EventTaskType.PATIENT_FINISH_SPORTS_FORM)
                .setUserId(AuthContextHolder.getInstance().getContext().getId())
                .setPatientId(patientService.getCurrentPatientId())
                .setBizId(reqVO.getId().toString());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }
}