package com.ykl.med.app.controller.medical;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.app.service.PatientService;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.medical.api.AdverseReactionDrugFeign;
import com.ykl.med.medical.api.AdverseReactionFeign;
import com.ykl.med.medical.api.AdverseReactionRecordsFeign;
import com.ykl.med.medical.vo.adverse.*;
import com.ykl.med.medical.vo.data.AdverseReactionDataRelationQueryVO;
import com.ykl.med.medical.vo.data.AdverseReactionDataRelationVO;
import com.ykl.med.mix.api.medical.MixAdverseReactionDataFeign;
import com.ykl.med.mix.api.medical.MixAdverseReactionFeign;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskOperationType;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Tag(name = "不良反应服务")
@RestController
@RequestMapping("/adverseReaction")
@Slf4j
@EnableCommonResponseAdvice
public class AdverseReactionController {
    @Resource
    private AdverseReactionDrugFeign adverseReactionDrugFeign;

    @Resource
    private AdverseReactionRecordsFeign adverseReactionRecordsFeign;

    @Resource
    private AdverseReactionFeign adverseReactionFeign;

    @Resource
    private PatientService patientService;

    @Resource
    private MixAdverseReactionDataFeign mixAdverseReactionDataFeign;

    @Resource
    private EventTaskFeign eventTaskFeign;

    @Resource
    private MixAdverseReactionFeign mixAdverseReactionFeign;


    @PostMapping("/query")
    @Operation(summary = "查询列表", description = "返回列表数据")
    public PageResult<AdverseReactionColorVO> query(@RequestBody AdverseReactionQueryVO queryVO) {
        return mixAdverseReactionFeign.queryAdverseReactionList(queryVO);
    }

    @PostMapping("/getByDrugAndPatient")
    @Operation(summary = "根据药品获取用户不良反应列表,药品id")
    public List<AdverseReactionColorVO> getByDrugAndPatient(@RequestBody IdReqVO idReqVO) {
        List<AdverseReactionDrugVO> drugVOS = adverseReactionDrugFeign.getByDrugAndPatient(idReqVO.getId(), patientService.getCurrentPatientId());
        if (CollectionUtils.isEmpty(drugVOS)) {
            return null;
        }
        return mixAdverseReactionFeign.queryByIds(drugVOS.stream().map(AdverseReactionDrugVO::getAdverseReactionId).collect(Collectors.toList()));
    }

    @PostMapping("/queryById")
    @Operation(summary = "根据ID查询不良反应", description = "返回单个数据")
    public AdverseReactionVO queryById(@RequestBody IdReqVO idReqVO) {
        return adverseReactionFeign.queryById(idReqVO.getId());
    }

    @PostMapping("/add")
    @Operation(summary = "增加不良反应")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "增加不良反应",module = "用药管理")
    public void add(@RequestBody AdverseReactionAddVO addVO) {
        addVO.setPatientId(patientService.getCurrentPatientId());
        AdverseReactionVO adverseReaction=  mixAdverseReactionFeign.add(addVO);


        JSONObject jsonObject = new JSONObject();
        jsonObject.put("changeBiz", addVO.getItemName());
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setExtJson(jsonObject)
                .setPatientId(patientService.getCurrentPatientId())
                .setEventTime(LocalDateTime.now())
                .setOperation(EventTaskOperationType.ADD)
                .setBizType(EventTaskType.PATIENT_ADVERSE_REACTION_CHANGE)
                .setRequestId(UUID.randomUUID().toString())
                .setBizId(adverseReaction.getId().toString())
                .setUserId(addVO.getCurrentUserId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @PostMapping("/update")
    @Operation(summary = "不良反应消失")
    @OperateLog(eventType = LogConstants.LogEventType.MODIFY, eventContent = "修改不良反应",module = "用药管理")
    public void adverseReactionStop(@RequestBody IdReqVO idReqVO) {
        adverseReactionFeign.adverseReactionStop(idReqVO.getId());

        AdverseReactionVO adverseReaction = adverseReactionFeign.queryById(idReqVO.getId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("changeBiz", adverseReaction.getItemName());
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setExtJson(jsonObject)
                .setPatientId(patientService.getCurrentPatientId())
                .setEventTime(LocalDateTime.now())
                .setOperation(EventTaskOperationType.DELETE)
                .setBizType(EventTaskType.PATIENT_ADVERSE_REACTION_CHANGE)
                .setRequestId(UUID.randomUUID().toString())
                .setBizId(adverseReaction.getId().toString())
                .setUserId(AuthContextHolder.getInstance().getContext().getId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @PostMapping("/queryAdverseReactionRecords")
    @Operation(summary = "查询不良反应记录", description = "根据不良反应ID查询表现记录")
    public PageResult<AdverseReactionRecordsColorVO> queryAdverseReactionRecords(@RequestBody AdverseReactionRecordsQueryVO queryVO) {
        queryVO.setPatientId(patientService.getCurrentPatientId());
        return mixAdverseReactionFeign.query(queryVO);
    }

    @PostMapping("/deleteAdverseReactionRecords")
    @Operation(summary = "删除不良反应记录")
    @OperateLog(eventType = LogConstants.LogEventType.DELETE, eventContent = "删除不良反应记录",module = "用药管理")
    public void deleteAdverseReactionRecords(@RequestBody IdReqVO idReqVO) {
        adverseReactionRecordsFeign.delete(idReqVO.getId());
    }

    @PostMapping("/queryAdverseReactionDataRelation")
    @Operation(summary = "查询不良反应基础数据 （症状疾病临床等数据）", description = "返回列表数据")
    public List<AdverseReactionDataRelationVO> queryAdverseReactionDataRelation(@RequestBody AdverseReactionDataRelationQueryVO queryVO) {
        return mixAdverseReactionDataFeign.queryAdverseReactionDataRelation(queryVO);
    }


    @PostMapping("/queryDrugAdverseReaction")
    @Operation(summary = "根据患者用药查询不良反应基础数据 （症状疾病临床等数据），药品id")
    public List<AdverseReactionDataRelationVO> queryDrugAdverseReaction(@RequestBody IdReqVO idReqVO) {
        return mixAdverseReactionDataFeign.queryPatientDrugAdverseReaction(idReqVO.getId(), patientService.getCurrentPatientId());
    }

    @PostMapping("/queryAdverseReactionDrug")
    @Operation(summary = "查询患者不良反应相关的药品列表")
    public List<AdverseReactionDrugVO> queryAdverseReactionDrug() {
        return adverseReactionDrugFeign.getByPatientId(patientService.getCurrentPatientId());
    }


}
