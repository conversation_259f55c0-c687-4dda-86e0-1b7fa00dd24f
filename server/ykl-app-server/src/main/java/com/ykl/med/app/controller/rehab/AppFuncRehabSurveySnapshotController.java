package com.ykl.med.app.controller.rehab;

import com.ykl.med.app.service.PatientService;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.patient.PatientSimpleVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.rehab.api.FuncRehabFeign;
import com.ykl.med.rehab.api.FuncRehabSurveySnapshotFeign;
import com.ykl.med.rehab.vo.req.RehabSurveySnapshotCompleteReqVO;
import com.ykl.med.rehab.vo.req.SurveySnapshotListReqVO;
import com.ykl.med.rehab.vo.req.func.FuncRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabSurveySnapshotVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@RestController
@Tag(name = "康复管理 - 功能康复调查快照")
@RequestMapping("/funcRehab/surveySnapshot")
@EnableCommonResponseAdvice
public class AppFuncRehabSurveySnapshotController {
    @Resource
    private FuncRehabSurveySnapshotFeign funcRehabSurveySnapshotFeign;
    @Resource
    private PatientUserFeign patientUserFeign;
    @Resource
    private EventTaskFeign eventTaskFeign;
    @Resource
    private PatientService patientService;
    @Resource
    private FuncRehabFeign funcRehabFeign;

    @PostMapping("/listAll")
    @Operation(summary = "获取问卷快照列表", description = "返回问卷快照列表")
    public List<FuncRehabSurveySnapshotVO> listAll() {
        PatientSimpleVO patientSimpleVO = patientUserFeign
                .getPatientSimpleByUserId(AuthContextHolder.getInstance().getContext().getId());
        return funcRehabSurveySnapshotFeign.list(new SurveySnapshotListReqVO()
                .setPatientId(patientSimpleVO.getPatientId()));
    }

    @PostMapping("/details")
    @Operation(summary = "问卷详情")
    public FuncRehabSurveySnapshotVO details(@RequestParam(name = "id") Long id) {
        return funcRehabSurveySnapshotFeign.details(id);
    }

    @PostMapping("/complete")
    @Operation(summary = "完成问卷快照", description = "完成问卷快照")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "功能康复调查快照完成", module = "功能康复")
    public void complete(@RequestBody RehabSurveySnapshotCompleteReqVO reqVO) {
        funcRehabSurveySnapshotFeign.complete(reqVO);
        FuncRehabSurveySnapshotVO details = funcRehabSurveySnapshotFeign.details(reqVO.getId());
        FuncRehabPlanSaveOrUpdateReqVO basePlanReq = funcRehabFeign.getBasePlanReq(details.getPatientId());
        if (basePlanReq != null) {
            EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(details.getPatientId())
                    .setRequestId(UUID.randomUUID().toString())
                    .setExecuteTime(LocalDateTime.now().plusMinutes(5))
                    .setBizId(details.getPatientId().toString())
                    .setBizType(EventTaskType.FUNC_REHAB_BASE_PLAN);
            eventTaskFeign.addEventTask(eventTaskAddVO);
        }
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setRequestId(reqVO.getId().toString())
                .setEventTime(LocalDateTime.now())
                .setBizType(EventTaskType.PATIENT_FINISH_FUNCTIONAL_FORM)
                .setUserId(AuthContextHolder.getInstance().getContext().getId())
                .setPatientId(patientService.getCurrentPatientId())
                .setBizId(reqVO.getId().toString());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }
}