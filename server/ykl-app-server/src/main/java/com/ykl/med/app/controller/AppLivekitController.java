package com.ykl.med.app.controller;

import com.ykl.med.app.constants.AppErrorCode;
import com.ykl.med.authority.intercepter.AuthContextHolder;
import com.ykl.med.base.handler.EnableCommonResponseAdvice;
import com.ykl.med.constants.LogConstants;
import com.ykl.med.doctors.api.DoctorInfoFeign;
import com.ykl.med.doctors.entity.vo.DoctorInfoVO;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.interfaces.OperateLog;
import com.ykl.med.livekit.feign.LivekitFeign;
import com.ykl.med.livekit.vo.req.RoomCreateReqVO;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.order.api.OrderFeign;
import com.ykl.med.order.vo.resp.OrderVO;
import com.ykl.med.push.api.PushSocketFeign;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Tag(name = "Livekit服务")
@RestController
@RequestMapping("/livekit")
@Slf4j
@EnableCommonResponseAdvice
public class AppLivekitController {

    @Resource
    private LivekitFeign livekitFeign;

    @Resource
    private OrderFeign orderFeign;

    @Resource
    private DoctorInfoFeign doctorInfoFeign;

    @Resource
    private PushSocketFeign pushSocketFeign;

    @PostMapping("/createRoom")
    @Operation(summary = "创建房间")
    @OperateLog(eventType = LogConstants.LogEventType.ADD, eventContent = "创建房间",module = "线上门诊")
    public RoomCreateRespVO createRoom(@Valid @RequestBody RoomCreateReqVO reqVO) {
        if (reqVO.getDoctorId() == null || StringUtils.isBlank(reqVO.getOrderCode())) {
            return null;
        }
        OrderVO order = orderFeign.getOrderDetailByOrderCode(reqVO.getOrderCode());
        if (order == null || order.getUserId() == null) {
            return null;
        }

        DoctorInfoVO doctor = doctorInfoFeign.querySingle(reqVO.getDoctorId());
        AssertUtils.notNull(doctor, AppErrorCode.DOCTOR_IS_NULL);

        // 赋值
        reqVO.setDoctorName(doctor.getName());
        reqVO.setUserId(order.getUserId());
        reqVO.setUserName(order.getPatientName());

        RoomCreateRespVO roomCreateRespVO = livekitFeign.createRoom(reqVO);

        // 推送通知
        pushSocketFeign.sendLiveNotice(order.getUserId(), null, reqVO.getOrderCode());

        return roomCreateRespVO;
    }

    @GetMapping("/getRoomInfo")
    @Operation(summary = "获取房间信息")
    public RoomCreateRespVO getRoomInfo(String orderCode) {
        return livekitFeign.getRoomInfo(orderCode, AuthContextHolder.getInstance().getContext().getId());
    }

    @GetMapping("/getRoomInfoByBizId")
    @Operation(summary = "获取房间信息")
    public RoomCreateRespVO getRoomInfoByBizId(Long bizId) {
        return livekitFeign.getRoomInfoByBizId(bizId, AuthContextHolder.getInstance().getContext().getId());
    }
}
