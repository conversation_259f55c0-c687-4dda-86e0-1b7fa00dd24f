package com.ykl.med.app.controller.medical;

import com.ykl.med.medical.vo.order.MedicalOrderAddVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * @Author: wang <EMAIL>
 * @Description: 微服务注册-规则管理系统
 * @Date: 2023-11-28 11:01:35
 * @Version: 1.0
 */
@FeignClient(name = "ykl-rule-manager-service", path = "/system/rule")
public interface RuleTagManagerFeign {

    @PostMapping("/tagByPatientMedicalOrder")
    @Operation(summary = "患者用药标签查询")
    Map<String,Object> getMedicTagByData(@RequestBody MedicalOrderAddVO medicalOrderAddVO);
}
