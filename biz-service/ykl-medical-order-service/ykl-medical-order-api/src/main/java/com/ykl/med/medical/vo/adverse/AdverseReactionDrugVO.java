package com.ykl.med.medical.vo.adverse;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "用户不良反应与药品关联")
public class AdverseReactionDrugVO {

    @Schema(description = "唯一标识", example = "12345")
    @Stringify
    private Long id;

    @Schema(description = "患者ID", example = "67890")
    @Stringify
    private Long patientId;

    @Schema(description = "不良反应ID", example = "98765")
    @Stringify
    private Long adverseReactionId;

    @Schema(description = "药品ID", example = "54321")
    @Stringify
    private Long drugId;

    @Schema(description = "药品名称", example = "阿司匹林")
    private String drugName;

    @Schema(description = "药品包装规格", example = "500mg*20片/盒")
    private String drugPackageSpec;

    @Schema(description = "药品产地", example = "中国")
    private String drugCountry;

}
