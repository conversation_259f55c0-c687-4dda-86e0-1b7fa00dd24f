package com.ykl.med.medical.vo.execOrder;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用药执行数据請求对象")
public class ExecMedicalOrderReqVO {

    @Schema(description = "唯一标识", example = "")
    @Stringify
    private Long id;

    @Schema(description = "执行时间（打卡）", example = "")
    private String execTime;
}
