package com.ykl.med.medical.vo.order;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.masterdata.entiry.vo.FrequencyBizVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderDetailVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "用药列表数据对象")
public class MedicalOrderListVO extends MedicalOrderListSimpleVO {
    @Schema(description = "项目类型（药品:西药/中成药；诊疗：检查/检验/手术）")
    private String itemType;

    @Schema(description = "项目名称")
    private String itemName;

    @Schema(description = "项目规格")
    private String itemSpec;

    @Schema(description = "项目厂商名称")
    private String itemProducerName;

    @Schema(description = "项目用法名称（如：口服/注射）")
    private String usageName;

    @Schema(description = "单次用量")
    private String onceDosage;

    @Schema(description = "单次用量单位")
    private String onceDosageUnit;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "用药开始执行时间")
    @TimestampConvert
    private LocalDateTime startExecTime;

    @Schema(description = "用药结束执行时间")
    @TimestampConvert
    private LocalDateTime endExecTime;

    @Schema(description = "商品名称", example = "好吃")
    private String goodsName;

    @Schema(description = "打卡记录")
    private List<ExecMedicalOrderDetailVO> execMedicalOrderList;

    @Schema(description = "频次信息")
    private FrequencyBizVO frequencyBiz;

    @Schema(description = "当天下次打卡时间")
    @TimestampConvert
    private LocalDateTime nextExecTime;
}
