package com.ykl.med.medical.vo.medicalAdvice;

import com.ykl.med.medical.enums.PrescriptionAuditStateEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@Data
public class AuditStateMedicalAdviceReqVO {

    @NotNull(message = "医嘱id 不能为空")
    @Schema(description = "医嘱id")
    private Long medicalAdviceId;

    @NotNull(message = "审核状态 不能为空")
    @Schema(description = "审核状态 PrescriptionAuditStateEnum 枚举")
    private PrescriptionAuditStateEnum auditState;

    @Schema(description = "药师id")
    private Long pharmacistId;

    @Schema(description = "审核结论")
    private String auditConclusion;

}
