package com.ykl.med.medical.api;

import com.ykl.med.medical.vo.adverse.AdverseReactionDrugAddVO;
import com.ykl.med.medical.vo.adverse.AdverseReactionDrugVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "ykl-medical-order-service", path = "/ykl-medical-order-service/adverseReactionDrug")
public interface AdverseReactionDrugFeign {
    @PostMapping("/getByDrugAndPatient")
    List<AdverseReactionDrugVO> getByDrugAndPatient(@RequestParam(value = "drugId") Long drugId, @RequestParam(value = "patientId") Long patientId);

    @PostMapping("/getByAdverseReactionIds")
    List<AdverseReactionDrugVO> getByAdverseReactionIds(@RequestBody List<Long> ids);

    @PostMapping("/getByPatientId")
    List<AdverseReactionDrugVO> getByPatientId(@RequestParam(value = "patientId") Long patientId);

    @PostMapping("/add")
    void add(@RequestBody @Valid AdverseReactionDrugAddVO addVO);

    @PostMapping("/batchAdd")
    void batchAdd(@RequestBody @Valid List<AdverseReactionDrugAddVO> addVOS);
}
