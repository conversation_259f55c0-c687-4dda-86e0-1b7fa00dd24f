package com.ykl.med.medical.vo.data;

import com.ykl.med.base.utils.CustomizeLevelInfo;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;

@Schema(description = "临床表现添加视图类")
@Data
public class ClinicalPerformanceAddVO {

    @Schema(description = "ID编号", example = "1")
    @Stringify
    private Long id;

    @Schema(description = "名称", example = "临床表现1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "编码", example = "CP001")
    private String code;

    @Schema(description = "临床表现分类，取的字典", example = "CP")
    private String type;

    @Schema(description = "备注", example = "该临床表现关于...")
    private String remark;

    @Schema(description = "排序值", example = "5")
    private Integer sort;

    @Schema(description = "状态")
    private CommonStatusEnum status = CommonStatusEnum.ENABLE;

    @Schema(description = "是否自定义等级", example = "true")
    private Boolean customizeLevel;

    @Schema(description = "自定义等级信息")
    private List<CustomizeLevelInfo> customizeLevelInfos;

    @Schema(description = "一级程度描述", example = "一级程度描述例")
    private String remarkLevelOne;

    @Schema(description = "二级程度描述", example = "二级程度描述例")
    private String remarkLevelTwo;

    @Schema(description = "三级程度描述", example = "三级程度描述例")
    private String remarkLevelThree;
}
