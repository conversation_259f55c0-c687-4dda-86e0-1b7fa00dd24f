package com.ykl.med.medical.api;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderDetailVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderQueryVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@FeignClient(name = "ykl-medical-order-service", path = "/ykl-medical-order-service/execMedicalOrder")
public interface ExecMedicalOrderFeign {


    @PostMapping("/exec")
    ExecMedicalOrderDetailVO exec(@RequestParam(value = "id") Long id,
              @RequestParam(value = "execTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime execTime);

    @PostMapping("/notify")
    void notify(@RequestParam(value = "id") Long id,
                @RequestParam(value = "notifyFlag") Long notifyFlag);

    @PostMapping("/delete")
    void delete(@RequestParam(value = "id") Long id);


    @PostMapping("/getById")
    ExecMedicalOrderVO getById(@RequestParam(value = "id") Long id);

    @PostMapping("/query")
    List<ExecMedicalOrderVO> query(@RequestBody @Valid ExecMedicalOrderQueryVO queryVO);

    @PostMapping("/queryDetail")
    List<ExecMedicalOrderDetailVO> queryDetail(@RequestBody @Valid ExecMedicalOrderQueryVO queryVO);

    @PostMapping("/queryPage")
    PageResult<ExecMedicalOrderDetailVO> queryPage(@RequestBody @Valid ExecMedicalOrderQueryVO queryVO);

    @PostMapping("/nextExec")
    ExecMedicalOrderVO nextExec(@RequestParam(value = "medicalOrderId") Long medicalOrderId);
}
