package com.ykl.med.medical.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Getter
@AllArgsConstructor
@Schema(description = "状态(WAIT_BUY-待购买、NOT_PAY-待支付、PAY-已支付、INVALID-已过期)")
public enum MedicalAdviceBuyStateEnum {

    WAIT_BUY("WAIT_BUY", "待购买"),
    NOT_PAY("NOT_PAY", "待支付"),
    PAY("PAY", "已支付"),
    INVALID("INVALID", "已过期"),
    ;
    private String type;
    private String desc;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    private static final Map<String, MedicalAdviceBuyStateEnum> ENUM_MAP = new HashMap<>();

    static {
        for (MedicalAdviceBuyStateEnum code : MedicalAdviceBuyStateEnum.values()) {
            ENUM_MAP.put(code.getType(), code);
        }
    }

    public static MedicalAdviceBuyStateEnum getByType(String type) {
        return ENUM_MAP.get(type);
    }
}
