package com.ykl.med.medical.vo.adverse;

import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.medical.enums.AdverseReactionClass;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 添加不良反应数据对象
 */
@Data
@Schema(description = "添加不良反应数据对象")
public class AdverseReactionAddVO implements AutoBuildUserId {
    @Schema(description = "用户Id", hidden = true)
    private Long currentUserId;

    @Schema(description = "患者ID", hidden = true)
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "项目类别（如：症状，疾病）")
    @NotNull(message = "项目类别不能为空")
    private AdverseReactionClass itemClass;

    @Schema(description = "项目值")
    @NotBlank(message = "项目值不能为空")
    private String itemValue;

    @Schema(description = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String itemName;

    @Schema(description = "部位【字典】")
    private List<String> position;

    @Schema(description = "表现【字典】")
    private String performance;

    @Schema(description = "发生频率【字典】")
    private String frequency;

    @Schema(description = "级别（0~10级）")
    @Stringify
    private Long level;

    @Schema(description = "开始时间")
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    @Schema(description = "相对上次频率")
    private String relativeFrequency;

    @Schema(description = "备注", example = "备注")
    private String remark;

    @Schema(description = "级别名称(轻中重)", example = "中度", hidden = true)
    private String levelName;

    @Schema(description = "是否自定义等级", hidden = true)
    private Boolean customizeLevel;
}
