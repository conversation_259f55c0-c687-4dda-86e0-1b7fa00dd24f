package com.ykl.med.medical.vo.order;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.medical.enums.MedicalOrderItemClass;
import com.ykl.med.medical.enums.MedicalOrderStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@Schema(description = "用药VO")
public class MedicalOrderVO {
    @Schema(description = "用药ID")
    @Stringify
    private Long id;

    @Schema(description = "患者ID")
    @Stringify
    private Long patientId;

    /**
     * 业务Id
     */
    @Schema(description = "项目ID")
    @Stringify
    private Long itemId;

    @Schema(description = "项目类别（药品/诊疗/观察指导）")
    private MedicalOrderItemClass itemClass;

    /**
     * 子类型
     */
    @Schema(description = "项目类型（药品:西药/中成药；诊疗：检查/检验/手术）")
    private String itemType;

    @Schema(description = "项目名称")
    private String itemName;

    @Schema(description = "项目规格")
    private String itemSpec;

    @Schema(description = "项目厂商名称")
    private String itemProducerName;

    @Schema(description = "项目用法名称（如：口服/注射）")
    private String usageName;

    @Schema(description = "单次用量")
    private String onceDosage;

    @Schema(description = "单次用量单位")
    private String onceDosageUnit;

    @Schema(description = "频次ID")
    @Stringify
    private Long frequencyId;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "频次数目单位", example = "1")
    private String frequencyQuantityUnit;

    @Schema(description = "提醒时间（如：08:00:00;12:00:00;18:00:00）")
    private String times;

    @Schema(description = "使用周期（天）")
    @Stringify
    private Long usageCycleDays;

    @Schema(description = "开立数量")
    @Stringify
    private Long quantity;

    @Schema(description = "开立数量单位")
    private String quantityUnit;

    @Schema(description = "用药类型(0长期用药, 1临时用药)")
    @Stringify
    private Long medicalOrderType;

    @Schema(description = "用药开始执行时间")
    @TimestampConvert
    private LocalDateTime startExecTime;

    @Schema(description = "用药结束执行时间")
    @TimestampConvert
    private LocalDateTime endExecTime;

    @Schema(description = "停嘱标志；0未停嘱，1已停嘱")
    @Stringify
    private Long stopFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "最后一次生成执行计划的时间")
    @TimestampConvert
    private LocalDateTime lastExecPlanTime;

    @Schema(description = "用药状态")
    private MedicalOrderStatus status;

    @Schema(description = "用药指导")
    private String medicationGuidance;

    @Schema(description = "停药原因")
    private String stopReason;

    @Schema(description = "作废时间")
    @TimestampConvert
    private LocalDateTime invalidTime;
}