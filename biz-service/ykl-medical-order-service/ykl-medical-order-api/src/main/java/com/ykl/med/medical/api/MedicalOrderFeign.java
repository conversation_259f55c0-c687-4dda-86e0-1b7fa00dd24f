package com.ykl.med.medical.api;

import com.ykl.med.framework.common.pojo.IdListReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.medical.vo.execOrder.PreExecMedicalOrderVO;
import com.ykl.med.medical.vo.order.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "ykl-medical-order-service", path = "/ykl-medical-order-service/medicalOrder")
public interface MedicalOrderFeign {

    @PostMapping("/batchAdd")
    List<MedicalOrderVO> batchAdd(@RequestBody @Valid MedicalOrderBatchAddVO medicalOrderBatchAddVO);

    @PostMapping("/add")
    MedicalOrderVO add(@RequestBody @Valid MedicalOrderAddVO addVO);

    @PostMapping("/update")
    void update(@RequestBody @Valid MedicalOrderUpdateVO updateVO);

    @PostMapping("/getById")
    MedicalOrderDetailVO getById(@RequestParam(value = "id") Long id);

    @PostMapping("/getByIds")
    List<MedicalOrderVO> getByIds(@RequestBody List<Long> ids);

    @PostMapping("/getSimpleByPatientId")
    List<MedicalOrderListSimpleVO> getSimpleByPatientId(@RequestParam(value = "patientId") Long patientId);

    @PostMapping("/getByPatientId")
    List<MedicalOrderListVO> getByPatientId(@RequestParam(value = "patientId") Long patientId);

    /**
     * 获取预生成的用药执行计划，暂时7天
     *
     * @param patientId 患者ID
     * @return 用药执行计划
     */
    @PostMapping("/getPreExecMedicalOrderList")
    List<PreExecMedicalOrderVO> getPreExecMedicalOrderList(@RequestParam(value = "patientId") Long patientId);

    @PostMapping("/genMedicalOrderExecPlan")
    void genMedicalOrderExecPlan(@RequestParam(value = "id") Long id);

    @PostMapping("/queryApp")
    PageResult<MedicalOrderListAppVO> queryApp(@RequestBody @Valid MedicalOrderQueryAppVO queryVO);

    @PostMapping("/queryWeb")
    PageResult<MedicalOrderListWebVO> queryWeb(@RequestBody @Valid MedicalOrderQueryWebVO queryVO);

    @PostMapping("/waitExecPlanGen")
    List<MedicalOrderVO> waitExecPlanGen();

    @PostMapping("/autoStopMedicalOrder")
    void autoStopMedicalOrder();

    @PostMapping("/queryTargetDrugsByPatientIds")
    List<MedicalOrderVO> queryTargetDrugsByPatientIds(@RequestBody IdListReqVO reqVO);
}
