package com.ykl.med.medical.api;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.vo.req.BatchUpdateStatusReqVO;
import com.ykl.med.medical.enums.AdverseReactionDataType;
import com.ykl.med.medical.vo.data.AdverseReactionDataAddVO;
import com.ykl.med.medical.vo.data.AdverseReactionDataBatchUpdateVO;
import com.ykl.med.medical.vo.data.AdverseReactionDataQueryVO;
import com.ykl.med.medical.vo.data.AdverseReactionDataVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "ykl-medical-order-service", path = "/ykl-medical-order-service/adverseReactionData")
public interface AdverseReactionDataFeign {

    @PostMapping("/batchSave")
    void batchSave(@RequestBody @Valid AdverseReactionDataAddVO addVO);

    @PostMapping("/batchUpdate")
    void batchUpdate(@RequestBody @Valid AdverseReactionDataBatchUpdateVO addVO);

    @PostMapping("/saveOrUpdate")
    void saveOrUpdate(@RequestBody @Valid AdverseReactionDataAddVO addVO);

    @PostMapping("/changeStatus")
    void changeStatus(@RequestBody @Valid BatchUpdateStatusReqVO reqVO);

    @PostMapping("/getBizIdsByTypeAndBizIds")
    List<Long> getBizIdsByTypeAndBizIds(@RequestParam("type") AdverseReactionDataType type,
                                        @RequestBody List<Long> bizIds);

    @PostMapping("/pageList")
    PageResult<AdverseReactionDataVO> pageList(@RequestBody @Valid AdverseReactionDataQueryVO queryVO);

    @PostMapping("/findById")
    AdverseReactionDataVO findById(@RequestParam("id") Long id);

    @PostMapping("/findByIds")
    List<AdverseReactionDataVO> findByIds(@RequestBody List<Long> ids);

    @PostMapping("/queryPatientDrugAdverseReaction")
    List<AdverseReactionDataVO> queryPatientDrugAdverseReaction(@RequestParam(value = "drugIds", required = false) Long drugIds, @RequestParam("patientId") Long patientId);

    @PostMapping("/getByTypeAndBizIds")
    List<AdverseReactionDataVO> getByTypeAndBizIds(@RequestParam("type") AdverseReactionDataType type,
                                                   @RequestBody List<Long> bizIds);
}