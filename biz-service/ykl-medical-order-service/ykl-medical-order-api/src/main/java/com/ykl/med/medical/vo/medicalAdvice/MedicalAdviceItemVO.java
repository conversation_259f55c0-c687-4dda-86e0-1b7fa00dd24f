package com.ykl.med.medical.vo.medicalAdvice;

import com.ykl.med.base.number.NumberUnitExchange;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/10/12
 */
@Data
public class MedicalAdviceItemVO {

    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "医嘱id")
    @Stringify
    private Long medicalAdviceId;

    @Schema(description = "skuId")
    @Stringify
    private Long skuId;

    @Schema(description = "项目ID")
    @Stringify
    private Long itemId;

    @Schema(description = "项目类别（药品/诊疗/观察指导）")
    private String itemClass;

    @Schema(description = "项目类型（药品:西药/中成药；诊疗：检查/检验/手术）")
    private String itemType;

    @Schema(description = "项目名称")
    private String itemName;

    @Schema(description = "项目规格")
    private String itemSpec;

    @Schema(description = "项目规格/单位 【字典】")
    private String itemSpecUnit;

    @Schema(description = "是否处方药（false-不是/true-是）")
    private Boolean prescriptionFlag;

    @Schema(description = "项目厂商名称")
    private String itemProducerName;

    @Schema(description = "项目用法名称（如：口服/注射）")
    private String usageName;

    @Schema(description = "单次用量")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal onceDosage;

    @Schema(description = "单次用量单位")
    private String onceDosageUnit;

    @Schema(description = "频次ID")
    @Stringify
    private Long frequencyId;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "包装剂量")
    @NumberUnitExchange(showModule = 1)
    private BigDecimal preparation;

    @Schema(description = "包装剂量单位【字典】")
    private String preparationUnit;

    @Schema(description = "提醒时间（如：08:00:00;12:00:00;18:00:00）")
    private String times;

    @Schema(description = "使用周期（天）")
    @Stringify
    private Long usageCycleDays;

    @Schema(description = "开立数量")
    private Long quantity;

    @Schema(description = "开立数量单位")
    private String quantityUnit;

    @Schema(description = "价格,单位/分")
    private Integer price;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "用药指导")
    private String medicationGuidance;

    @Schema(description = "医嘱开始时间")
    @TimestampConvert
    private LocalDate startTime;

    @Schema(description = "医嘱结束时间")
    @TimestampConvert
    private LocalDate endTime;

    @Schema(description = "his医嘱项目Id(冗余字段，来源sku表)")
    private Long hisOrditemId;

    @Schema(description = "his药品id(冗余字段，来源sku表)")
    private Long hisGoodsId;

    @Schema(description = "医嘱记录ID（创建医嘱his返回的医嘱记录id）")
    private Long hisOrderId;

}
