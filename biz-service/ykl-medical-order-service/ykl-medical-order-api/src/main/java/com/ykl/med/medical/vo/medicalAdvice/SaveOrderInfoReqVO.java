package com.ykl.med.medical.vo.medicalAdvice;

import com.ykl.med.framework.common.enums.OrderStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@Data
public class SaveOrderInfoReqVO {

    @Schema(description = "用户id,发货必传")
    private Long userId;

    @NotNull(message = "orderCode 不能为空")
    @Schema(description = "orderCode")
    private String orderCode;

    @Schema(description = "订单状态")
    private OrderStatus orderState;

    @Schema(description = "订单支付时间")
    private LocalDateTime orderPayTime;

    @Schema(description = "订单发货时间")
    private LocalDateTime deliverTime;



}
