package com.ykl.med.medical.vo.execOrder;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "预生成的用药执行")
public class PreExecMedicalOrderVO {

    @Schema(description = "用药ID")
    @Stringify
    private Long medicalOrderId;

    @Schema(description = "计划执行时间列表, 格式: 2000-01-01 08:00:00")
    public List<String> frequencyGenExecTimeList;

    @Schema(description = "项目名称")
    private String itemName;

    @Schema(description = "项目规格")
    private String itemSpec;

    @Schema(description = "项目厂商名称")
    private String itemProducerName;

    @Schema(description = "项目用法名称（如：口服/注射）")
    private String usageName;

    @Schema(description = "单次用量")
    private String onceDosage;

    @Schema(description = "单次用量单位")
    private String onceDosageUnit;
}
