package com.ykl.med.medical.vo.adverse;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@Schema(description = "查询不良反应药品数据对象")
public class AdverseReactionDrugQueryVO{

    @Schema(description = "患者ID")
    @Stringify
    @NotNull(message = "药品ID不能为空")
    private Long patientId;

    @Schema(description = "不良反应ID")
    private List<Long> adverseReactionIds;

}
