package com.ykl.med.medical.vo.data;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.medical.enums.AdverseReactionClass;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "不良反应数据关联的返回对象")
public class AdverseReactionDataRelationVO {

    @Schema(description = "不良反应数据ID", example = "**********")
    @Stringify
    private Long adverseReactionDataId;

    @Schema(description = "不良反应类别")
    private AdverseReactionClass adverseReactionClass;

    @Schema(description = "业务ID", example = "**********")
    @Stringify
    private Long bizId;

    @Schema(description = "业务名称", example = "业务名称示例")
    private String bizName;

    @Schema(description = "系统类型（字典）", example = "人体系统")
    private String type;

    @Schema(description = "启用禁用状态")
    private CommonStatusEnum status;
}
