package com.ykl.med.medical.vo.adverse;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "不良反应表现记录查询对象")
public class AdverseReactionRecordsQueryVO extends PageParam {
    @Schema(description = "患者ID", example = "")
    @Stringify
    private Long patientId;

    @Schema(description = "不良反应ID", example = "")
    @Stringify
    private Long adverseReactionId;
}
