package com.ykl.med.medical.vo.adverse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
@Schema(description = "用户所有的不良反应用药")
public class AdverseReactionMedicalOrderTotalVO {
    @Schema(description = "有关联的")
    private List<AdverseReactionMedicalOrderVO> relatedList;
    @Schema(description = "无关联的")
    private List<AdverseReactionMedicalOrderVO> unrelatedList;
}
