package com.ykl.med.medical.vo.medicalAdvice;

import com.ykl.med.medical.enums.MedicalAdviceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/16
 */
@Data
public class SaveMedicalAdviceItemReqVO {

    @Schema(description = "医嘱id")
    private Long medicalAdviceId;

    @Schema(description = "医嘱类型，药品、检查...，MedicalAdviceTypeEnum 枚举")
    private MedicalAdviceTypeEnum medicalAdviceType;

    @Schema(description = "明细")
    private List<CreateMedicalAdviceItemReqVO> item;

}
