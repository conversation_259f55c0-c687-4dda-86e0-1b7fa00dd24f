package com.ykl.med.medical.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.medical.api.AdverseReactionFeign;
import com.ykl.med.medical.service.AdverseReactionService;
import com.ykl.med.medical.vo.adverse.AdverseReactionAddVO;
import com.ykl.med.medical.vo.adverse.AdverseReactionQueryVO;
import com.ykl.med.medical.vo.adverse.AdverseReactionQueryWebVO;
import com.ykl.med.medical.vo.adverse.AdverseReactionVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "用户不良反应")
@RestController
@RequestMapping("adverseReaction")
@Validated
public class AdverseReactionController implements AdverseReactionFeign {
    @Resource
    private AdverseReactionService adverseReactionService;

    @Override
    @PostMapping("/queryAdverseReactionList")
    public PageResult<AdverseReactionVO> queryAdverseReactionList(@RequestBody @Valid AdverseReactionQueryVO queryVO) {
        return adverseReactionService.queryAdverseReactionList(queryVO);
    }

    @Override
    @PostMapping("/queryAdverseReactionWeb")
    public PageResult<AdverseReactionVO> queryAdverseReactionWeb(@RequestBody @Valid AdverseReactionQueryWebVO queryVO) {
        return adverseReactionService.queryAdverseReactionWeb(queryVO);
    }

    @Override
    @PostMapping("/queryByIds")
    public List<AdverseReactionVO> queryByIds(@RequestBody List<Long> ids) {
        return adverseReactionService.queryByIds(ids);
    }


    @Override
    @PostMapping("/queryById")
    public AdverseReactionVO queryById(@RequestParam(value = "id") Long id) {
        return adverseReactionService.queryById(id);
    }


    @Override
    @PostMapping("/add")
    public AdverseReactionVO add(@RequestBody @Valid AdverseReactionAddVO addVO) {
        return adverseReactionService.add(addVO);
    }

    @Override
    @PostMapping("/adverseReactionStop")
    public void adverseReactionStop(@RequestParam(value = "id") Long id) {
        adverseReactionService.adverseReactionStop(id);
    }


}
