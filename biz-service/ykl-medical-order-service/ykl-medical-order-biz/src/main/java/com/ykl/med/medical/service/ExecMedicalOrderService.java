package com.ykl.med.medical.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.trx.TransactionalAfterCommitExecutor;
import com.ykl.med.medical.constans.MedicalErrorCodeConstants;
import com.ykl.med.medical.db.entity.ExecMedicalOrderDO;
import com.ykl.med.medical.db.mapper.ExecMedicalOrderMapper;
import com.ykl.med.medical.service.stat.MedicalOrderStatService;
import com.ykl.med.medical.utils.ExecMedicalOrderConvert;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderAddVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderDetailVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderQueryVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderVO;
import com.ykl.med.push.api.ToDoMessageFeign;
import com.ykl.med.push.enums.ToDoMessageType;
import com.ykl.med.push.vo.todo.ToDoMessageReqVO;
import com.ykl.med.push.vo.todo.ToDoMessageSystemDeleteReqVO;
import com.ykl.med.push.vo.todo.ToDoMessageSystemExecutedReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExecMedicalOrderService {
    @Resource
    private ExecMedicalOrderMapper execMedicalOrderMapper;
    @Resource
    private IdServiceImpl idService;
    @Resource
    private ToDoMessageFeign toDoMessageFeign;
    @Resource
    private MedicalOrderStatService medicalOrderStatService;

    @Transactional(rollbackFor = Exception.class)
    public void add(ExecMedicalOrderAddVO addVO, String itemName) {
        log.info("添加执行计划，addVO:{}", JSON.toJSONString(addVO));
        LambdaQueryWrapper<ExecMedicalOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExecMedicalOrderDO::getMedicalOrderId, addVO.getMedicalOrderId());
        queryWrapper.eq(ExecMedicalOrderDO::getShouldExecTime, addVO.getShouldExecTime());
        ExecMedicalOrderDO exist = execMedicalOrderMapper.selectOne(queryWrapper);
        if (exist != null) {
            log.info("已存在执行计划，不再重复添加,修改删除状态即可");
            exist.setDeleteFlag(false);
            execMedicalOrderMapper.updateById(exist);
            this.notifyTodo(exist, itemName);
            return;
        }
        ExecMedicalOrderDO execMedicalOrderDO = CopyPropertiesUtil.normalCopyProperties(addVO, ExecMedicalOrderDO.class);
        execMedicalOrderDO.setId(idService.nextId());
        execMedicalOrderMapper.insert(execMedicalOrderDO);
        this.notifyTodo(execMedicalOrderDO, itemName);
    }


    @Transactional(rollbackFor = Exception.class)
    public ExecMedicalOrderDetailVO exec(Long id, LocalDateTime execTime) {
        log.info("执行用药，id:{},execTime:{}", id, execTime);

        execTime = ExecMedicalOrderDO.checkExecTimeAndReturn(execTime);
        ExecMedicalOrderDO execMedicalOrderDO = execMedicalOrderMapper.selectById(id);
        // 如果执行时间早于三天，则不执行
        AssertUtils.isTrue(execMedicalOrderDO.getShouldExecTime().isAfter(LocalDateTime.now().minusDays(3)), MedicalErrorCodeConstants.MEDICAL_ORDER_EXEC_DATE_TIME_OUT);
        execMedicalOrderDO.setExecFlag(1L);
        execMedicalOrderDO.setExecTime(execTime);
        execMedicalOrderMapper.updateById(execMedicalOrderDO);
        this.notifyTodoExec(execMedicalOrderDO);
        ExecMedicalOrderVO execMedicalOrderVO = CopyPropertiesUtil.normalCopyProperties(execMedicalOrderDO, ExecMedicalOrderVO.class);
        ExecMedicalOrderDetailVO execMedicalOrderDetailVO = CopyPropertiesUtil.normalCopyProperties(execMedicalOrderVO, ExecMedicalOrderDetailVO.class);
        execMedicalOrderDetailVO.setExecMedicalOrderType(ExecMedicalOrderConvert.getExecTypeAlreadyExec(execMedicalOrderVO));
        TransactionalAfterCommitExecutor.executeAfterCommit(() -> medicalOrderStatService.refreshStat(execMedicalOrderDO.getPatientId()));
        return execMedicalOrderDetailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void notify(Long id, Long notifyFlag) {
        ExecMedicalOrderDO execMedicalOrderDO = execMedicalOrderMapper.selectById(id);
        execMedicalOrderDO.setNotifyFlag(notifyFlag);
        execMedicalOrderMapper.updateById(execMedicalOrderDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<ExecMedicalOrderAddVO> addVOList, String itemName) {
        for (ExecMedicalOrderAddVO addVO : addVOList) {
            add(addVO, itemName);
        }
        TransactionalAfterCommitExecutor.executeAfterCommit(() -> medicalOrderStatService.refreshStat(addVOList.get(0).getPatientId()));
    }

//    @Transactional(rollbackFor = Exception.class)
//    public void delete(Long id) {
//        ExecMedicalOrderDO execMedicalOrderDO = execMedicalOrderMapper.selectById(id);
//        execMedicalOrderDO.setDeleteFlag(true);
//        execMedicalOrderMapper.updateById(execMedicalOrderDO);
//        this.deleteTodo(execMedicalOrderDO);
//    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteToday(Long medicalOrderId) {
        LambdaQueryWrapper<ExecMedicalOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExecMedicalOrderDO::getMedicalOrderId, medicalOrderId);
        queryWrapper.eq(ExecMedicalOrderDO::getExecFlag, 0);
        queryWrapper.eq(ExecMedicalOrderDO::getDeleteFlag, false);
        queryWrapper.ge(ExecMedicalOrderDO::getShouldExecTime, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        List<ExecMedicalOrderDO> execMedicalOrderDOList = execMedicalOrderMapper.selectList(queryWrapper);
        for (ExecMedicalOrderDO execMedicalOrderDO : execMedicalOrderDOList) {
            execMedicalOrderDO.setDeleteFlag(true);
            execMedicalOrderMapper.updateById(execMedicalOrderDO);
            this.deleteTodo(execMedicalOrderDO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(Long medicalOrderId) {
        LambdaQueryWrapper<ExecMedicalOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExecMedicalOrderDO::getMedicalOrderId, medicalOrderId);
        queryWrapper.eq(ExecMedicalOrderDO::getExecFlag, 0);
        queryWrapper.eq(ExecMedicalOrderDO::getDeleteFlag, false);
        List<ExecMedicalOrderDO> execMedicalOrderDOList = execMedicalOrderMapper.selectList(queryWrapper);
        for (ExecMedicalOrderDO execMedicalOrderDO : execMedicalOrderDOList) {
            execMedicalOrderDO.setDeleteFlag(true);
            execMedicalOrderMapper.updateById(execMedicalOrderDO);
            this.deleteTodo(execMedicalOrderDO);
        }
    }

    public ExecMedicalOrderVO getById(Long id) {
        ExecMedicalOrderDO execMedicalOrderDO = execMedicalOrderMapper.selectById(id);
        return CopyPropertiesUtil.normalCopyProperties(execMedicalOrderDO, ExecMedicalOrderVO.class);
    }


    public List<ExecMedicalOrderVO> query(ExecMedicalOrderQueryVO queryVO) {
        LambdaQueryWrapper<ExecMedicalOrderDO> queryWrapper = execMedicalOrderMapper.buildQuery(queryVO);
        List<ExecMedicalOrderDO> execMedicalOrderDOS = execMedicalOrderMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(execMedicalOrderDOS, ExecMedicalOrderVO.class);
    }

    public List<ExecMedicalOrderDetailVO> queryDetail(ExecMedicalOrderQueryVO queryVO) {
        List<ExecMedicalOrderVO> vos = query(queryVO);
        return ExecMedicalOrderConvert.voToExecMedicalOrderDetailVOList(vos);
    }

    public PageResult<ExecMedicalOrderDetailVO> queryPage(ExecMedicalOrderQueryVO queryVO) {
        LambdaQueryWrapper<ExecMedicalOrderDO> queryWrapper = execMedicalOrderMapper.buildQuery(queryVO);
        Page<ExecMedicalOrderDO> page = execMedicalOrderMapper.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), queryWrapper);
        List<ExecMedicalOrderVO> vos = CopyPropertiesUtil.normalCopyProperties(page.getRecords(), ExecMedicalOrderVO.class);
        List<ExecMedicalOrderDetailVO> detailVOS = ExecMedicalOrderConvert.voToExecMedicalOrderDetailVOList(vos);
        return new PageResult<>(detailVOS, page.getTotal());
    }

    public Map<Long, List<ExecMedicalOrderDetailVO>> getExecMedicalOrderDetailMapToday(List<Long> medicalOrderIdList) {
        Map<Long, List<ExecMedicalOrderVO>> execMedicalOrderListMap = getExecMedicalOrderListMapToday(medicalOrderIdList);
        Map<Long, List<ExecMedicalOrderDetailVO>> result = new HashMap<>();
        for (Long k : execMedicalOrderListMap.keySet()) {
            result.put(k, ExecMedicalOrderConvert.voToExecMedicalOrderDetailVOList(execMedicalOrderListMap.get(k)));
        }
        return result;
    }

    public Map<Long, List<ExecMedicalOrderVO>> getExecMedicalOrderListMapToday(List<Long> medicalOrderIdList) {
        ExecMedicalOrderQueryVO execMedicalOrderQueryVO = new ExecMedicalOrderQueryVO();
        execMedicalOrderQueryVO.setMedicalOrderIds(medicalOrderIdList);
        execMedicalOrderQueryVO.setShouldExecTimeStart(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        execMedicalOrderQueryVO.setShouldExecTimeEnd(LocalDateTimeUtil.endOfDay(LocalDateTime.now(), true));
        List<ExecMedicalOrderVO> execMedicalOrderVOList = this.query(execMedicalOrderQueryVO);
        Map<Long, List<ExecMedicalOrderVO>> execMedicalOrderListMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(execMedicalOrderVOList)) {
            execMedicalOrderListMap = execMedicalOrderVOList.stream().
                    collect(Collectors.groupingBy(ExecMedicalOrderVO::getMedicalOrderId));
        }
        //把map里面的列表排序，ExecMedicalOrderList，按shouldExecTime排序
        execMedicalOrderListMap.forEach((k, v) -> {
            v.sort(Comparator.comparing(ExecMedicalOrderVO::getShouldExecTime));
        });
        return execMedicalOrderListMap;
    }

    /**
     * 计算下次打卡数据
     */
    public ExecMedicalOrderVO nextExec(Long medicalOrderId) {
        List<ExecMedicalOrderVO> execMedicalOrderVOList = this.getExecMedicalOrderListMapToday(Lists.newArrayList(medicalOrderId)).get(medicalOrderId);
        return ExecMedicalOrderConvert.nextExec(execMedicalOrderVOList);
    }


    @Transactional(rollbackFor = Exception.class)
    public void notifyTodo(ExecMedicalOrderDO execMedicalOrderDO, String itemName) {
        //todo 有时间的话，需要解耦，不能影响本身业务
        log.info("添加执行计划，通知待办，execMedicalOrderDO:{}", JSON.toJSONString(execMedicalOrderDO));
        ToDoMessageReqVO toDoMessageReqVO = new ToDoMessageReqVO();
        toDoMessageReqVO.setOutBizId(execMedicalOrderDO.getId().toString());
        toDoMessageReqVO.setPatientId(execMedicalOrderDO.getPatientId());
        toDoMessageReqVO.setType(ToDoMessageType.DRUGS);
        toDoMessageReqVO.setContent(itemName + " 用药打卡");
        toDoMessageReqVO.setExpireTime(execMedicalOrderDO.getShouldExecTime());
        toDoMessageFeign.addToDoMessage(toDoMessageReqVO);
        this.notify(execMedicalOrderDO.getId(), 1L);
    }

    public void deleteTodo(ExecMedicalOrderDO execMedicalOrderDO) {
        log.info("删除待办，execMedicalOrderDO:{}", JSON.toJSONString(execMedicalOrderDO));
        ToDoMessageSystemDeleteReqVO toDoMessageReqVO = new ToDoMessageSystemDeleteReqVO();
        toDoMessageReqVO.setOutBizId(execMedicalOrderDO.getId().toString());
        toDoMessageReqVO.setPatientId(execMedicalOrderDO.getPatientId());
        toDoMessageReqVO.setType(ToDoMessageType.DRUGS);
        toDoMessageFeign.systemDelete(toDoMessageReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void notifyTodoExec(ExecMedicalOrderDO execMedicalOrderDO) {
        log.info("执行用药，通知待办，execMedicalOrderDO:{}", JSON.toJSONString(execMedicalOrderDO));
        ToDoMessageSystemExecutedReqVO toDoMessageSystemExecutedReqVO = new ToDoMessageSystemExecutedReqVO();
        toDoMessageSystemExecutedReqVO.setOutBizId(execMedicalOrderDO.getId().toString());
        toDoMessageSystemExecutedReqVO.setPatientId(execMedicalOrderDO.getPatientId());
        toDoMessageSystemExecutedReqVO.setType(ToDoMessageType.DRUGS);
        toDoMessageSystemExecutedReqVO.setExecutedTime(execMedicalOrderDO.getExecTime());
        toDoMessageSystemExecutedReqVO.setNeedError(false);
        toDoMessageFeign.toDoMessageExecutedSystem(toDoMessageSystemExecutedReqVO);
        this.notify(execMedicalOrderDO.getId(), 2L);
    }

    public Integer countByPatientId(Long patientId, Boolean execFlag, LocalDateTime startTime, LocalDateTime endTime) {
        return execMedicalOrderMapper.countByPatientId(patientId, execFlag, startTime, endTime);
    }
}
