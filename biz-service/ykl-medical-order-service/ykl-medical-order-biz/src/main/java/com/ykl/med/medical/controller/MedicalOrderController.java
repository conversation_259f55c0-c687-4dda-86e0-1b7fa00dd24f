package com.ykl.med.medical.controller;

import com.ykl.med.framework.common.pojo.IdListReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.medical.api.MedicalOrderFeign;
import com.ykl.med.medical.service.MedicalOrderService;
import com.ykl.med.medical.vo.execOrder.PreExecMedicalOrderVO;
import com.ykl.med.medical.vo.order.*;
import com.ykl.med.push.api.PushSocketFeign;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "用药")
@RestController
@RequestMapping("medicalOrder")
@Validated
public class MedicalOrderController implements MedicalOrderFeign {
    @Resource
    private MedicalOrderService medicalOrderService;
    @Resource
    private PushSocketFeign pushSocketFeign;

    @Override
    @PostMapping("/batchAdd")
    public List<MedicalOrderVO> batchAdd(@RequestBody @Valid MedicalOrderBatchAddVO medicalOrderBatchAddVO) {
        return medicalOrderService.batchAdd(medicalOrderBatchAddVO);
    }

    @Override
    @PostMapping("/add")
    public MedicalOrderVO add(@RequestBody @Valid MedicalOrderAddVO addVO) {
        return medicalOrderService.add(addVO);
    }

    @Override
    @PostMapping("/update")
    public void update(@RequestBody @Valid MedicalOrderUpdateVO updateVO) {
        medicalOrderService.update(updateVO);
    }


    @Override
    @PostMapping("/getById")
    public MedicalOrderDetailVO getById(@RequestParam(value = "id") Long id) {
        return medicalOrderService.getById(id);
    }

    @Override
    @PostMapping("/getByIds")
    public List<MedicalOrderVO> getByIds(@RequestBody List<Long> ids) {
        return medicalOrderService.getByIds(ids);
    }

    @Override
    @PostMapping("/getSimpleByPatientId")
    public List<MedicalOrderListSimpleVO> getSimpleByPatientId(@RequestParam(value = "patientId") Long patientId) {
        return medicalOrderService.getSimpleByPatientId(patientId);
    }

    @Override
    @PostMapping("/getByPatientId")
    public List<MedicalOrderListVO> getByPatientId(@RequestParam(value = "patientId") Long patientId) {
        return medicalOrderService.getByPatientId(patientId);
    }

    @Override
    @PostMapping("/getPreExecMedicalOrderList")
    public List<PreExecMedicalOrderVO> getPreExecMedicalOrderList(@RequestParam(value = "patientId") Long patientId) {
        return medicalOrderService.getPreExecMedicalOrderList(patientId);
    }

    @Override
    @PostMapping("/genMedicalOrderExecPlan")
    public void genMedicalOrderExecPlan(@RequestParam(value = "id") Long id) {
        medicalOrderService.genMedicalOrderExecPlan(id);
    }

    @Override
    @PostMapping("/queryApp")
    public PageResult<MedicalOrderListAppVO> queryApp(@RequestBody @Valid MedicalOrderQueryAppVO queryVO) {
        return medicalOrderService.queryApp(queryVO);
    }

    @Override
    @PostMapping("/queryWeb")
    public PageResult<MedicalOrderListWebVO> queryWeb(@RequestBody @Valid MedicalOrderQueryWebVO queryVO) {
        return medicalOrderService.queryWeb(queryVO);
    }

    @Override
    @PostMapping("/waitExecPlanGen")
    public List<MedicalOrderVO> waitExecPlanGen() {
        return medicalOrderService.waitExecPlanGen();
    }

    @Override
    @PostMapping("/autoStopMedicalOrder")
    public void autoStopMedicalOrder() {
        medicalOrderService.autoStopMedicalOrder();
    }


    @Override
    @PostMapping("/queryTargetDrugsByPatientIds")
    public List<MedicalOrderVO> queryTargetDrugsByPatientIds(@RequestBody IdListReqVO reqVO) {
        return medicalOrderService.queryTargetDrugsByPatientIds(reqVO.getIdList());
    }

}
