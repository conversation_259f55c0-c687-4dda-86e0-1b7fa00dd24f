package com.ykl.med.medical.utils;

import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.medical.enums.ExecMedicalOrderType;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderDetailVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderVO;
import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class ExecMedicalOrderConvert {
    public static List<ExecMedicalOrderDetailVO> voToExecMedicalOrderDetailVOList(List<ExecMedicalOrderVO> execMedicalOrderVOList) {
        LocalDateTime now = LocalDateTime.now();
        List<ExecMedicalOrderVO> canExecList = getCanExecList(execMedicalOrderVOList, now);
        List<LocalDateTime> canExecTime = canExecList.stream().map(ExecMedicalOrderVO::getShouldExecTime).collect(Collectors.toList());
        List<ExecMedicalOrderDetailVO> appVOS = new ArrayList<>();
        for (ExecMedicalOrderVO execMedicalOrderVO : execMedicalOrderVOList) {
            ExecMedicalOrderDetailVO execMedicalOrderDetailVO = CopyPropertiesUtil.normalCopyProperties(execMedicalOrderVO, ExecMedicalOrderDetailVO.class);
            if (canExecTime.contains(execMedicalOrderVO.getShouldExecTime())) {
                if (execMedicalOrderVO.getExecFlag() == 0L) {
                    //判断是否超时
                    if (execMedicalOrderVO.getShouldExecTime().isBefore(now)) {
                        execMedicalOrderDetailVO.setExecMedicalOrderType(ExecMedicalOrderType.NOT_EXEC_EXPIRED);
                    } else {
                        execMedicalOrderDetailVO.setExecMedicalOrderType(ExecMedicalOrderType.NOT_EXEC);
                    }
                } else {
                    execMedicalOrderDetailVO.setExecMedicalOrderType(getExecTypeAlreadyExec(execMedicalOrderVO));
                }
            } else {
                //没打的属于漏打卡
                if (execMedicalOrderVO.getExecFlag() == 0L) {
                    //判断是否是三天前该打卡，如果三天前的就设置为过期
                    if (execMedicalOrderVO.getShouldExecTime().isBefore(now.minusDays(3))) {
                        execMedicalOrderDetailVO.setExecMedicalOrderType(ExecMedicalOrderType.MISS_EXEC_EXPIRED);
                    } else {
                        execMedicalOrderDetailVO.setExecMedicalOrderType(ExecMedicalOrderType.MISS_EXEC);
                    }
                } else {
                    execMedicalOrderDetailVO.setExecMedicalOrderType(getExecTypeAlreadyExec(execMedicalOrderVO));
                }
            }
            appVOS.add(execMedicalOrderDetailVO);
        }
        return appVOS;
    }

    /**
     * 根据已执行的时间判断类型
     *
     * @param execMedicalOrderVO 执行记录
     * @return 执行类型
     */
    public static ExecMedicalOrderType getExecTypeAlreadyExec(ExecMedicalOrderVO execMedicalOrderVO) {
        if (execMedicalOrderVO.getExecTime().isBefore(execMedicalOrderVO.getShouldExecTime().minusHours(1))) {
            return ExecMedicalOrderType.AHEAD_EXEC;
        } else if (execMedicalOrderVO.getExecTime().isAfter(execMedicalOrderVO.getShouldExecTime().plusHours(1))) {
            return ExecMedicalOrderType.DELAYED_EXEC;
        } else {
            return ExecMedicalOrderType.NORMAL;
        }
    }


    public static ExecMedicalOrderVO nextExec(List<ExecMedicalOrderVO> execMedicalOrderVOList) {
        if (CollectionUtils.isEmpty(execMedicalOrderVOList)) {
            return null;
        }
        if (execMedicalOrderVOList.size() == 1) {
            return execMedicalOrderVOList.get(0).getExecFlag() == 1L ? null : execMedicalOrderVOList.get(0);
        }
        LocalDateTime now = LocalDateTime.now();
        List<ExecMedicalOrderVO> canExecList = getCanExecList(execMedicalOrderVOList, now);
        //取第一个没执行的点
        for (ExecMedicalOrderVO execMedicalOrderVO : canExecList) {
            if (execMedicalOrderVO.getExecFlag() == 0L) {
                return execMedicalOrderVO;
            }
        }
        return null;
    }

    public static List<ExecMedicalOrderVO> getCanExecList(List<ExecMedicalOrderVO> execMedicalOrderVOList, LocalDateTime now) {
        if (CollectionUtils.isEmpty(execMedicalOrderVOList)){
            return execMedicalOrderVOList;
        }
        //当前时间能打卡的列表，(T1<T2<now<T3)，会返回(T2，T3)
        //默认取最后一个点
         execMedicalOrderVOList= execMedicalOrderVOList.stream().sorted(Comparator.comparing(ExecMedicalOrderVO::getShouldExecTime)).collect(Collectors.toList());
        int index = execMedicalOrderVOList.size() - 1;
        for (int i = 0; i < execMedicalOrderVOList.size(); i++) {
            if (execMedicalOrderVOList.get(i).getShouldExecTime().isAfter(now)) {
                //取上一个点，没有上一个点就以这个点为起始
                if (i >= 1) {
                    index = i - 1;
                } else {
                    index = i;
                }
                break;
            }
        }
        return execMedicalOrderVOList.subList(index, execMedicalOrderVOList.size());
    }

}
