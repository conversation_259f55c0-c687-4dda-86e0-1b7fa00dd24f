package com.ykl.med.medical.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.base.utils.CustomizeLevelInfo;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

@Data
@TableName(value = "t_clinical_performance",autoResultMap = true)
public class ClinicalPerformanceDO extends BaseDO {
    private String name;
    private String code;
    // 症状表现分类，取的字典
    private String type;
    private String remark;
    private Integer sort;
    private CommonStatusEnum status;

    /**
     * 是否自定义等级
     */
    private Boolean customizeLevel;

    /**
     * 自定等级信息
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<CustomizeLevelInfo> customizeLevelInfos;

    /**
     * 三级的程度描述
     */
    private String remarkLevelOne;
    private String remarkLevelTwo;
    private String remarkLevelThree;
}
