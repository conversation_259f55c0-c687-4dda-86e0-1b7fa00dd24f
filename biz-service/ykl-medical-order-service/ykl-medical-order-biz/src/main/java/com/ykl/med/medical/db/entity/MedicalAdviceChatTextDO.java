package com.ykl.med.medical.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.OrderStatus;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.medical.enums.MedicalAdviceSourceEnum;
import com.ykl.med.medical.enums.MedicalAdviceTypeEnum;
import com.ykl.med.medical.enums.PrescriptionAuditStateEnum;
import com.ykl.med.medical.enums.PrescriptionTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 医嘱-问诊聊天chat Data Object
 *
 * <AUTHOR>
 * 2024-10-9 9:38:31
 */
@Data
@TableName("t_medical_advice_chat_text")
public class MedicalAdviceChatTextDO extends BaseDO {

    /*** 医嘱id */
    private Long medicalAdviceId;
    /*** 问诊聊天chat */
    private String consultChatText;
    /** 删除标志；false-未删，true-已删 */
    private Boolean deleteFlag;

}