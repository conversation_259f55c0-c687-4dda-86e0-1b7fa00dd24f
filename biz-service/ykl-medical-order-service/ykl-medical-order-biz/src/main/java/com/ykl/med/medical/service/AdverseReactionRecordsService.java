package com.ykl.med.medical.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.medical.db.entity.AdverseReactionDO;
import com.ykl.med.medical.db.entity.AdverseReactionRecordsDO;
import com.ykl.med.medical.db.mapper.AdverseReactionRecordsMapper;
import com.ykl.med.medical.vo.adverse.AdverseReactionAddVO;
import com.ykl.med.medical.vo.adverse.AdverseReactionRecordsQueryVO;
import com.ykl.med.medical.vo.adverse.AdverseReactionRecordsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
public class AdverseReactionRecordsService {
    @Resource
    private AdverseReactionRecordsMapper adverseReactionRecordsMapper;
    @Resource
    private IdServiceImpl idService;

    public List<AdverseReactionRecordsVO> getByAdverseReactionIds(List<Long> ids) {
        LambdaQueryWrapper<AdverseReactionRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AdverseReactionRecordsDO::getAdverseReactionId, ids);
        queryWrapper.eq(AdverseReactionRecordsDO::getDeleteFlag, false);
        List<AdverseReactionRecordsDO> adverseReactionRecordsDOS = adverseReactionRecordsMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(adverseReactionRecordsDOS, AdverseReactionRecordsVO.class);
    }

    public PageResult<AdverseReactionRecordsVO> query(AdverseReactionRecordsQueryVO queryVO) {
        LambdaQueryWrapper<AdverseReactionRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdverseReactionRecordsDO::getAdverseReactionId, queryVO.getAdverseReactionId());
        queryWrapper.eq(AdverseReactionRecordsDO::getDeleteFlag, false);
        queryWrapper.orderByDesc(AdverseReactionRecordsDO::getStartTime);
        Page<AdverseReactionRecordsDO> adverseReactionRecordsDOPage = adverseReactionRecordsMapper.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), queryWrapper);
        List<AdverseReactionRecordsVO> adverseReactionRecordsVOS = CopyPropertiesUtil.normalCopyProperties(adverseReactionRecordsDOPage.getRecords(), AdverseReactionRecordsVO.class);
        return new PageResult<>(adverseReactionRecordsVOS, adverseReactionRecordsDOPage.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(AdverseReactionAddVO addVO, Long adverseReactionId) {
        AdverseReactionRecordsDO adverseReactionRecordsDO = CopyPropertiesUtil.normalCopyProperties(addVO, AdverseReactionRecordsDO.class);
        adverseReactionRecordsDO.setPatientId(addVO.getPatientId());
        adverseReactionRecordsDO.setId(idService.nextId());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(addVO.getStartTime(), formatter);
        adverseReactionRecordsDO.setStartTime(dateTime);
        adverseReactionRecordsDO.setAdverseReactionId(adverseReactionId);
        adverseReactionRecordsDO.setCreateUserId(addVO.getCurrentUserId());
        adverseReactionRecordsMapper.insert(adverseReactionRecordsDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        AdverseReactionRecordsDO adverseReactionRecordsDO = adverseReactionRecordsMapper.selectById(id);
        adverseReactionRecordsDO.setDeleteFlag(true);
        adverseReactionRecordsMapper.updateById(adverseReactionRecordsDO);
    }
}
