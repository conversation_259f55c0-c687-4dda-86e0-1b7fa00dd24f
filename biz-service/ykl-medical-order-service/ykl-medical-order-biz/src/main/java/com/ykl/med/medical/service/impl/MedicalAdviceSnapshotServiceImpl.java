package com.ykl.med.medical.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.vo.ConsultDiagnosisVO;
import com.ykl.med.masterdata.api.DiseaseFeign;
import com.ykl.med.masterdata.vo.resp.DiseaseVO;
import com.ykl.med.medical.db.entity.MedicalAdviceSnapshotDO;
import com.ykl.med.medical.db.mapper.MedicalAdviceSnapshotMapper;
import com.ykl.med.medical.service.MedicalAdviceSnapshotService;
import com.ykl.med.medical.vo.medicalAdvice.CreateMedicalAdviceSnapshotReqVO;
import com.ykl.med.medical.vo.medicalAdvice.MedicalAdviceSnapshotVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/12
 */
@Slf4j
@Service
public class MedicalAdviceSnapshotServiceImpl implements MedicalAdviceSnapshotService {


    @Resource
    private IdServiceImpl idService;
    @Resource
    private MedicalAdviceSnapshotMapper medicalAdviceSnapshotMapper;


    @Override
    public MedicalAdviceSnapshotVO queryByMedicalAdviceId(Long medicalAdviceId) {
        List<MedicalAdviceSnapshotVO> snapshotVOS = queryByMedicalAdviceIds(Collections.singletonList(medicalAdviceId));
        if (CollectionUtils.isEmpty(snapshotVOS)) {
            return null;
        }

        return snapshotVOS.get(0);
    }

    @Override
    public void save(CreateMedicalAdviceSnapshotReqVO param) {
        MedicalAdviceSnapshotDO build = new MedicalAdviceSnapshotDO();
        build.setId(idService.nextId());
        build.setDeleteFlag(false);
        build.setMedicalAdviceId(param.getMedicalAdviceId());
        build.setDiagnosis(JSONObject.toJSONString(param.getDiagnosisIds()));
        build.setOnsetTime(param.getOnsetTime());
        build.setAppeal(param.getAppeal());
        build.setPastMedicalHistory(param.getPastMedicalHistory());
        build.setAllergyHistory(param.getAllergyHistory());
        build.setMedicalHistoryDesc(param.getMedicalHistoryDesc());
        build.setPatientId(param.getPatientId());
        build.setPatientName(param.getPatientName());
        build.setPatientSex(param.getPatientSex());
        build.setPatientAge(param.getPatientAge());
        build.setPatientDiseases(param.getPatientDiseases());
        build.setPatientStage(param.getPatientStage());
        build.setPatientClinicalStaging(param.getPatientClinicalStaging());
        build.setPatientPathologicalType(param.getPatientPathologicalType());
        build.setPatientOtherDisease(param.getPatientOtherDisease());
        build.setPatientCurrentMedication(param.getPatientCurrentMedication());
        medicalAdviceSnapshotMapper.insert(build);
    }

    @Override
    public List<MedicalAdviceSnapshotVO> queryByMedicalAdviceIds(List<Long> medicalAdviceIds) {
        LambdaQueryWrapper<MedicalAdviceSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MedicalAdviceSnapshotDO::getMedicalAdviceId, medicalAdviceIds);
        queryWrapper.eq(MedicalAdviceSnapshotDO::getDeleteFlag, false);
        List<MedicalAdviceSnapshotDO> consultDiseaseHistoryDOS = medicalAdviceSnapshotMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(consultDiseaseHistoryDOS)) {
            return new ArrayList<>();
        }

        return consultDiseaseHistoryDOS.stream().map(e -> {
            MedicalAdviceSnapshotVO consultDiseaseHistoryVO = CopyPropertiesUtil.normalCopyProperties(e, MedicalAdviceSnapshotVO.class);
            consultDiseaseHistoryVO.setDiagnosis(JSONObject.parseArray(e.getDiagnosis(), ConsultDiagnosisVO.class));
            return consultDiseaseHistoryVO;
        }).collect(Collectors.toList());
    }
}
