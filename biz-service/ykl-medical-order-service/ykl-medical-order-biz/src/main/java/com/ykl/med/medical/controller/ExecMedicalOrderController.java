package com.ykl.med.medical.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.medical.api.ExecMedicalOrderFeign;
import com.ykl.med.medical.service.ExecMedicalOrderService;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderDetailVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderQueryVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderVO;
import com.ykl.med.push.api.PushSocketFeign;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "执行用药")
@RestController
@RequestMapping("execMedicalOrder")
@Validated
public class ExecMedicalOrderController implements ExecMedicalOrderFeign {
    @Resource
    private ExecMedicalOrderService execMedicalOrderService;
    @Resource
    private PushSocketFeign pushSocketFeign;

    @Override
    @PostMapping("/exec")
    public ExecMedicalOrderDetailVO exec(@RequestParam(value = "id") Long id,
                     @RequestParam(value = "execTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime execTime) {
        ExecMedicalOrderDetailVO execMedicalOrderVO = execMedicalOrderService.exec(id, execTime);
        pushSocketFeign.sendMedicalOrderChangeNotice(execMedicalOrderVO.getPatientId());
        return execMedicalOrderVO;
    }

    @Override
    @PostMapping("/notify")
    public void notify(@RequestParam(value = "id") Long id,
                       @RequestParam(value = "notifyFlag") Long notifyFlag) {
        execMedicalOrderService.notify(id, notifyFlag);
    }


    @Override
    @PostMapping("/delete")
    public void delete(@RequestParam(value = "id") Long id) {
        execMedicalOrderService.delete(id);
    }


    @Override
    @PostMapping("/getById")
    public ExecMedicalOrderVO getById(@RequestParam(value = "id") Long id) {
        return execMedicalOrderService.getById(id);
    }

    @Override
    @PostMapping("/query")
    public List<ExecMedicalOrderVO> query(@RequestBody @Valid ExecMedicalOrderQueryVO queryVO) {
        return execMedicalOrderService.query(queryVO);
    }

    @Override
    @PostMapping("/queryDetail")
    public List<ExecMedicalOrderDetailVO> queryDetail(@RequestBody @Valid ExecMedicalOrderQueryVO queryVO){
        return execMedicalOrderService.queryDetail(queryVO);
    }

    @Override
    @PostMapping("/queryPage")
    public PageResult<ExecMedicalOrderDetailVO> queryPage(@RequestBody @Valid ExecMedicalOrderQueryVO queryVO) {
        return execMedicalOrderService.queryPage(queryVO);
    }

    @Override
    @PostMapping("/nextExec")
    public ExecMedicalOrderVO nextExec(@RequestParam(value = "medicalOrderId") Long medicalOrderId){
        return execMedicalOrderService.nextExec(medicalOrderId);
    }

}
