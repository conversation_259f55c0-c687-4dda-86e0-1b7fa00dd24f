package com.ykl.med.medical.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.CommonResult;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.CrudUtils;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.api.FrequencyDataFeign;
import com.ykl.med.masterdata.api.FrequencyFeign;
import com.ykl.med.masterdata.entiry.dto.FrequencyBizAddDTO;
import com.ykl.med.masterdata.entiry.dto.FrequencyBizQueryDTO;
import com.ykl.med.masterdata.entiry.vo.FrequencyBizVO;
import com.ykl.med.masterdata.vo.FrequencyVO;
import com.ykl.med.medical.constans.MedicalErrorCodeConstants;
import com.ykl.med.medical.db.entity.MedicalOrderDO;
import com.ykl.med.medical.db.mapper.MedicalOrderMapper;
import com.ykl.med.medical.enums.MedicalOrderItemClass;
import com.ykl.med.medical.enums.MedicalOrderStatus;
import com.ykl.med.medical.utils.ExecMedicalOrderConvert;
import com.ykl.med.medical.utils.MedicalOrderConvert;
import com.ykl.med.medical.utils.MedicalOrderTimeUtil;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderAddVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderDetailVO;
import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderVO;
import com.ykl.med.medical.vo.execOrder.PreExecMedicalOrderVO;
import com.ykl.med.medical.vo.order.*;
import com.ykl.med.pharmacy.api.DrugFeign;
import com.ykl.med.pharmacy.entity.dto.DrugQueryDTO;
import com.ykl.med.pharmacy.entity.vo.DrugVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.enums.EventTaskOperationType;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.user.api.UserFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MedicalOrderService {
    public static final String ONCE = "ONCE";
    @Resource
    private MedicalOrderMapper medicalOrderMapper;
    @Resource
    private FrequencyFeign frequencyFeign;
    @Resource
    private ExecMedicalOrderService execMedicalOrderService;
    @Resource
    private IdServiceImpl idService;
    @Resource
    private DrugFeign drugFeign;
    @Resource
    private EventTaskFeign eventTaskFeign;
    @Resource
    private FrequencyDataFeign frequencyDataFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private DictDataFeign dictDataFeign;
    private final static String TARGET_DRUGS_DICT = "DRUG_TYPES_RADIOACTIVE_7";

    public MedicalOrderService(MedicalOrderMapper medicalOrderMapper, IdServiceImpl idService, EventTaskFeign eventTaskFeign,
                               ExecMedicalOrderService execMedicalOrderService, FrequencyFeign frequencyFeign,
                               FrequencyDataFeign frequencyDataFeign, DrugFeign drugFeign) {
        this.medicalOrderMapper = medicalOrderMapper;
        this.idService = idService;
        this.eventTaskFeign = eventTaskFeign;
        this.execMedicalOrderService = execMedicalOrderService;
        this.frequencyFeign = frequencyFeign;
        this.frequencyDataFeign = frequencyDataFeign;
        this.drugFeign = drugFeign;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<MedicalOrderVO> batchAdd(MedicalOrderBatchAddVO medicalOrderBatchAddVO) {
        List<MedicalOrderVO> medicalOrderVOS = new ArrayList<>();
        for (MedicalOrderAddVO addVO : medicalOrderBatchAddVO.getAddVOS()) {
            addVO.setCurrentUserId(medicalOrderBatchAddVO.getCurrentUserId());
            addVO.setPatientId(medicalOrderBatchAddVO.getPatientId());
            addVO.setFrequencyBizAddDTO(new FrequencyBizAddDTO().setFrequencyId(addVO.getFrequencyId()));
            medicalOrderVOS.add(this.add(addVO));
        }
        return medicalOrderVOS;
    }


    @Transactional(rollbackFor = Exception.class)
    public MedicalOrderVO add(MedicalOrderAddVO addVO) {
        log.info("新增用药，addVO:{}", JSONObject.toJSONString(addVO));
        DrugVO drugVO = CrudUtils.getById(drugFeign::crud, addVO.getItemId());
        MedicalOrderDO medicalOrderDO = MedicalOrderConvert.addVOToDO(drugVO, addVO);
        //频率组装
        this.addExecPlan(addVO.getFrequencyBizAddDTO(), medicalOrderDO);
        //临时一次并且没有结束时间，就把结束时间设置为开始当天的最后一刻
        if (ONCE.equals(medicalOrderDO.getFrequencyQuantityUnit()) && medicalOrderDO.getEndExecTime() == null) {
            medicalOrderDO.setEndExecTime(LocalDateTimeUtil.endOfDay(medicalOrderDO.getStartExecTime(), true));
        }
        medicalOrderDO.setId(idService.nextId());
        medicalOrderMapper.insert(medicalOrderDO);
        this.genMedicalOrderExecPlan(medicalOrderDO.getId());
        if (medicalOrderDO.getStatus() == MedicalOrderStatus.NOT_STARTED) {
            //添加用药开始的时间，设置执行时间为开始时间
            EventTaskAddVO eventTaskAddVO = MedicalOrderConvert.doToEventTaskMedicalOrderStartAddVO(medicalOrderDO);
            eventTaskFeign.addEventTask(eventTaskAddVO);
        }
        this.addEvent(medicalOrderDO, EventTaskOperationType.ADD, addVO.getCurrentUserId());
        return MedicalOrderConvert.doToVO(medicalOrderDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(MedicalOrderUpdateVO updateVO) {
        log.info("update用药，addVO:{}", JSONObject.toJSONString(updateVO));
        MedicalOrderDO medicalOrderDO = medicalOrderMapper.selectById(updateVO.getId());
        if (medicalOrderDO.getStatus() != MedicalOrderStatus.INVALID && updateVO.getStatus() == MedicalOrderStatus.INVALID) {
            medicalOrderDO.setInvalidTime(LocalDateTime.now());
        }
        MedicalOrderConvert.updateVOToDO(updateVO, medicalOrderDO);
        if (updateVO.getFrequencyBizAddDTO() != null) {
            Map<Long, List<ExecMedicalOrderVO>> execMedicalOrderListMap = execMedicalOrderService.getExecMedicalOrderListMapToday(Lists.newArrayList(medicalOrderDO.getId()));
            LocalDateTime lastExecTime = null;
            if (execMedicalOrderListMap.get(medicalOrderDO.getId()) != null) {
                //把列表中execFlag为1的最大时间取出来
                lastExecTime = execMedicalOrderListMap.get(medicalOrderDO.getId()).stream()
                        .filter(a -> a.getExecFlag() == 1L)
                        .map(ExecMedicalOrderVO::getShouldExecTime)
                        .max(LocalDateTime::compareTo)
                        .orElse(null);
            }
            //不允许 lastExecTime > frequencyStartTime
            if (CollectionUtils.isNotEmpty(updateVO.getFrequencyBizAddDTO().getFrequencyStartTime())) {
                for (String frequencyStartTime : updateVO.getFrequencyBizAddDTO().getFrequencyStartTime()) {
                    LocalDateTime frequencyStartTimeDataTime;
                    try {
                        //这个字段有可能是时间戳，有可能是年月日时分秒
                        frequencyStartTimeDataTime = LocalDateTime.parse(frequencyStartTime, MedicalOrderTimeUtil.INPUT_FORMATTER);
                    } catch (DateTimeParseException e) {
                        //按时间戳转换
                        frequencyStartTimeDataTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(frequencyStartTime)), ZoneId.systemDefault());
                    }
                    if (lastExecTime != null && lastExecTime.isAfter(frequencyStartTimeDataTime)) {
                        throw new ServiceException(MedicalErrorCodeConstants.FREQUENCY_START_TIME_ERROR);
                    }
                    if (medicalOrderDO.getStartExecTime().isAfter(frequencyStartTimeDataTime)) {
                        throw new ServiceException(MedicalErrorCodeConstants.FREQUENCY_START_TIME_ERROR1);
                    }
                }
            }
            this.addExecPlan(updateVO.getFrequencyBizAddDTO(), medicalOrderDO);
        }
        medicalOrderMapper.updateById(medicalOrderDO);
        if (medicalOrderDO.getStatus() == MedicalOrderStatus.INVALID || medicalOrderDO.getStatus() == MedicalOrderStatus.DISABLE) {
            execMedicalOrderService.deleteAll(medicalOrderDO.getId());
        } else {
            //进行中要重新算今天的打卡
            if (updateVO.getFrequencyBizAddDTO() != null) {
                execMedicalOrderService.deleteToday(medicalOrderDO.getId());
                this.genMedicalOrderExecPlan(medicalOrderDO.getId());
            }
        }
        EventTaskOperationType eventTaskOperationType = updateVO.getStatus() == MedicalOrderStatus.INVALID || updateVO.getStatus() == MedicalOrderStatus.DISABLE
                ? EventTaskOperationType.DELETE : EventTaskOperationType.UPDATE;
        this.addEvent(medicalOrderDO, eventTaskOperationType, updateVO.getCurrentUserId());
    }

    public MedicalOrderDetailVO getById(Long id) {
        MedicalOrderDO medicalOrderDO = medicalOrderMapper.selectById(id);
        Map<Long, List<ExecMedicalOrderVO>> execMedicalOrderListMap = execMedicalOrderService.getExecMedicalOrderListMapToday(Lists.newArrayList(id));
        MedicalOrderDetailVO medicalOrderVO = MedicalOrderConvert.doToDetailVO(medicalOrderDO);
        medicalOrderVO.setExecMedicalOrderList(execMedicalOrderListMap.get(medicalOrderDO.getId()));
        LocalDateTime lastExecTime = null;
        if (medicalOrderVO.getExecMedicalOrderList() != null) {
            //把列表中execFlag为1的最大时间取出来
            lastExecTime = medicalOrderVO.getExecMedicalOrderList().stream()
                    .filter(a -> a.getExecFlag() == 1L)
                    .map(ExecMedicalOrderVO::getShouldExecTime)
                    .max(LocalDateTime::compareTo)
                    .orElse(null);
        }
        FrequencyBizVO frequencyBizVO = this.getFrequencyExecTimeList(medicalOrderDO, lastExecTime);
        medicalOrderVO.setFrequencyBiz(frequencyBizVO);
//        medicalOrderVO.setFrequencyExecTimeList(frequencyBizVO.getFrequencyExecTimeList());
//        medicalOrderVO.setFrequencyType(frequencyBizVO.getFrequencyType());
//        medicalOrderVO.setFrequencyGenExecTimeList(frequencyBizVO.getFrequencyGenExecTimeList());
//        medicalOrderVO.setNextGenExecTimeList(frequencyBizVO.getNextGenExecTimeList());
        return medicalOrderVO;
    }

    public List<MedicalOrderVO> getByIds(List<Long> ids) {
        List<MedicalOrderDO> medicalOrderDOList = medicalOrderMapper.selectBatchIds(ids);
        return CopyPropertiesUtil.normalCopyProperties(medicalOrderDOList, MedicalOrderVO.class);
    }

    public List<MedicalOrderListSimpleVO> getSimpleByPatientId(Long patientId) {
        LambdaQueryWrapper<MedicalOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicalOrderDO::getPatientId, patientId);
        List<MedicalOrderDO> medicalOrderDOList = medicalOrderMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(medicalOrderDOList, MedicalOrderListSimpleVO.class);
    }

    public List<MedicalOrderListVO> getByPatientId(Long patientId) {
        LambdaQueryWrapper<MedicalOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicalOrderDO::getPatientId, patientId);
        List<MedicalOrderDO> medicalOrderDOList = medicalOrderMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(medicalOrderDOList, MedicalOrderListVO.class);
    }

    public List<PreExecMedicalOrderVO> getPreExecMedicalOrderList(Long patientId) {
        List<MedicalOrderDO> medicalOrderDOList = medicalOrderMapper.selectActiveOrdersByPatientId(patientId);
        if (CollectionUtils.isEmpty(medicalOrderDOList)) {
            return Collections.emptyList();
        }
        List<Long> medicalOrderIdList = medicalOrderDOList.stream().map(MedicalOrderDO::getId).collect(Collectors.toList());
        Map<Long, List<ExecMedicalOrderVO>> execMedicalOrderListMap = execMedicalOrderService.getExecMedicalOrderListMapToday(medicalOrderIdList);
        List<PreExecMedicalOrderVO> preExecMedicalOrderVOList = new ArrayList<>();
        for (MedicalOrderDO medicalOrderDO : medicalOrderDOList) {
            List<ExecMedicalOrderVO> execMedicalOrderList = execMedicalOrderListMap.get(medicalOrderDO.getId());
            //LocalDateTime realStartTime = MedicalOrderTimeUtil.getRealStartExecTime(medicalOrderDO, execMedicalOrderList);
            if (ONCE.equals(medicalOrderDO.getFrequencyQuantityUnit())) {
                PreExecMedicalOrderVO preExecMedicalOrderVO = MedicalOrderTimeUtil.doToOncePreExecMedicalOrderVO(medicalOrderDO, execMedicalOrderList);
                if (preExecMedicalOrderVO != null) {
                    preExecMedicalOrderVOList.add(preExecMedicalOrderVO);
                }
            } else {
                FrequencyBizQueryDTO frequencyBizQueryDTO = MedicalOrderTimeUtil.buildFrequencyBizQueryDTOPre(medicalOrderDO, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
                log.debug("queryBizData 入参:{}", JSONObject.toJSONString(frequencyBizQueryDTO));
                PageResult<FrequencyBizVO> frequencyBizPageResult = frequencyDataFeign.queryBizData(frequencyBizQueryDTO);
                log.debug("queryBizData 返回结果:{}", JSONObject.toJSONString(frequencyBizPageResult));
                if (frequencyBizPageResult != null && CollectionUtils.isNotEmpty(frequencyBizPageResult.getList())) {
                    preExecMedicalOrderVOList.add(MedicalOrderTimeUtil.doToNormalPreExecMedicalOrderVO(medicalOrderDO, frequencyBizPageResult.getList().get(0), execMedicalOrderList));
                }
            }
        }
        return preExecMedicalOrderVOList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void genMedicalOrderExecPlan(Long medicalOrderId) {
        log.info("生成当天用药执行计划，medicalOrderId:{}", medicalOrderId);
        MedicalOrderDO medicalOrderDO = medicalOrderMapper.selectById(medicalOrderId);
        List<LocalDateTime> execTimeList = new ArrayList<>();
        if (ONCE.equals(medicalOrderDO.getFrequencyQuantityUnit())) {
            if (MedicalOrderTimeUtil.isToday(medicalOrderDO.getStartExecTime())) {
                execTimeList.add(medicalOrderDO.getStartExecTime());
            }
        } else {
            FrequencyBizQueryDTO frequencyBizQueryDTO = MedicalOrderTimeUtil.buildTodayFrequencyBizQueryDTO(medicalOrderDO);
            log.debug("genMedicalOrderExecPlan queryBizData 入参:{}", JSONObject.toJSONString(frequencyBizQueryDTO));
            PageResult<FrequencyBizVO> frequencyBizPageResult = frequencyDataFeign.queryBizData(frequencyBizQueryDTO);
            log.debug("genMedicalOrderExecPlan queryBizData 返回结果:{}", JSONObject.toJSONString(frequencyBizPageResult));
            if (frequencyBizPageResult == null || CollectionUtils.isEmpty(frequencyBizPageResult.getList())) {
                return;
            }
//            AssertUtils.notNull(frequencyBizPageResult, MedicalErrorCodeConstants.FREQUENCY_NOT_EXIST);
//            AssertUtils.notEmpty(frequencyBizPageResult.getList(), MedicalErrorCodeConstants.FREQUENCY_NOT_EXIST);
            FrequencyBizVO frequencyBizVO = frequencyBizPageResult.getList().get(0);
            for (String time : frequencyBizVO.getFrequencyGenExecTimeList()) {
                execTimeList.add(LocalDateTime.parse(time, MedicalOrderTimeUtil.INPUT_FORMATTER));
            }
        }
        if (CollectionUtils.isNotEmpty(execTimeList)) {
            execMedicalOrderService.deleteToday(medicalOrderDO.getId());
            List<ExecMedicalOrderAddVO> addVOList = MedicalOrderConvert.doToExecMedicalOrderAddVO(medicalOrderDO, execTimeList);
            if (CollectionUtils.isNotEmpty(addVOList)) {
                execMedicalOrderService.batchAdd(addVOList, medicalOrderDO.getItemName());
            }
        }
        medicalOrderDO.setLastExecPlanTime(LocalDateTime.now());
        medicalOrderMapper.updateById(medicalOrderDO);
    }


    public PageResult<MedicalOrderListAppVO> queryApp(MedicalOrderQueryAppVO queryVO) {
        if (StringUtils.isNotEmpty(queryVO.getDrugType())) {
            handleDrugType(queryVO);
            if (CollectionUtils.isEmpty(queryVO.getItemId())) {
                return PageResult.empty();
            }
        }
        LambdaQueryWrapper<MedicalOrderDO> buildQueryApp = medicalOrderMapper.buildQueryApp(queryVO);
        Page<MedicalOrderDO> medicalOrderDOPage = medicalOrderMapper.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), buildQueryApp);
        return processMedicalOrderPage(medicalOrderDOPage, MedicalOrderConvert::doToAppVO);
    }

    public PageResult<MedicalOrderListWebVO> queryWeb(MedicalOrderQueryWebVO queryVO) {
        if (StringUtils.isNotEmpty(queryVO.getDrugType())) {
            handleDrugType(queryVO);
            if (CollectionUtils.isEmpty(queryVO.getItemId())) {
                return PageResult.empty();
            }
        }
        LambdaQueryWrapper<MedicalOrderDO> queryWrapper = medicalOrderMapper.buildQueryWeb(queryVO);
        Page<MedicalOrderDO> medicalOrderDOPage = medicalOrderMapper.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), queryWrapper);
        return processMedicalOrderPage(medicalOrderDOPage, MedicalOrderConvert::doToWebVO);
    }

    private void handleDrugType(MedicalOrderQueryVO queryVO) {
        List<DrugVO> drugVOS = CrudUtils.getList(drugFeign::crud, new DrugQueryDTO().setDrugType(Lists.newArrayList(queryVO.getDrugType())));
        if (CollectionUtils.isEmpty(drugVOS)) {
            queryVO.setItemId(Collections.emptyList());
        } else {
            queryVO.setItemId(drugVOS.stream().map(a -> Long.valueOf(a.getId())).collect(Collectors.toList()));
        }
    }

    private <R extends MedicalOrderListVO> PageResult<R> processMedicalOrderPage(Page<MedicalOrderDO> medicalOrderDOPage, Function<MedicalOrderDO, R> convertFunction) {
        if (CollectionUtils.isEmpty(medicalOrderDOPage.getRecords())) {
            return PageResult.empty();
        }
        List<Long> medicalOrderIds = medicalOrderDOPage.getRecords().stream().map(MedicalOrderDO::getId).collect(Collectors.toList());
        Map<Long, List<ExecMedicalOrderDetailVO>> execMedicalOrderListMap = execMedicalOrderService.getExecMedicalOrderDetailMapToday(medicalOrderIds);
        List<Long> itemIds = medicalOrderDOPage.getRecords().stream().map(MedicalOrderDO::getItemId).distinct().collect(Collectors.toList());
        List<DrugVO> drugVOS = CrudUtils.getByIdsLong(drugFeign::crud, itemIds);
        Map<Long, DrugVO> drugVOMap = drugVOS.stream().collect(Collectors.toMap(e -> Long.valueOf(e.getId()), Function.identity()));
        List<R> medicalOrderVOS = new ArrayList<>();
        for (MedicalOrderDO medicalOrderDO : medicalOrderDOPage.getRecords()) {
            R medicalOrderVO = convertFunction.apply(medicalOrderDO);
            medicalOrderVO.setExecMedicalOrderList(execMedicalOrderListMap.get(medicalOrderDO.getId()));
            medicalOrderVO.setFrequencyBiz(this.getFrequencyExecTimeList(medicalOrderDO, null));
//            medicalOrderVO.setFrequencyExecTimeList(frequencyBizVO.getFrequencyExecTimeList());
//            medicalOrderVO.setFrequencyType(frequencyBizVO.getFrequencyType());
//            medicalOrderVO.setFrequencyGenExecTimeList(frequencyBizVO.getFrequencyGenExecTimeList());
            medicalOrderVO.setGoodsName(drugVOMap.containsKey(medicalOrderDO.getItemId()) ? drugVOMap.get(medicalOrderDO.getItemId()).getGoodsName() : "");
            if (CollectionUtils.isNotEmpty(medicalOrderVO.getExecMedicalOrderList())) {
                List<ExecMedicalOrderVO> execMedicalOrderVOList = CopyPropertiesUtil.normalCopyProperties(medicalOrderVO.getExecMedicalOrderList(), ExecMedicalOrderVO.class);
                ExecMedicalOrderVO execMedicalOrderVO = ExecMedicalOrderConvert.nextExec(execMedicalOrderVOList);
                medicalOrderVO.setNextExecTime(execMedicalOrderVO == null ? null : execMedicalOrderVO.getShouldExecTime());
            }
            medicalOrderVOS.add(medicalOrderVO);
        }
        return new PageResult<>(medicalOrderVOS, medicalOrderDOPage.getTotal());
    }

    public List<MedicalOrderVO> waitExecPlanGen() {
        LambdaQueryWrapper<MedicalOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicalOrderDO::getStatus, MedicalOrderStatus.ENABLE);
        queryWrapper.eq(MedicalOrderDO::getItemClass, MedicalOrderItemClass.MEDICAL_ORDER_ITEM_CLASS_DRUG);
        queryWrapper.le(MedicalOrderDO::getStartExecTime, LocalDateTime.now());
        queryWrapper.le(MedicalOrderDO::getLastExecPlanTime, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        //判断结束时间是否大于当前时间 或者结束时间为空
        queryWrapper.and(i -> i.isNull(MedicalOrderDO::getEndExecTime).or().ge(MedicalOrderDO::getEndExecTime, LocalDateTime.now()));
        List<MedicalOrderDO> medicalOrderDOList = medicalOrderMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(medicalOrderDOList, MedicalOrderVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public void autoStopMedicalOrder() {
        LambdaQueryWrapper<MedicalOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicalOrderDO::getStatus, MedicalOrderStatus.ENABLE);
        queryWrapper.le(MedicalOrderDO::getEndExecTime, LocalDateTime.now());
        List<MedicalOrderDO> medicalOrderDOList = medicalOrderMapper.selectList(queryWrapper);
        for (MedicalOrderDO medicalOrderDO : medicalOrderDOList) {
            medicalOrderDO.setStatus(MedicalOrderStatus.DISABLE);
            medicalOrderMapper.updateById(medicalOrderDO);
            execMedicalOrderService.deleteAll(medicalOrderDO.getId());
            this.addEvent(medicalOrderDO, null, null);
        }
    }


    private FrequencyBizVO getFrequencyExecTimeList(MedicalOrderDO medicalOrderDO, LocalDateTime lastExecTime) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (ONCE.equals(medicalOrderDO.getFrequencyQuantityUnit())) {
            //临时一次不管
            FrequencyBizVO frequencyBizVO = new FrequencyBizVO();
            frequencyBizVO.setFrequencyExecTimeList(Lists.newArrayList(inputFormatter.format(medicalOrderDO.getStartExecTime())));
            frequencyBizVO.setFrequencyType("临时一次");
            frequencyBizVO.setFrequencyId("5");
            return frequencyBizVO;
        }
        FrequencyBizQueryDTO frequencyBizQueryDTO = new FrequencyBizQueryDTO();
        if (lastExecTime != null) {
            lastExecTime = lastExecTime.plusSeconds(1);
            frequencyBizQueryDTO.setStartTime(lastExecTime.format(inputFormatter));
        } else {
            if (medicalOrderDO.getStartExecTime().isAfter(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))) {
                frequencyBizQueryDTO.setStartTime(inputFormatter.format(medicalOrderDO.getStartExecTime()));
            } else {
                frequencyBizQueryDTO.setStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()).format(inputFormatter));
            }
        }
        if (medicalOrderDO.getEndExecTime() != null) {
            frequencyBizQueryDTO.setEndTime(inputFormatter.format(medicalOrderDO.getEndExecTime()));
        }
        frequencyBizQueryDTO.setId(medicalOrderDO.getFrequencyBizId().toString());
        log.debug("queryBizData 入参:{}", JSONObject.toJSONString(frequencyBizQueryDTO));
        PageResult<FrequencyBizVO> frequencyBizPageResult = frequencyDataFeign.queryBizData(frequencyBizQueryDTO);
        log.debug("frequencyBizPageResult 结果:{}", JSONObject.toJSONString(frequencyBizPageResult));
        if (frequencyBizPageResult != null && CollectionUtils.isNotEmpty(frequencyBizPageResult.getList())) {
            return frequencyBizPageResult.getList().get(0);
        }
        return null;
        //throw new ServiceException(MedicalErrorCodeConstants.FREQUENCY_NOT_EXIST);
    }

    private void addExecPlan(FrequencyBizAddDTO frequencyBizAddDTO, MedicalOrderDO medicalOrderDO) {

        if (StringUtils.isNotEmpty(frequencyBizAddDTO.getFrequencyId())) {
            medicalOrderDO.setFrequencyId(Long.valueOf(frequencyBizAddDTO.getFrequencyId()));
            FrequencyVO frequencyVO = frequencyFeign.getById(medicalOrderDO.getFrequencyId());
            AssertUtils.notNull(frequencyVO, MedicalErrorCodeConstants.FREQUENCY_NOT_EXIST);
            medicalOrderDO.setFrequencyName(frequencyVO.getName());
            medicalOrderDO.setFrequencyQuantityUnit(frequencyVO.getQuantityUnit());
            if (ONCE.equals(frequencyVO.getQuantityUnit())) {
                //临时一次不管
                return;
            }
        }
        if (frequencyBizAddDTO.getFrequencyStartTime() == null) {
            frequencyBizAddDTO.setFrequencyStartTime(Lists.newArrayList(medicalOrderDO.getStartExecTime().format(MedicalOrderTimeUtil.INPUT_FORMATTER)));
        }

        // 判断生成执行时间列表是否大于设置的次数标志
        boolean flag = false;

        // 如果是每周/每月,且有结束时间,则需要设置开始时间和结束时间,并得到执行时间列表,用于判断之后是否有执行计划
        if (StringUtils.isNotBlank(frequencyBizAddDTO.getFrequencyType())
                && (frequencyBizAddDTO.getFrequencyType().equals("每周") || frequencyBizAddDTO.getFrequencyType().equals("每月"))
                && medicalOrderDO.getEndExecTime() != null) {
            frequencyBizAddDTO.setStartTime(medicalOrderDO.getStartExecTime().format(MedicalOrderTimeUtil.INPUT_FORMATTER));
            frequencyBizAddDTO.setEndTime(medicalOrderDO.getEndExecTime().format(MedicalOrderTimeUtil.INPUT_FORMATTER));
            flag = true;
        }

        log.info("添加执行计划入参:{}", JSONObject.toJSONString(frequencyBizAddDTO));
        CommonResult<FrequencyBizVO> voCommonResult = frequencyDataFeign.addBizData(frequencyBizAddDTO);
        log.info("添加执行计划结果:{}", JSONObject.toJSONString(voCommonResult));
        if (voCommonResult != null && voCommonResult.getData() != null) {

            // 判断生成的执行时间列表数量是否大于设置的次数
            if (flag) {
                if (voCommonResult.getData().getFrequencyGenExecTimeList().size() < frequencyBizAddDTO.getFrequencyExecTimeList().size()) {
                    throw new ServiceException(MedicalErrorCodeConstants.FREQUENCY_NOTE_TIME_OVER_RANGE.getCode(),
                            MedicalErrorCodeConstants.FREQUENCY_NOTE_TIME_OVER_RANGE.getMsg()
                                    + ",\n用药周期结束时间为：" + medicalOrderDO.getEndExecTime().format(MedicalOrderTimeUtil.INPUT_FORMATTER));
                }
            }

            medicalOrderDO.setFrequencyBizId(Long.valueOf(voCommonResult.getData().getId()));
            if (StringUtils.isEmpty(frequencyBizAddDTO.getFrequencyId())) {
                //自定义频率
                medicalOrderDO.setFrequencyName(voCommonResult.getData().getFrequencyName());
                medicalOrderDO.setFrequencyQuantityUnit(voCommonResult.getData().getFrequencyIntervalTimeUnit());
            }
            return;
        }
        throw new ServiceException(MedicalErrorCodeConstants.FREQUENCY_SET_EXIST);
    }

    private void addEvent(MedicalOrderDO medicalOrderDO, EventTaskOperationType eventTaskOperationType, Long operationUserId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("changeBiz", medicalOrderDO.getItemName());
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setExtJson(jsonObject)
                .setPatientId(medicalOrderDO.getPatientId())
                .setEventTime(LocalDateTime.now())
                .setOperation(eventTaskOperationType)
                .setBizType(EventTaskType.PATIENT_DRUG_CHANGE)
                .setRequestId(UUID.randomUUID().toString())
                .setBizId(medicalOrderDO.getId().toString())
                .setUserId(operationUserId);
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    public List<MedicalOrderVO> queryTargetDrugsByPatientIds(List<Long> patientIds) {
        if (CollectionUtils.isEmpty(patientIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MedicalOrderDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MedicalOrderDO::getPatientId, patientIds);
        wrapper.eq(MedicalOrderDO::getStatus, MedicalOrderStatus.ENABLE);
        List<MedicalOrderDO> medicalOrderDOS = medicalOrderMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(medicalOrderDOS)) {
            return new ArrayList<>();
        }
        // 获取靶向药
        List<Long> outBizIds = medicalOrderDOS.stream().map(MedicalOrderDO::getItemId).collect(Collectors.toList());
        List<DrugVO> drugVOS = CrudUtils.getByIdsLong(drugFeign::crud, outBizIds);
        List<Long> targetDrugsIds = drugVOS.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getDrugType()) && e.getDrugType().contains(TARGET_DRUGS_DICT))
                .map(e -> Long.valueOf(e.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetDrugsIds)) {
            return new ArrayList<>();
        }
        return medicalOrderDOS.stream()
                .filter(e -> targetDrugsIds.contains(e.getItemId()))
                .map(e -> CopyPropertiesUtil.normalCopyProperties(e,MedicalOrderVO.class))
                .collect(Collectors.toList());
    }
}
