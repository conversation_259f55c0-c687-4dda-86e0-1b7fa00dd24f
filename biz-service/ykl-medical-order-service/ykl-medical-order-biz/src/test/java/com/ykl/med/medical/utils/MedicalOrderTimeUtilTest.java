//package com.ykl.med.medical.utils;
//
//import com.google.common.collect.Lists;
//import com.ykl.med.masterdata.entiry.dto.FrequencyBizQueryDTO;
//import com.ykl.med.masterdata.entiry.vo.FrequencyBizVO;
//import com.ykl.med.medical.db.entity.MedicalOrderDO;
//import com.ykl.med.medical.vo.execOrder.ExecMedicalOrderVO;
//import com.ykl.med.medical.vo.execOrder.PreExecMedicalOrderVO;
//import org.junit.jupiter.api.Test;
//
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//import java.util.Collections;
//import java.util.List;
//
//import static org.junit.jupiter.api.Assertions.*;
//class MedicalOrderTimeUtilTest {
//
//
//    @Test
//    void getRealStartExecTime_withEmptyExecList_returnsStartExecTime() {
//        MedicalOrderDO medicalOrderDO = new MedicalOrderDO();
//        medicalOrderDO.setStartExecTime(LocalDateTime.of(2023, 10, 1, 10, 0));
//        List<ExecMedicalOrderVO> execMedicalOrderList = Collections.emptyList();
//
//        LocalDateTime result = MedicalOrderTimeUtil.getRealStartExecTime(medicalOrderDO, execMedicalOrderList);
//
//        assertEquals(LocalDateTime.of(2023, 10, 1, 10, 0), result);
//    }
//
//    @Test
//    void getRealStartExecTime_withExecList_returnsLatestExecTime() {
//        MedicalOrderDO medicalOrderDO = new MedicalOrderDO();
//        medicalOrderDO.setStartExecTime(LocalDateTime.of(2023, 10, 1, 10, 0));
//        ExecMedicalOrderVO exec1 = new ExecMedicalOrderVO();
//        exec1.setExecFlag(1L);
//        exec1.setExecTime(LocalDateTime.of(2023, 10, 2, 10, 0));
//        ExecMedicalOrderVO exec2 = new ExecMedicalOrderVO();
//        exec2.setExecFlag(1L);
//        exec2.setExecTime(LocalDateTime.of(2023, 10, 3, 10, 0));
//        List<ExecMedicalOrderVO> execMedicalOrderList = Lists.newArrayList(exec1, exec2);
//
//        LocalDateTime result = MedicalOrderTimeUtil.getRealStartExecTime(medicalOrderDO, execMedicalOrderList);
//
//        assertEquals(LocalDateTime.of(2023, 10, 3, 10, 0), result);
//    }
//
//    @Test
//    void buildFrequencyBizQueryDTO_withEndExecTimeBefore7Days_setsEndTimeToEndExecTime() {
//        MedicalOrderDO medicalOrderDO = new MedicalOrderDO();
//        medicalOrderDO.setFrequencyBizId(1L);
//        medicalOrderDO.setStartExecTime(LocalDateTime.of(2023, 10, 1, 10, 0));
//        medicalOrderDO.setEndExecTime(LocalDateTime.of(2023, 10, 5, 10, 0));
//        LocalDateTime realStartTime = LocalDateTime.of(2023, 10, 1, 10, 0);
//
//        FrequencyBizQueryDTO result = MedicalOrderTimeUtil.buildFrequencyBizQueryDTO(medicalOrderDO, realStartTime);
//
//        assertEquals("2023-10-05 10:00:00", result.getEndTime());
//    }
//
//    @Test
//    void buildTodayFrequencyBizQueryDTO_withStartExecTimeBeforeNow_setsStartTimeToNow() {
//        MedicalOrderDO medicalOrderDO = new MedicalOrderDO();
//        medicalOrderDO.setFrequencyBizId(1L);
//        medicalOrderDO.setStartExecTime(LocalDateTime.of(2023, 10, 1, 10, 0));
//
//        FrequencyBizQueryDTO result = MedicalOrderTimeUtil.buildTodayFrequencyBizQueryDTO(medicalOrderDO);
//
//        assertEquals(LocalDateTime.now().format(MedicalOrderTimeUtil.INPUT_FORMATTER), result.getStartTime());
//    }
//
//    @Test
//    void doToOncePreExecMedicalOrderVO_withStartExecTimeAfterRealStartTime_returnsNull() {
//        MedicalOrderDO medicalOrderDO = new MedicalOrderDO();
//        medicalOrderDO.setStartExecTime(LocalDateTime.of(2023, 10, 1, 10, 0));
//        LocalDateTime realStartTime = LocalDateTime.of(2023, 10, 2, 10, 0);
//
//        PreExecMedicalOrderVO result = MedicalOrderTimeUtil.doToOncePreExecMedicalOrderVO(medicalOrderDO, realStartTime);
//
//        assertNull(result);
//    }
//
//    @Test
//    void doToNormalPreExecMedicalOrderVO_withValidData_returnsPreExecMedicalOrderVO() {
//        MedicalOrderDO medicalOrderDO = new MedicalOrderDO();
//        medicalOrderDO.setId(1L);
//        medicalOrderDO.setOnceDosage(new BigDecimal("10.00"));
//        FrequencyBizVO frequencyBizVO = new FrequencyBizVO();
//        frequencyBizVO.setFrequencyGenExecTimeList(Lists.newArrayList("2023-10-01 10:00:00"));
//
//        PreExecMedicalOrderVO result = MedicalOrderTimeUtil.doToNormalPreExecMedicalOrderVO(medicalOrderDO, frequencyBizVO);
//
//        assertEquals("10", result.getOnceDosage());
//        assertEquals(Lists.newArrayList("2023-10-01 10:00:00"), result.getFrequencyGenExecTimeList());
//    }
//
//    @Test
//    void isToday_withCurrentDate_returnsTrue() {
//        LocalDateTime now = LocalDateTime.now();
//
//        Boolean result = MedicalOrderTimeUtil.isToday(now);
//
//        assertTrue(result);
//    }
//
//    @Test
//    void isToday_withDifferentDate_returnsFalse() {
//        LocalDateTime differentDate = LocalDateTime.of(2023, 1, 1, 10, 0);
//
//        Boolean result = MedicalOrderTimeUtil.isToday(differentDate);
//
//        assertFalse(result);
//    }
//}