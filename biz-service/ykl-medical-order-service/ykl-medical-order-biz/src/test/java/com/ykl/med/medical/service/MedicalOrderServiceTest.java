//package com.ykl.med.medical.service;
//
//import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
//import com.ykl.med.base.service.IdServiceImpl;
//import com.ykl.med.framework.common.exception.ServiceException;
//import com.ykl.med.masterdata.api.DrugFeign;
//import com.ykl.med.masterdata.api.FrequencyDataFeign;
//import com.ykl.med.masterdata.api.FrequencyFeign;
//import com.ykl.med.masterdata.entiry.vo.FrequencyBizVO;
//import com.ykl.med.masterdata.vo.FrequencyVO;
//import com.ykl.med.medical.db.entity.MedicalOrderDO;
//import com.ykl.med.medical.db.mapper.MedicalOrderMapper;
//import com.ykl.med.medical.enums.MedicalOrderStatus;
//import com.ykl.med.medical.utils.MedicalOrderConvert;
//import com.ykl.med.medical.vo.order.MedicalOrderAddVO;
//import com.ykl.med.medical.vo.order.MedicalOrderVO;
//import com.ykl.med.push.api.EventTaskFeign;
//import com.ykl.med.push.vo.event.EventTaskAddVO;
//import org.junit.jupiter.api.Test;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//import org.junit.jupiter.api.Test;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//
//import java.time.LocalDateTime;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//import static org.mockito.Mockito.when;
//
//class MedicalOrderServiceTest {
//
//    @Test
//    public void add_withValidData_returnsMedicalOrderVO() {
//        // Arrange
//        MedicalOrderAddVO addVO = new MedicalOrderAddVO();
//        addVO.setFrequencyExecTimeList(Lists.newArrayList("2023-10-01 10:00:00"));
//        addVO.setFrequencyId(1L);
//        addVO.setStartExecTime(LocalDateTime.now());
//
//        MedicalOrderDO medicalOrderDO = new MedicalOrderDO();
//        medicalOrderDO.setId(1L);
//        medicalOrderDO.setFrequencyId(1L);
//        medicalOrderDO.setStartExecTime(LocalDateTime.now());
//        medicalOrderDO.setStatus(MedicalOrderStatus.NOT_STARTED);
//
//        MedicalOrderMapper medicalOrderMapper = mock(MedicalOrderMapper.class);
//        IdServiceImpl idService = mock(IdServiceImpl.class);
//        EventTaskFeign eventTaskFeign = mock(EventTaskFeign.class);
//        ExecMedicalOrderService execMedicalOrderService = mock(ExecMedicalOrderService.class);
//        FrequencyFeign frequencyFeign = mock(FrequencyFeign.class);
//        FrequencyDataFeign frequencyDataFeign = mock(FrequencyDataFeign.class);
//        DrugFeign drugFeign = mock(DrugFeign.class);
//
//        MedicalOrderService medicalOrderService = new MedicalOrderService(medicalOrderMapper, idService, eventTaskFeign,
//                execMedicalOrderService, frequencyFeign, frequencyDataFeign, drugFeign);
//
//        when(idService.nextId()).thenReturn(1L);
//
//        try (MockedStatic<MedicalOrderConvert> mockedConvert = mockStatic(MedicalOrderConvert.class)) {
//            mockedConvert.when(() -> MedicalOrderConvert.addVOToDO(addVO)).thenReturn(medicalOrderDO);
//            mockedConvert.when(() -> MedicalOrderConvert.doToVO(medicalOrderDO)).thenReturn(new MedicalOrderVO());
//            when(frequencyFeign.getById(5L)).thenReturn(new FrequencyVO().setId(5L).setQuantityUnit("ONCE"));
//            // Act
//            MedicalOrderVO result = medicalOrderService.add(addVO);
//
//            // Assert
//            assertNotNull(result);
//            verify(medicalOrderMapper, times(1)).insert(medicalOrderDO);
//            verify(eventTaskFeign, times(1)).addEventTask(any(EventTaskAddVO.class));
//        }
//    }
//
//    @Test
//    void add_withNullFrequencyExecTimeList_throwsException() {
//        MedicalOrderAddVO addVO = new MedicalOrderAddVO();
//        addVO.setFrequencyExecTimeList(null);
//
//        MedicalOrderMapper medicalOrderMapper = mock(MedicalOrderMapper.class);
//        IdServiceImpl idService = mock(IdServiceImpl.class);
//        EventTaskFeign eventTaskFeign = mock(EventTaskFeign.class);
//        MedicalOrderConvert medicalOrderConvert = mock(MedicalOrderConvert.class);
//        ExecMedicalOrderService execMedicalOrderService = mock(ExecMedicalOrderService.class);
//        FrequencyFeign frequencyFeign = mock(FrequencyFeign.class);
//        FrequencyDataFeign frequencyDataFeign = mock(FrequencyDataFeign.class);
//        DrugFeign drugFeign = mock(DrugFeign.class);
//        MedicalOrderService medicalOrderService = new MedicalOrderService(medicalOrderMapper, idService, eventTaskFeign,
//                execMedicalOrderService, frequencyFeign, frequencyDataFeign, drugFeign);
//
//        assertThrows(NullPointerException.class, () -> medicalOrderService.add(addVO));
//    }
//
//    @Test
//    void add_withInvalidStatus_doesNotAddEventTask() {
//        MedicalOrderAddVO addVO = new MedicalOrderAddVO();
//        addVO.setFrequencyExecTimeList(Lists.newArrayList("2023-10-01 10:00:00"));
//        addVO.setFrequencyId(1L);
//
//        MedicalOrderDO medicalOrderDO = new MedicalOrderDO();
//        medicalOrderDO.setId(1L);
//        medicalOrderDO.setStatus(MedicalOrderStatus.ENABLE);
//
//        MedicalOrderMapper medicalOrderMapper = mock(MedicalOrderMapper.class);
//        IdServiceImpl idService = mock(IdServiceImpl.class);
//        EventTaskFeign eventTaskFeign = mock(EventTaskFeign.class);
//        MedicalOrderConvert medicalOrderConvert = mock(MedicalOrderConvert.class);
//        ExecMedicalOrderService execMedicalOrderService = mock(ExecMedicalOrderService.class);
//        FrequencyFeign frequencyFeign = mock(FrequencyFeign.class);
//        FrequencyDataFeign frequencyDataFeign = mock(FrequencyDataFeign.class);
//        DrugFeign drugFeign = mock(DrugFeign.class);
//
//        when(idService.nextId()).thenReturn(1L);
//        when(medicalOrderConvert.addVOToDO(addVO)).thenReturn(medicalOrderDO);
//        when(medicalOrderConvert.doToVO(medicalOrderDO)).thenReturn(new MedicalOrderVO());
//
//        MedicalOrderService medicalOrderService = new MedicalOrderService(medicalOrderMapper, idService, eventTaskFeign,
//                execMedicalOrderService, frequencyFeign, frequencyDataFeign, drugFeign);
//
//        MedicalOrderVO result = medicalOrderService.add(addVO);
//
//        assertNotNull(result);
//        verify(medicalOrderMapper, times(1)).insert(medicalOrderDO);
//        verify(eventTaskFeign, times(0)).addEventTask(any(EventTaskAddVO.class));
//    }
//}