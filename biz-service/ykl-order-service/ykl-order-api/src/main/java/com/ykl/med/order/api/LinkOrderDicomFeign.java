package com.ykl.med.order.api;

import com.ykl.med.order.vo.req.link.LinkOrderImageSubmitReqVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "ykl-order-service", path = "linkOrderDicom")
public interface LinkOrderDicomFeign {
    @PostMapping("/imageSubmit")
    @Operation(summary = "提交Dicom")
    void imageSubmit(@RequestBody @Valid LinkOrderImageSubmitReqVO reqVO);

    @PostMapping("/imageSubmitApi")
    @Operation(summary = "提交Dicom到零氪")
    void imageSubmitApi(@RequestParam(value = "id") Long id);

    @PostMapping("/getUploadingIdList")
    @Operation(summary = "获取需要上传的列表")
    List<Long> getUploadingIdList(@RequestParam(value = "linkOrderId") Long linkOrderId);
}
