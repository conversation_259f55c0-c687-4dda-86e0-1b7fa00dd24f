package com.ykl.med.order.api;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.order.vo.req.*;
import com.ykl.med.order.vo.resp.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 订单服务
 */
@FeignClient(name="ykl-order-service", path="order")
public interface OrderFeign {

    /**
     * 创建订单
     */
    @PostMapping("/createOrder")
    @Operation(summary = "创建订单")
    OrderCreateRespVO createOrder(@RequestBody @Valid OrderCreateReqVO reqVO);

    /**
     * 订单支付
     */
    @PostMapping("/payOrder")
    @Operation(summary = "订单支付")
    void payOrder(@RequestBody @Valid OrderPayReqVO reqVO);

    /**
     * 订单退款
     *
     * @param reqVO 订单退款请求
     */
    @PostMapping("/refundOrder")
    @Operation(summary = "订单退款")
    void refundOrder(@RequestBody @Valid OrderRefundReqVO reqVO);

    @PostMapping("/returnOrder")
    @Operation(summary = "订单退货")
    void returnOrder(@RequestBody @Valid OrderReturnReqVO reqVO);

    @PostMapping("/deliveredOrder")
    @Operation(summary = "订单发货")
    void deliveredOrder(@RequestBody @Valid OrderDeliveredReqVO reqVO);

    /**
     * 改变订单状态
     *
     */
    @PostMapping("/changeOrderStatus")
    @Operation(summary = "改变订单状态")
    void changeOrderStatus(@RequestBody @Valid ChangeOrderStatusReqVO reqVO);

    /**
     * 管理后台查询订单列表
     *
     * @param reqVO 查询条件
     * @return 订单列表
     */
    @PostMapping("/adminQueryOrder")
    @Operation(summary = "管理后台查询订单列表")
    PageResult<OrderAdminListVO> adminQueryOrder(@RequestBody @Valid OrderAdminQueryReqVO reqVO);

    /**
     * app查询订单列表
     *
     * @param reqVO 查询条件
     * @return 订单列表
     */
    @PostMapping("/appQueryOrder")
    @Operation(summary = "app查询订单列表")
    PageResult<OrderAppListVO> appQueryOrder(@RequestBody @Valid OrderAppQueryReqVO reqVO);

    @PostMapping("/appOrderByOrderCode")
    @Operation(summary = "app查询订单详情")
    OrderAppDetailVO appOrderByOrderCode(@RequestParam(value = "orderCode") String orderCode);

    @PostMapping("/adminOrderByOrderCode")
    @Operation(summary = "admin查询订单详情")
    OrderAdminDetailVO adminOrderByOrderCode(@RequestParam(value = "orderCode") String orderCode);

    @PostMapping("/getOrderDetailByOrderCode")
    @Operation(summary = "查询订单详情")
    OrderVO getOrderDetailByOrderCode(@RequestParam(value = "orderCode") String orderCode);


    @PostMapping("/getOrderDetailByOrderCodeBatch")
    @Operation(summary = "查询订单列表")
    List<OrderVO> getOrderDetailByOrderCodeBatch(@RequestBody List<String> orderCodes);

    @PostMapping("/queryOrder")
    @Operation(summary = "查询订单")
    PageResult<OrderVO> queryOrder(@RequestBody OrderQueryReqVO reqVO);
}