package com.ykl.med.order.vo.req;

import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "订单简短请求")
public class OrderSimpleReqVO implements AutoBuildUserId {
    @Schema(description = "用户id", example = "123456", hidden = true)
    private Long currentUserId;

    @Schema(description = "订单代码", example = "ORD1234", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "订单代码不能为空")
    private String orderCode;
}