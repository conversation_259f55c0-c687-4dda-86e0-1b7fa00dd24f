package com.ykl.med.order.vo.req;

import com.ykl.med.framework.common.enums.PayType;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Schema(description = "订单支付请求")
public class OrderPayReqVO {

    @Schema(description = "订单代码", example = "ORD1234", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "订单代码不能为空")
    private String orderCode;

    @Schema(description = "支付方式", example = "Credit Card", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付方式不能为空")
    private PayType payType;

    @Schema(description = "支付编号", example = "PAY9876", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "支付编号不能为空")
    private String payNo;

    @TimestampConvert
    @Schema(description = "支付时间", example = "123543453453", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime payTime;

    @Schema(description = "用户id", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "备注", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    private String remark;
}