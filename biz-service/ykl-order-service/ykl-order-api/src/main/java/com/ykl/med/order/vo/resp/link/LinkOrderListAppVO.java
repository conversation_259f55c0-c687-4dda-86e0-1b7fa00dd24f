package com.ykl.med.order.vo.resp.link;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.order.enums.LinkOrderStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "零氪工单列表-app端")
@Data
public class LinkOrderListAppVO {
    @Stringify
    @Schema(description = "工单id")
    private Long id;
    @Schema(description = "商品名称")
    private String productName;
    @Schema(description = "sku名称")
    private String skuName;
    @Schema(description = "工单状态")
    private LinkOrderStatus status;
    @TimestampConvert
    @Schema(description = "第三方回调时间(报告生成时间)")
    private LocalDateTime callBackTime;

    @Schema(description = "机构名称")
    private String serviceOrganizationName;

    @Schema(description = "生成的报告url")
    private String reportBaseUrl;
}
