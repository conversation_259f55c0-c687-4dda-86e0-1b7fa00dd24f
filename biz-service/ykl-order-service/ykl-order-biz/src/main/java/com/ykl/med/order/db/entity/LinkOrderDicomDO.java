package com.ykl.med.order.db.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * 零氪工单dicom表
 */
@TableName(value = "t_link_order_dicom")
@Data
public class LinkOrderDicomDO extends BaseDO {
    /**
     * 批次号
     */
    private Long batchId;
    /**
     * 零氪工单id
     */
    private Long linkOrderId;
    /**
     * dicom文件地址
     */
    private String dicomUrl;
    /**
     * 上传状态
     */
    private Boolean uploadStatus;

    /**
     * 删除状态
     */
    private Boolean deleteStatus;
}
