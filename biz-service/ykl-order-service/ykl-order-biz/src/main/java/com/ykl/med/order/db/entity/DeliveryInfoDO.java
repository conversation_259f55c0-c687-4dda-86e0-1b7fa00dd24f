package com.ykl.med.order.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.EncryptTypeHandler;
import com.ykl.med.order.enums.DeliveryMethod;
import com.ykl.med.framework.common.enums.DeliveryStatus;
import com.ykl.med.order.enums.DeliveryType;
import lombok.Data;

import java.time.LocalDateTime;

@TableName(value = "t_delivery_info",autoResultMap = true)
@Data
public class DeliveryInfoDO extends BaseDO {
    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 配送状态
     */
    private DeliveryStatus deliveryStatus;
    /**
     * 配送方式
     */
    private DeliveryType deliveryType;


    /**
     * 收货人姓名(自提联系人)
     */
    private String receiverName;

    /**
     * 收货人电话(自提联系电话)
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String receiverPhone;

    /**
     * 收货人地址(自提地址)
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String receiverAddress;

    /**
     * 快递公司，物流方式
     */
    private DeliveryMethod deliveryMethod;

    /**
     * 快递单号
     */
    private String deliveryNo;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;

    /**
     * 发货人
     */
    private Long deliveryUserId;
}
