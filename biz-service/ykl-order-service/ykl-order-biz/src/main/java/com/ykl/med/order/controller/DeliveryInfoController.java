package com.ykl.med.order.controller;

import com.ykl.med.framework.common.enums.DeliveryStatus;
import com.ykl.med.order.api.DeliveryInfoFeign;
import com.ykl.med.order.service.DeliveryInfoService;
import com.ykl.med.order.vo.req.OrderDeliveredReqVO;
import com.ykl.med.order.vo.resp.DeliveryInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "配送信息服务")
@RestController
@RequestMapping("deliveryInfo")
@Validated
public class DeliveryInfoController implements DeliveryInfoFeign {
    @Resource
    private DeliveryInfoService deliveryInfoService;

    @Override
    @PostMapping("/changeStatus")
    @Operation(summary = "更改配送状态")
    public void changeStatus(@RequestParam(value = "orderCode") String orderCode,
                             @RequestParam(value = "deliveryStatus") DeliveryStatus deliveryStatus) {
        deliveryInfoService.changeStatus(orderCode, deliveryStatus);
    }

    @Override
    @PostMapping("/updateDeliveryInfo")
    @Operation(summary = "补充发货物流信息")
    public void updateDeliveryInfo(@RequestBody OrderDeliveredReqVO reqVO) {
        deliveryInfoService.updateDeliveryInfo(reqVO);
    }

    @Override
    @PostMapping("/getDeliveryInfo")
    @Operation(summary = "获取配送信息")
    public DeliveryInfoVO getDeliveryInfo(@RequestParam(value = "orderCode") String orderCode) {
        return deliveryInfoService.getDeliveryInfo(orderCode);
    }
}
