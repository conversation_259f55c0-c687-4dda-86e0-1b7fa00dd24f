package com.ykl.med.order.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.pojo.CommonResult;
import com.ykl.med.linkdoc.service.LinkDocService;
import com.ykl.med.linkdoc.vo.UploadImageReqVO;
import com.ykl.med.linkdoc.vo.UploadImageRespVO;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.order.constants.OrderErrorCodeConstants;
import com.ykl.med.order.db.entity.LinkOrderDicomDO;
import com.ykl.med.order.db.mapper.LinkOrderDicomMapper;
import com.ykl.med.order.vo.req.link.LinkOrderImageSubmitReqVO;
import com.ykl.med.order.vo.resp.link.LinkOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LinkOrderDicomService extends ServiceImpl<LinkOrderDicomMapper, LinkOrderDicomDO> {

    @Resource
    private LinkOrderService linkOrderService;
    @Resource
    private LinkDocService linkDocService;
    @Resource
    private IdServiceImpl idService;
    @Resource
    private CommonConfigFeign commonConfigFeign;

    @Transactional(rollbackFor = Exception.class)
    public void imageSubmit(LinkOrderImageSubmitReqVO reqVO) {
        linkOrderService.update(reqVO.getLinkOrderId(), reqVO.getAttachmentList(), reqVO.getNotes());
        linkOrderService.uploadImageBegin(reqVO.getLinkOrderId());
        LambdaQueryWrapper<LinkOrderDicomDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LinkOrderDicomDO::getLinkOrderId, reqVO.getLinkOrderId());
        queryWrapper.eq(LinkOrderDicomDO::getDeleteStatus, false);
        List<LinkOrderDicomDO> oldList = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(oldList)) {
            oldList.forEach(linkOrderDicomDO -> {
                linkOrderDicomDO.setDeleteStatus(true);
            });
            this.updateBatchById(oldList);
        }
        List<LinkOrderDicomDO> newList = new ArrayList<>();
        Long batchId = idService.nextId();
        for (String url : reqVO.getFileUrls()) {
            LinkOrderDicomDO linkOrderDicomDO = new LinkOrderDicomDO();
            linkOrderDicomDO.setId(idService.nextId());
            linkOrderDicomDO.setDicomUrl(url);
            linkOrderDicomDO.setLinkOrderId(reqVO.getLinkOrderId());
            linkOrderDicomDO.setUploadStatus(false);
            linkOrderDicomDO.setDeleteStatus(false);
            linkOrderDicomDO.setBatchId(batchId);
            newList.add(linkOrderDicomDO);
        }
        this.saveBatch(newList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void imageSubmitApi(Long id) {
        LinkOrderDicomDO linkOrderDicomDO = this.getById(id);
        if (linkOrderDicomDO.getUploadStatus()) {
            return;
        }
        LinkOrderVO linkOrderVO = linkOrderService.findById(linkOrderDicomDO.getLinkOrderId());
        AssertUtils.notNull(linkOrderVO, OrderErrorCodeConstants.LINK_ORDER_NOT_EXIST);
        AssertUtils.notNull(linkOrderVO.getLinkOrderOutBizId(), OrderErrorCodeConstants.LINK_ORDER_NOT_EXIST);
        UploadImageReqVO uploadImageReqVO = new UploadImageReqVO();
        uploadImageReqVO.setOrderId(linkOrderVO.getLinkOrderOutBizId());
        JSONObject jsonObject = commonConfigFeign.getCommonConfigValueJsonByKey("oss_config");
        String host = jsonObject.getString("host");
        uploadImageReqVO.setFileUrl(host + linkOrderDicomDO.getDicomUrl());
        log.info("linkDocService.uploadImage param:{}", JSON.toJSONString(uploadImageReqVO));
        CommonResult<UploadImageRespVO> result = linkDocService.uploadImage(uploadImageReqVO);
        AssertUtils.isTrue(result.isSuccess() && result.getData() != null && result.getData().getStudyUid() != null, OrderErrorCodeConstants.LINK_ORDER_IMAGE_SUBMIT_FAIL);
        linkOrderDicomDO.setUploadStatus(true);
        this.updateById(linkOrderDicomDO);
    }

    public List<Long> getUploadingIdList(Long linkOrderId) {
        LambdaQueryWrapper<LinkOrderDicomDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LinkOrderDicomDO::getLinkOrderId, linkOrderId);
        queryWrapper.eq(LinkOrderDicomDO::getDeleteStatus, false);
        queryWrapper.eq(LinkOrderDicomDO::getUploadStatus, false);
        return this.list(queryWrapper).stream().map(LinkOrderDicomDO::getId).collect(Collectors.toList());
    }
}
