package com.ykl.med.order.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.PayType;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.NoNullFastjsonTypeHandler;
import com.ykl.med.framework.common.enums.OrderStatus;
import com.ykl.med.order.enums.OrderType;
import com.ykl.med.order.vo.OrderExtraVO;
import lombok.Data;

import java.time.LocalDateTime;

@TableName(value = "t_order",autoResultMap = true)
@Data
public class OrderDO extends BaseDO {
    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单状态
     */
    private OrderStatus status;
    /**
     * 订单状态
     */
    private OrderType type;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者手机号
     */
    private String patientPhone;

    /**
     * 医疗组id (所购买的这个东西属于哪个医疗组)
     */
    private Long medicalTeamId;

    /**
     * 患者医疗组id
     */
    private Long patientMedicalTeamId;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    /**
     * 支付金额
     */
    private Integer payAmount;
    /**
     * 配送费
     */
    private Integer deliveryFee;
    /**
     * 总金额
     */
    private Integer totalAmount;

    /**
     * 订单备注
     */
    private String remark;
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 扩展json
     */
    @TableField(typeHandler = NoNullFastjsonTypeHandler.class)
    private OrderExtraVO extJson;
    /**
     * 订单来源的外部Id
     * 医嘱订单：医嘱Id
     */
    private String orderSourceBizId;





    /**
     * 支付方式
     */
    private PayType payType;
    /**
     * 支付用户id
     */
    private Long payUserId;
    /**
     * 支付流水号
     */
    private String payNo;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;


    /**
     * 退货单号
     */
    private String returnNo;
    /**
     * 退货失败原因
     */
    private String returnErrorMsg;
    /**
     * 退货时间
     */
    private LocalDateTime returnTime;


    /**
     * 退款流水号
     */
    private String refundNo;
    /**
     * 退款时间
     */
    private LocalDateTime refundTime;
    /**
     * 退款失败原因
     */
    private String refundErrorMsg;
    /**
     * 退款金额
     */
    private Integer refundAmount;

}
