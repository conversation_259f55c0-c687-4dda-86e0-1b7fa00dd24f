package com.ykl.med.order.utils;


import com.ykl.med.framework.common.enums.OrderStatus;
import com.ykl.med.framework.common.enums.ProductType;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.order.db.entity.OrderDO;
import com.ykl.med.order.db.entity.OrderItemDO;
import com.ykl.med.order.vo.SkuOrderExtraVO;
import com.ykl.med.order.vo.req.OrderCreateReqVO;
import com.ykl.med.order.vo.req.OrderItemCreateReqVO;
import com.ykl.med.order.vo.resp.OrderAdminDetailVO;
import com.ykl.med.order.vo.resp.OrderAdminListVO;
import com.ykl.med.order.vo.resp.OrderAppListVO;
import com.ykl.med.order.vo.resp.OrderItemAppListVO;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class OrderConvert {
    public static OrderDO createReqVOToDo(OrderCreateReqVO reqVO, Integer expireTime, List<OrderItemDO> orderItemDOList) {
        OrderDO orderDO = new OrderDO();
        BeanUtils.copyProperties(reqVO, orderDO);
        orderDO.setUserId(reqVO.getCurrentUserId());
        orderDO.setStatus(OrderStatus.PRE_ORDER);
        orderDO.setExpireTime(LocalDateTime.now().plusSeconds(expireTime / 1000));
        Integer totalAmount = orderItemDOList.stream().map(e -> e.getPrice() * e.getQuantity()).reduce(0, Integer::sum);
        Integer payAmount = orderItemDOList.stream().map(e -> e.getPayPrice() * e.getQuantity()).reduce(0, Integer::sum);
        orderDO.setTotalAmount(totalAmount);
        orderDO.setPayAmount(payAmount + reqVO.getDeliveryFee());
        return orderDO;
    }

    public static OrderAdminDetailVO doToAdminDetailVO(OrderDO orderDO, List<OrderItemDO> orderItemDOList) {
        OrderAdminDetailVO orderAdminDetailVO = new OrderAdminDetailVO();
        BeanUtils.copyProperties(orderDO, orderAdminDetailVO);
        if (orderDO.getExtJson() != null) {
            orderAdminDetailVO.setMedicalTeamName(orderDO.getExtJson().getMedicalTeamName());
            orderAdminDetailVO.setMyselfPhone(orderDO.getPatientPhone());
        }
        List<OrderAdminDetailVO.OrderItemAdminDetailVO> orderItemAdminDetailVOList = new ArrayList<>();
        for (OrderItemDO orderItemDO : orderItemDOList) {
            OrderAdminDetailVO.OrderItemAdminDetailVO orderItemAdminDetailVO = new OrderAdminDetailVO.OrderItemAdminDetailVO();
            BeanUtils.copyProperties(orderItemDO, orderItemAdminDetailVO);
            orderItemAdminDetailVOList.add(orderItemAdminDetailVO);
        }
        orderAdminDetailVO.setOrderItemList(orderItemAdminDetailVOList);
        return orderAdminDetailVO;
    }

    public static List<OrderItemDO> buildOrderItemProduct(OrderCreateReqVO reqVO) {
        List<OrderItemDO> orderItemDOList = new ArrayList<>();
        for (OrderItemCreateReqVO orderItemCreateReqVO : reqVO.getOrderItems()) {
            OrderItemDO orderItemDO = new OrderItemDO();
            BeanUtils.copyProperties(orderItemCreateReqVO, orderItemDO);
            orderItemDO.setPrice(orderItemCreateReqVO.getRetailPrice());
            if (orderItemCreateReqVO.getPreSellPrice() != null) {
                //有预售价就以预售价结算
                orderItemDO.setPayPrice(orderItemCreateReqVO.getPreSellPrice());
            } else {
                orderItemDO.setPayPrice(reqVO.getIsMember() ? orderItemCreateReqVO.getMemberPrice() : orderItemCreateReqVO.getRetailPrice());
            }
            if (orderItemCreateReqVO.getSkuAttribute() != null) {
                SkuOrderExtraVO skuOrderExtraVO = CopyPropertiesUtil.normalCopyProperties(orderItemCreateReqVO.getSkuAttribute(), SkuOrderExtraVO.class);
                orderItemDO.setExtJson(skuOrderExtraVO);
            }
            orderItemDOList.add(orderItemDO);
        }
        return orderItemDOList;
    }

    public static List<OrderAdminListVO> doToAdminListVO(List<OrderDO> orderDOList, Map<Long, List<OrderItemDO>> orderItemMap) {
        List<OrderAdminListVO> orderAdminListVOList = new ArrayList<>();
        for (OrderDO orderDO : orderDOList) {
            OrderAdminListVO orderAdminListVO = new OrderAdminListVO();
            BeanUtils.copyProperties(orderDO, orderAdminListVO);
            if (orderDO.getExtJson() != null) {
                orderAdminListVO.setMedicalTeamName(orderDO.getExtJson().getMedicalTeamName());
                orderAdminListVO.setMyselfPhone(orderDO.getPatientPhone());
                orderAdminListVO.setPatientName(orderDO.getPatientName());
            }
            List<OrderItemDO> orderItems = orderItemMap.get(orderDO.getId());
            List<OrderAdminListVO.OrderItemAdminListVO> orderItemAdminListVOList = new ArrayList<>();
            for (OrderItemDO orderItemDO : orderItems) {
                OrderAdminListVO.OrderItemAdminListVO orderItemAdminListVO = new OrderAdminListVO.OrderItemAdminListVO();
                orderItemAdminListVO.setProductName(OrderConvert.buildAppProductName(orderItemDO, orderDO));
                orderItemAdminListVO.setProductType(orderItemDO.getProductType());
                orderItemAdminListVOList.add(orderItemAdminListVO);
            }
            orderAdminListVO.setOrderItems(orderItemAdminListVOList);
            orderAdminListVOList.add(orderAdminListVO);
        }
        return orderAdminListVOList;
    }

    public static List<OrderAppListVO> doToAppListVO(List<OrderDO> orderDOList, Map<Long, List<OrderItemDO>> orderItemMap) {
        List<OrderAppListVO> orderAppListVOS = new ArrayList<>();
        for (OrderDO orderDO : orderDOList) {
            OrderAppListVO orderAppListVO = new OrderAppListVO();
            BeanUtils.copyProperties(orderDO, orderAppListVO);
            List<OrderItemDO> orderItems = orderItemMap.get(orderDO.getId());
            List<OrderItemAppListVO> orderItemVOList = new ArrayList<>();
            for (OrderItemDO orderItemDO : orderItems) {
                OrderItemAppListVO orderItemVO = CopyPropertiesUtil.normalCopyProperties(orderItemDO, OrderItemAppListVO.class);
                orderItemVO.setProductName(OrderConvert.buildAppProductName(orderItemDO, orderDO));
                orderItemVOList.add(orderItemVO);
            }
            orderAppListVO.setOrderItemList(orderItemVOList);
            orderAppListVOS.add(orderAppListVO);
        }
        return orderAppListVOS;
    }

    public static String buildAppProductName(OrderItemDO orderItemDO, OrderDO orderDO) {
        StringBuilder productName = new StringBuilder();
        if (orderItemDO.getProductType() == ProductType.PATIENT_EDUCATION_COURSES) {
            productName.append(orderItemDO.getProductName());
            return productName.toString();
        } else if (orderItemDO.getProductType() == ProductType.VIDEO_CONSULTATION) {
            productName.append(orderDO.getExtJson().getDoctorName())
                    .append(" ")
                    .append(orderItemDO.getProductName())
                    .append(" ")
                    .append(orderItemDO.getSkuName());
            return productName.toString();
        } else if (orderItemDO.getProductType() == ProductType.PRESCRIPTION_DRUG) {
            productName.append(orderItemDO.getProductName());
            return productName.toString();
        } else if (orderItemDO.getProductType() == ProductType.TREATMENT_PROJECT) {
            productName.append(orderItemDO.getProductName())
                    .append(" ")
                    .append(orderItemDO.getSkuName());
            return productName.toString();
        }
        if (orderItemDO.getProductType() == ProductType.MEMBER_VERSION) {
            productName.append(orderItemDO.getSkuName())
                    .append("x")
                    .append(orderItemDO.getExtJson().getNumber());
        } else {
            productName.append(orderItemDO.getProductName())
                    .append(" ")
                    .append(orderItemDO.getSkuName())
                    .append("x")
                    .append(orderItemDO.getExtJson().getNumber());
        }

        if (orderItemDO.getExtJson().getUnit() != null) {
            String unitName = "";
            switch (orderItemDO.getExtJson().getUnit()) {
                case DAYS:
                    unitName = "天";
                    break;
                case MONTHS:
                    unitName = "月";
                    break;
                case YEARS:
                    unitName = "年";
                    break;
                case WEEKS:
                    unitName = "周";
                    break;
                default:
                    break;
            }
            productName.append(unitName);
        } else if (orderItemDO.getProductType() == ProductType.SERVICE_PACKAGE) {
            productName.append("次");
        }
        return productName.toString();
    }
}
