package com.ykl.med.order.service;

import com.ykl.med.order.db.entity.OrderDO;
import com.ykl.med.order.db.entity.OrderItemDO;

import java.util.List;
import java.util.Map;

public interface OrderItemService {
    void insertOrderItem(List<OrderItemDO> orderItemDOList);

    List<OrderItemDO> selectByOrderIds(List<Long> orderIds);

    List<OrderItemDO> selectByOrderId(Long orderId);

    Map<Long, List<OrderItemDO>> getOrderItemMap(List<Long> orderIds);

    Long countBuyNum(Long skuId, Long patientId);

    Map<Long, Integer> getSalesAmount(List<Long> productIds);
}
