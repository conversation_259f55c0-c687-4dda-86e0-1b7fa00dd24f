package com.ykl.med.payment.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.enums.PayType;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.payment.constants.PaymentErrorCodeConstants;
import com.ykl.med.payment.db.entity.PayOrderDO;
import com.ykl.med.payment.db.mysql.PayOrderMapper;
import com.ykl.med.payment.enums.PayOrderStatus;
import com.ykl.med.payment.vo.CreatePayOrderReqVO;
import com.ykl.med.payment.vo.PayOrderVO;
import com.ykl.med.payment.vo.api.ApiPayOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class PayOrderServiceImpl extends ServiceImpl<PayOrderMapper, PayOrderDO> implements PayOrderService {
    @Resource
    private PayOrderMapper payOrderMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayOrderVO createPayOrder(CreatePayOrderReqVO reqVO) {
        log.info("创建支付订单，reqVO={}", JSON.toJSONString(reqVO));
        PayOrderDO payOrderDO = payOrderMapper.selectByOrderCodeAndPayType(reqVO.getOrderCode(), reqVO.getPayType());
        AssertUtils.isNull(payOrderDO, PaymentErrorCodeConstants.PAY_ORDER_IS_EXISTS);
        payOrderDO = CopyPropertiesUtil.normalCopyProperties(reqVO, PayOrderDO.class);
        payOrderDO.setStatus(PayOrderStatus.WAIT_PAID);
        this.save(payOrderDO);
        return CopyPropertiesUtil.normalCopyProperties(payOrderDO, PayOrderVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePayOrder(ApiPayOrderVO apiPayOrderVO, String orderCode, PayType payType) {
        log.info("更新支付订单状态，orderCode={},apiPayOrderVO={}", orderCode, JSON.toJSONString(apiPayOrderVO));
        PayOrderDO payOrderDO = payOrderMapper.selectByOrderCodeAndPayType(orderCode, payType);
        AssertUtils.notNull(payOrderDO, PaymentErrorCodeConstants.PAY_ORDER_NOT_EXISTS);
        if (payOrderDO.getStatus() == apiPayOrderVO.getStatus()) {
            return;
        }
        int result = 0;
        switch (apiPayOrderVO.getStatus()) {
            case SUCCESS:
                AssertUtils.isTrue(payOrderDO.getStatus() == PayOrderStatus.WAIT_PAID, PaymentErrorCodeConstants.PAY_ORDER_STATUS_ERROR);
                result = payOrderMapper.successOrder(orderCode, apiPayOrderVO.getTransactionNo(), apiPayOrderVO.getSuccessTime(), apiPayOrderVO.getPayerOpenid());
                break;
            case REFUND:
                AssertUtils.isTrue(payOrderDO.getStatus() == PayOrderStatus.SUCCESS, PaymentErrorCodeConstants.PAY_ORDER_STATUS_ERROR);
                result = payOrderMapper.refundOrder(orderCode);
                break;
            case CLOSED:
                AssertUtils.isTrue(payOrderDO.getStatus() == PayOrderStatus.WAIT_PAID, PaymentErrorCodeConstants.PAY_ORDER_STATUS_ERROR);
                result = payOrderMapper.closedOrder(orderCode, apiPayOrderVO.getTransactionNo(), apiPayOrderVO.getTradeStateDesc());
                break;
            default:
                break;
        }
        AssertUtils.isTrue(result > 0, PaymentErrorCodeConstants.PAY_ORDER_STATUS_ERROR);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setPrepayId(String orderCode, PayType payType, String prepayId) {
        log.info("设置支付订单二维码，orderCode={},payType={},prepayId={}", orderCode, payType, prepayId);
        PayOrderDO payOrderDO = payOrderMapper.selectByOrderCodeAndPayType(orderCode, payType);
        AssertUtils.notNull(payOrderDO, PaymentErrorCodeConstants.PAY_ORDER_NOT_EXISTS);
        payOrderDO.setPrepayId(prepayId);
        this.updateById(payOrderDO);
    }

    @Override
    public List<PayOrderVO> getPayOrderListByOrderCode(String orderCode) {
        LambdaQueryWrapper<PayOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayOrderDO::getOrderCode, orderCode);
        return CopyPropertiesUtil.normalCopyProperties(this.list(queryWrapper), PayOrderVO.class);
    }

    @Override
    public List<PayOrderVO> getExpiredPayOrderList() {
        LambdaQueryWrapper<PayOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.le(PayOrderDO::getExpireTime, LocalDateTime.now());
        queryWrapper.eq(PayOrderDO::getStatus, PayOrderStatus.WAIT_PAID);
        return CopyPropertiesUtil.normalCopyProperties(this.list(queryWrapper), PayOrderVO.class);
    }

    @Override
    public List<PayOrderVO> getWaitPaidPayOrderList() {
        LambdaQueryWrapper<PayOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayOrderDO::getStatus, PayOrderStatus.WAIT_PAID);
        return CopyPropertiesUtil.normalCopyProperties(this.list(queryWrapper), PayOrderVO.class);
    }

    @Override
    public PayOrderVO getPayOrderByOrderCodeAndPayType(String orderCode, PayType payType) {
        PayOrderDO payOrderDO = payOrderMapper.selectByOrderCodeAndPayType(orderCode, payType);
        AssertUtils.notNull(payOrderDO, PaymentErrorCodeConstants.PAY_ORDER_NOT_EXISTS);
        return CopyPropertiesUtil.normalCopyProperties(payOrderDO, PayOrderVO.class);
    }
}
