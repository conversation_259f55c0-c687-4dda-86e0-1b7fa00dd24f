package com.ykl.med.payment.service;

import com.ykl.med.framework.common.enums.PayType;
import com.ykl.med.payment.vo.CreatePayOrderReqVO;
import com.ykl.med.payment.vo.PayOrderVO;
import com.ykl.med.payment.vo.api.ApiPayOrderVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface PayOrderService {
    PayOrderVO createPayOrder(CreatePayOrderReqVO reqVO);

    void setPrepayId(String orderCode, PayType payType, String prepayId);

    List<PayOrderVO> getPayOrderListByOrderCode(String orderCode);

    List<PayOrderVO> getExpiredPayOrderList();

    List<PayOrderVO> getWaitPaidPayOrderList();

    PayOrderVO getPayOrderByOrderCodeAndPayType(String orderCode, PayType payType);

    void updatePayOrder(ApiPayOrderVO apiPayOrderVO, String orderCode, PayType payType);
}
