package com.ykl.med.payment.config;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class WechatPayAutoCertificateConfig {
    @Resource
    private WechatConfig wechatConfig;

    @Bean
    public RSAPublicKeyConfig rsaAutoCertificateConfig() {
        return new RSAPublicKeyConfig.Builder()
                        .merchantId(wechatConfig.getMerchantId())
                        .privateKeyFromPath(wechatConfig.getPrivateKeyPath())
                        .publicKeyFromPath(wechatConfig.getPublicKeyPath())
                        .publicKeyId(wechatConfig.getPublicKeyId())
                        .merchantSerialNumber(wechatConfig.merchantSerialNumber)
                        .apiV3Key(wechatConfig.getApiV3Key())
                        .build();
    }

}
