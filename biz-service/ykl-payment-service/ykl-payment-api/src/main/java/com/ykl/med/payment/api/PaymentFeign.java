package com.ykl.med.payment.api;

import com.ykl.med.framework.common.enums.PayType;
import com.ykl.med.payment.enums.PayOrderStatus;
import com.ykl.med.payment.enums.RefundStatus;
import com.ykl.med.payment.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "ykl-payment-service", path = "payment")
public interface PaymentFeign {
    @PostMapping("/createPayOrder")
    @Operation(summary = "创建支付单")
    CreatePayOrderRespVO createPayOrder(@RequestBody @Valid CreatePayOrderReqVO reqVO);

    @PostMapping("/refreshPayOrder")
    @Operation(summary = "刷新支付单")
    PayOrderStatus refreshPayOrder(@RequestParam(value = "orderCode") String orderCode, @RequestParam(value = "payType") PayType payType);

    @PostMapping("/closeOtherPayOrder")
    @Operation(summary = "关闭其他渠道支付单")
    void closeOtherPayOrder(@RequestParam(value = "orderCode") String orderCode,
                            @RequestParam(value = "payType") PayType payType);

    @PostMapping("/closePayOrder")
    @Operation(summary = "关闭支付单")
    PayOrderVO closePayOrder(@RequestParam(value = "orderCode") String orderCode);

    @PostMapping("/refund")
    @Operation(summary = "创建退款")
    CreateRefundRespVO refund(@RequestBody @Valid CreateRefundReqVO reqVO);

    @PostMapping("/refreshRefund")
    @Operation(summary = "刷新退款单")
    RefundStatus refreshRefund(@RequestParam(value = "refundCode") String refundCode, @RequestParam(value = "payType") PayType payType);


    @PostMapping("/getPayOrderByOrderCodeAndPayType")
    @Operation(summary = "获取支付单")
    PayOrderVO getPayOrderByOrderCodeAndPayType(@RequestParam(value = "orderCode") String orderCode,
                                                @RequestParam(value = "payType") PayType payType);

    @PostMapping("/getPayOrderListByOrderCode")
    @Operation(summary = "根据订单号获取支付单")
    List<PayOrderVO> getPayOrderListByOrderCode(@RequestParam(value = "orderCode") String orderCode);

    @PostMapping("/getRefundByRefundCode")
    @Operation(summary = "根据退款单号获取退款单")
    List<RefundVO> getRefundByRefundCode(String refundCode);

    @PostMapping("/getRefundByRefundCodeAndType")
    @Operation(summary = "根据退款单号和类型获取退款单")
    RefundVO getRefundByRefundCodeAndType(@RequestParam(value = "refundCode") String refundCode,
                                          @RequestParam(value = "payType") PayType payType);
}
