package com.ykl.med.payment.vo;

import com.ykl.med.framework.common.enums.PayType;
import com.ykl.med.payment.enums.RefundStatus;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RefundVO {
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Long id;
    private String orderCode;
    private Integer amount;
    private Long userId;
    private Long patientId;
    /**
     * 服务包id,有点类似于币种
     * 如果是线上视频门诊，就是挂号类型id，如果是通用的，就是0
     */
    private Long servicePackageId;
    private PayType payType;
    /**
     * 调用第三方退款需要的幂等号，每次退款都不一样，
     * 目前一个订单只能退一次，就直接是订单号，后续可以考虑加上退款次数
     */
    private String refundCode;
    private RefundStatus status;
    private String channelType;
    private String userReceivedAccount;
    private String outRefundId;
    private String fundsAccount;
    private LocalDateTime successTime;
}
