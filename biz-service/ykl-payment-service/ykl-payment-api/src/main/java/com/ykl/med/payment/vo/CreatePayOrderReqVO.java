package com.ykl.med.payment.vo;

import com.ykl.med.framework.common.enums.PayType;
import com.ykl.med.framework.common.json.TimestampConvert;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class CreatePayOrderReqVO {
    @NotNull(message = "userId不能为空")
    private Long userId;
    @NotNull(message = "patientId不能为空")
    private Long patientId;

    private Long servicePackageId;

    @NotNull(message = "payType不能为空")
    private PayType payType;

    private String description;

    @NotBlank(message = "orderCode不能为空")
    private String orderCode;

    @NotNull(message = "amount不能为空")
    private Integer amount;

    @NotNull(message = "expireTime不能为空")
    @TimestampConvert
    private LocalDateTime expireTime;
}
