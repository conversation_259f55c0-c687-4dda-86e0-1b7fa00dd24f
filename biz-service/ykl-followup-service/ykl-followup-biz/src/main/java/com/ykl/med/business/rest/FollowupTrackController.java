package com.ykl.med.business.rest;

import com.ykl.med.business.service.FollowupTrackItemService;
import com.ykl.med.business.service.FollowupTrackService;
import com.ykl.med.followup.api.FollowupTrackFeign;
import com.ykl.med.followup.entity.param.*;
import com.ykl.med.followup.entity.vo.FollowupListByDoctorGroupVO;
import com.ykl.med.followup.entity.vo.FollowupTrackHistoryVO;
import com.ykl.med.followup.entity.vo.FollowupTrackItemVO;
import com.ykl.med.followup.entity.vo.FollowupTrackVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.vo.FormVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 跟踪随访
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
@RestController
@RequestMapping("/followup/track")
public class FollowupTrackController implements FollowupTrackFeign {

    @Resource
    private FollowupTrackService followupTrackService;
    @Resource
    private FollowupTrackItemService followupTrackItemService;


    @Override
    @PostMapping("/info/web")
    public FollowupTrackVO infoWeb(@Valid @RequestBody QueryFollowupTrackInfoParam param) {
        return followupTrackService.info(param.getFollowupId());
    }


    @Override
    @PostMapping("/current")
    public FollowupTrackVO current(@Valid @RequestBody QueryCurrentFollowupTrackParam param) {
        return followupTrackService.current(param.getPatientId());
    }


    @Override
    @PostMapping("/list")
    public List<FollowupTrackVO> list(@Valid @RequestBody QueryFollowupTrackParam param) {
        return followupTrackService.list(param);
    }

    @Override
    @PostMapping("/history")
    public List<FollowupTrackHistoryVO> history(@Valid @RequestBody QueryFollowupTrackHistoryParam param) {
        return followupTrackService.history(param);
    }


    @Override
    @PostMapping("/create")
    public List<Long> create(@Valid @RequestBody CreateFollowupTrackParam param) {
        return followupTrackService.create(param);
    }


    @Override
    @PostMapping("/start")
    public void start(@Valid @RequestBody StartFollowupTrackParam param) {
        followupTrackService.start(param);
    }


    @Override
    @PostMapping("/finish")
    public void finish(@Valid @RequestBody FinishFollowupTrackParam param) {
        followupTrackService.finish(param);
    }


    @Override
    @PostMapping("/updateDoctorRemark")
    public void updateDoctorRemark(@Valid @RequestBody UpdateDoctorRemarkParam param) {
        followupTrackService.updateDoctorRemark(param);
    }


    @Override
    @PostMapping("/updatePatientRemark")
    public void updatePatientRemark(@Valid @RequestBody UpdatePatientRemarkParam param) {
        followupTrackService.updatePatientRemark(param);
    }


    @Override
    @PostMapping("/updateFollowupItem")
    public void updateFollowupItem(@Valid @RequestBody UpdateFollowupItemParam param) {
        followupTrackItemService.updateFollowupItem(param);
    }


    @Override
    @PostMapping("/queryItemQuestion")
    public FormVO queryItemQuestion(@Valid @RequestBody QueryItemQuestionParam param) {
        return followupTrackItemService.queryItemQuestion(param.getItemId());
    }


    @Override
    @PostMapping("/queryItemQuestion/v2")
    public FollowupTrackItemVO queryItemQuestionV2(@Valid @RequestBody QueryItemQuestionParam param) {
        return followupTrackItemService.queryItemQuestionV2(param.getItemId());
    }


    @Override
    @PostMapping("/finishItemQuestion")
    public List<Long> finishItemQuestion(@Valid @RequestBody FinishItemQuestionParam param) {
        return followupTrackItemService.finishItemQuestion(param);
    }


    @Override
    @PostMapping("/initAllFollowupTrackState")
    public List<Long> initAllStateByNuStart() {
        return followupTrackService.initAllFollowupTrackState();
    }


    @Override
    @PostMapping("/followupListByDoctorGroup")
    public PageResult<FollowupListByDoctorGroupVO> followupListByDoctorGroup(@Valid @RequestBody QueryFollowupListByDoctorGroupParam param) {
        return followupTrackService.followupListByDoctorGroup(param);
    }


    @Override
    @PostMapping("/getFollowupTrackItemById")
    public FollowupTrackItemVO getFollowupTrackItemById(@Valid @RequestBody QueryFollowupTrackItemParam param) {
        return followupTrackItemService.getById(param.getItemId());
    }


    @Override
    @PostMapping("/getByItemType")
    public List<FollowupTrackItemVO> getByItemType(@RequestParam(name = "followId") Long followId,
                                                   @RequestParam(name = "itemType") Integer itemType) {
        return followupTrackItemService.getByItemType(followId, itemType);
    }

    @Override
    @PostMapping("/updateConsultTime")
    public void updateConsultTime(@RequestBody UpdateFollowupConsultTimeReqVO reqVO) {
        followupTrackService.updateConsultTime(reqVO);
    }


    @Override
    @PostMapping("/finishForm")
    public void finishForm(@Valid @RequestBody FinishFormFollowupReqVO reqVO) {
        followupTrackService.finishForm(reqVO);
    }


    @Override
    @PostMapping("/updateFollowupItemPatientFormId")
    public void updateFollowupItemPatientFormId(@RequestParam(name = "followupItemId") Long followupItemId,
                                                @RequestParam(name = "patientFormId") Long patientFormId) {
        followupTrackItemService.updateFollowupItemPatientFormId(followupItemId,patientFormId);
    }

    @Override
    @PostMapping("/initFollowupItemPatientForm")
    public void initFollowupItemPatientForm() {
        followupTrackItemService.initFollowupItemPatientForm();
    }

    @Override
    @PostMapping("/saveSummary")
    public void saveSummary(@RequestBody SaveFollowupSummaryReqVO reqVO) {
        followupTrackService.saveSummary(reqVO);
    }

    @Override
    @PostMapping("/saveFollowupSummaryStatus")
    public void saveFollowupSummaryStatus(@RequestBody SaveFollowupSummaryStatusReqVO reqVO) {
        followupTrackService.saveFollowupSummaryStatus(reqVO);
    }


}
