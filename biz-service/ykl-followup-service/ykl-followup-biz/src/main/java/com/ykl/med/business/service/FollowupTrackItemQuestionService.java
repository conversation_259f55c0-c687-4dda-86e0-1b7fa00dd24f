package com.ykl.med.business.service;

import com.ykl.med.business.entity.FollowupTrackItemQuestionDO;
import com.ykl.med.followup.entity.param.QueryQuestionParam;
import com.ykl.med.followup.entity.vo.FollowupTrackItemQuestionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
public interface FollowupTrackItemQuestionService {
    void create(List<FollowupTrackItemQuestionDO> questionDOS);

    List<FollowupTrackItemQuestionVO> queryItemQuestion(Long itemId);

    List<Long> finishQuestion(List<QueryQuestionParam> questions);
}
