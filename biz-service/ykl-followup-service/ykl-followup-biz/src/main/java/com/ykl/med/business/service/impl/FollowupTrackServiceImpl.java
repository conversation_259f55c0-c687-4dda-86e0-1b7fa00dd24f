package com.ykl.med.business.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.business.entity.FollowupTrackDO;
import com.ykl.med.business.entity.FollowupTrackItemDO;
import com.ykl.med.business.mapper.FollowupTrackMapper;
import com.ykl.med.business.service.FollowupTrackItemService;
import com.ykl.med.business.service.FollowupTrackService;
import com.ykl.med.doctors.api.DoctorFeign;
import com.ykl.med.doctors.constants.DoctorErrorCodeConstants;
import com.ykl.med.doctors.entity.vo.DoctorVO;
import com.ykl.med.followup.constants.FollowupErrorCodeConstants;
import com.ykl.med.followup.entity.enums.FollowupTrackStatusEnums;
import com.ykl.med.followup.entity.enums.FollowupTrackSummaryStatusEnum;
import com.ykl.med.followup.entity.param.*;
import com.ykl.med.followup.entity.vo.*;
import com.ykl.med.followup.enums.FollowupTimeModeEnums;
import com.ykl.med.followup.enums.FollowupTimeUnitEnums;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.framework.common.util.date.LocalDateTimeUtils;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.constants.PatientErrorCodeConstants;
import com.ykl.med.patient.vo.PatientVO;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.patient.vo.patient.PatientQueryVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.api.PushSocketFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.push.vo.event.EventTaskCancelVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FollowupTrackServiceImpl extends ServiceImpl<FollowupTrackMapper, FollowupTrackDO> implements FollowupTrackService {

    private final IdServiceImpl idService;
    private final PatientFeign patientFeign;
    private final EventTaskFeign eventTaskFeign;
    private final DoctorFeign doctorFeign;
    private final FollowupTrackMapper followupTrackMapper;
    private final FollowupTrackItemService followupTrackItemService;
    private final PushSocketFeign pushSocketFeign;


    @Override
    public List<FollowupTrackVO> list(QueryFollowupTrackParam param) {
        LambdaQueryWrapper<FollowupTrackDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FollowupTrackDO::getDeleteFlag, false);
        queryWrapper.eq(FollowupTrackDO::getPatientId, param.getPatientId());
        if (!CollectionUtils.isEmpty(param.getState())) {
            queryWrapper.in(FollowupTrackDO::getFollowupState, param.getState());
        }
        if (StringUtils.isNotBlank(param.getFollowupMode())) {
            queryWrapper.eq(FollowupTrackDO::getFollowupMode, param.getFollowupMode());
        }
        if (StringUtils.isNotBlank(param.getFollowupTimeStart())) {
            queryWrapper.ge(FollowupTrackDO::getFollowupTime, LocalDateTimeUtils.convertToLocalDateTime(Long.valueOf(param.getFollowupTimeStart())).toLocalDate());
        }
        if (StringUtils.isNotBlank(param.getFollowupTimeEnd())) {
            queryWrapper.le(FollowupTrackDO::getFollowupTime, LocalDateTimeUtils.convertToLocalDateTime(Long.valueOf(param.getFollowupTimeEnd())).toLocalDate());
        }
        switch (param.getSortType()) {
            case 0:
                queryWrapper.orderByAsc(FollowupTrackDO::getSort);
                break;
            case 1:
                queryWrapper.orderByAsc(FollowupTrackDO::getFollowupTime);
                break;
            case 2:
                queryWrapper.orderByDesc(FollowupTrackDO::getFollowupTime);
                break;
        }
        List<FollowupTrackDO> followupTrackDOS = followupTrackMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(followupTrackDOS)) {
            return null;
        }

        List<Long> followupIds = followupTrackDOS.stream().map(FollowupTrackDO::getId).collect(Collectors.toList());

        List<FollowupTrackItemVO> itemVOS = followupTrackItemService.getByFollowupIds(followupIds);

        Map<Long, List<FollowupTrackItemVO>> listMap = itemVOS.stream().collect(Collectors.groupingBy(FollowupTrackItemVO::getFollowupId));

        List<FollowupTrackVO> followupTrackVOS = CopyPropertiesUtil.normalCopyProperties(followupTrackDOS, FollowupTrackVO.class);
        for (FollowupTrackVO followupTrackVO : followupTrackVOS) {
            followupTrackVO.setItems(listMap.get(followupTrackVO.getId()));
        }
        return followupTrackVOS;
    }


    @Override
    public List<FollowupTrackHistoryVO> history(QueryFollowupTrackHistoryParam param) {
        List<FollowupTrackVO> followupTrackVOS = list(new QueryFollowupTrackParam()
                .setPatientId(param.getPatientId())
                .setState(Arrays.asList(FollowupTrackStatusEnums.FINISH, FollowupTrackStatusEnums.TIME_OUT))
                .setFollowupMode(param.getFollowupMode())
                .setFollowupTimeStart(param.getFollowupTimeStart())
                .setFollowupTimeEnd(param.getFollowupTimeEnd())
                .setSortType(param.getSortType()));
        if (CollectionUtils.isEmpty(followupTrackVOS)) {
            return new ArrayList<>();
        }

        return CopyPropertiesUtil.normalCopyProperties(followupTrackVOS, FollowupTrackHistoryVO.class);
    }


    @Override
    public FollowupTrackVO info(Long id) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(id);
        if (followupTrackDO == null) {
            return null;
        }

        FollowupTrackVO followupTrackVO = CopyPropertiesUtil.normalCopyProperties(followupTrackDO, FollowupTrackVO.class);
        followupTrackVO.setItems(followupTrackItemService.getByFollowupId(id));
        return followupTrackVO;
    }


    @Override
    public FollowupTrackVO current(Long patientId) {
        // 只获取待确认和进行中的随访，按随访时间正序排，取第一条数据
        List<FollowupTrackVO> followupTrackVOS = list(new QueryFollowupTrackParam()
                .setPatientId(patientId)
                .setState(Arrays.asList(FollowupTrackStatusEnums.UN_CONFIRM, FollowupTrackStatusEnums.STARTING, FollowupTrackStatusEnums.AUDIT))
                .setSortType(1));
        if (CollectionUtils.isEmpty(followupTrackVOS)) {
            return null;
        }

        // 只获取第一个
        return followupTrackVOS.get(0);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> create(CreateFollowupTrackParam param) {
        if (CollectionUtils.isEmpty(param.getFollowups())) {
            return null;
        }
        Long patientId = param.getPatientId();
        PatientVO patientVO = patientFeign.getPatientById(patientId);
        AssertUtils.notNull(patientVO.getMemberVersionTime(), PatientErrorCodeConstants.PATIENT_IS_NOT_MEMBER);
        DoctorVO doctorVO = doctorFeign.getById(param.getDoctorId());
        AssertUtils.notNull(doctorVO, DoctorErrorCodeConstants.DIGITAL_DOCTOR_IS_NULL);

        // 移除所有未开始的随访计划
        followupTrackMapper.delAllFollowupByUnStart(patientId);
        // 获取进行中的随访时间
        List<LocalDate> followDateList = getStartingFollowFollowDate(patientId);
        List<FollowupTrackDO> followupTrackDOS = new ArrayList<>();
        List<FollowupTrackItemDO> followupTrackItemDOS = new ArrayList<>();
        // 加入会员时间
        LocalDate memberVersionTime = patientVO.getMemberVersionTime().toLocalDate();
        LocalDate now = LocalDate.now();
        int sort = 1;
        List<Long> followupIds = new ArrayList<>();
        for (FollowupTrackParam followupParam : param.getFollowups()) {
            FollowupTrackDO followupTrackDO = CopyPropertiesUtil.normalCopyProperties(followupParam, FollowupTrackDO.class);
            long followupId = idService.nextId();
            followupTrackDO.setId(followupId);
            followupTrackDO.setDeleteFlag(false);
            followupTrackDO.setDoctorId(doctorVO.getId());
            followupTrackDO.setMedicalTeamId(patientVO.getMedicalTeamId());
            followupTrackDO.setPatientId(patientId);
            LocalDate followupTime;
            if (followupParam.getFollowupTime() != null) {
                followupTime = followupParam.getFollowupTime().toLocalDate();
            } else {
                // 计算随访日期
                followupTime = execFollowupTime(memberVersionTime, followupParam.getFollowupTimeMode(), followupParam.getFollowupTimeLimit(), followupParam.getFollowupTimeLimitUnit());
            }
            AssertUtils.isTrue(!followupTime.isBefore(now), FollowupErrorCodeConstants.FOLLOWUP_TIME_IS_BEFORE_ERROR);
            followupTrackDO.setFollowupTime(followupTime);
            followDateList.add(followupTime);
            if (followupTime.minusDays(4).isBefore(LocalDate.now())) {
                // 计算出的随访时间，在当前时间3天以内，这条随访状态是待确认
                followupTrackDO.setFollowupState(FollowupTrackStatusEnums.UN_CONFIRM);
                followupTrackDO.setFollowupStateDesc(FollowupTrackStatusEnums.UN_CONFIRM.getDesc());
                List<PlanConsultStartTimeVO> planConsultStartTimeDOS = initPlanConsultTime(followupTime, 1);
                followupTrackDO.setPlanConsultStartTime(planConsultStartTimeDOS.get(0).getPlanConsultStartTime());
                followupTrackDO.setPlanConsultEndTime(planConsultStartTimeDOS.get(0).getPlanConsultEndTime());
                followupIds.add(followupId);
            } else {
                // 超过3天，默认未开始
                followupTrackDO.setFollowupState(FollowupTrackStatusEnums.UN_START);
                followupTrackDO.setFollowupStateDesc(FollowupTrackStatusEnums.UN_START.getDesc());
            }
            followupTrackDO.setSort(sort);
            followupTrackDOS.add(followupTrackDO);
            followupTrackItemDOS.addAll(initFollowupTrackItem(followupTrackDO.getId(), followupParam.getItems()));
            sort++;
        }

        // 判断时间重复
        checkFollowDateRepeat(followDateList);
        if (!CollectionUtils.isEmpty(followupTrackDOS)) {
            super.saveBatch(followupTrackDOS);
        }
        if (!CollectionUtils.isEmpty(followupTrackItemDOS)) {
            followupTrackItemService.createItems(followupTrackItemDOS);
        }
        // 添加 随访开始事件
        for (FollowupTrackDO followupTrackDO : followupTrackDOS) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("followName", followupTrackDO.getFollowupName());
            eventTaskFeign.addEventTask(new EventTaskAddVO()
                    .setUserId(param.getUserId())
                    .setPatientId(patientId)
                    .setRequestId(UUID.randomUUID().toString())
                    .setBizId(String.valueOf(followupTrackDO.getId()))
                    .setBizType(EventTaskType.FOLLOW_START)
                    .setEventTime(LocalDateTime.now())
                    .setExecuteTime(followupTrackDO.getFollowupTime().atStartOfDay())
                    .setExtJson(jsonObject)
            );
        }
        return followupIds;
    }

    private void checkFollowDateRepeat(List<LocalDate> followDateList) {
        List<LocalDate> collect = followDateList.stream().distinct().collect(Collectors.toList());
        AssertUtils.isTrue(collect.size() == followDateList.size(), FollowupErrorCodeConstants.FOLLOWUP_TIME_IS_BEFORE_ERROR);
    }

    private List<LocalDate> getStartingFollowFollowDate(Long patientId) {
        // 只获取待确认和进行中的随访，按随访时间正序排，取第一条数据
        List<FollowupTrackVO> followupTrackVOS = list(new QueryFollowupTrackParam()
                .setPatientId(patientId)
                .setState(Arrays.asList(FollowupTrackStatusEnums.UN_CONFIRM, FollowupTrackStatusEnums.STARTING)));
        if (CollectionUtils.isEmpty(followupTrackVOS)) {
            return new ArrayList<>();
        }

        return followupTrackVOS.stream().map(FollowupTrackVO::getFollowupTime).collect(Collectors.toList());
    }

    private List<FollowupTrackItemDO> initFollowupTrackItem(Long followupId, List<FollowupTrackItemParam> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        long count = items.stream().map(FollowupTrackItemParam::getFormId).distinct().count();
        AssertUtils.isTrue(count == items.size(), FollowupErrorCodeConstants.FOLLOWUP_TRACK_ITEM_NOT_ONLY);

        List<FollowupTrackItemDO> itemDOS = new ArrayList<>();
        int sort = 1;
        for (FollowupTrackItemParam item : items) {
            FollowupTrackItemDO followupTrackItemDO = new FollowupTrackItemDO();
            followupTrackItemDO.setFollowupId(followupId);
            followupTrackItemDO.setItemState(false);
            followupTrackItemDO.setItemType(item.getItemType());
            followupTrackItemDO.setItemName(item.getItemName());
            followupTrackItemDO.setItemContent(item.getItemContent());
            followupTrackItemDO.setFormId(item.getFormId());
            followupTrackItemDO.setSort(sort);
            followupTrackItemDO.setDeleteFlag(false);
            itemDOS.add(followupTrackItemDO);
            sort++;
        }
        return itemDOS;
    }

    private LocalDate execFollowupTime(LocalDate memberVersionTime, String followupTimeMode, Integer followupTimeLimit, String followupTimeLimitUnit) {
        LocalDate date;
        if (FollowupTimeModeEnums.JOIN_MEMBER.getCode().equals(followupTimeMode)) {
            FollowupTimeUnitEnums unitEnums = FollowupTimeUnitEnums.getByCode(followupTimeLimitUnit);
            switch (unitEnums) {
                case DAY:
                    date = memberVersionTime.plusDays(followupTimeLimit);
                    break;
                case WEEK:
                    date = memberVersionTime.plusWeeks(followupTimeLimit);
                    break;
                case MONTH:
                    date = memberVersionTime.plusMonths(followupTimeLimit);
                    break;
                case YEAR:
                    date = memberVersionTime.plusYears(followupTimeLimit);
                    break;
                default:
                    throw new ServiceException(FollowupErrorCodeConstants.FOLLOWUP_TIME_UNIT_ERROR);
            }
        } else {
            throw new ServiceException(FollowupErrorCodeConstants.FOLLOWUP_TIME_MODE_ERROR);
        }
        return date;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void start(StartFollowupTrackParam param) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(param.getFollowupId());
        AssertUtils.notNull(followupTrackDO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);
        AssertUtils.isTrue(followupTrackDO.getFollowupState().equals(FollowupTrackStatusEnums.UN_CONFIRM), FollowupErrorCodeConstants.FOLLOWUP_STATE_IS_NOT_UN_START);

        DoctorVO doctorVO = doctorFeign.getById(param.getUserId());
        AssertUtils.notNull(doctorVO, DoctorErrorCodeConstants.DIGITAL_DOCTOR_IS_NULL);

        followupTrackDO.setFollowupState(FollowupTrackStatusEnums.STARTING);
        followupTrackDO.setFollowupStateDesc(FollowupTrackStatusEnums.STARTING.getDesc());
        followupTrackDO.setDoctorId(doctorVO.getId());
        followupTrackDO.setMedicalTeamId(doctorVO.getMedicalTeamId());
        followupTrackMapper.updateById(followupTrackDO);
    }


    @Override
    public void updateDoctorRemark(UpdateDoctorRemarkParam param) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(param.getFollowupId());
        AssertUtils.notNull(followupTrackDO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);

        followupTrackDO.setDoctorRemark(param.getDoctorRemark());
        followupTrackMapper.updateById(followupTrackDO);
    }

    @Override
    public void updatePatientRemark(UpdatePatientRemarkParam param) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(param.getFollowupId());
        AssertUtils.notNull(followupTrackDO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);

        followupTrackDO.setPatientRemark(param.getPatientRemark());
        followupTrackMapper.updateById(followupTrackDO);
    }


    @Override
    public List<Long> initAllFollowupTrackState() {
        LocalDate date = LocalDate.now().plusDays(3);
        LambdaQueryWrapper<FollowupTrackDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FollowupTrackDO::getDeleteFlag, false);
        queryWrapper.eq(FollowupTrackDO::getFollowupState, FollowupTrackStatusEnums.UN_START);
        queryWrapper.le(FollowupTrackDO::getFollowupTime, date);
        List<FollowupTrackDO> followupTrackDOS = followupTrackMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(followupTrackDOS)) {
            return new ArrayList<>();
        }

        // 计算可以使用的视频时间
        List<PlanConsultStartTimeVO> planConsultStartTimeDOS = initPlanConsultTime(date, followupTrackDOS.size());
        for (int i = 0; i < followupTrackDOS.size(); i++) {
            FollowupTrackDO followupTrackDO = followupTrackDOS.get(i);
            followupTrackDO.setFollowupState(FollowupTrackStatusEnums.UN_CONFIRM);
            followupTrackDO.setFollowupStateDesc(FollowupTrackStatusEnums.UN_CONFIRM.getDesc());
            followupTrackDO.setPlanConsultStartTime(planConsultStartTimeDOS.get(i).getPlanConsultStartTime());
            followupTrackDO.setPlanConsultEndTime(planConsultStartTimeDOS.get(i).getPlanConsultEndTime());
            followupTrackMapper.updateById(followupTrackDO);

            // 将之前未完成的随访设置成已失访
            initAllStateByTimeOut(followupTrackDO.getPatientId(), followupTrackDO.getFollowupTime());
        }
        return followupTrackDOS.stream().map(FollowupTrackDO::getId).collect(Collectors.toList());
    }

    List<String> generateTimeSlots() {
        List<String> slots = new ArrayList<>();
        for (int hour = 9; hour < 17; hour++) {
            slots.add(String.format("%s-%s", hour, hour + 1));
        }
        return slots; // ["9-10","10-11",...,"16-17"]
    }

    public List<PlanConsultStartTimeVO> initPlanConsultTime(LocalDate date, int waitAddDataSize) {
        List<PlanConsultStartTimeVO> result = new ArrayList<>();
        List<String> allTimeSlots = generateTimeSlots();
        LocalDateTime localDateTime = date.atStartOfDay();
        // 当天所有的随访数据
        LambdaQueryWrapper<FollowupTrackDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FollowupTrackDO::getDeleteFlag, false);
        queryWrapper.eq(FollowupTrackDO::getFollowupTime, date);
        queryWrapper.isNotNull(FollowupTrackDO::getPlanConsultStartTime);
        queryWrapper.isNotNull(FollowupTrackDO::getPlanConsultEndTime);
        List<FollowupTrackDO> followupTrackDOS = followupTrackMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(followupTrackDOS)) {
            int i = 0;
            while (result.size() != waitAddDataSize) {
                String s = allTimeSlots.get(i % allTimeSlots.size());
                // 拆解开始与结束时间
                String[] split = s.split("-");
                LocalDateTime startTime = localDateTime.plusHours(Integer.parseInt(split[0]));
                LocalDateTime endTime = localDateTime.plusHours(Integer.parseInt(split[1]));
                PlanConsultStartTimeVO planConsultStartTimeVO = new PlanConsultStartTimeVO();
                planConsultStartTimeVO.setPlanConsultStartTime(startTime);
                planConsultStartTimeVO.setPlanConsultEndTime(endTime);
                result.add(planConsultStartTimeVO);
                i++;
            }
        } else {
            // 已有的随访时间
            Set<String> collect = followupTrackDOS.stream().map(e -> String.format("%s-%s", e.getPlanConsultStartTime().getHour(), e.getPlanConsultEndTime().getHour())).collect(Collectors.toSet());
            int i = 0;
            boolean first = true;
            while (result.size() != waitAddDataSize) {
                String s = allTimeSlots.get(i % allTimeSlots.size());
                if (first && collect.contains(s)) {
                    i++;
                    if (i == allTimeSlots.size()) {
                        first = false;
                    }
                    continue;
                }
                // 更新索引和循环状态
                String[] split = s.split("-");
                LocalDateTime startTime = localDateTime.plusHours(Integer.parseInt(split[0]));
                LocalDateTime endTime = localDateTime.plusHours(Integer.parseInt(split[1]));
                PlanConsultStartTimeVO planConsultStartTimeVO = new PlanConsultStartTimeVO();
                planConsultStartTimeVO.setPlanConsultStartTime(startTime);
                planConsultStartTimeVO.setPlanConsultEndTime(endTime);
                result.add(planConsultStartTimeVO);
                i++;
            }
        }
        return result;
    }

    private void initAllStateByTimeOut(Long patientId, LocalDate followupTime) {
        LambdaQueryWrapper<FollowupTrackDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FollowupTrackDO::getDeleteFlag, false);
        queryWrapper.eq(FollowupTrackDO::getPatientId, patientId);
        queryWrapper.in(FollowupTrackDO::getFollowupState, Arrays.asList(FollowupTrackStatusEnums.UN_START,
                FollowupTrackStatusEnums.UN_CONFIRM,
                FollowupTrackStatusEnums.STARTING,
                FollowupTrackStatusEnums.AUDIT));
        queryWrapper.lt(FollowupTrackDO::getFollowupTime, followupTime);
        List<FollowupTrackDO> followupTrackDOS = followupTrackMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(followupTrackDOS)) {
            return;
        }

        for (FollowupTrackDO followupTrackDO : followupTrackDOS) {
            followupTrackDO.setFollowupState(FollowupTrackStatusEnums.TIME_OUT);
            followupTrackDO.setFollowupStateDesc(FollowupTrackStatusEnums.TIME_OUT.getDesc());
            followupTrackMapper.updateById(followupTrackDO);
        }
    }

    @Override
    public PageResult<FollowupListByDoctorGroupVO> followupListByDoctorGroup(QueryFollowupListByDoctorGroupParam param) {
        DoctorVO doctorVO = doctorFeign.getById(param.getCurrentUserId());
        LambdaQueryWrapper<FollowupTrackDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FollowupTrackDO::getDeleteFlag, false);
        queryWrapper.eq(FollowupTrackDO::getMedicalTeamId, doctorVO.getMedicalTeamId());  // 医疗组
        List<FollowupTrackStatusEnums> followupTrackStatusEnums = new ArrayList<>();
        followupTrackStatusEnums.add(FollowupTrackStatusEnums.UN_CONFIRM);
        followupTrackStatusEnums.add(FollowupTrackStatusEnums.STARTING);
        /* 主管医生权限 */
        boolean isManagingDoctor = !CollectionUtils.isEmpty(param.getDoctor_manage())
                && param.getDoctor_manage().contains(param.getCurrentUserId());
        if (isManagingDoctor) {
            followupTrackStatusEnums.add(FollowupTrackStatusEnums.AUDIT);
        }
        queryWrapper.in(FollowupTrackDO::getFollowupState, followupTrackStatusEnums);
        queryWrapper.orderByAsc(FollowupTrackDO::getFollowupTime);
        Page<FollowupTrackDO> followupTrackPage = followupTrackMapper.selectPage(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
        List<FollowupTrackDO> records = followupTrackPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageResult<>();
        }

        List<Long> patientIds = records.stream().map(FollowupTrackDO::getPatientId).collect(Collectors.toList());
        List<PatientBaseVO> patientBaseVOS = patientFeign.queryPatient(new PatientQueryVO().setIds(patientIds));

        Map<Long, List<PatientBaseVO>> recipeDishDOS = patientBaseVOS.stream().collect(Collectors.groupingBy(PatientBaseVO::getPatientId));
        List<FollowupListByDoctorGroupVO> followupListByDoctorGroupVOS = new ArrayList<>();
        for (FollowupTrackDO followupTrackDO : records) {
            FollowupListByDoctorGroupVO followupListByDoctorGroupVO = CopyPropertiesUtil.normalCopyProperties(followupTrackDO, FollowupListByDoctorGroupVO.class);
            List<PatientBaseVO> patientUserDetailVOS = recipeDishDOS.get(followupTrackDO.getPatientId());
            if (!CollectionUtils.isEmpty(patientUserDetailVOS)) {
                PatientBaseVO patientBaseVO = patientUserDetailVOS.get(0);
                Long patientId = patientBaseVO.getPatientId();
                followupListByDoctorGroupVO.setPatientId(String.valueOf(patientId));
                followupListByDoctorGroupVO.setPatientName(patientBaseVO.getName());
                followupListByDoctorGroupVO.setSex(patientBaseVO.getSex());
                followupListByDoctorGroupVO.setAge(patientBaseVO.getAge());
                followupListByDoctorGroupVO.setPhone(patientBaseVO.getContactPhone());
            }
            followupListByDoctorGroupVOS.add(followupListByDoctorGroupVO);
        }

        return new PageResult<>(followupListByDoctorGroupVOS, followupTrackPage.getTotal());
    }

    @Override
    public void updateConsultTime(UpdateFollowupConsultTimeReqVO reqVO) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(reqVO.getFollowupId());
        AssertUtils.notNull(followupTrackDO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);
        if (reqVO.getPlanConsultStartTime() != null) {
            followupTrackDO.setPlanConsultStartTime(reqVO.getPlanConsultStartTime());
        }
        if (reqVO.getPlanConsultEndTime() != null) {
            followupTrackDO.setPlanConsultEndTime(reqVO.getPlanConsultEndTime());
        }
        if (reqVO.getConsultStartTime() != null) {
            followupTrackDO.setConsultStartTime(reqVO.getConsultStartTime());
        }
        if (reqVO.getConsultEndTime() != null) {
            followupTrackDO.setConsultEndTime(reqVO.getConsultEndTime());
        }
        followupTrackDO.setUpdateTime(DateTimeUtils.getNow());
        followupTrackMapper.updateById(followupTrackDO);
    }

    @Override
    public void finishForm(FinishFormFollowupReqVO reqVO) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(reqVO.getBizId());
        AssertUtils.notNull(followupTrackDO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);
        followupTrackItemService.finishForm(reqVO);
    }

    @Override
    public void saveSummary(SaveFollowupSummaryReqVO reqVO) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(reqVO.getFollowupId());
        AssertUtils.notNull(followupTrackDO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);
        followupTrackDO.setFollowupConclusion(reqVO.getSummary());
        followupTrackDO.setFollowupSummaryStatus(FollowupTrackSummaryStatusEnum.SUCCESS);
        followupTrackDO.setUpdateTime(DateTimeUtils.getNow());
        followupTrackMapper.updateById(followupTrackDO);

        // 通知医生刷新
        pushSocketFeign.refreshPageNotice(reqVO.getDoctorId(), followupTrackDO.getPatientId());
    }


    @Override
    public void finish(FinishFollowupTrackParam param) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(param.getFollowupId());
        AssertUtils.notNull(followupTrackDO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);

        AssertUtils.isTrue(followupTrackDO.getFollowupState().equals(FollowupTrackStatusEnums.STARTING)
                || followupTrackDO.getFollowupState().equals(FollowupTrackStatusEnums.AUDIT), FollowupErrorCodeConstants.FOLLOWUP_STATE_IS_NOT_STARTING);

        followupTrackDO.setFollowupState(param.getStatus());
        followupTrackDO.setFollowupStateDesc(param.getStatus().getDesc());
        followupTrackDO.setDoctorRemark(param.getDoctorRemark());
        followupTrackDO.setFollowupConclusion(param.getFollowupConclusion());
        followupTrackDO.setFollowupTimeEnd(LocalDateTime.now());
        followupTrackDO.setFollowupSummaryStatus(FollowupTrackSummaryStatusEnum.SUCCESS);
        followupTrackMapper.updateById(followupTrackDO);

        if (param.getStatus().equals(FollowupTrackStatusEnums.FINISH)) {
            // 关闭 随访完成 通知
            eventTaskFeign.cancel(new EventTaskCancelVO()
                    .setPatientId(followupTrackDO.getPatientId())
                    .setBizId(String.valueOf(followupTrackDO.getId()))
                    .setBizType(EventTaskType.FOLLOW_END)
            );
        }
    }

    @Override
    public void saveFollowupSummaryStatus(SaveFollowupSummaryStatusReqVO reqVO) {
        FollowupTrackDO followupTrackDO = followupTrackMapper.selectById(reqVO.getFollowupId());
        AssertUtils.notNull(followupTrackDO, FollowupErrorCodeConstants.FOLLOWUP_NOT_EXIST);
        followupTrackDO.setFollowupSummaryStatus(reqVO.getSummaryStatusEnums());
        if (reqVO.getSummaryStatusEnums().equals(FollowupTrackSummaryStatusEnum.STARTING)) {
            followupTrackDO.setFollowupConclusion(null);
        }
        followupTrackDO.setUpdateTime(DateTimeUtils.getNow());
        followupTrackMapper.updateById(followupTrackDO);

        // 通知医生刷新
        pushSocketFeign.refreshPageNotice(reqVO.getDoctorId(), followupTrackDO.getPatientId());
    }
}
