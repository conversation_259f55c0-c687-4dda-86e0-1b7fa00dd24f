package com.ykl.med.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/8/14
 */
@Data
@TableName(value = "t_followup_item")
public class FollowupInspectItemDO extends BaseDO {

    /** 随访ID */
    private Long followupId;
    /** 项目ID */
    private Long itemId;
    /** 患者ID */
    private Long patientId;
    /** 项目加入编号 */
    private Integer number;
    /** 项目类别（诊疗/表单/康复训练） */
    private String itemClass;
    /** 项目类型（检查/检验/手术/随访问卷/正念冥想） */
    private String itemType;
    /** 项目名称 */
    private String itemName;
    /** 项目内容（可存表单数据） */
    private String itemContent;
    /** 是否可选：0必选，1可选 */
    private Boolean isOptional;
    /** 项目组号，标注属于同一组执行 */
    private Integer groupNo;
    /** 执行标志，false-未执行，true-已执行 */
    private Boolean execFlag;
    /** 执行时间 */
    private LocalDateTime execTime;
    /** 备注（项目说明） */
    private String remark;
    /** 创建者ID【实体】 */
    private Long createUserId;
    /** 最后一次操作者ID【实体】 */
    private Long lastUserId;
    /** 状态；启用：ENABLE，禁用：DISABLE */
    private String status;
    /** 待办通知标志；0待办未通知，1待办已通知，2执行已通知； */
    private Integer todoNotifyFlag;
    /** 删除标志；false-未删，true-已删 */
    private Boolean deleteFlag;

}
