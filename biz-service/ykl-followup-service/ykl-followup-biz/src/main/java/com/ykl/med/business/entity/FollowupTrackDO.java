package com.ykl.med.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.followup.entity.enums.FollowupTrackStatusEnums;
import com.ykl.med.followup.entity.enums.FollowupTrackSummaryStatusEnum;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@Data
@TableName(value = "t_followup_track")
public class FollowupTrackDO extends BaseDO {

    /** 患者id */
    private Long patientId;

    /** 随访状态 */
    private FollowupTrackStatusEnums followupState;

    /** 随访状态描述 */
    private String followupStateDesc;

    /** 随访名称 */
    private String followupName;

    /** 随访方式(字典表) */
    private String followupMode;

    /** 随访时间方式(字典表) */
    private String followupTimeMode;

    /** 随访时间 - 时限 */
    private Integer followupTimeLimit;

    /** 随访时间 - 时限单位:DAY-天/MONTY-月/WEEK-周/YEAR-年 */
    private String followupTimeLimitUnit;

    /** 随访时间 - 计算后的具体随访时间 */
    private LocalDate followupTime;

    /** 随访完成时间 */
    private LocalDateTime followupTimeEnd;

    /** 随访结论 */
    private String followupConclusion;

    /** 医生备注 - 只有医生能看 */
    private String doctorRemark;

    /** 患者备注 - 都能看 */
    private String patientRemark;

    /** 排序 */
    private Integer sort;

    /** 删除标志(0/false-未删除,1/true-已删除) */
    private Boolean deleteFlag;

    /** 随访医生 */
    private Long doctorId;

    /** 视频随访开始时间（计划） */
    private LocalDateTime planConsultStartTime;

    /** 视频随访开始时间（计划） */
    private LocalDateTime planConsultEndTime;

    /** 视频随访开始时间（实际） */
    private LocalDateTime consultStartTime;

    /** 视频随访开始时间（实际） */
    private LocalDateTime consultEndTime;

    /** 医疗组id */
    private Long medicalTeamId;

    /** 随访小结状态 */
    private FollowupTrackSummaryStatusEnum followupSummaryStatus;

}
