package com.ykl.med.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.business.entity.FollowupInspectDO;
import com.ykl.med.business.mapper.FollowupInspectMapper;
import com.ykl.med.business.service.FollowupInspectService;
import com.ykl.med.followup.entity.vo.FollowupInspectVO;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.vo.PatientVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/14
 */
@Slf4j
@Service
public class FollowupInspectServiceImpl extends ServiceImpl<FollowupInspectMapper, FollowupInspectDO> implements FollowupInspectService {

    @Resource
    private PatientFeign patientFeign;
    @Resource
    private FollowupInspectMapper followupInspectMapper;


    @Override
    public FollowupInspectVO info(Long followupId) {
        FollowupInspectDO followupInspectDO = followupInspectMapper.selectById(followupId);
        if (followupInspectDO == null) {
            return null;
        }

        FollowupInspectVO followupInspectVO = CopyPropertiesUtil.normalCopyProperties(followupInspectDO, FollowupInspectVO.class);
        PatientVO patientVO = patientFeign.getPatientById(followupInspectVO.getPatientId());

        FollowupInspectVO lastFollowupInspectVO = queryNext(followupInspectVO.getPatientId(), followupInspectDO.getStartTime());
        if (lastFollowupInspectVO != null) {
            followupInspectVO.setUpStartTime(lastFollowupInspectVO.getEndTime() != null ? lastFollowupInspectVO.getEndTime() : lastFollowupInspectVO.getStartTime());
            followupInspectVO.setUpEndTime(followupInspectVO.getStartTime());
        } else {
            followupInspectVO.setUpStartTime(patientVO.getMemberVersionTime());
            followupInspectVO.setUpEndTime(followupInspectVO.getStartTime());
        }

        if (patientVO != null) {
            followupInspectVO.setPatientName(patientVO.getName());
            followupInspectVO.setAge(patientVO.getAge());
            followupInspectVO.setSex(patientVO.getSex());
            followupInspectVO.setPhone(patientVO.getContactPhone());
        }

        return followupInspectVO;
    }

    private FollowupInspectVO queryNext(Long patientId, LocalDateTime startTime) {
        LambdaQueryWrapper<FollowupInspectDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FollowupInspectDO::getDeleteFlag, false);
        queryWrapper.eq(FollowupInspectDO::getPatientId, patientId);
        queryWrapper.lt(FollowupInspectDO::getStartTime, startTime);
        queryWrapper.orderByDesc(FollowupInspectDO::getStartTime);
        List<FollowupInspectDO> followupInspectDOS = followupInspectMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(followupInspectDOS)) {
            return null;
        }
        return CopyPropertiesUtil.normalCopyProperties(followupInspectDOS.get(0), FollowupInspectVO.class);
    }


}
