package com.ykl.med.business.service;

import com.ykl.med.followup.entity.param.*;
import com.ykl.med.followup.entity.vo.FollowupListByDoctorGroupVO;
import com.ykl.med.followup.entity.vo.FollowupTrackHistoryVO;
import com.ykl.med.followup.entity.vo.FollowupTrackVO;
import com.ykl.med.framework.common.pojo.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
public interface FollowupTrackService {
    FollowupTrackVO info(Long id);

    FollowupTrackVO current(Long patientId);

    List<FollowupTrackVO> list(QueryFollowupTrackParam param);

    List<FollowupTrackHistoryVO> history(QueryFollowupTrackHistoryParam param);

    void finish(FinishFollowupTrackParam param);

    List<Long> create(CreateFollowupTrackParam param);

    void start(StartFollowupTrackParam param);

    void updateDoctorRemark(UpdateDoctorRemarkParam param);

    void updatePatientRemark(UpdatePatientRemarkParam param);

    List<Long> initAllFollowupTrackState();

    PageResult<FollowupListByDoctorGroupVO> followupListByDoctorGroup(QueryFollowupListByDoctorGroupParam param);

    void updateConsultTime(UpdateFollowupConsultTimeReqVO reqVO);

    void finishForm(FinishFormFollowupReqVO reqVO);

    void saveSummary(SaveFollowupSummaryReqVO reqVO);

    void saveFollowupSummaryStatus(SaveFollowupSummaryStatusReqVO reqVO);
}
