package com.ykl.med.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@Data
@TableName(value = "t_followup_track_item")
public class FollowupTrackItemDO extends BaseDO {

    /** 随访id */
    private Long followupId;

    /** 随访项目状态(0-待完成、1-已完成) */
    private Boolean itemState;

    /** 项目类型(1-文本、2-问卷) */
    private Integer itemType;

    /** 项目名称 */
    private String itemName;

    /** 项目内容 */
    private String itemContent;

    /** 完成时间 */
    private LocalDateTime itemTimeEnd;

    /** 表单id */
    private Long formId;

    /** 患者表单id */
    private Long patientFormId;

    /** 排序 */
    private Integer sort;

    /** 删除标志(0/false-未删除,1/true-已删除) */
    private Boolean deleteFlag;


}
