package com.ykl.med.application.entity;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/12/23 19:42
 */
@Getter
public enum ResponseCode {
    /**
     * 请求返回的状态InvalidTokenException
     */
    SUCCESS("成功", 0),
    INVALID_ARGUMENT("参数不合法", 502, "参数不合法"),
    EXPORT_EXCEL_EXCEPTION("导出excel失败", 402, "导出excel失败"),
    REQUEST_BODY_FAIL("请求体错误", 403, "请求体错误或不存在"),
    DATA_REPOSITORY_DRIVER_FAIL("数据仓库驱动错误", 504, "数据仓库驱动为NULL"),
    FAIL("失败", 500),
    EXEC_SQL_FAIL("执行SQL失败", 501),
    EXEC_SQL_WRITE_DATA_REPEAT("数据重复", 505, "不可重复"),
    UNKNOWN_EXCEPTION("未知错误", 1000, "当前请求发生未知错误，请稍后再试");

    ResponseCode(String label, Integer code) {
        this.label = label;
        this.code = code;
        this.message = null;
    }

    ResponseCode(String label, Integer code, String message) {
        this.label = label;
        this.code = code;
        this.message = message;
    }

    private final String label;
    private final Integer code;
    private final String message;
}
