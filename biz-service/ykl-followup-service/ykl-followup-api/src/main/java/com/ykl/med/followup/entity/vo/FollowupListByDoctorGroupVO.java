package com.ykl.med.followup.entity.vo;

import com.ykl.med.followup.entity.enums.FollowupTrackStatusEnums;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/8
 */
@Data
public class FollowupListByDoctorGroupVO {

    @Schema(description = "跟踪随访id")
    @Stringify
    private Long id;

    @Schema(description = "随访状态")
    private FollowupTrackStatusEnums followupState;

    @Schema(description = "随访状态描述")
    private String followupStateDesc;

    @Schema(description = "随访名称")
    private String followupName;

    @Schema(description = "随访方式(字典表)")
    private String followupMode;

    @Schema(description = "随访时间")
    @TimestampConvert
    private LocalDate followupTime;

    @Schema(description = "随访完成时间")
    @TimestampConvert
    private LocalDateTime followupTimeEnd;

    @Schema(description = "患者id")
    private String patientId;

    @Schema(description = "患者-名字")
    private String patientName;

    @Schema(description = "患者-性别")
    private String sex;

    @Schema(description = "患者-年龄")
    private Integer age;

    @Schema(description = "患者-手机号")
    private String phone;

    @Schema(description = "患者-病种阶段")
    private String stage;

    @Schema(description = "诊断（该字段及以下为随访报告字段）")
    private String diagnosis;

    @Schema(description = "临床分期")
    private String clinicalStaging;

    @Schema(description = "病理分型")
    private String pathologicalType;

    @Schema(description = "手术日期距离现在多少周或者多少月")
    private String surgeryDateDistance;

    @Schema(description = "视频随访开始时间（计划）")
    @TimestampConvert
    private LocalDateTime planConsultStartTime;

    @Schema(description = "视频随访开始时间（计划）")
    @TimestampConvert
    private LocalDateTime planConsultEndTime;

}
