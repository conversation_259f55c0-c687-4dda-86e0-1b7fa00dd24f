package com.ykl.med.followup.api;

import com.ykl.med.followup.entity.vo.FollowupItemVO;
import com.ykl.med.framework.common.pojo.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-followup-service", path = "/ykl-followup-service/api/followupItem")
public interface FollowupItemFeign {
    /**
     * 随访管理增删改查
     * @param httpMethod : http请求的方法: GET/POST/PUT/DELETE
     * @param obj : http请求对象
     * */
    @PostMapping("/crud")
    PageResult<FollowupItemVO> crud(@RequestParam(value = "httpMethod") String httpMethod, @RequestBody Object obj);
}
