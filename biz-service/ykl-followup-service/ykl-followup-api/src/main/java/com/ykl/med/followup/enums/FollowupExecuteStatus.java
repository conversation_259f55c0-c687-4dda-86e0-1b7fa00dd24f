package com.ykl.med.followup.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 随访执行状态
 * */
@AllArgsConstructor
@Getter
public enum FollowupExecuteStatus {

    NOT_STARTED("未开始","0","NOT_STARTED"),
    TO_BE_CONFIRMED("待确认","10","TO_BE_CONFIRMED"),
    IN_PROGRESS("进行中","20","IN_PROGRESS"),
    COMPLETED("已完成","30","COMPLETED"),
    LOST_FOLLOWUP("已失访","40","LOST_FOLLOWUP"),
    ;

    private final String label;
    private final String value;
    private final String name;

    /**
     * 使用value查找
     * */
    public static FollowupExecuteStatus fromValue(String value){
        for ( FollowupExecuteStatus enumItem : FollowupExecuteStatus.values() ){
            if ( enumItem.value.equals(value) ){
                return enumItem;
            }
        }

        return null;
    }

    /**
     * 使用name查找
     * */
    public static FollowupExecuteStatus fromName(String name){
        for ( FollowupExecuteStatus enumItem : FollowupExecuteStatus.values() ){
            if ( enumItem.name.equals(name) ){
                return enumItem;
            }
        }

        return null;
    }
}
