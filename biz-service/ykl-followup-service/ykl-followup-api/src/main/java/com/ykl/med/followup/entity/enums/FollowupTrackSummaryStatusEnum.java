package com.ykl.med.followup.entity.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/6/19
 */
@Getter
@AllArgsConstructor
@Schema(description = "随访小结状态枚举：WAIT(待生成), " +
        "STARTING(生成中), " +
        "SUCCESS(成功), " +
        "FAIL(生成失败)")
public enum FollowupTrackSummaryStatusEnum {

    WAIT("待生成"),
    STARTING("生成中"),
    SUCCESS("成功"),
    FAIL("生成失败");

    private final String desc;

}
