package com.ykl.med.followup.entity.param;

import com.ykl.med.followup.entity.enums.FollowupTrackSummaryStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/6/19
 */
@Data
public class SaveFollowupSummaryStatusReqVO {

    @Schema(description = "随访id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long followupId;

    @Schema(description = "随访小结状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private FollowupTrackSummaryStatusEnum summaryStatusEnums;


}
