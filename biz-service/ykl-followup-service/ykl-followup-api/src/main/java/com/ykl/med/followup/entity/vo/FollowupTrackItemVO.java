package com.ykl.med.followup.entity.vo;

import com.ykl.med.followup.entity.enums.FollowupTrackStatusEnums;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.masterdata.vo.FormManageVO;
import com.ykl.med.masterdata.vo.FormVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/2
 */
@Data
@Schema(description = "随访项目")
public class FollowupTrackItemVO {

    @Schema(description = "随访id")
    @Stringify
    private Long followupId;

    @Schema(description = "随访状态")
    private FollowupTrackStatusEnums followupState;

    @Schema(description = "随访状态描述")
    private String followupStateDesc;

    @Schema(description = "项目id")
    @Stringify
    private Long itemId;

    @Schema(description = "随访项目状态(0-待完成、1-已完成)")
    private Boolean itemState;

    @Schema(description = "项目类型(1-文本、2-问卷)")
    private Integer itemType;

    @Schema(description = "项目名称")
    private String itemName;

    @Schema(description = "项目内容")
    private String itemContent;

    @Schema(description = "完成时间")
    @TimestampConvert
    private LocalDateTime itemTimeEnd;

    @Schema(description = "表单")
    private FormVO formVO;

    @Schema(description = "表单id")
    @Stringify
    private Long formId;

    @Schema(description = "患者表单id")
    @Stringify
    private Long patientFormId;

}
