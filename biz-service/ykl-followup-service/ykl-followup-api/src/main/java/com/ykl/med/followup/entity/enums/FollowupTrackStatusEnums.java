package com.ykl.med.followup.entity.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  随访状态
 * <AUTHOR>
 * @since 2024/7/2
 */
@Getter
@AllArgsConstructor
@Schema(description = "随访状态枚举：UN_START(未开始), " +
        "UN_CONFIRM(待确认), " +
        "STARTING(进行中), " +
        "AUDIT(审核中), " +
        "FINISH(已完成), " +
        "TIME_OUT(已失访)")
public enum FollowupTrackStatusEnums {

    UN_START("未开始"),
    UN_CONFIRM("待确认"),
    STARTING("进行中"),
    AUDIT("审核中"),
    FINISH("已完成"),
    TIME_OUT("已失访"),
    ;

    private final String desc;

}
