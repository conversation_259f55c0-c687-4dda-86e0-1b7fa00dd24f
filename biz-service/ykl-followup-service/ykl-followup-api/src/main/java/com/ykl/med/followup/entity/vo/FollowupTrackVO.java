package com.ykl.med.followup.entity.vo;

import com.ykl.med.followup.entity.enums.FollowupTrackStatusEnums;
import com.ykl.med.followup.entity.enums.FollowupTrackSummaryStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.util.DateTimeUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/2
 */
@Data
@Schema(description = "跟踪随访详情")
public class FollowupTrackVO {

    @Schema(description = "跟踪随访id")
    @Stringify
    private Long id;

    @Schema(description = "患者id")
    private Long patientId;

    @Schema(description = "随访状态")
    private FollowupTrackStatusEnums followupState;

    @Schema(description = "随访状态描述")
    private String followupStateDesc;

    @Schema(description = "随访名称")
    private String followupName;

    @Schema(description = "随访方式(字典表)")
    private String followupMode;

    @Schema(description = "随访时间方式(字典表)")
    private String followupTimeMode;

    @Schema(description = "时间")
    private Integer followupTimeLimit;

    @Schema(description = "时间单位(字典)")
    private String followupTimeLimitUnit;

    @Schema(description = "随访时间")
    @TimestampConvert
    private LocalDate followupTime;

    @Schema(description = "随访完成时间")
    @TimestampConvert
    private LocalDateTime followupTimeEnd;

    @Schema(description = "随访结论")
    private String followupConclusion;

    @Schema(description = "医生备注")
    private String doctorRemark;

    @Schema(description = "患者备注")
    private String patientRemark;

    @Schema(description = "随访医生")
    @Stringify
    private Long doctorId;

    @Schema(description = "当前医生角色（1-主管医生、2-团队医生）")
    private Integer loginDoctorRole;

    @Schema(description = "视频随访开始时间（计划）")
    @TimestampConvert
    private LocalDateTime planConsultStartTime;

    @Schema(description = "视频随访开始时间（计划）")
    @TimestampConvert
    private LocalDateTime planConsultEndTime;

    @Schema(description = "视频随访开始时间（实际）")
    @TimestampConvert
    private LocalDateTime consultStartTime;

    @Schema(description = "视频随访开始时间（实际）")
    @TimestampConvert
    private LocalDateTime consultEndTime;

    @Schema(description = "问诊Id")
    @Stringify
    private Long consultId;

    @Schema(description = "问诊userId")
    @Stringify
    private Long consultUserId;

    @Schema(description = "是否允许修改问诊人，false-不允许、true-允许")
    private Boolean saveConsultUserIdFlag;

    @Schema(description = "问诊是否结束，已经结束的随访，视频随访的按钮需要置灰（false-未结束、true-已结束）")
    private Boolean consultEndFlag;

    @Schema(description = "聊天标题")
    private String title;

    @Schema(description = "视频聊天")
    private String chatStr;

    @Schema(description = "随访小结状态")
    private FollowupTrackSummaryStatusEnum followupSummaryStatus;

    @Schema(description = "项目列表")
    private List<FollowupTrackItemVO> items;

}
