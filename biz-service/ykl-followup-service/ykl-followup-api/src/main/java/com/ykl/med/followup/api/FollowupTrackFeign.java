package com.ykl.med.followup.api;

import com.ykl.med.followup.entity.param.*;
import com.ykl.med.followup.entity.vo.FollowupListByDoctorGroupVO;
import com.ykl.med.followup.entity.vo.FollowupTrackHistoryVO;
import com.ykl.med.followup.entity.vo.FollowupTrackItemVO;
import com.ykl.med.followup.entity.vo.FollowupTrackVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.masterdata.vo.FormVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@FeignClient(name = "ykl-followup-service", path = "/ykl-followup-service/followup/track")
public interface FollowupTrackFeign {
    @PostMapping("/info/web")
    FollowupTrackVO infoWeb(@Valid @RequestBody QueryFollowupTrackInfoParam param);

    @PostMapping("/current")
    FollowupTrackVO current(@Valid @RequestBody QueryCurrentFollowupTrackParam param);

    @PostMapping("/list")
    List<FollowupTrackVO> list(@Valid @RequestBody QueryFollowupTrackParam param);

    @PostMapping("/history")
    List<FollowupTrackHistoryVO> history(@Valid @RequestBody QueryFollowupTrackHistoryParam param);

    @PostMapping("/finish")
    void finish(@Valid @RequestBody FinishFollowupTrackParam param);

    @PostMapping("/create")
    List<Long> create(@Valid @RequestBody CreateFollowupTrackParam param);

    @PostMapping("/start")
    void start(@Valid @RequestBody StartFollowupTrackParam param);

    @PostMapping("/updateDoctorRemark")
    void updateDoctorRemark(@Valid @RequestBody UpdateDoctorRemarkParam param);

    @PostMapping("/updatePatientRemark")
    void updatePatientRemark(@Valid @RequestBody UpdatePatientRemarkParam param);

    @PostMapping("/updateFollowupItem")
    void updateFollowupItem(@Valid @RequestBody UpdateFollowupItemParam param);

    @PostMapping("/queryItemQuestion")
    FormVO queryItemQuestion(@Valid @RequestBody QueryItemQuestionParam param);

    @PostMapping("/queryItemQuestion/v2")
    FollowupTrackItemVO queryItemQuestionV2(@Valid @RequestBody QueryItemQuestionParam param);

    @PostMapping("/finishItemQuestion")
    List<Long> finishItemQuestion(@Valid @RequestBody FinishItemQuestionParam param);

    @PostMapping("/initAllFollowupTrackState")
    List<Long> initAllStateByNuStart();

    @PostMapping("/followupListByDoctorGroup")
    PageResult<FollowupListByDoctorGroupVO> followupListByDoctorGroup(@Valid @RequestBody QueryFollowupListByDoctorGroupParam param);

    @PostMapping("/getFollowupTrackItemById")
    FollowupTrackItemVO getFollowupTrackItemById(@Valid @RequestBody QueryFollowupTrackItemParam param);

    @PostMapping("/getByItemType")
    List<FollowupTrackItemVO> getByItemType(@RequestParam(name = "followId") Long followId,
                                            @RequestParam(name = "itemType") Integer itemType);

    @PostMapping("/updateConsultTime")
    void updateConsultTime(@RequestBody UpdateFollowupConsultTimeReqVO reqVO);

    @PostMapping("/finishForm")
    void finishForm(@Valid @RequestBody FinishFormFollowupReqVO reqVO);

    @PostMapping("/updateFollowupItemPatientFormId")
    void updateFollowupItemPatientFormId(@RequestParam(name = "followupItemId") Long followupItemId,
                                         @RequestParam(name = "patientFormId") Long patientFormId);


    @PostMapping("/initFollowupItemPatientForm")
    void initFollowupItemPatientForm();

    @PostMapping("/saveSummary")
    void saveSummary(@RequestBody SaveFollowupSummaryReqVO reqVO);

    @PostMapping("/saveFollowupSummaryStatus")
    void saveFollowupSummaryStatus(@RequestBody SaveFollowupSummaryStatusReqVO reqVO);
}
