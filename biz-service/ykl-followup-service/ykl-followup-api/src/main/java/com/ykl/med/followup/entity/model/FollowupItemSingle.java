package com.ykl.med.followup.entity.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 随访计划项目表;
 * <AUTHOR> xkli
 * @date : 2023-11-24
 */
@Data
@Schema(description = "随访计划项目表")
@TableName("t_followup_item")
public class FollowupItemSingle {
    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long id ;

    @Schema(description = "随访ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long followupId ;

    @Schema(description = "患者ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long patientId ;

    @Schema(description = "项目ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private Long itemId ;

    @Schema(description = "项目加入编号",example = "")
    private String number ;

    @Schema(description = "项目类别（诊疗/问卷/康复训练）", example = "")
    private String itemClass ;

    @Schema(description = "项目类型（检查/检验/手术/随访问卷/正念冥想）", example = "")
    private String itemType ;

    @Schema(description = "项目名称", example = "")
    @TableId
    @TableLogic("WHERE:WL_MATCH:%:%")
    private String itemName ;

    @Schema(description = "项目内容（可存表单数据）", example = "")
    private Object itemContent ;

    @Schema(description = "是否可选：0必选，1可选", example = "")
    private Byte isOptional ;

    @Schema(description = "项目组号，标注属于同一组执行", example = "")
    private Integer groupNo ;

    @Schema(description = "执行标志，0未执行，1已执行", example = "")
    @TableId
    @TableLogic("FILLED:SGV_UPDATE:execTime=NN:1")
    private Byte execFlag ;

    @Schema(description = "执行时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date execTime ;

    @Schema(description = "备注（项目说明）", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    private String status ;

    @Schema(description = "待办通知标志：0未通知；1已通知", example = "")
    @TableId
    private Byte todoNotifyFlag ;

    @Schema(description = "删除标志；0未删，1已删", example = "")
    @TableId
    @TableLogic("FILLED:SGV_WHERE:0,FIELD_PROC:FP_GET_HIDE")
    private Byte deleteFlag ;
}
