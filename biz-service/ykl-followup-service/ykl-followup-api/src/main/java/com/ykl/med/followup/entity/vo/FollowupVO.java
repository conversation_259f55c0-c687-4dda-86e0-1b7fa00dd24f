package com.ykl.med.followup.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "随访计划数据对象")
public class FollowupVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "患者ID",example = "")
    private String patientId ;

    @Schema(description = "随访次数（第几次）",example = "")
    private String number ;

    @Schema(description = "随访名称",example = "")
    private String name ;

    @Schema(description = "随访类别，如：大随访、小随访等",example = "")
    private String category ;

    @Schema(description = "随访时间描述",example = "")
    private String timeExplain ;

    @Schema(description = "随访时间数值",example = "")
    private Short timeValue ;

    @Schema(description = "随访时间数值单位（天/周/月/年）",example = "")
    private String timeValueUnit ;

    @Schema(description = "初始时间点",example = "")
    private String initTime ;

    @Schema(description = "随访时间点（通过时间描述+时间值计算得到）",example = "")
    private String startTime ;

    @Schema(description = "随访结束时间（已填写报告完成为准）",example = "")
    private String endTime ;

    @Schema(description = "随访结束标志",example = "")
    private Byte execFlag ;

    @Schema(description = "诊断（该字段及以下为随访报告字段）",example = "")
    private String diagnosis ;

    @Schema(description = "患者分类",example = "")
    private String patientType ;

    @Schema(description = "随访分类",example = "")
    private String followupType ;

    @Schema(description = "随访方式",example = "")
    private String followupMode ;

    @Schema(description = "随访小结",example = "")
    private String followupSummary ;

    @Schema(description = "执行状态：0未开始；10待确认；20进行中；30已完成；40已失访；",example = "")
    private String executeStatus ;

    @Schema(description = "备注",example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】",example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】",example = "")
    private String lastUserId ;

    @Schema(description = "创建时间",example = "")
    private String createTime ;

    @Schema(description = "更新时间",example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE",example = "")
    private String status ;

    @Schema(description = "待办通知标志：0未通知；1已通知", example = "")
    private String todoNotifyFlag ;

    @Schema(description = "随访计划项目列表", example = "")
    private List<FollowupItemVO> followupItemList;

    /**
     * 患者信息字段
     * */
    @Schema(description = "名字", defaultValue = "患者名字")
    private String patientName;

    @Schema(description = "病种阶段", defaultValue = "病种阶段")
    private String stage;

    @Schema(description = "性别", defaultValue = "male")
    private String sex;

    @Schema(description = "年龄", defaultValue = "15")
    private Integer age;

    @Schema(description = "手机号", defaultValue = "18109074912")
    private String phone;

    @Schema(description = "临床分期")
    private String clinicalStaging;

    @Schema(description = "病理分型")
    private String pathologicalType;

    @Schema(description = "手术日期距离现在多少周或者多少月")
    private String surgeryDateDistance;

    @Schema(description = "上次随访时间点",example = "")
    private String upStartTime ;

    @Schema(description = "上次随访结束时间",example = "")
    private String upEndTime ;
}
