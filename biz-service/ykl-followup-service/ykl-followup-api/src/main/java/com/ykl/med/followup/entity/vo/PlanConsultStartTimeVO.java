package com.ykl.med.followup.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ykl.med.framework.common.util.DateTimeUtils;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/16
 */
@Data
public class PlanConsultStartTimeVO {

    /** 视频随访开始时间（计划） */
    @JsonFormat(pattern = DateTimeUtils.DATE_FORMAT_ALL)
    private LocalDateTime planConsultStartTime;
    /** 视频随访开始时间（计划） */
    @JsonFormat(pattern = DateTimeUtils.DATE_FORMAT_ALL)
    private LocalDateTime planConsultEndTime;

}
