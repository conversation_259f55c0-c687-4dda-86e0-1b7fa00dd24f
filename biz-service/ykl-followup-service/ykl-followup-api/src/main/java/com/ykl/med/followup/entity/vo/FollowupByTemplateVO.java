package com.ykl.med.followup.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "随访模版数据对象")
public class FollowupByTemplateVO {
    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "模版ID",example = "")
    private String followupTemplateId ;

    @Schema(description = "随访次数（第几次）",example = "")
    private Short number ;

    @Schema(description = "随访名称",example = "")
    private String name ;

    @Schema(description = "随访类别，如：大随访、小随访等",example = "")
    private String category ;

    @Schema(description = "随访时间描述",example = "")
    private String timeExplain ;

    @Schema(description = "随访时间数值",example = "")
    private Short timeValue ;

    @Schema(description = "随访时间数值单位（天/周/月/年）",example = "")
    private String timeValueUnit ;

    @Schema(description = "随访时间点（通过时间描述+时间值计算得到）",example = "")
    private String startTime ;

    @Schema(description = "随访结束时间（已填写报告完成为准）",example = "")
    private String endTime ;

    @Schema(description = "随访结束标志",example = "")
    private Byte execFlag ;

    @Schema(description = "诊断（该字段及以下为随访报告字段）",example = "")
    private String diagnosis ;

    @Schema(description = "患者分类",example = "")
    private String patientType ;

    @Schema(description = "随访分类",example = "")
    private String followupType ;

    @Schema(description = "随访方式",example = "")
    private String followupMode ;

    @Schema(description = "随访小结",example = "")
    private String followupSummary ;

    @Schema(description = "备注",example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】",example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】",example = "")
    private String lastUserId ;

    @Schema(description = "创建时间",example = "")
    private String createTime ;

    @Schema(description = "更新时间",example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE",example = "")
    private String status ;

    @Schema(description = "随访计划项目列表", example = "")
    private List<FollowupItemVO> followupItemList;
}
