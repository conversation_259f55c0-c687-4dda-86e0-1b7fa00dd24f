package com.ykl.med.followup.entity.vo;

import com.ykl.med.masterdata.vo.FormVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "随访计划项目数据对象")
public class FollowupItemVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "患者ID",example = "")
    private String patientId ;

    @Schema(description = "项目ID",example = "")
    private String itemId ;

    @Schema(description = "随访ID",example = "")
    private String followupId ;

    @Schema(description = "项目加入编号",example = "")
    private String number ;

    @Schema(description = "项目类别（诊疗/表单/康复训练）",example = "")
    private String itemClass ;

    @Schema(description = "项目类型（检查/检验/手术/随访问卷/正念冥想）",example = "")
    private String itemType ;

    @Schema(description = "项目名称",example = "")
    private String itemName ;

    @Schema(description = "项目内容（可存表单数据）",example = "")
    private FormVO itemContent ;

    @Schema(description = "是否可选：0必选，1可选",example = "")
    private String isOptional ;

    @Schema(description = "项目组号，标注属于同一组执行",example = "")
    private String groupNo ;

    @Schema(description = "执行标志，0未执行，1已执行",example = "")
    private String execFlag ;

    @Schema(description = "执行时间",example = "")
    private String execTime ;

    @Schema(description = "备注（项目说明）",example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】",example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】",example = "")
    private String lastUserId ;

    @Schema(description = "创建时间",example = "")
    private String createTime ;

    @Schema(description = "更新时间",example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE",example = "")
    private String status ;

    @Schema(description = "待办通知标志：0未通知；1已通知", example = "")
    private String todoNotifyFlag ;
}
