package com.ykl.med.followup.entity.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025/6/19
 */
@Data
public class SaveFollowupSummaryReqVO {

    @NotNull(message = "随访id不能为空")
    @Schema(description = "随访id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long followupId;

    @NotNull(message = "随访小结不能为空")
    @Schema(description = "随访小结", requiredMode = Schema.RequiredMode.REQUIRED)
    private String summary;

}

