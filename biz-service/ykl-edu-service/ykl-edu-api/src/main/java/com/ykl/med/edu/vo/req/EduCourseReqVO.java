package com.ykl.med.edu.vo.req;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "Edu 课程请求视图模型")
public class EduCourseReqVO extends CommonDoctorIdVO {
    @Schema(description = "ID", example = "1")
    private Long id;

    @Schema(description = "章节ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "章节ID不能为空")
    private Long chapterId;

    @Schema(description = "标题", example = "课程一", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "标题不能为空")
    private String title;

    @Schema(description = "排序", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "媒体URL", example = "http://example.com", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "媒体URL不能为空")
    private String mediaUrl;

    @Schema(description = "总时长", example = "111", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer totalTime;
}