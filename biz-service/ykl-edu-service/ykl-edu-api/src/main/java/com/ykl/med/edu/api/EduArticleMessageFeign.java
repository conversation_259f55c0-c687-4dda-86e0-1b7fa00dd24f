package com.ykl.med.edu.api;

import com.ykl.med.edu.enums.PatientArticleResource;
import com.ykl.med.edu.vo.req.EduArticleMessageQueryVO;
import com.ykl.med.edu.vo.req.EduArticleMessageReqVO;
import com.ykl.med.edu.vo.resp.EduArticleBrowseHistoryAppListVO;
import com.ykl.med.edu.vo.resp.EduArticleMineAppListVO;
import com.ykl.med.edu.vo.resp.HomePageTopEduCourseVO;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 教育文章消息服务接口
 */
@FeignClient(name = "ykl-edu-service", path = "eduArticleMessage")
public interface EduArticleMessageFeign {
    /**
     * 保存或更新文章消息
     *
     * @param reqVO 文章消息请求的视图对象
     */
    @PostMapping("/saveOrUpdate")
    void saveOrUpdate(@RequestBody EduArticleMessageReqVO reqVO);

    /**
     * 系统推荐文章
     */
    @PostMapping("/systemRecommend")
    void systemRecommend(@RequestBody List<Long> articleIds,
                         @RequestParam(value = "patientId") Long patientId,
                         @RequestParam(value = "resource") PatientArticleResource resource);

    /**
     * 发送文章消息
     */
    @PostMapping("/sendMessage")
    void sendMessage(@RequestParam(value = "id") Long id);

    /**
     * 根据患者ID获取文章列表
     *
     * @param queryVO 我的患教列表查询视图对象
     * @return 返回患教文章列表
     */
    @PostMapping("/pageByPatientId")
    PageResult<EduArticleMineAppListVO> pageByPatientId(@RequestBody EduArticleMessageQueryVO queryVO);

    /**
     * 首页top患教列表
     */
    @PostMapping("/homePageTopEduArticle")
    List<HomePageTopEduCourseVO> homePageTopEduArticle(@RequestParam(value = "patientId") Long patientId,
                                                       @RequestParam(value = "userId") Long userId);

    /**
     * 获取需要发送消息的文章ID列表
     */
    @PostMapping("/getNeedSendMessage")
    List<Long> getNeedSendMessage();

    /**
     * 更新文章的阅读状态
     */
    @PostMapping("/updateReadStatus")
    void updateReadStatus(@RequestParam(value = "userId") Long userId, @RequestBody List<Long> articleIds);

    /**
     * 查询文章的浏览记录
     */
    @PostMapping("/queryBrowseHistory")
    PageResult<EduArticleBrowseHistoryAppListVO> queryBrowseHistory(@RequestParam(value = "userId") Long userId, @RequestBody PageParam pageParam);
}