package com.ykl.med.edu.vo.resp;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "Edu 文章章节视图模型")
public class EduArticleChapterVO {
    @Stringify
    @Schema(description = "唯一标识", example = "1")
    private Long id;

    @Schema(description = "文章ID", example = "1")
    private Long articleId;

    @Schema(description = "章节名称", example = "章节一")
    private String chapterName;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "课程列表")
    private List<EduCourseVO> eduCourseVOList;
}