package com.ykl.med.edu.vo.req;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "Edu 类别查询请求视图模型")
public class EduCategoryQueryVO {
    @Schema(description = "名称", example = "默认名称")
    private String name;

    @Schema(description = "状态", example = "ENABLE")
    private CommonStatusEnum status;
}
