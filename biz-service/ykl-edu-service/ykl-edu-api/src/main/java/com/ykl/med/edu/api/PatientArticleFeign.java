package com.ykl.med.edu.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 患者患教接口
 */
@FeignClient(value = "ykl-edu-service", path = "/patientArticle")
public interface PatientArticleFeign {

    /**
     * 患者患教接口
     *
     * @param patientId 患者ID
     * @param articleId 文章ID
     */
    @PostMapping("/addPatientArticle")
    void addPatientArticle(@RequestParam(value = "patientId") Long patientId, @RequestParam(value = "articleId") Long articleId);
}