package com.ykl.med.edu.api;

import com.ykl.med.edu.vo.column.*;
import com.ykl.med.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "ykl-edu-service", path = "/eduArticleColumn")
public interface EduArticleColumnFeign {
    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存")
    void saveOrUpdate(@RequestBody @Valid EduArticleColumnAddVO addVO);

    @PostMapping("/query")
    @Operation(summary = "查询")
    PageResult<EduArticleColumnListVO> query(@RequestBody @Valid EduArticleColumnQueryVO queryVO);

    @PostMapping("/detail")
    @Operation(summary = "详情")
    EduArticleColumnDetailVO detail(@RequestParam(value = "id") Long id);

    @PostMapping("/findByName")
    @Operation(summary = "根据名称搜索")
    EduArticleColumnVO findByName(@RequestParam(value = "id") String name);

    @PostMapping("/getRelationByColumnId")
    @Operation(summary = "获取专栏下的所有患教")
    List<EduArticleColumnRelationVO> getRelationByColumnId(@RequestParam(value = "columnId") Long columnId);

    @PostMapping("/changeRelationSort")
    @Operation(summary = "修改专栏下的患教排序")
    void changeRelationSort(@RequestBody @Valid EduArticleColumnRelationSortVO sortVO);
}
