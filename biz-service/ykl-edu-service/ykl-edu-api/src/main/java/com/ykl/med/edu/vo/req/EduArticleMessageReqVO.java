package com.ykl.med.edu.vo.req;

import com.ykl.med.edu.enums.PatientArticleResource;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "文章消息请求的视图对象")
public class EduArticleMessageReqVO {

    @Schema(description = "文章ID", example = "1")
    @NotNull(message = "文章ID不能为空")
    private Long articleId;

    @Schema(description = "患者ID", example = "1")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "发送者ID", example = "1")
    private Long senderId;

    @Schema(description = "来源", example = "DOCTOR_SEND")
    private PatientArticleResource resource;
}