package com.ykl.med.edu.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "患者查看权限,DO_NOT_SEND_VISIBLE(不发送可见),VISIBLE_TO_PATIENTS_WHEN_SENT(发送给患者可见)")
public enum PatientViewType {
    DO_NOT_SEND_VISIBLE("不发送可见"),
    VISIBLE_TO_PATIENTS_WHEN_SENT("发送给患者可见"),
    ;
    private final String desc;
}
