package com.ykl.med.edu.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "app患教文章详情VO")
public class EduArticleAppDetailVO extends EduArticleAppListVO {
    @Schema(description = "内容", example = "行啊实打实大师大师大师")
    private String content;

    @Schema(description = "媒体文件地址", defaultValue = "1")
    private String mediaFileUrl;

    @Schema(description = "音频信息", defaultValue = "1")
    private ContentAudioVO audioInfo;
}
