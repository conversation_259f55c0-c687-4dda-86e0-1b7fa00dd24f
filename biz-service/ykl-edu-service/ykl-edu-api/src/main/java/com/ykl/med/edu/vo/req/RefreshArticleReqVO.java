package com.ykl.med.edu.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "app患教文章刷新VO")
public class RefreshArticleReqVO {
    @Schema(description = "文章id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long articleId;

    @Schema(description = "标签id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> tagIds;
}
