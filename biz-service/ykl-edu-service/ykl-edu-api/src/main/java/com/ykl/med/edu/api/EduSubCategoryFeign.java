package com.ykl.med.edu.api;

import com.ykl.med.edu.vo.category.sub.EduSubCategoryAddVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryListVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryQueryVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryVO;
import com.ykl.med.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "ykl-edu-service", path = "/eduSubCategory")
public interface EduSubCategoryFeign {

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存")
    void saveOrUpdate(@RequestBody @Valid EduSubCategoryAddVO reqVO);

    @PostMapping("/query")
    @Operation(summary = "查询")
    PageResult<EduSubCategoryListVO> query(@RequestBody @Valid EduSubCategoryQueryVO queryVO);

    @PostMapping("/getById")
    @Operation(summary = "查询详情")
    EduSubCategoryVO getById(@RequestParam(value = "id") Long id);

    @PostMapping("/getByCategoryId")
    @Operation(summary = "根据分类Id查询子分类")
    List<EduSubCategoryVO> getByCategoryId(@RequestParam(value = "categoryId") Long categoryId);

    @PostMapping("/getAll")
    @Operation(summary = "获取所有子分类")
    List<EduSubCategoryVO> getAll();
}
