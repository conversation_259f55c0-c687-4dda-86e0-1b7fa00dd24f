package com.ykl.med.edu.vo.resp;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "首页top视频")
public class HomePageTopEduCourseVO {
    @Stringify
    @Schema(description = "唯一标识", example = "1")
    private Long id;

    @Schema(description = "媒体URL", example = "http://example.com")
    private String mediaUrl;

    @Schema(description = "总时长", example = "111")
    private Integer totalTime;

    //todo 3月改版后没有学习进度了
    @Schema(description = "最后观看时间", example = "30")
    private Integer lastTime = 0;

    @Schema(description = "文章ID", example = "1")
    private Long articleId;
}
