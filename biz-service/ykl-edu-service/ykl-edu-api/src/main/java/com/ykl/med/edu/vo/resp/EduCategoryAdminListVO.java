package com.ykl.med.edu.vo.resp;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "Edu 类别视图模型")
public class EduCategoryAdminListVO {
    @Stringify
    @Schema(description = "唯一标识", example = "1")
    private Long id;

    @Schema(description = "名称", example = "Edu 类别")
    private String name;

    @Schema(description = "状态，默认为ENABLE", example = "ENABLE")
    private CommonStatusEnum status;

    @Stringify
    @Schema(description = "创建人ID", example = "1")
    private Long creatorId;

    @Schema(description = "创建人名称", example = "张三")
    private String creatorName;

    @Schema(description = "创建时间", example = "10082384523")
    @TimestampConvert
    private LocalDateTime createTime;

    @Schema(description = "子类别名称列表")
    private List<String> subCategoryNames;
}