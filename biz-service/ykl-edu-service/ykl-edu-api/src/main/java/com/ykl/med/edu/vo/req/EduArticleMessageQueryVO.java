package com.ykl.med.edu.vo.req;

import com.ykl.med.framework.common.interfaces.AutoBuildPatientId;
import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "我的患教列表查询VO")
public class EduArticleMessageQueryVO extends PageParam implements AutoBuildPatientId, AutoBuildUserId {
    @Schema(description = "当前患者id", example = "1", hidden = true)
    private Long currentPatientId;

    @Schema(description = "当前用户id", example = "1", hidden = true)
    private Long currentUserId;

    @Schema(description = "是否查询历史消息", example = "true")
    private Boolean history;
}
