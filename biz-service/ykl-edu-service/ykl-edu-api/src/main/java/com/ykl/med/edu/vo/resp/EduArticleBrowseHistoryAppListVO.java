package com.ykl.med.edu.vo.resp;

import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "app患教文章 浏览记录列表VO")
public class EduArticleBrowseHistoryAppListVO extends EduArticleAppListVO {
    @Schema(description = "浏览时间", example = "1243952523")
    @TimestampConvert
    private LocalDateTime browseTime;
}
