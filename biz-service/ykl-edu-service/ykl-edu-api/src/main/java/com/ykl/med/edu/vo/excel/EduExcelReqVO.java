package com.ykl.med.edu.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 患教导入请求参数
 */
@Data
@Accessors(chain = false)
public class EduExcelReqVO {
    @ExcelProperty("患教标题")
    private String name;

    @ExcelProperty("类别")
    private String category;

    @ExcelProperty("子类别")
    private String subCategory;

    @ExcelProperty("病种")
    private String disease;

    @ExcelProperty("排序")
    private Integer sort;

    @ExcelProperty("标签")
    private String tags;

    @ExcelProperty("专栏")
    private String column;

    @ExcelProperty("封面图片")
    private String cover;

    @ExcelProperty("作者")
    private String author;

    @ExcelProperty("媒体来源")
    private String media;

    @ExcelProperty("媒体来源时长")
    private String mediaTime;

    @ExcelProperty("失败原因")
    private String failReason;
}
