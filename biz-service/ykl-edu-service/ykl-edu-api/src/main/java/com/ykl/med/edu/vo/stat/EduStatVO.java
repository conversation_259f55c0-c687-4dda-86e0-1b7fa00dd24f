package com.ykl.med.edu.vo.stat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "患教统计")
public class EduStatVO {
    @Schema(description = "患者id")
    private Long patientId;
    @Schema(description = "喜爱的子分类")
    private List<String> loveSubCategories;
    @Schema(description = "喜爱的子分类文章数量")
    private Integer subLoveCount;
    @Schema(description = "总浏览量")
    private Integer totalBrowseCount;
    @Schema(description = "总浏览图文量")
    private Integer totalBrowseGraphicCount;
    @Schema(description = "总浏览视频量")
    private Integer totalBrowseVideoCount;
}
