package com.ykl.med.edu.vo.req;

import com.ykl.med.edu.enums.ArticleType;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.interfaces.AutoBuildMedicalTeamId;
import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
@Schema(description = "管理后台文章查询VO")
public class EduArticleAdminQueryVO extends PageParam implements AutoBuildUserId, AutoBuildMedicalTeamId {
    @Schema(description = "当前用户id", example = "1", hidden = true)
    private Long currentUserId;
    @Schema(description = "当前用户医疗组id", example = "1", hidden = true)
    private Long currentMedicalTeamId;
    @Schema(description = "当前用户医疗组ids", example = "1", hidden = true)
    private Set<Long> currentMedicalTeamIds;

    @Schema(description = "标题", example = "这是一个标题")
    private String title;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "子分类ID", example = "1")
    private Long subCategoryId;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @Schema(description = "文章类型")
    private List<ArticleType> types;

    @Schema(description = "标签的id", example = "[1,2,3]")
    private List<Long> tagIds;

    @Schema(description = "文章id", example = "[1,2,3]", hidden = true)
    private List<Long> eduArticleIds;
}