package com.ykl.med.edu.vo.resp;

import com.ykl.med.edu.enums.ArticleType;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

@Data
@Schema(description = "管理后台文章列表VO")
public class EduArticleAdminListVO {

    @Schema(description = "标签的ID列表")
    private Set<String> tagIds;

    @Schema(description = "标签的名称列表")
    private Set<String> tagNames;

    @Stringify
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "文章的标题", example = "一个示例文章")
    private String title;

    @Schema(description = "文章的类型", example = "GRAPHIC")
    private ArticleType type;

    @Schema(description = "文章的封面图片", example = "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png")
    private String cover;

    @Stringify
    @Schema(description = "分类的ID", example = "1")
    private Long categoryId;
    @Schema(description = "分类的名称", example = "默认分类")
    private String categoryName;

    @Schema(description = "专栏子分类id")
    @Stringify
    private Long subCategoryId;
    @Schema(description = "专栏子分类名称")
    private String subCategoryName;

    @Schema(description = "创建者ID", example = "1", hidden = true)
    private Long creatorId;
    @Schema(description = "创建者名称", example = "张三")
    private String creatorName;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "浏览量", example = "1")
    private Integer viewCount;

    @Schema(description = "创建时间", example = "1243952523")
    @TimestampConvert
    private LocalDateTime createTime;

}
