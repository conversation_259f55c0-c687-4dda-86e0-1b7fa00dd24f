package com.ykl.med.edu.vo.resp;

import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "患者收藏的患教文章列表VO")
public class EduArticleCollectionAppListVO extends EduArticleAppListVO {
    @TimestampConvert
    @Schema(description = "收藏时间", example = "12384385834")
    private LocalDateTime collectionTime;
}
