package com.ykl.med.edu.api;

import com.ykl.med.edu.vo.req.EduArticleChapterReqVO;
import com.ykl.med.edu.vo.resp.EduArticleChapterVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "ykl-edu-service",path = "/eduArticleChapter")
public interface EduArticleChapterFeign {

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存或更新章节")
    void saveOrUpdate(@RequestBody @Valid EduArticleChapterReqVO reqVO);

    @PostMapping("/deleteChapter")
    @Operation(summary = "删除章节")
    void deleteChapter(@RequestParam(value = "id") Long id);

    @PostMapping("/queryEduArticleChapter")
    @Operation(summary = "查询文章章节")
    List<EduArticleChapterVO> queryEduArticleChapter(@RequestParam(value = "articleId") Long articleId);
}