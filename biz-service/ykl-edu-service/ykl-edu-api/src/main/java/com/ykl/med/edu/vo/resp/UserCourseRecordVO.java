package com.ykl.med.edu.vo.resp;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户课程记录视图模型")
public class UserCourseRecordVO {
    @Stringify
    @Schema(description = "唯一标识", example = "1")
    private Long id;

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "文章ID", example = "1")
    private Long articleId;

    @Schema(description = "课程ID", example = "1")
    private Long courseId;

    @Schema(description = "观看毫秒", example = "30")
    private Integer viewTime = 0;

    @Schema(description = "最后观看时间", example = "30")
    private Integer lastTime = 0;
}