package com.ykl.med.edu.api;

import com.ykl.med.edu.vo.req.EduCategoryQueryVO;
import com.ykl.med.edu.vo.req.EduCategoryReqVO;
import com.ykl.med.edu.vo.resp.EduCategoryAdminListVO;
import com.ykl.med.edu.vo.resp.EduCategoryVO;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 这是Edu分类的Feign客户端接口.
 * 与ykl-edu-service微服务的/eduCategory路径对应.
 */
@FeignClient(value = "ykl-edu-service", path = "/eduCategory")
public interface EduCategoryFeign {

    /**
     * 保存或者更新Edu类别。
     *
     * @param reqVO EduCategoryReqVO对象,包含ID，名称和状态
     */
    @PostMapping("/saveOrUpdate")
    void saveOrUpdate(@RequestBody @Valid EduCategoryReqVO reqVO);

    /**
     * 通过ID更新Edu类别的状态。
     *
     * @param id     Long型的Edu类别ID
     * @param status CommonStatusEnum枚举,表示新的状态
     */
    @PostMapping("/updateStatusById")
    void updateStatusById(@RequestParam(value = "id") Long id, @RequestParam(value = "status") CommonStatusEnum status);

    /**
     * 查询符合条件的Edu类别,返回列表类型的查询结果。
     *
     * @param queryVO EduCategoryQueryVO对象，包含名称和状态
     * @return 返回EduCategoryAdminListVO列表
     */
    @PostMapping("/queryEduCategory")
    List<EduCategoryAdminListVO> queryEduCategory(@RequestBody @Valid EduCategoryQueryVO queryVO);

    /**
     * 根据一组ID,查询Edu类别，返回结果为一个Map,键是ID,值是名称。
     *
     * @param ids List<Long>类型,一组Edu类别的ID
     * @return 返回一个Map
     */
    @PostMapping("/queryEduCategoryMap")
    Map<Long, String> queryEduCategoryMap(@RequestBody List<Long> ids);

    /**
     * 获取所有的Edu类别。
     *
     * @return 返回EduCategoryVO列表
     */
    @PostMapping("/getAllCategory")
    List<EduCategoryVO> getAllCategory();
}