package com.ykl.med.edu.api;

import com.ykl.med.edu.vo.stat.EduStatVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ykl-edu-service", path = "/stat")
public interface EduStatFeign {
    @PostMapping("/getStatByPatientId")
    @Operation(summary = "通过患者Id获取患教统计")
    EduStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId);
}
