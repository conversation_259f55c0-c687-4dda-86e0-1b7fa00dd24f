package com.ykl.med.edu.vo.req;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@Schema(description = "批量更新状态请求")
public class EduBatchUpdateStatusReqVO {
    @Schema(description = "ID列表")
    @NotNull(message = "ID列表不能为空")
    @Size(min = 1, message = "ID列表不能为空")
    private List<Long> ids;

    @Schema(description = "通用状态", example = "ENABLE", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "通用状态不能为空")
    private CommonStatusEnum status;
}