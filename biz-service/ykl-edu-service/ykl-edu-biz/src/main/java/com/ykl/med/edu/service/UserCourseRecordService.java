package com.ykl.med.edu.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.edu.vo.req.UserCourseRecordReqVO;
import com.ykl.med.edu.vo.resp.UserCourseRecordVO;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserCourseRecordService extends ServiceImpl<UserCourseRecordMapper, UserCourseRecordDO> {

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(UserCourseRecordReqVO reqVO) {
        log.info("UserCourseRecordServiceImpl.saveOrUpdate reqVO:{}", JSON.toJSONString(reqVO));
        //根据userId和courseId查询记录
        UserCourseRecordDO record = super.lambdaQuery()
                .eq(UserCourseRecordDO::getUserId, reqVO.getCurrentUserId())
                .eq(UserCourseRecordDO::getCourseId, reqVO.getCourseId())
                .one();
        if (record == null) {
            record = new UserCourseRecordDO();
            record.setUserId(reqVO.getCurrentUserId());
            record.setCourseId(reqVO.getCourseId());
            record.setViewTime(reqVO.getViewTime());
        }
        record.setViewTime(Math.max(reqVO.getViewTime(), record.getViewTime()));
        record.setLastTime(reqVO.getLastTime());
        this.saveOrUpdate(record);
    }

    public Map<Long, UserCourseRecordVO> getCourseViewMinutes(Long userId, List<Long> courseIds) {
        List<UserCourseRecordDO> recordDOS = super.lambdaQuery()
                .eq(UserCourseRecordDO::getUserId, userId)
                .in(UserCourseRecordDO::getCourseId, courseIds)
                .list();
        if (recordDOS.isEmpty()) {
            return new HashMap<>();
        }
        List<UserCourseRecordVO> recordVOS = CopyPropertiesUtil.normalCopyProperties(recordDOS, UserCourseRecordVO.class);
        return recordVOS.stream().collect(Collectors.toMap(UserCourseRecordVO::getCourseId, e -> e));
    }

    public Map<Long, UserCourseRecordVO> getCourseViewMinutesByArticleId(Long userId, List<Long> articleIds){
        List<UserCourseRecordDO> recordDOS = super.lambdaQuery()
                .eq(UserCourseRecordDO::getUserId, userId)
                .in(UserCourseRecordDO::getArticleId, articleIds)
                .list();
        if (recordDOS.isEmpty()) {
            return new HashMap<>();
        }
        List<UserCourseRecordVO> recordVOS = CopyPropertiesUtil.normalCopyProperties(recordDOS, UserCourseRecordVO.class);
        return recordVOS.stream().collect(Collectors.toMap(UserCourseRecordVO::getArticleId, e -> e));
    }

    public UserCourseRecordVO getByArticleId(Long userId, Long articleId) {
        UserCourseRecordDO recordDOS = super.lambdaQuery()
                .eq(UserCourseRecordDO::getUserId, userId)
                .eq(UserCourseRecordDO::getArticleId, articleId)
                .one();
        return CopyPropertiesUtil.normalCopyProperties(recordDOS, UserCourseRecordVO.class);
    }
}