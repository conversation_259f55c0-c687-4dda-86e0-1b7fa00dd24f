package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.EduArticleColumnFeign;
import com.ykl.med.edu.service.column.EduArticleColumnRelationService;
import com.ykl.med.edu.service.column.EduArticleColumnService;
import com.ykl.med.edu.vo.column.*;
import com.ykl.med.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/eduArticleColumn")
@AllArgsConstructor
@Validated
public class EduArticleColumnController implements EduArticleColumnFeign {

    private final EduArticleColumnService eduArticleColumnService;

    private final EduArticleColumnRelationService eduArticleColumnRelationService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存")
    @Override
    public void saveOrUpdate(@RequestBody @Valid EduArticleColumnAddVO addVO) {
        eduArticleColumnService.saveOrUpdate(addVO);
    }

    @PostMapping("/query")
    @Operation(summary = "查询")
    @Override
    public PageResult<EduArticleColumnListVO> query(@RequestBody @Valid EduArticleColumnQueryVO queryVO) {
        return eduArticleColumnService.query(queryVO);
    }

    @PostMapping("/detail")
    @Operation(summary = "详情")
    @Override
    public EduArticleColumnDetailVO detail(@RequestParam(value = "id") Long id) {
        return eduArticleColumnService.detail(id);
    }

    @PostMapping("/findByName")
    @Operation(summary = "根据名称搜索")
    @Override
    public EduArticleColumnVO findByName(@RequestParam(value = "id") String name) {
        return eduArticleColumnService.findByName(name);
    }

    @PostMapping("/getRelationByColumnId")
    @Operation(summary = "获取专栏下的所有患教")
    @Override
    public List<EduArticleColumnRelationVO> getRelationByColumnId(@RequestParam(value = "columnId") Long columnId) {
        return eduArticleColumnRelationService.getRelationByColumnId(columnId);
    }

    @PostMapping("/changeRelationSort")
    @Operation(summary = "修改专栏下的患教排序")
    @Override
    public void changeRelationSort(@RequestBody @Valid EduArticleColumnRelationSortVO sortVO) {
        eduArticleColumnRelationService.changeRelationSort(sortVO);
    }
}
