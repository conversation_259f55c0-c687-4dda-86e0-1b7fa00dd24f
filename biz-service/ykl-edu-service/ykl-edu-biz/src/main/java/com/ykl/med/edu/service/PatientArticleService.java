package com.ykl.med.edu.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.edu.db.entity.PatientArticleDO;
import com.ykl.med.edu.db.mapper.PatientArticleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class PatientArticleService extends ServiceImpl<PatientArticleMapper, PatientArticleDO> {


    @Transactional(rollbackFor = Exception.class)
    public void addPatientArticle(Long patientId, Long articleId) {
        log.info("PatientArticleServiceImpl.addPatientArticle patientId:{},articleId:{}", patientId, articleId);
        LambdaQueryWrapper<PatientArticleDO> lambdaQueryWrapper = new LambdaQueryWrapper<PatientArticleDO>().eq(PatientArticleDO::getPatientId, patientId).eq(PatientArticleDO::getArticleId, articleId);
        PatientArticleDO patientArticleDO = super.getOne(lambdaQueryWrapper);
        if (patientArticleDO != null) {
            return;
        }
        patientArticleDO = new PatientArticleDO();
        patientArticleDO.setPatientId(patientId);
        patientArticleDO.setArticleId(articleId);
        super.save(patientArticleDO);
    }

    public Boolean checkPatientArticle(Long patientId, Long articleId) {
        //log.info("PatientArticleServiceImpl.checkPatientArticle patientId:{},articleId:{}", patientId, articleId);
        LambdaQueryWrapper<PatientArticleDO> lambdaQueryWrapper = new LambdaQueryWrapper<PatientArticleDO>().eq(PatientArticleDO::getPatientId, patientId).eq(PatientArticleDO::getArticleId, articleId);
        PatientArticleDO patientArticleDO = super.getOne(lambdaQueryWrapper);
        return patientArticleDO != null;
    }
}
