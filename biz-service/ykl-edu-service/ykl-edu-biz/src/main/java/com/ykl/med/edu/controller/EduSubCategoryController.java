package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.EduSubCategoryFeign;
import com.ykl.med.edu.service.category.EduSubCategoryService;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryAddVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryListVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryQueryVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryVO;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/eduSubCategory")
@AllArgsConstructor
@Validated
public class EduSubCategoryController implements EduSubCategoryFeign {
    private final EduSubCategoryService eduSubCategoryService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存")
    @Override
    public void saveOrUpdate(@RequestBody @Valid EduSubCategoryAddVO reqVO) {
        eduSubCategoryService.saveOrUpdate(reqVO);
    }

    @PostMapping("/query")
    @Operation(summary = "查询")
    @Override
    public PageResult<EduSubCategoryListVO> query(@RequestBody @Valid EduSubCategoryQueryVO queryVO) {
        return eduSubCategoryService.query(queryVO);
    }

    @PostMapping("/getById")
    @Operation(summary = "查询详情")
    @Override
    public EduSubCategoryVO getById(@RequestParam(value = "id") Long id) {
        return eduSubCategoryService.getSubCategoryById(id);
    }

    @PostMapping("/getByCategoryId")
    @Operation(summary = "根据分类Id查询子分类,(查启用的)")
    @Override
    public List<EduSubCategoryVO> getByCategoryId(@RequestParam(value = "categoryId") Long categoryId) {
        return eduSubCategoryService.getByCategoryId(categoryId, CommonStatusEnum.ENABLE);
    }

    @PostMapping("/getAll")
    @Operation(summary = "获取所有子分类")
    @Override
    public List<EduSubCategoryVO> getAll() {
        return eduSubCategoryService.getAll();
    }
}
