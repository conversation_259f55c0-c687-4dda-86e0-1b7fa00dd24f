package com.ykl.med.edu.service.article;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.edu.db.es.EduArticleEO;
import com.ykl.med.edu.db.es.EduArticleEsRepository;
import com.ykl.med.edu.enums.PatientViewType;
import com.ykl.med.edu.service.category.EduCategoryService;
import com.ykl.med.edu.service.category.EduSubCategoryService;
import com.ykl.med.edu.service.column.EduArticleColumnRelationService;
import com.ykl.med.edu.service.column.EduArticleColumnService;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryVO;
import com.ykl.med.edu.vo.column.EduArticleColumnRelationVO;
import com.ykl.med.edu.vo.column.EduArticleColumnVO;
import com.ykl.med.edu.vo.req.EduArticleAppQueryVO;
import com.ykl.med.edu.vo.req.RefreshArticleReqVO;
import com.ykl.med.edu.vo.resp.EduArticleAppListVO;
import com.ykl.med.edu.vo.resp.EduArticleVO;
import com.ykl.med.edu.vo.resp.EduCategoryVO;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EduArticleEsService {
    @Resource
    private EduArticleEsRepository eduArticleEsRepository;
    @Resource
    private EduArticleService eduArticleService;
    @Resource
    private EduArticleColumnService eduArticleColumnService;
    @Resource
    private EduArticleColumnRelationService eduArticleColumnRelationService;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Resource
    private EduCategoryService eduCategoryService;
    @Resource
    private EduSubCategoryService eduSubCategoryService;
    @Resource
    private EduArticleRedisService eduArticleRedisService;


    public void refreshArticle(RefreshArticleReqVO reqVO) {
        log.info("刷新文章:{}", JSONObject.toJSONString(reqVO));
        EduArticleVO eduArticleVO = eduArticleService.getArticle(reqVO.getArticleId());
        EduArticleEO eduArticleEO = eduArticleEsRepository.findById(reqVO.getArticleId()).orElse(null);
        if (eduArticleEO == null) {
            eduArticleEO = new EduArticleEO();
            eduArticleEO.setId(eduArticleVO.getId());
        }
        EduArticleColumnRelationVO eduArticleColumnRelationVO = eduArticleColumnRelationService.getByEduArticleId(eduArticleVO.getId());
        BeanUtils.copyProperties(eduArticleVO, eduArticleEO);
        eduArticleEO.setTagIds(reqVO.getTagIds());
        if (eduArticleColumnRelationVO != null) {
            eduArticleEO.setColumnId(eduArticleColumnRelationVO.getColumnId());
        }
        eduArticleEsRepository.save(eduArticleEO);
    }


    public List<EduCategoryVO> queryEduCategory(EduArticleAppQueryVO queryVO) {
        BoolQueryBuilder boolQuery = this.buildQuery(queryVO);
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQuery)
                .withAggregations(AggregationBuilders.terms("category_ids").field("categoryId"))
                .build();
        SearchHits<EduArticleEO> searchHits = elasticsearchRestTemplate.search(query, EduArticleEO.class);
        AggregationsContainer<?> aggregationsContainer = searchHits.getAggregations();
        if (aggregationsContainer == null) {
            return Collections.emptyList();
        }
        Aggregations aggregations = (Aggregations) aggregationsContainer.aggregations();
        Terms terms = aggregations.get("category_ids"); // 直接获取 Terms 聚合
        if (terms == null) {
            return Collections.emptyList();
        }
        List<Long> categoryIds = terms.getBuckets()
                .stream()
                .map(Terms.Bucket::getKeyAsNumber)
                .map(Number::longValue)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }
        List<EduCategoryVO> eduCategoryVOS = eduCategoryService.getCategoryByIds(categoryIds);
        //按sort排序从小到大排序
        eduCategoryVOS.sort(Comparator.comparing(EduCategoryVO::getSort));
        return eduCategoryVOS;
    }

    public List<EduSubCategoryVO> queryEduSubCategory(EduArticleAppQueryVO queryVO) {
        BoolQueryBuilder boolQuery = this.buildQuery(queryVO);
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQuery)
                .withAggregations(AggregationBuilders.terms("sub_category_ids").field("subCategoryId"))
                .build();
        SearchHits<EduArticleEO> searchHits = elasticsearchRestTemplate.search(query, EduArticleEO.class);
        AggregationsContainer<?> aggregationsContainer = searchHits.getAggregations();
        if (aggregationsContainer == null) {
            return Collections.emptyList();
        }
        Aggregations aggregations = (Aggregations) aggregationsContainer.aggregations();
        Terms terms = aggregations.get("sub_category_ids"); // 直接获取 Terms 聚合
        if (terms == null) {
            return Collections.emptyList();
        }
        List<Long> categoryIds = terms.getBuckets()
                .stream()
                .map(Terms.Bucket::getKeyAsNumber)
                .map(Number::longValue)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }
        List<EduSubCategoryVO> eduSubCategoryVOS = eduSubCategoryService.getSubCategoryByIds(categoryIds);
        //按sort排序从小到大排序
        eduSubCategoryVOS.sort(Comparator.comparing(EduSubCategoryVO::getSort));
        return eduSubCategoryVOS;
    }

    public List<EduArticleColumnVO> queryEduColumn(EduArticleAppQueryVO queryVO) {
        BoolQueryBuilder boolQuery = this.buildQuery(queryVO);
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQuery)
                .withAggregations(AggregationBuilders.terms("column_ids").field("columnId"))
                .build();
        SearchHits<EduArticleEO> searchHits = elasticsearchRestTemplate.search(query, EduArticleEO.class);
        AggregationsContainer<?> aggregationsContainer = searchHits.getAggregations();
        if (aggregationsContainer == null) {
            return Collections.emptyList();
        }
        Aggregations aggregations = (Aggregations) aggregationsContainer.aggregations();
        Terms terms = aggregations.get("column_ids");
        if (terms == null) {
            return Collections.emptyList();
        }
        List<Long> columnIds = terms.getBuckets()
                .stream()
                .map(Terms.Bucket::getKeyAsNumber)
                .map(Number::longValue)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(columnIds)) {
            return Collections.emptyList();
        }
        return eduArticleColumnService.findByIds(columnIds);
    }

    public PageResult<EduArticleAppListVO> query(EduArticleAppQueryVO queryVO) {
        BoolQueryBuilder boolQuery = this.buildQuery(queryVO);
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQuery)
                .withSorts(SortBuilders.scoreSort().order(SortOrder.ASC), SortBuilders.fieldSort("sort").order(SortOrder.ASC))
                .withPageable(PageRequest.of(queryVO.getPageNo() - 1, queryVO.getPageSize()))
                .build();
        SearchHits<EduArticleEO> searchHits = elasticsearchRestTemplate.search(query, EduArticleEO.class);
        List<EduArticleEO> eduArticleEOS = searchHits.get().map(SearchHit::getContent).collect(Collectors.toList());
        List<EduArticleAppListVO> eduArticleAppListVOS = new ArrayList<>();
        for (EduArticleEO eo : eduArticleEOS) {
            EduArticleVO eduArticleVO = eduArticleRedisService.getById(eo.getId());
            if (eduArticleVO == null) {
                continue;
            }
            eduArticleAppListVOS.add(CopyPropertiesUtil.normalCopyProperties(eduArticleVO, EduArticleAppListVO.class));
        }
        return new PageResult<>(eduArticleAppListVOS, searchHits.getTotalHits());
    }

    private BoolQueryBuilder buildQuery(EduArticleAppQueryVO queryVO) {
        // 构建基础查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (queryVO.getCategoryId() != null) {
            boolQuery.must(QueryBuilders.termQuery("categoryId", queryVO.getCategoryId()));
        }
        if (queryVO.getSubCategoryId() != null) {
            boolQuery.must(QueryBuilders.termQuery("subCategoryId", queryVO.getSubCategoryId()));
        }
        if (queryVO.getTagId() != null) {
            boolQuery.must(QueryBuilders.termQuery("tagIds", queryVO.getTagId()));
        }
        if (queryVO.getDiseaseId() != null) {
            boolQuery.must(QueryBuilders.termQuery("diseaseIds", queryVO.getDiseaseId()));
        }
        if (queryVO.getColumnId() != null) {
            boolQuery.must(QueryBuilders.termQuery("columnId", queryVO.getColumnId()));
        }
        if (StringUtils.hasText(queryVO.getKeyword())) {
            boolQuery.must(QueryBuilders.matchPhraseQuery("title", queryVO.getKeyword()));
        }
        boolQuery.must(QueryBuilders.termQuery("status", CommonStatusEnum.ENABLE.name()));
        boolQuery.must(QueryBuilders.termQuery("patientViewType", PatientViewType.DO_NOT_SEND_VISIBLE.name()));
        // 构建权限过滤条件
        BoolQueryBuilder permissionQuery = QueryBuilders.boolQuery()
                .should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("patientMedicalTeamIds")));
        if (queryVO.getCurrentPatientMedicalTeamId() != null) {
            permissionQuery.should(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("patientMedicalTeamIds", queryVO.getCurrentPatientMedicalTeamId())));
        }
        boolQuery.must(permissionQuery);
        return boolQuery;
    }

}
