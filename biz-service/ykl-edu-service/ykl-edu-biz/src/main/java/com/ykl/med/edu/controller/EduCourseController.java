package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.EduCourseFeign;
import com.ykl.med.edu.vo.req.EduCourseReqVO;
import com.ykl.med.edu.vo.resp.EduCourseVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/eduCourse")
public class EduCourseController implements EduCourseFeign {
    @Resource
    private EduCourseService eduCourseService;

    @Override
    @PostMapping("/saveOrUpdate")
    public void saveOrUpdate(@RequestBody @Valid EduCourseReqVO reqVO) {
        eduCourseService.saveOrUpdate(reqVO);
    }

    @Override
    @PostMapping("/deleteCourse")
    public void deleteCourse(@RequestParam(value = "id") Long id) {
        EduCourseVO eduCourseVO = eduCourseService.getEduCourse(id);
        eduCourseService.deleteCourse(id);
    }


    @Override
    @PostMapping("/viewCountAdd")
    public void viewCountAdd(@RequestParam(value = "id") Long id) {
        eduCourseService.viewCountAdd(id);
    }

}