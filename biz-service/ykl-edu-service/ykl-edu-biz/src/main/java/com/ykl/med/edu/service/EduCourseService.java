package com.ykl.med.edu.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.edu.constants.EduErrorCodeConstants;
import com.ykl.med.edu.vo.req.EduCourseReqVO;
import com.ykl.med.edu.vo.resp.EduCourseVO;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EduCourseService extends ServiceImpl<EduCourseMapper, EduCourseDO> {

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(EduCourseReqVO reqVO) {
        log.info("EduCourseServiceImpl.saveOrUpdate reqVO:{}", JSON.toJSONString(reqVO));
        EduCourseDO eduCourseDO;
        if (reqVO.getId() != null) {
            eduCourseDO = super.getById(reqVO.getId());
            AssertUtils.notNull(eduCourseDO, GlobalErrorCodeConstants.BAD_REQUEST);
            if (!eduCourseDO.getMediaUrl().equals(reqVO.getMediaUrl())) {
                eduCourseDO.setMediaUrlThumb(null);
            }
        } else {
            eduCourseDO = new EduCourseDO();
        }
        BeanUtils.copyProperties(reqVO, eduCourseDO);
        try {
            super.saveOrUpdate(eduCourseDO);
        } catch (DuplicateKeyException e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("uq_chapter_title")) {
                throw new ServiceException(EduErrorCodeConstants.EDU_COURSE_DUPLICATE_KEY);
            }
            throw e;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void deleteCourse(Long id) {
        log.info("EduCourseServiceImpl.deleteCourse id:{}", id);
        EduCourseDO eduCourseDO = super.getById(id);
//        AssertUtils.notNull(eduCourseDO, GlobalErrorCodeConstants.BAD_REQUEST);
//        eduCourseDO.setDeleted(true);
        super.removeById(eduCourseDO);
    }


    @Transactional(rollbackFor = Exception.class)
    public void deleteCourseByChapterId(Long chapterId) {
        log.info("EduCourseServiceImpl.deleteCourse chapterId:{}", chapterId);
        List<EduCourseDO> eduCourseDOList = super.list(new LambdaQueryWrapper<EduCourseDO>().eq(EduCourseDO::getChapterId, chapterId));
//        eduCourseDOList.forEach(eduCourseDO -> {
//            eduCourseDO.setDeleted(true);
//        });
        if (CollectionUtils.isEmpty(eduCourseDOList)) {
            return;
        }
        super.removeBatchByIds(eduCourseDOList);
    }


    @Transactional(rollbackFor = Exception.class)
    public void viewCountAdd(Long id) {
        EduCourseDO eduCourseDO = super.getById(id);
        AssertUtils.notNull(eduCourseDO, GlobalErrorCodeConstants.BAD_REQUEST);
        eduCourseDO.setViewCount(eduCourseDO.getViewCount() + 1);
        super.updateById(eduCourseDO);
    }


    public List<EduCourseVO> queryEduCourse(Long chapterId) {
        LambdaQueryWrapper<EduCourseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduCourseDO::getChapterId, chapterId);
        queryWrapper.eq(EduCourseDO::getDeleted, false);
        queryWrapper.orderByAsc(EduCourseDO::getSort);
        List<EduCourseDO> eduCourseDOList = super.list(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(eduCourseDOList, EduCourseVO.class);
    }


    public Map<Long, List<EduCourseVO>> queryEduCourseByChapterId(List<Long> chapterIds) {
        LambdaQueryWrapper<EduCourseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EduCourseDO::getChapterId, chapterIds);
        queryWrapper.eq(EduCourseDO::getDeleted, false);
        queryWrapper.orderByAsc(EduCourseDO::getSort);
        List<EduCourseDO> eduCourseDOList = super.list(queryWrapper);
        if (eduCourseDOList == null || eduCourseDOList.isEmpty()) {
            return new HashMap<>();
        }
        return eduCourseDOList.stream().collect(Collectors.groupingBy(EduCourseDO::getChapterId, Collectors.mapping(eduCourseDO -> {
            EduCourseVO eduCourseVO = new EduCourseVO();
            BeanUtils.copyProperties(eduCourseDO, eduCourseVO);
            return eduCourseVO;
        }, Collectors.toList())));
    }


    public EduCourseVO getEduCourse(Long id) {
        EduCourseDO eduCourseDO = super.getById(id);
        return CopyPropertiesUtil.normalCopyProperties(eduCourseDO, EduCourseVO.class);
    }
}