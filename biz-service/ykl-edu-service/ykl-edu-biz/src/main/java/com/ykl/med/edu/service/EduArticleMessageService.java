package com.ykl.med.edu.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.edu.db.entity.EduArticleDO;
import com.ykl.med.edu.db.entity.EduArticleMessageDO;
import com.ykl.med.edu.db.mapper.EduArticleMessageMapper;
import com.ykl.med.edu.enums.ArticleType;
import com.ykl.med.edu.enums.PatientArticleResource;
import com.ykl.med.edu.service.article.EduArticleService;
import com.ykl.med.edu.vo.req.EduArticleMessageQueryVO;
import com.ykl.med.edu.vo.req.EduArticleMessageReqVO;
import com.ykl.med.edu.vo.resp.EduArticleMineAppListVO;
import com.ykl.med.edu.vo.resp.EduArticleVO;
import com.ykl.med.edu.vo.resp.HomePageTopEduCourseVO;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.patient.api.PatientDoctorFeign;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.vo.message.BizMessageBaseVO;
import com.ykl.med.push.vo.message.MessageSendPatientReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EduArticleMessageService extends ServiceImpl<EduArticleMessageMapper, EduArticleMessageDO> {
    @Resource
    private EduArticleService eduArticleService;
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private IdServiceImpl idService;
    @Resource
    private PatientDoctorFeign patientDoctorFeign;


    @Transactional(rollbackFor = Exception.class)
    public void systemRecommend(List<Long> articleIds, Long patientId, PatientArticleResource resource) {
        log.info("systemRecommend articleIds:{},patientId:{},resource:{}", JSON.toJSONString(articleIds), patientId, resource);
        LambdaQueryWrapper<EduArticleMessageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleMessageDO::getPatientId, patientId);
        queryWrapper.eq(EduArticleMessageDO::getDeleted, false);
        queryWrapper.notIn(EduArticleMessageDO::getArticleId, articleIds);
        queryWrapper.in(EduArticleMessageDO::getResource, Lists.newArrayList(PatientArticleResource.SYSTEM_RULE_CHANGE, PatientArticleResource.PATIENT_CHANGE));
        List<EduArticleMessageDO> list = this.list(queryWrapper);
        Map<Long, EduArticleMessageDO> map = new HashMap<>();
        //把之前的作废
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setDeleted(true);
                map.put(item.getArticleId(), item);
            });
        }
        List<EduArticleMessageDO> saveList = new ArrayList<>();
        LambdaQueryWrapper<EduArticleMessageDO> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(EduArticleMessageDO::getPatientId, patientId);
        queryWrapper1.in(EduArticleMessageDO::getArticleId, articleIds);
        list = this.list(queryWrapper1);
        Map<Long, EduArticleMessageDO> oldMap = list.stream().collect(Collectors.toMap(EduArticleMessageDO::getArticleId, item -> item));

        for (Long articleId : articleIds) {
            EduArticleMessageDO eduArticleMessageDO = oldMap.get(articleId);
            if (eduArticleMessageDO != null) {
                if (eduArticleMessageDO.getResource() != PatientArticleResource.DOCTOR_SEND) {
                    eduArticleMessageDO.setResource(resource);
                }
                eduArticleMessageDO.setPushTime(LocalDateTime.now());
                eduArticleMessageDO.setDeleted(false);
            } else {
                eduArticleMessageDO = new EduArticleMessageDO();
                eduArticleMessageDO.setArticleId(articleId);
                eduArticleMessageDO.setPatientId(patientId);
                eduArticleMessageDO.setResource(resource);
                eduArticleMessageDO.setPushTime(LocalDateTime.now());
                eduArticleMessageDO.setId(idService.nextId());
                eduArticleMessageDO.setSendMessage(false);
                eduArticleMessageDO.setDeleted(false);
            }
            saveList.add(eduArticleMessageDO);
        }
        saveList.addAll(map.values());
        this.saveOrUpdateBatch(saveList);
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(EduArticleMessageReqVO reqVO) {
        log.info("saveOrUpdate reqVO:{}", JSON.toJSONString(reqVO));
        //根据文章ID和患者ID查询是否存在
        LambdaQueryWrapper<EduArticleMessageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleMessageDO::getArticleId, reqVO.getArticleId());
        queryWrapper.eq(EduArticleMessageDO::getPatientId, reqVO.getPatientId());
        EduArticleMessageDO eduArticleMessageDO = this.getOne(queryWrapper);
        if (eduArticleMessageDO != null) {
            if (eduArticleMessageDO.getResource() != PatientArticleResource.DOCTOR_SEND) {
                eduArticleMessageDO.setResource(reqVO.getResource());
            }
            eduArticleMessageDO.setDeleted(false);
            eduArticleMessageDO.setPushTime(LocalDateTime.now());
        } else {
            eduArticleMessageDO = CopyPropertiesUtil.normalCopyProperties(reqVO, EduArticleMessageDO.class);
            eduArticleMessageDO.setId(idService.nextId());
            //医生发送的，消息先被发送了，后入这边的库
            eduArticleMessageDO.setSendMessage(eduArticleMessageDO.getResource() == PatientArticleResource.DOCTOR_SEND);
            eduArticleMessageDO.setPushTime(LocalDateTime.now());
        }
        this.saveOrUpdate(eduArticleMessageDO);
        if (!eduArticleMessageDO.getSendMessage() && eduArticleMessageDO.getResource() == PatientArticleResource.PATIENT_CHANGE) {
            //患者行为触发的，直接发送消息
            this.sendMessage(eduArticleMessageDO.getId());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(Long id) {
        EduArticleMessageDO eduArticleMessageDO = this.getById(id);
        if (eduArticleMessageDO.getSendMessage()) {
            return;
        }
        log.info("sendMessage eduArticleMessageDO:{}", JSON.toJSONString(eduArticleMessageDO));

        Long doctorId = patientDoctorFeign.getDoctorIdByPatientId(eduArticleMessageDO.getPatientId());
        if (doctorId == null) {
            log.error("sendMessage 没有找到医生信息，消息：{}", JSON.toJSONString(eduArticleMessageDO));
            return;
        }
        EduArticleVO eduArticleVO = eduArticleService.getArticle(eduArticleMessageDO.getArticleId());
        MessageSendPatientReqVO messageSendReqVO = new MessageSendPatientReqVO();
        messageSendReqVO.setCurrentUserId(doctorId);
        messageSendReqVO.setPatientId(eduArticleMessageDO.getPatientId());
        messageSendReqVO.setRequestId(eduArticleMessageDO.getId().toString());
        messageSendReqVO.setType(MessageType.PATIENT_EDUCATION);
        BizMessageBaseVO bizMessageBaseVO = new BizMessageBaseVO();
        bizMessageBaseVO.setBizId(eduArticleMessageDO.getArticleId().toString());
        bizMessageBaseVO.setBizName(eduArticleVO.getTitle());
        bizMessageBaseVO.setBizSummary(eduArticleVO.getSummary());
        bizMessageBaseVO.setBizType(eduArticleVO.getType().name());
        JSONObject media = new JSONObject();
        media.put("path", eduArticleVO.getCover());
        bizMessageBaseVO.setMediaIds(Lists.newArrayList(media.toJSONString()));
        bizMessageBaseVO.setBizAuthor(eduArticleVO.getAuthor());
        messageSendReqVO.setExtra(bizMessageBaseVO);
        messageFeign.sendMessagePatient(messageSendReqVO);
        eduArticleMessageDO.setSendMessage(true);
        this.updateById(eduArticleMessageDO);
    }


    public PageResult<EduArticleMineAppListVO> pageByPatientId(EduArticleMessageQueryVO queryVO) {
        MPJLambdaWrapper<EduArticleMessageDO> wrapper = JoinWrappers.lambda(EduArticleMessageDO.class)
                .selectAll(EduArticleMessageDO.class)
                .leftJoin(EduArticleDO.class, EduArticleDO::getId, EduArticleMessageDO::getArticleId);
        wrapper.eq(EduArticleMessageDO::getPatientId, queryVO.getCurrentPatientId());
        if (!queryVO.getHistory()) {
            wrapper.eq(EduArticleMessageDO::getDeleted, false);
        }
        wrapper.orderByAsc(EduArticleDO::getSort);
        wrapper.orderByDesc(EduArticleDO::getUpdateTime);
        IPage<EduArticleMessageDO> page = this.page(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), wrapper);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }
        Map<Long, EduArticleMessageDO> eduArticleMessageDOMap = page.getRecords().stream().collect(Collectors.toMap(EduArticleMessageDO::getArticleId, eduArticleMessageDO -> eduArticleMessageDO));
        List<Long> eduArticleIds = page.getRecords().stream().map(EduArticleMessageDO::getArticleId).collect(Collectors.toList());
        List<EduArticleVO> eduArticleVOS = eduArticleService.getArticleList(eduArticleIds);
        //排序还是要按sort+UpdateTime来
        eduArticleVOS.sort((o1, o2) -> {
            if (o1.getSort().equals(o2.getSort())) {
                return o2.getUpdateTime().compareTo(o1.getUpdateTime());
            }
            return o1.getSort().compareTo(o2.getSort());
        });
        List<EduArticleMineAppListVO> eduArticleMineListVOList = new ArrayList<>();
        for (EduArticleVO eduArticleVO : eduArticleVOS) {
            EduArticleMessageDO eduArticleMessageDO = eduArticleMessageDOMap.get(eduArticleVO.getId());
            EduArticleMineAppListVO eduArticleMineListVO = CopyPropertiesUtil.normalCopyProperties(eduArticleVO, EduArticleMineAppListVO.class);
            eduArticleMineListVO.setPushTime(eduArticleMessageDO.getPushTime());
            eduArticleMineListVOList.add(eduArticleMineListVO);
        }
        return new PageResult<>(eduArticleMineListVOList, page.getTotal());
    }


    public List<HomePageTopEduCourseVO> homePageTopEduArticle(Long patientId, Long userId) {
        MPJLambdaWrapper<EduArticleMessageDO> wrapper = JoinWrappers.lambda(EduArticleMessageDO.class)
                .selectAll(EduArticleMessageDO.class)
                .leftJoin(EduArticleDO.class, EduArticleDO::getId, EduArticleMessageDO::getArticleId)
                .eq(EduArticleDO::getType, ArticleType.VIDEO)
                .eq(EduArticleDO::getStatus, CommonStatusEnum.ENABLE)
                .isNotNull(EduArticleDO::getMediaFileUrl)
                .eq(EduArticleMessageDO::getPatientId, patientId)
                .eq(EduArticleMessageDO::getDeleted, false);
        wrapper.orderByAsc(EduArticleDO::getSort);
        wrapper.orderByDesc(EduArticleDO::getUpdateTime);
        Page<EduArticleMessageDO> page = this.page(new Page<>(1, 5), wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return Collections.emptyList();
        }
        List<Long> eduArticleIds = page.getRecords().stream().map(EduArticleMessageDO::getArticleId).collect(Collectors.toList());
        List<EduArticleVO> eduArticleVOS = eduArticleService.getArticleList(eduArticleIds);
        List<HomePageTopEduCourseVO> result = new ArrayList<>();
        for (EduArticleVO eduArticleVO : eduArticleVOS) {
            if (StringUtils.isEmpty(eduArticleVO.getMediaFileUrl())) {
                continue;
            }
            HomePageTopEduCourseVO homePageTopEduCourseVO = new HomePageTopEduCourseVO();
            homePageTopEduCourseVO.setArticleId(eduArticleVO.getId());
            homePageTopEduCourseVO.setMediaUrl(eduArticleVO.getMediaFileUrl());
            homePageTopEduCourseVO.setTotalTime(eduArticleVO.getTotalTime());
            result.add(homePageTopEduCourseVO);
        }
        //排序还是要按eduArticleIds来
        result.sort((o1, o2) -> {
            int index1 = eduArticleIds.indexOf(o1.getArticleId());
            int index2 = eduArticleIds.indexOf(o2.getArticleId());
            return index1 - index2;
        });
        return result;
    }


    public List<Long> getNeedSendMessage() {
        LambdaQueryWrapper<EduArticleMessageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleMessageDO::getSendMessage, false);
        return this.list(queryWrapper).stream().map(EduArticleMessageDO::getId).collect(Collectors.toList());
    }


}