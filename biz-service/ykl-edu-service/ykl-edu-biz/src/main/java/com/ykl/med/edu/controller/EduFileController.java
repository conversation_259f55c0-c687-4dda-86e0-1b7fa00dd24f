package com.ykl.med.edu.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.edu.api.EduFileFeign;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/eduFile")
public class EduFileController implements EduFileFeign {
    @Resource
    private EduFileService eduFileService;
    @Resource
    private EduCourseMapper eduCourseMapper;

    @PostMapping("/moveVideo")
    @Override
    public void moveVideo() {
        eduFileService.moveVideo();
    }

    @PostMapping("/moveCover")
    @Override
    public void moveCover() {
        eduFileService.moveCover();
    }

    @PostMapping("/coverThumb")
    @Override
    public void coverThumb() {
        eduFileService.coverThumb();
    }

    @PostMapping("/videoThumb")
    @Override
    public void videoThumb() {
        LambdaQueryWrapper<EduCourseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(EduCourseDO::getMediaUrlThumb);
        List<EduCourseDO> all = eduCourseMapper.selectList(queryWrapper);
        for (EduCourseDO eduCourseDO : all) {
            eduFileService.videoThumb(eduCourseDO);
        }
    }

}
