package com.ykl.med.edu.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.edu.db.entity.UserBrowseHistoryDO;
import com.ykl.med.edu.db.mapper.UserBrowseHistoryMapper;
import com.ykl.med.edu.service.article.EduArticleService;
import com.ykl.med.edu.vo.resp.EduArticleBrowseHistoryAppListVO;
import com.ykl.med.edu.vo.resp.EduArticleVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.PatientUserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserBrowseHistoryService extends ServiceImpl<UserBrowseHistoryMapper, UserBrowseHistoryDO> {
    @Resource
    private UserBrowseHistoryMapper userBrowseHistoryMapper;
    @Resource
    private EduArticleService eduArticleService;
    @Resource
    private PatientUserFeign patientUserFeign;

    @Transactional(rollbackFor = Exception.class)
    public void add(Long userId, List<Long> articleIds) {
        log.info("updateReadStatus userId:{},articleIds:{}", userId, JSON.toJSONString(articleIds));
        LambdaQueryWrapper<UserBrowseHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserBrowseHistoryDO::getUserId, userId);
        queryWrapper.in(UserBrowseHistoryDO::getArticleId, articleIds);
        List<UserBrowseHistoryDO> userBrowseHistoryDOS = this.list(queryWrapper);
        Map<Long, UserBrowseHistoryDO> userBrowseHistoryDOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(userBrowseHistoryDOS)) {
            //已经浏览过的，就不再插入，更新时间
            for (UserBrowseHistoryDO eduArticleMessageDO : userBrowseHistoryDOS) {
                eduArticleMessageDO.setBrowseTime(LocalDateTime.now());
                userBrowseHistoryDOMap.put(eduArticleMessageDO.getArticleId(), eduArticleMessageDO);
            }
        }
        List<UserBrowseHistoryDO> userBrowseHistoryDOList = new ArrayList<>();
        for (Long articleId : articleIds) {
            UserBrowseHistoryDO userBrowseHistoryDO = userBrowseHistoryDOMap.get(articleId);
            if (userBrowseHistoryDO != null) {
                userBrowseHistoryDOList.add(userBrowseHistoryDO);
            } else {
                UserBrowseHistoryDO userBrowseHistoryDO1 = new UserBrowseHistoryDO();
                userBrowseHistoryDO1.setUserId(userId);
                userBrowseHistoryDO1.setArticleId(articleId);
                userBrowseHistoryDO1.setBrowseTime(LocalDateTime.now());
                userBrowseHistoryDOList.add(userBrowseHistoryDO1);
            }
        }
        this.saveOrUpdateBatch(userBrowseHistoryDOList);
    }

    public PageResult<EduArticleBrowseHistoryAppListVO> queryBrowseHistory(Long userId, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<UserBrowseHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserBrowseHistoryDO::getUserId, userId);
        queryWrapper.orderByDesc(UserBrowseHistoryDO::getBrowseTime);
        Page<UserBrowseHistoryDO> page = userBrowseHistoryMapper.selectPage(new Page<>(pageNo, pageSize), queryWrapper);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }
        Map<Long, UserBrowseHistoryDO> userBrowseHistoryDOMap = page.getRecords().stream().collect(Collectors.toMap(UserBrowseHistoryDO::getArticleId, eduArticleMessageDO -> eduArticleMessageDO));
        List<Long> eduArticleIds = page.getRecords().stream().map(UserBrowseHistoryDO::getArticleId).collect(Collectors.toList());
        List<EduArticleVO> eduArticleVOS = eduArticleService.getArticleList(eduArticleIds);
        List<EduArticleBrowseHistoryAppListVO> eduArticleBrowseHistoryListVOList = new ArrayList<>();
        for (EduArticleVO eduArticleVO : eduArticleVOS) {
            UserBrowseHistoryDO userBrowseHistoryDO = userBrowseHistoryDOMap.get(eduArticleVO.getId());
            EduArticleBrowseHistoryAppListVO eduArticleBrowseHistoryListVO = CopyPropertiesUtil.normalCopyProperties(eduArticleVO, EduArticleBrowseHistoryAppListVO.class);
            eduArticleBrowseHistoryListVO.setBrowseTime(userBrowseHistoryDO.getBrowseTime());
            eduArticleBrowseHistoryListVOList.add(eduArticleBrowseHistoryListVO);
        }
        eduArticleBrowseHistoryListVOList.sort(Comparator.comparing(EduArticleBrowseHistoryAppListVO::getBrowseTime).reversed());
        return new PageResult<>(eduArticleBrowseHistoryListVOList, page.getTotal());
    }

    /**
     * 获取患者整个家庭的浏览记录
     *
     * @param patientId 患者id
     * @return 浏览记录
     */
    public List<UserBrowseHistoryDO> getByPatientId(Long patientId) {
        List<PatientUserVO> patientUserVOS = patientUserFeign.getFamilyByPatientId(patientId);
        List<Long> userIds = patientUserVOS.stream().map(PatientUserVO::getUserId).collect(Collectors.toList());
        LambdaQueryWrapper<UserBrowseHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserBrowseHistoryDO::getUserId, userIds);
        return userBrowseHistoryMapper.selectList(queryWrapper);
    }
}
