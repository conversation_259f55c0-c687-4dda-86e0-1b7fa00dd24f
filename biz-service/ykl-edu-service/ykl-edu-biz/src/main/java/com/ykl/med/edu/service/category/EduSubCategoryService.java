package com.ykl.med.edu.service.category;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.edu.db.entity.EduSubCategoryDO;
import com.ykl.med.edu.db.mapper.EduSubCategoryMapper;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryAddVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryListVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryQueryVO;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryVO;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EduSubCategoryService extends ServiceImpl<EduSubCategoryMapper, EduSubCategoryDO> {
    @Resource
    private IdServiceImpl idService;
    @Resource
    private UserFeign userFeign;

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(EduSubCategoryAddVO addVO) {
        log.info("保存或更新子分类，参数：{}", JSON.toJSONString(addVO));
        EduSubCategoryDO dataDO;
        if (addVO.getId() != null) {
            dataDO = this.getById(addVO.getId());
        } else {
            dataDO = new EduSubCategoryDO();
            dataDO.setId(idService.nextId());
            dataDO.setCreatorId(addVO.getCurrentUserId());
        }

        dataDO.setCategoryId(addVO.getCategoryId());
        dataDO.setName(addVO.getName());
        dataDO.setStatus(addVO.getStatus());
        this.saveOrUpdate(dataDO);
    }

    public PageResult<EduSubCategoryListVO> query(EduSubCategoryQueryVO queryVO) {
        LambdaQueryWrapper<EduSubCategoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(queryVO.getName()), EduSubCategoryDO::getName, queryVO.getName());
        queryWrapper.eq(queryVO.getStatus() != null, EduSubCategoryDO::getStatus, queryVO.getStatus());
        queryWrapper.orderByDesc(EduSubCategoryDO::getCreateTime);
        Page<EduSubCategoryDO> page = new Page<>(queryVO.getPageNo(), queryVO.getPageSize());
        Page<EduSubCategoryDO> pageResult = this.page(page, queryWrapper);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return PageResult.empty();
        }
        List<Long> creatorIds = pageResult.getRecords().stream().map(EduSubCategoryDO::getCreatorId).collect(Collectors.toList());
        List<UserSimpleVO> users = userFeign.listByUserIds(new IdListReqVO().setIdList(creatorIds));
        Map<Long, String> userMap = users.stream().collect(Collectors.toMap(UserSimpleVO::getId, UserSimpleVO::getName));
        List<EduSubCategoryListVO> result = new ArrayList<>();
        for (EduSubCategoryDO pageResultRecord : pageResult.getRecords()) {
            EduSubCategoryListVO listVO = CopyPropertiesUtil.normalCopyProperties(pageResultRecord, EduSubCategoryListVO.class);
            listVO.setCreatorName(userMap.get(pageResultRecord.getCreatorId()));
            result.add(listVO);
        }
        return new PageResult<>(result, pageResult.getTotal());
    }


    public EduSubCategoryVO getSubCategoryById(Long id) {
        return CopyPropertiesUtil.normalCopyProperties(this.getById(id), EduSubCategoryVO.class);
    }

    public List<EduSubCategoryVO> getSubCategoryByIds(List<Long> ids) {
        List<EduSubCategoryDO> dataDOList = this.lambdaQuery().in(EduSubCategoryDO::getId, ids).list();
        return CopyPropertiesUtil.normalCopyProperties(dataDOList, EduSubCategoryVO.class);
    }

    public List<EduSubCategoryVO> getByCategoryId(Long categoryId, CommonStatusEnum status) {
        List<EduSubCategoryDO> dataDOList = this.lambdaQuery().eq(EduSubCategoryDO::getCategoryId, categoryId)
                .eq(status != null, EduSubCategoryDO::getStatus, status).list();
        return CopyPropertiesUtil.normalCopyProperties(dataDOList, EduSubCategoryVO.class);
    }

    public List<EduSubCategoryVO> getAll() {
        List<EduSubCategoryDO> dataDOList = this.lambdaQuery().eq(EduSubCategoryDO::getStatus, CommonStatusEnum.ENABLE).list();
        return CopyPropertiesUtil.normalCopyProperties(dataDOList, EduSubCategoryVO.class);
    }

    public Map<Long, List<EduSubCategoryVO>> getByCategoryIds(List<Long> categoryIds) {
        List<EduSubCategoryDO> dataDOList = this.lambdaQuery().in(EduSubCategoryDO::getCategoryId, categoryIds).list();
        return CopyPropertiesUtil.normalCopyProperties(dataDOList, EduSubCategoryVO.class).stream().collect(Collectors.groupingBy(EduSubCategoryVO::getCategoryId));
    }

    public Map<Long, String> queryEduCategoryMap(List<Long> subCategoryIds) {
        if (CollectionUtils.isEmpty(subCategoryIds)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<EduSubCategoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EduSubCategoryDO::getId, subCategoryIds);
        List<EduSubCategoryDO> eduCategoryDOList = super.list(queryWrapper);
        return eduCategoryDOList.stream().collect(Collectors.toMap(EduSubCategoryDO::getId, EduSubCategoryDO::getName));
    }
}
