package com.ykl.med.edu.service.article;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.edu.constants.EduErrorCodeConstants;
import com.ykl.med.edu.db.entity.EduArticleDO;
import com.ykl.med.edu.db.mapper.EduArticleMapper;
import com.ykl.med.edu.enums.ArticleType;
import com.ykl.med.edu.service.PatientArticleService;
import com.ykl.med.edu.service.category.EduCategoryService;
import com.ykl.med.edu.service.category.EduSubCategoryService;
import com.ykl.med.edu.service.column.EduArticleColumnRelationService;
import com.ykl.med.edu.vo.column.EduArticleColumnRelationVO;
import com.ykl.med.edu.vo.req.EduArticleAdminQueryVO;
import com.ykl.med.edu.vo.req.EduArticleRefreshMqVO;
import com.ykl.med.edu.vo.req.EduArticleReqVO;
import com.ykl.med.edu.vo.req.UpdateStatusReqVO;
import com.ykl.med.edu.vo.resp.*;
import com.ykl.med.framework.common.constants.topic.EduArticleContentAudioTopic;
import com.ykl.med.framework.common.constants.topic.EduArticleRefreshTopic;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.api.DiseaseFeign;
import com.ykl.med.masterdata.vo.resp.DiseaseVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EduArticleService extends ServiceImpl<EduArticleMapper, EduArticleDO> {
    @Resource
    private EduCategoryService eduCategoryService;
    @Resource
    private EduArticleMapper eduArticleMapper;
    @Resource
    private PatientArticleService patientArticleService;
    @Resource
    private UserFeign userFeign;
    @Resource
    private EduSubCategoryService eduSubCategoryService;
    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private DiseaseFeign diseaseFeign;
    @Resource
    private EduArticleColumnRelationService eduArticleColumnRelationService;
    @Resource
    private EduArticleRedisService eduArticleRedisService;


    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(EduArticleReqVO reqVO) {
        log.info("EduArticleServiceImpl.saveOrUpdate reqVO:{}", JSON.toJSONString(reqVO));
        EduArticleDO eduArticleDO;
        if (reqVO.getId() != null) {
            eduArticleDO = super.getById(reqVO.getId());
            AssertUtils.notNull(eduArticleDO, EduErrorCodeConstants.THE_ARTICLE_NOT_EXISTS);
            //鉴权
            AssertUtils.isTrue(EduArticleDO.checkDoctorArticleAuth(eduArticleDO, reqVO.getCurrentUserId(), reqVO.getCurrentMedicalTeamIds()), EduErrorCodeConstants.INSUFFICIENT_PERMISSIONS);
        } else {
            eduArticleDO = new EduArticleDO();
            eduArticleDO.setCreatorId(reqVO.getCurrentUserId());
        }
        eduArticleDO.buildFromReqVO(reqVO);
        try {
            super.saveOrUpdate(eduArticleDO);
        } catch (DuplicateKeyException e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("uq_title")) {
                throw new ServiceException(EduErrorCodeConstants.EDU_ARTICLE_DUPLICATE_KEY);
            }
            throw e;
        }
        eduArticleColumnRelationService.addRelation(reqVO.getColumnId(), eduArticleDO.getId());
        if (reqVO.getType() == ArticleType.GRAPHIC) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("articleId", eduArticleDO.getId());
            jsonObject.put("content", reqVO.getContent());
            rocketMQTemplate.syncSend(EduArticleContentAudioTopic.RequestContentAudio.DESTINATION, jsonObject.toString());
        }
        EduArticleRefreshMqVO eduArticleRefreshMqVO = new EduArticleRefreshMqVO();
        eduArticleRefreshMqVO.setArticleId(eduArticleDO.getId());
        rocketMQTemplate.syncSend(EduArticleRefreshTopic.TOPIC, eduArticleRefreshMqVO);
        return eduArticleDO.getId();
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByIds(UpdateStatusReqVO reqVO) {
        log.info("EduArticleServiceImpl.updateStatusByIds :{}", JSON.toJSONString(reqVO));
        List<EduArticleDO> eduArticleDOS = super.listByIds(reqVO.getIds());
        AssertUtils.notEmpty(eduArticleDOS, EduErrorCodeConstants.THE_ARTICLE_NOT_EXISTS);
        eduArticleDOS.forEach(eduArticleDO -> {
            AssertUtils.isTrue(EduArticleDO.checkDoctorArticleAuth(eduArticleDO, reqVO.getCurrentUserId(), reqVO.getCurrentMedicalTeamIds()), EduErrorCodeConstants.INSUFFICIENT_PERMISSIONS);
            eduArticleDO.setStatus(reqVO.getStatus());
        });
        super.updateBatchById(eduArticleDOS);
        for (EduArticleDO eduArticleDO : eduArticleDOS) {
            EduArticleRefreshMqVO eduArticleRefreshMqVO = new EduArticleRefreshMqVO();
            eduArticleRefreshMqVO.setArticleId(eduArticleDO.getId());
            rocketMQTemplate.syncSend(EduArticleRefreshTopic.TOPIC, eduArticleRefreshMqVO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAudioInfo(ContentAudioVO audioVO) {
        EduArticleDO eduArticleDO = this.getById(audioVO.getArticleId());
        AssertUtils.notNull(eduArticleDO, EduErrorCodeConstants.THE_ARTICLE_NOT_EXISTS);
        eduArticleDO.setAudioInfo(audioVO);
        super.updateById(eduArticleDO);
    }


    public PageResult<EduArticleAdminListVO> queryEduArticle(EduArticleAdminQueryVO queryVO) {
        Page<EduArticleDO> eduArticleDOPage = eduArticleMapper.adminQuery(queryVO);
        if (eduArticleDOPage.getRecords().isEmpty()) {
            return PageResult.empty();
        }
        List<Long> userIds = new ArrayList<>();
        List<Long> categoryIds = new ArrayList<>();
        List<Long> subCategoryIds = new ArrayList<>();
        for (EduArticleDO record : eduArticleDOPage.getRecords()) {
            if (record.getCreatorId() != null) {
                userIds.add(record.getCreatorId());
            }
            if (record.getCategoryId() != null) {
                categoryIds.add(record.getCategoryId());
            }
            if (record.getSubCategoryId() != null) {
                subCategoryIds.add(record.getSubCategoryId());
            }
        }
        List<UserSimpleVO> users = userFeign.listByUserIds(new IdListReqVO().setIdList(userIds));
        Map<Long, String> userMap = users.stream().collect(Collectors.toMap(UserSimpleVO::getId, UserSimpleVO::getName));
        Map<Long, String> eduCategoryMap = eduCategoryService.queryEduCategoryMap(categoryIds);
        Map<Long, String> eduSubCategoryMap = eduSubCategoryService.queryEduCategoryMap(subCategoryIds);
        List<EduArticleAdminListVO> result = new ArrayList<>();
        for (EduArticleDO eduArticleDO : eduArticleDOPage.getRecords()) {
            EduArticleAdminListVO listVO = CopyPropertiesUtil.normalCopyProperties(eduArticleDO, EduArticleAdminListVO.class);
            listVO.setCreatorName(userMap.get(eduArticleDO.getCreatorId()));
            listVO.setCategoryName(eduCategoryMap.get(eduArticleDO.getCategoryId()));
            listVO.setSubCategoryName(eduSubCategoryMap.get(eduArticleDO.getSubCategoryId()));
            result.add(listVO);
        }
        return new PageResult<>(result, eduArticleDOPage.getTotal());
    }


    public List<EduArticleAdminSelectVO> getAllMediaArticle() {
        LambdaQueryWrapper<EduArticleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EduArticleDO::getType, ArticleType.VIDEO, ArticleType.AUDIO);
        return CopyPropertiesUtil.normalCopyProperties(super.list(queryWrapper), EduArticleAdminSelectVO.class);
    }


    public EduArticleAdminDetailVO getAdminDetailById(Long id) {
        EduArticleDO eduArticleDO = super.getById(id);
        AssertUtils.notNull(eduArticleDO, EduErrorCodeConstants.THE_ARTICLE_NOT_EXISTS);
        EduArticleAdminDetailVO vo = CopyPropertiesUtil.normalCopyProperties(eduArticleDO, EduArticleAdminDetailVO.class);
        if (CollectionUtils.isNotEmpty(eduArticleDO.getDiseaseIds())) {
            List<DiseaseVO> diseaseList = diseaseFeign.queryByIds(eduArticleDO.getDiseaseIds());
            Map<Long, String> diseaseMap = diseaseList.stream().collect(Collectors.toMap(DiseaseVO::getId, DiseaseVO::getSystemName));
            vo.setDiseaseMap(diseaseMap);
        }
        EduArticleColumnRelationVO relationVO = eduArticleColumnRelationService.getByEduArticleId(eduArticleDO.getId());
        if (relationVO != null) {
            vo.setColumnId(relationVO.getColumnId());
        }
        return vo;
    }


    public EduArticleAppDetailUserVO getAppDetail(Long id, Long userId, Long patientId) {
        EduArticleVO eduArticleVO = eduArticleRedisService.getById(id);
        AssertUtils.notNull(eduArticleVO, EduErrorCodeConstants.THE_ARTICLE_NOT_EXISTS);
        Boolean has = patientId != null && patientArticleService.checkPatientArticle(patientId, id);
        EduArticleAppDetailUserVO vo = CopyPropertiesUtil.normalCopyProperties(eduArticleVO, EduArticleAppDetailUserVO.class);
        vo.setBuy(has);
        return vo;
    }


    public List<EduArticleSimpleVO> getArticleSimpleByIds(List<Long> ids) {
        List<EduArticleDO> eduArticleDOS = super.listByIds(ids);
        return CopyPropertiesUtil.normalCopyProperties(eduArticleDOS, EduArticleSimpleVO.class);
    }


    public EduArticleVO getArticle(Long id) {
        return CopyPropertiesUtil.normalCopyProperties(super.getById(id), EduArticleVO.class);
    }

    public List<EduArticleVO> getArticleList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return CopyPropertiesUtil.normalCopyProperties(super.listByIds(ids), EduArticleVO.class);
    }

    public EduArticleVO findByTitle(String title) {
        LambdaQueryWrapper<EduArticleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleDO::getTitle, title);
        return CopyPropertiesUtil.normalCopyProperties(super.getOne(queryWrapper), EduArticleVO.class);
    }

    public void refreshArticleAll() {
        List<Long> eduIds = eduArticleMapper.getAllIds();
        for (Long eduId : eduIds) {
            EduArticleRefreshMqVO eduArticleRefreshMqVO = new EduArticleRefreshMqVO();
            eduArticleRefreshMqVO.setArticleId(eduId);
            rocketMQTemplate.syncSend(EduArticleRefreshTopic.TOPIC, eduArticleRefreshMqVO);
        }
    }

}