package com.ykl.med.edu.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.LongListTypeHandler;
import lombok.Data;

import java.util.List;

@TableName(value = "t_edu_article_column", autoResultMap = true)
@Data
public class EduArticleColumnDO extends BaseDO {
    private String name;
    private Long categoryId;
    private Long subCategoryId;
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> diseaseIds;
    private Integer sort;
    private Long creatorId;
}
