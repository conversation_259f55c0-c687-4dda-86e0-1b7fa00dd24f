package com.ykl.med.edu.db.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ykl.med.edu.db.entity.EduArticleColumnRelationDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper
public interface EduArticleColumnRelationMapper extends MPJBaseMapper<EduArticleColumnRelationDO> {
    default EduArticleColumnRelationDO getByArticleId(Long articleId) {
        LambdaQueryWrapper<EduArticleColumnRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleColumnRelationDO::getArticleId, articleId);
        return selectOne(queryWrapper);
    }

    default List<EduArticleColumnRelationDO> getByColumnId(Long columnId) {
        LambdaQueryWrapper<EduArticleColumnRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleColumnRelationDO::getColumnId, columnId);
        queryWrapper.orderByAsc(EduArticleColumnRelationDO::getSort);
        return selectList(queryWrapper);
    }

    default Map<Long, List<EduArticleColumnRelationDO>> getByColumnIds(List<Long> columnIds) {
        if (CollectionUtils.isEmpty(columnIds)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<EduArticleColumnRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EduArticleColumnRelationDO::getColumnId, columnIds);
        return selectList(queryWrapper).stream().collect(Collectors.groupingBy(EduArticleColumnRelationDO::getColumnId));
    }

    default EduArticleColumnRelationDO getByColumnIdAndArticleId(Long columnId, Long articleId) {
        LambdaQueryWrapper<EduArticleColumnRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleColumnRelationDO::getColumnId, columnId);
        queryWrapper.eq(EduArticleColumnRelationDO::getArticleId, articleId);
        return selectOne(queryWrapper);
    }

    default Integer getMaxSortByColumnId(Long columnId) {
        LambdaQueryWrapper<EduArticleColumnRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleColumnRelationDO::getColumnId, columnId);
        queryWrapper.orderByDesc(EduArticleColumnRelationDO::getSort);
        queryWrapper.last("limit 1");
        EduArticleColumnRelationDO eduArticleColumnRelationDO = selectOne(queryWrapper);
        if (eduArticleColumnRelationDO == null){
            return 0;
        }else {
            return eduArticleColumnRelationDO.getSort();
        }
    }
}
