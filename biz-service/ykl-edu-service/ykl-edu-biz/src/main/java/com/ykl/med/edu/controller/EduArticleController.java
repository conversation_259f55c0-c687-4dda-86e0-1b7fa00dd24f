package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.EduArticleFeign;
import com.ykl.med.edu.service.article.EduArticleEsService;
import com.ykl.med.edu.service.article.EduArticleRedisService;
import com.ykl.med.edu.service.article.EduArticleService;
import com.ykl.med.edu.vo.category.sub.EduSubCategoryVO;
import com.ykl.med.edu.vo.column.EduArticleColumnVO;
import com.ykl.med.edu.vo.req.*;
import com.ykl.med.edu.vo.resp.*;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/eduArticle")
public class EduArticleController implements EduArticleFeign {
    @Resource
    private EduArticleService eduArticleService;
    @Resource
    private EduArticleRedisService eduArticleRedisService;
    @Resource
    private EduArticleEsService eduArticleEsService;

    @Override
    @PostMapping("/saveOrUpdate")
    public Long saveOrUpdate(@RequestBody @Valid EduArticleReqVO reqVO) {
        Long id = eduArticleService.saveOrUpdate(reqVO);
        eduArticleRedisService.deleteById(id);
        return id;
    }

    @Override
    @PostMapping("/updateStatusByIds")
    public void updateStatusByIds(@RequestBody UpdateStatusReqVO reqVO) {
        eduArticleService.updateStatusByIds(reqVO);
        for (Long id : reqVO.getIds()) {
            eduArticleRedisService.deleteById(id);
        }
    }

    @Override
    @PostMapping("/queryEduArticle")
    public PageResult<EduArticleAdminListVO> queryEduArticle(@RequestBody EduArticleAdminQueryVO queryVO) {
        return eduArticleService.queryEduArticle(queryVO);
    }

    @Override
    @PostMapping("/getAllMediaArticle")
    public List<EduArticleAdminSelectVO> getAllMediaArticle() {
        return eduArticleService.getAllMediaArticle();
    }

    @Override
    @PostMapping("/queryEduArticleDetail")
    public EduArticleAdminDetailVO queryEduArticleDetail(@RequestParam(value = "id") Long id) {
        return eduArticleService.getAdminDetailById(id);
    }

    @Override
    @PostMapping("/viewCountAdd")
    public void viewCountAdd(@RequestParam(value = "id") Long id) {
        eduArticleRedisService.viewCountAdd(id);
    }


    @Override
    @PostMapping("/getArticleSimpleByIds")
    public List<EduArticleSimpleVO> getArticleSimpleByIds(@RequestBody List<Long> ids) {
        return eduArticleService.getArticleSimpleByIds(ids);
    }

    @Override
    @PostMapping("/queryArticle")
    public PageResult<EduArticleAppListVO> queryArticle(@RequestBody EduArticleAppQueryVO queryVO) {
        return eduArticleEsService.query(queryVO);
    }

    @Override
    @PostMapping("/queryArticleCategory")
    public List<EduCategoryVO> queryArticleCategory(@RequestBody EduArticleAppQueryVO queryVO) {
        return eduArticleEsService.queryEduCategory(queryVO);
    }

    @PostMapping("/queryEduSubCategory")
    @Override
    public List<EduSubCategoryVO> queryEduSubCategory(@RequestBody EduArticleAppQueryVO queryVO) {
        return eduArticleEsService.queryEduSubCategory(queryVO);
    }

    @PostMapping("/queryEduColumn")
    @Override
    public List<EduArticleColumnVO> queryEduColumn(@RequestBody EduArticleAppQueryVO queryVO) {
        return eduArticleEsService.queryEduColumn(queryVO);
    }

    @Override
    @PostMapping("/getAppDetail")
    public EduArticleAppDetailUserVO getAppDetail(@RequestParam(value = "id") Long id, @RequestParam(value = "userId") Long userId, @RequestParam(value = "patientId", required = false) Long patientId) {
        return eduArticleService.getAppDetail(id, userId, patientId);
    }

    @Override
    @PostMapping("/getAppDetailPreview")
    public EduArticleAppDetailVO getAppDetailPreview(@RequestParam(value = "id") Long id) {
        return CopyPropertiesUtil.normalCopyProperties(eduArticleRedisService.getById(id), EduArticleAppDetailVO.class);
    }

    @PostMapping("/getArticleList")
    @Override
    public List<EduArticleVO> getArticleList(@RequestBody List<Long> ids) {
        return eduArticleService.getArticleList(ids);
    }

    @PostMapping("/refreshArticle")
    @Override
    public void refreshArticle(@RequestBody RefreshArticleReqVO reqVO) {
        eduArticleEsService.refreshArticle(reqVO);
    }

    @PostMapping("/findByTitle")
    @Override
    public EduArticleVO findByTitle(@RequestParam(value = "title") String title) {
        return eduArticleService.findByTitle(title);
    }

    @PostMapping("/refreshArticleAll")
    @Override
    public void refreshArticleAll() {
        eduArticleService.refreshArticleAll();
    }
}