package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.EduCategoryFeign;
import com.ykl.med.edu.service.category.EduCategoryService;
import com.ykl.med.edu.vo.req.EduCategoryQueryVO;
import com.ykl.med.edu.vo.req.EduCategoryReqVO;
import com.ykl.med.edu.vo.resp.EduCategoryAdminListVO;
import com.ykl.med.edu.vo.resp.EduCategoryVO;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/eduCategory")
public class EduCategoryController implements EduCategoryFeign {
    @Resource
    private EduCategoryService eduCategoryService;

    @Override
    @PostMapping("/saveOrUpdate")
    public void saveOrUpdate(@RequestBody @Valid EduCategoryReqVO reqVO) {
        eduCategoryService.saveOrUpdate(reqVO);
    }

    @Override
    @PostMapping("/updateStatusById")
    public void updateStatusById(@RequestParam(value = "id") Long id, @RequestParam(value = "status") CommonStatusEnum status) {
        eduCategoryService.updateStatusById(id, status);
    }

    @Override
    @PostMapping("/queryEduCategory")
    public List<EduCategoryAdminListVO> queryEduCategory(@RequestBody @Valid EduCategoryQueryVO queryVO) {
        return eduCategoryService.queryEduCategory(queryVO);
    }

    @Override
    @PostMapping("/queryEduCategoryMap")
    public Map<Long, String> queryEduCategoryMap(@RequestBody List<Long> ids) {
        return eduCategoryService.queryEduCategoryMap(ids);
    }

    @Override
    @PostMapping("/getAllCategory")
    public List<EduCategoryVO> getAllCategory() {
        return eduCategoryService.getAllCategory();
    }
}