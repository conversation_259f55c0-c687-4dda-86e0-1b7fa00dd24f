package com.ykl.med.edu.db.mapper;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ykl.med.edu.db.entity.EduArticleDO;
import com.ykl.med.edu.vo.req.EduArticleAdminQueryVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface EduArticleMapper extends MPJBaseMapper<EduArticleDO> {
    default Page<EduArticleDO> adminQuery(EduArticleAdminQueryVO queryVO) {
        MPJLambdaWrapper<EduArticleDO> wrapper = new MPJLambdaWrapper<EduArticleDO>()
                .like(StringUtils.isNotEmpty(queryVO.getTitle()), EduArticleDO::getTitle, queryVO.getTitle())
                .eq(queryVO.getStatus() != null, EduArticleDO::getStatus, queryVO.getStatus())
                .eq(queryVO.getCategoryId() != null, EduArticleDO::getCategoryId, queryVO.getCategoryId())
                .eq(queryVO.getSubCategoryId() != null, EduArticleDO::getSubCategoryId, queryVO.getSubCategoryId())
                .in(CollectionUtil.isNotEmpty(queryVO.getTypes()), EduArticleDO::getType, queryVO.getTypes())
                .in(CollectionUtil.isNotEmpty(queryVO.getEduArticleIds()), EduArticleDO::getId, queryVO.getEduArticleIds());
        wrapper.orderByAsc(EduArticleDO::getSort);
        wrapper.orderByDesc(EduArticleDO::getUpdateTime);

        return this.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), wrapper);
    }

    @Select("select id from t_edu_article")
    List<Long> getAllIds();
}