package com.ykl.med.edu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.edu.constants.EduErrorCodeConstants;
import com.ykl.med.edu.vo.req.EduArticleChapterReqVO;
import com.ykl.med.edu.vo.resp.EduArticleChapterVO;
import com.ykl.med.edu.vo.resp.EduCourseVO;
import com.ykl.med.edu.vo.resp.HomePageTopEduCourseVO;
import com.ykl.med.edu.vo.resp.UserCourseRecordVO;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EduArticleChapterService extends ServiceImpl<EduArticleChapterMapper, EduArticleChapterDO>  {
    @Resource
    private EduCourseService eduCourseService;
    @Resource
    private UserCourseRecordService userCourseRecordService;


    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(EduArticleChapterReqVO reqVO) {
        log.info("EduArticleChapterServiceImpl.saveOrUpdate reqVO:{}", JSON.toJSONString(reqVO));
        EduArticleChapterDO eduArticleChapterDO;
        if (reqVO.getId() != null) {
            eduArticleChapterDO = super.getById(reqVO.getId());
            AssertUtils.notNull(eduArticleChapterDO, GlobalErrorCodeConstants.BAD_REQUEST);
        } else {
            eduArticleChapterDO = new EduArticleChapterDO();
        }
        BeanUtils.copyProperties(reqVO, eduArticleChapterDO);

        try {
            super.saveOrUpdate(eduArticleChapterDO);
        } catch (DuplicateKeyException e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("uq_article_chapter")) {
                throw new ServiceException(EduErrorCodeConstants.EDU_ARTICLE_CHAPTER_DUPLICATE_KEY);
            }
            throw e;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void deleteChapter(Long id) {
        log.info("EduArticleChapterServiceImpl.deleteChapter id:{}", id);
        EduArticleChapterDO eduArticleChapterDO = super.getById(id);
        AssertUtils.notNull(eduArticleChapterDO, GlobalErrorCodeConstants.BAD_REQUEST);
//        eduArticleChapterDO.setDeleted(true);
        List<EduCourseVO> eduCourseVOList = eduCourseService.queryEduCourse(id);
        AssertUtils.isTrue(CollectionUtils.isEmpty(eduCourseVOList), EduErrorCodeConstants.DELETE_CHAPTER_HAS_EDU_COURSE);

        super.removeById(eduArticleChapterDO);
        //super.saveOrUpdate(eduArticleChapterDO);
        eduCourseService.deleteCourseByChapterId(eduArticleChapterDO.getId());
    }


    public List<EduArticleChapterVO> queryEduArticleChapter(Long articleId) {
        LambdaQueryWrapper<EduArticleChapterDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduArticleChapterDO::getArticleId, articleId);
        queryWrapper.eq(EduArticleChapterDO::getDeleted, false);
        queryWrapper.orderByAsc(EduArticleChapterDO::getSort);
        List<EduArticleChapterDO> eduArticleChapterDOList = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(eduArticleChapterDOList)) {
            return null;
        }
        List<EduArticleChapterVO> chapterVOList = CopyPropertiesUtil.normalCopyProperties(eduArticleChapterDOList, EduArticleChapterVO.class);
        List<Long> chapterIdList = chapterVOList.stream().map(EduArticleChapterVO::getId).collect(Collectors.toList());
        Map<Long, List<EduCourseVO>> map = eduCourseService.queryEduCourseByChapterId(chapterIdList);
        chapterVOList.forEach(chapterVO -> {
            chapterVO.setEduCourseVOList(map.get(chapterVO.getId()));
        });
        return chapterVOList;
    }


    public EduArticleChapterVO getEduArticleChapter(Long id) {
        EduArticleChapterDO eduArticleChapterDO = super.getById(id);
        return CopyPropertiesUtil.normalCopyProperties(eduArticleChapterDO, EduArticleChapterVO.class);
    }


    public List<HomePageTopEduCourseVO> getFirstEduCourse(List<Long> articleIds, Long userId) {
        LambdaQueryWrapper<EduArticleChapterDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EduArticleChapterDO::getArticleId, articleIds);
        queryWrapper.eq(EduArticleChapterDO::getDeleted, false);
        queryWrapper.orderByAsc(EduArticleChapterDO::getSort);
        List<EduArticleChapterDO> eduArticleChapterDOList = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(eduArticleChapterDOList)) {
            return Collections.emptyList();
        }
        Map<Long, Long> map = new HashMap<>();
        for (EduArticleChapterDO eduArticleChapterDO : eduArticleChapterDOList) {
            map.computeIfAbsent(eduArticleChapterDO.getArticleId(), k -> eduArticleChapterDO.getId());
        }
        Map<Long, List<EduCourseVO>> eduMap = eduCourseService.queryEduCourseByChapterId(Lists.newArrayList(map.values()));
        List<HomePageTopEduCourseVO> list = new ArrayList<>();
        List<Long> courseIds = new ArrayList<>();
        for (Long articleId : articleIds) {
            Long chapterId = map.get(articleId);
            if (chapterId != null) {
                List<EduCourseVO> eduCourseVOList = eduMap.get(chapterId);
                if (CollectionUtils.isNotEmpty(eduCourseVOList)) {
                    HomePageTopEduCourseVO homePageTopEduCourseVO = new HomePageTopEduCourseVO();
                    EduCourseVO eduCourseVO = eduCourseVOList.get(0);
                    homePageTopEduCourseVO.setId(eduCourseVO.getId());
                    homePageTopEduCourseVO.setArticleId(articleId);
                    homePageTopEduCourseVO.setMediaUrl(eduCourseVO.getMediaUrlThumb());
                    homePageTopEduCourseVO.setTotalTime(eduCourseVO.getTotalTime());
                    courseIds.add(eduCourseVO.getId());
                    list.add(homePageTopEduCourseVO);
                }
            }
        }
        Map<Long, UserCourseRecordVO> courseViewMinutes = userCourseRecordService.getCourseViewMinutes(userId, courseIds);
        list.forEach(homePageTopEduCourseVO -> {
            UserCourseRecordVO userCourseRecordVO = courseViewMinutes.get(homePageTopEduCourseVO.getId());
            if (userCourseRecordVO != null) {
                homePageTopEduCourseVO.setLastTime(userCourseRecordVO.getLastTime());
            }
        });
        return list;
    }


    public Map<Long, Integer> getCourseCount(List<Long> articleIds) {
        if (CollectionUtils.isEmpty(articleIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<EduArticleChapterDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EduArticleChapterDO::getArticleId, articleIds);
        queryWrapper.eq(EduArticleChapterDO::getDeleted, false);
        queryWrapper.orderByAsc(EduArticleChapterDO::getSort);
        List<EduArticleChapterDO> eduArticleChapterDOList = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(eduArticleChapterDOList)) {
            return Collections.emptyMap();
        }
        Map<Long, List<EduArticleChapterDO>> map = eduArticleChapterDOList.stream().collect(Collectors.groupingBy(EduArticleChapterDO::getArticleId));
        List<Long> chapterIds = eduArticleChapterDOList.stream().map(EduArticleChapterDO::getId).collect(Collectors.toList());
        Map<Long, List<EduCourseVO>> eduMap = eduCourseService.queryEduCourseByChapterId(chapterIds);
        Map<Long, Integer> resultMap = new HashMap<>();
        for (Long articleId : articleIds) {
            int count = 0;
            List<EduArticleChapterDO> eduArticleChapterDOList1 = map.get(articleId);
            if (CollectionUtils.isNotEmpty(eduArticleChapterDOList1)) {
                for (EduArticleChapterDO eduArticleChapterDO : eduArticleChapterDOList1) {
                    List<EduCourseVO> eduCourseVOList = eduMap.get(eduArticleChapterDO.getId());
                    if (CollectionUtils.isNotEmpty(eduCourseVOList)) {
                        count += eduCourseVOList.size();
                    }
                }
            }
            resultMap.put(articleId, count);
        }
        return resultMap;
    }
}