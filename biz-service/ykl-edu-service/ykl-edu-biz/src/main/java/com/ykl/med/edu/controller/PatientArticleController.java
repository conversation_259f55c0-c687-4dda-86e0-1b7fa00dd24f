package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.PatientArticleFeign;
import com.ykl.med.edu.service.PatientArticleService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/patientArticle")
public class PatientArticleController implements PatientArticleFeign {

    @Resource
    private PatientArticleService patientArticleService;

    @Override
    @PostMapping("/addPatientArticle")
    public void addPatientArticle(@RequestParam(value = "patientId") Long patientId, @RequestParam(value = "articleId") Long articleId) {
        patientArticleService.addPatientArticle(patientId, articleId);
    }

}
