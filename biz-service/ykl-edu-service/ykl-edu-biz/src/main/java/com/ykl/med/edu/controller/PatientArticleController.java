package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.PatientArticleFeign;
import com.ykl.med.edu.service.PatientArticleService;
import com.ykl.med.edu.vo.req.UserCourseRecordReqVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/patientArticle")
public class PatientArticleController implements PatientArticleFeign {
    @Resource
    private UserCourseRecordService userCourseRecordService;

    @Resource
    private PatientArticleService patientArticleService;

    @Override
    @PostMapping("/saveOrUpdateRecord")
    public void saveOrUpdateRecord(@RequestBody @Valid UserCourseRecordReqVO reqVO) {
        userCourseRecordService.saveOrUpdate(reqVO);
    }

    @Override
    @PostMapping("/addPatientArticle")
    public void addPatientArticle(@RequestParam(value = "patientId") Long patientId, @RequestParam(value = "articleId") Long articleId) {
        patientArticleService.addPatientArticle(patientId, articleId);
    }

}
