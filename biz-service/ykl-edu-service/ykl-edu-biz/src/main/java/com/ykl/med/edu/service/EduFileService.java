package com.ykl.med.edu.service;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.edu.db.entity.EduArticleDO;
import com.ykl.med.edu.db.mapper.EduArticleMapper;
import com.ykl.med.file.api.FileFeign;
import com.ykl.med.file.constants.FileErrorCode;
import com.ykl.med.file.enums.FileBizType;
import com.ykl.med.file.vo.FileUploadRespVO;
import com.ykl.med.framework.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class EduFileService {
    @Resource
    private EduArticleMapper eduArticleMapper;
    @Resource
    private EduCourseMapper eduCourseMapper;
    @Resource
    private FileFeign fileFeign;

    @Transactional(rollbackFor = Exception.class)
    public void moveCover() {
        List<EduArticleDO> all = eduArticleMapper.selectList(null);
        for (EduArticleDO eduArticleDO : all) {
            try {
                if (eduArticleDO.getCover() != null && !eduArticleDO.getCover().contains(FileBizType.EDU_COVER.name())) {
                    log.info("迁移封面,{}", JSONObject.toJSONString(eduArticleDO));
                    //要迁移,但是由于消息里面使用了首页字段，所以不能删除源文件，所以只移动到EDU_COVER里面
                    FileUploadRespVO fileUploadRespVO = fileFeign.moveFile("ykl", "/admin", eduArticleDO.getCover(), FileBizType.EDU_COVER, false);
                    eduArticleDO.setCover(fileUploadRespVO.getPath());
                    eduArticleMapper.updateById(eduArticleDO);
                }
            } catch (Exception e) {
                log.error("迁移封面失败,{}", JSONObject.toJSONString(eduArticleDO), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void coverThumb() {
//        LambdaQueryWrapper<EduArticleDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.isNull(EduArticleDO::getCoverThumb);
//        List<EduArticleDO> all = eduArticleMapper.selectList(queryWrapper);
//        for (EduArticleDO eduArticleDO : all) {
//            try {
//                if (eduArticleDO.getCover() != null && eduArticleDO.getCover().contains(FileBizType.EDU_COVER.name())) {
//                    log.info("压缩首页,{}", JSONObject.toJSONString(eduArticleDO));
//                    //已经迁移过的，就生成缩略图
//                    FileUploadRespVO fileUploadRespVO = fileFeign.thumbImage("ykl", eduArticleDO.getCover(), 360, 480, FileBizType.EDU_COVER);
//                    eduArticleDO.setCoverThumb(fileUploadRespVO.getPath());
//                    eduArticleMapper.updateById(eduArticleDO);
//                }
//            } catch (Exception e) {
//                log.error("生成缩略图失败,{}", JSONObject.toJSONString(eduArticleDO), e);
//                eduArticleDO.setCoverThumb(eduArticleDO.getCover());
//                eduArticleMapper.updateById(eduArticleDO);
//            }
//        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void moveVideo() {
        List<EduCourseDO> all = eduCourseMapper.selectList(null);
        for (EduCourseDO eduCourseDO : all) {
            try {
                if (eduCourseDO.getMediaUrl() != null && !eduCourseDO.getMediaUrl().contains(FileBizType.EDU_VIDEO.name())) {
                    log.info("迁移视频,{}", JSONObject.toJSONString(eduCourseDO));
                    //要迁移,但是由于消息里面使用了首页字段，所以不能删除源文件，所以只移动到EDU_COVER里面
                    FileUploadRespVO fileUploadRespVO = fileFeign.moveFile("ykl", "/admin", eduCourseDO.getMediaUrl(), FileBizType.EDU_VIDEO, false);
                    eduCourseDO.setMediaUrl(fileUploadRespVO.getPath());
                    eduCourseMapper.updateById(eduCourseDO);
                }
            } catch (Exception e) {
                log.error("迁移视频失败,{}", JSONObject.toJSONString(eduCourseDO), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void videoThumb(EduCourseDO eduCourseDO) {
        try {
            if (eduCourseDO.getMediaUrl() != null && eduCourseDO.getMediaUrl().contains(FileBizType.EDU_VIDEO.name())) {
                log.info("压缩视频,{}", JSONObject.toJSONString(eduCourseDO));
                FileUploadRespVO fileUploadRespVO = fileFeign.videoCompression("ykl", eduCourseDO.getMediaUrl(), 720, FileBizType.EDU_VIDEO);
                eduCourseDO.setMediaUrlThumb(fileUploadRespVO.getPath());
                eduCourseMapper.updateById(eduCourseDO);
            }
        } catch (ServiceException se) {
            if (Objects.equals(se.getCode(), FileErrorCode.LIMIT_ERROR.getCode())) {
                log.info("压缩视频限流了,{}", JSONObject.toJSONString(eduCourseDO));
            }
            eduCourseDO.setMediaUrlThumb(eduCourseDO.getMediaUrl());
            eduCourseMapper.updateById(eduCourseDO);
        } catch (Exception e) {
            log.error("压缩视频失败,{}", JSONObject.toJSONString(eduCourseDO), e);
            eduCourseDO.setMediaUrlThumb(eduCourseDO.getMediaUrl());
            eduCourseMapper.updateById(eduCourseDO);
        }
    }

}
