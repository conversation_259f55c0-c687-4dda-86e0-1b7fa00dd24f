package com.ykl.med.edu.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

@TableName("t_edu_sub_category")
@Data
public class EduSubCategoryDO extends BaseDO {
    private Long categoryId;
    /**
     * 分类名称
     */
    private String name;

    /**
     * 状态
     */
    private CommonStatusEnum status;

    private Long creatorId;

    private Integer sort;
}
