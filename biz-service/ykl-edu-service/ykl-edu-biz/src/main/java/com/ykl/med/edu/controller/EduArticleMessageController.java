package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.EduArticleMessageFeign;
import com.ykl.med.edu.enums.PatientArticleResource;
import com.ykl.med.edu.service.EduArticleMessageService;
import com.ykl.med.edu.service.UserBrowseHistoryService;
import com.ykl.med.edu.vo.req.EduArticleMessageQueryVO;
import com.ykl.med.edu.vo.req.EduArticleMessageReqVO;
import com.ykl.med.edu.vo.resp.EduArticleBrowseHistoryAppListVO;
import com.ykl.med.edu.vo.resp.EduArticleMineAppListVO;
import com.ykl.med.edu.vo.resp.HomePageTopEduCourseVO;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/eduArticleMessage")
public class EduArticleMessageController implements EduArticleMessageFeign {
    @Resource
    private EduArticleMessageService eduArticleMessageService;
    @Resource
    private UserBrowseHistoryService userBrowseHistoryService;

    @Override
    @PostMapping("/saveOrUpdate")
    public void saveOrUpdate(@RequestBody @Valid EduArticleMessageReqVO reqVO) {
        eduArticleMessageService.saveOrUpdate(reqVO);
    }

    @Override
    @PostMapping("/systemRecommend")
    public void systemRecommend(@RequestBody List<Long> articleIds,
                                @RequestParam(value = "patientId") Long patientId,
                                @RequestParam(value = "resource") PatientArticleResource resource) {
        eduArticleMessageService.systemRecommend(articleIds, patientId, resource);
    }

    @Override
    @PostMapping("/sendMessage")
    public void sendMessage(@RequestParam(value = "id") Long id) {
        eduArticleMessageService.sendMessage(id);
    }

    @Override
    @PostMapping("/pageByPatientId")
    public PageResult<EduArticleMineAppListVO> pageByPatientId(@RequestBody @Valid EduArticleMessageQueryVO queryVO) {
        return eduArticleMessageService.pageByPatientId(queryVO);
    }

    @Override
    @PostMapping("/homePageTopEduArticle")
    public List<HomePageTopEduCourseVO> homePageTopEduArticle(@RequestParam(value = "patientId") Long patientId,
                                                              @RequestParam(value = "userId") Long userId) {
        return eduArticleMessageService.homePageTopEduArticle(patientId, userId);
    }

    @Override
    @PostMapping("/getNeedSendMessage")
    public List<Long> getNeedSendMessage() {
        return eduArticleMessageService.getNeedSendMessage();
    }

    @Override
    @PostMapping("/updateReadStatus")
    public void updateReadStatus(@RequestParam(value = "userId") Long userId, @RequestBody List<Long> articleIds) {
        userBrowseHistoryService.add(userId, articleIds);
    }

    @Override
    @PostMapping("/queryBrowseHistory")
    public PageResult<EduArticleBrowseHistoryAppListVO> queryBrowseHistory(@RequestParam(value = "userId") Long userId, @RequestBody PageParam pageParam) {
        return userBrowseHistoryService.queryBrowseHistory(userId, pageParam.getPageNo(), pageParam.getPageSize());
    }

}
