package com.ykl.med.edu.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.ykl.med.edu.db.entity.UserBrowseHistoryDO;
import com.ykl.med.edu.enums.ArticleType;
import com.ykl.med.edu.service.article.EduArticleService;
import com.ykl.med.edu.service.category.EduSubCategoryService;
import com.ykl.med.edu.vo.resp.EduArticleVO;
import com.ykl.med.edu.vo.stat.EduStatVO;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.vo.patient.PatientSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EduStatService {
    @Resource
    private UserBrowseHistoryService userBrowseHistoryService;
    @Resource
    private EduArticleService eduArticleService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private EduSubCategoryService eduSubCategoryService;
    @Resource
    private PatientUserFeign patientUserFeign;

    private final static String EDU_STAT_REDIS_KEY = "edu:stat:";

    public EduStatVO statByPatientId(Long patientId) {
        EduStatVO eduStat = new EduStatVO();
        eduStat.setLoveSubCategories(new ArrayList<>());
        List<UserBrowseHistoryDO> userBrowseHistoryList = userBrowseHistoryService.getByPatientId(patientId);
        List<Long> articleIds = userBrowseHistoryList.stream().map(UserBrowseHistoryDO::getArticleId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(articleIds)) {
            return eduStat;
        }
        List<EduArticleVO> eduArticles = eduArticleService.getArticleList(articleIds);
        Map<Long, Long> subCategoryMap = eduArticles.stream().filter(e -> e.getSubCategoryId() != null)
                .collect(Collectors.groupingBy(EduArticleVO::getSubCategoryId, Collectors.counting()));
        //阅读数量最多的子类别（多个并列第一都展示出来）及子类别阅读数量（多个类别的话累加数量）
        Long maxCount = subCategoryMap.values().stream().max(Long::compareTo).orElse(0L);
        if (maxCount > 0) {
            for (Map.Entry<Long, Long> entry : subCategoryMap.entrySet()) {
                if (Objects.equals(entry.getValue(), maxCount)) {
                    eduStat.getLoveSubCategories().add(eduSubCategoryService.getById(entry.getKey()).getName());
                    eduStat.setSubLoveCount(eduStat.getSubLoveCount() + entry.getValue().intValue());
                }
            }
        }
        for (EduArticleVO eduArticleVO : eduArticles) {
            eduStat.setTotalBrowseCount(eduStat.getTotalBrowseCount() + 1);
            if (eduArticleVO.getType() == ArticleType.GRAPHIC) {
                eduStat.setTotalBrowseGraphicCount(eduStat.getTotalBrowseGraphicCount() + 1);
            } else if (eduArticleVO.getType() == ArticleType.VIDEO) {
                eduStat.setTotalBrowseVideoCount(eduStat.getTotalBrowseVideoCount() + 1);
            }
        }

        return eduStat;
    }

    public EduStatVO getStatByPatientId(Long patientId) {
        String eduStatJson = stringRedisTemplate.opsForValue().get(EDU_STAT_REDIS_KEY + patientId);
        if (eduStatJson != null) {
            return JSONObject.parseObject(eduStatJson, EduStatVO.class);
        }
        EduStatVO eduStat = this.statByPatientId(patientId);
        stringRedisTemplate.opsForValue().set(EDU_STAT_REDIS_KEY + patientId, JSONObject.toJSONString(eduStat), 1, TimeUnit.DAYS);
        return eduStat;
    }

    public void refreshStat(Long userId) {
        PatientSimpleVO patient = patientUserFeign.getPatientSimpleByUserId(userId);
        if (patient != null) {
            stringRedisTemplate.delete(EDU_STAT_REDIS_KEY + patient.getPatientId());
        }
    }
}
