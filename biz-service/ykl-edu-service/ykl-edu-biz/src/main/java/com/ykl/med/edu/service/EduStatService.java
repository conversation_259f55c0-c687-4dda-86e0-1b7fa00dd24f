package com.ykl.med.edu.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.ykl.med.edu.db.entity.UserBrowseHistoryDO;
import com.ykl.med.edu.service.article.EduArticleService;
import com.ykl.med.edu.service.category.EduSubCategoryService;
import com.ykl.med.edu.vo.resp.EduArticleVO;
import com.ykl.med.edu.vo.stat.EduStatVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EduStatService {
    @Resource
    private UserBrowseHistoryService userBrowseHistoryService;
    @Resource
    private EduArticleService eduArticleService;
    @Resource
    private StringRedisTemplate redisTemplate;
    @Resource
    private EduSubCategoryService eduSubCategoryService;

    private final static String EDU_STAT_REDIS_KEY = "edu:stat:";

    public EduStatVO stat(Long patientId) {
        EduStatVO eduStat = new EduStatVO();
        List<UserBrowseHistoryDO> userBrowseHistoryList = userBrowseHistoryService.getByPatientId(patientId);
        List<Long> articleIds = userBrowseHistoryList.stream().map(UserBrowseHistoryDO::getArticleId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(articleIds)) {
            redisTemplate.opsForValue().set(EDU_STAT_REDIS_KEY + patientId, JSONObject.toJSONString(eduStat));
            return eduStat;
        }
        List<EduArticleVO> eduArticleVOS = eduArticleService.getArticleList(articleIds);
        //todo 完善
        return null;
    }
}
