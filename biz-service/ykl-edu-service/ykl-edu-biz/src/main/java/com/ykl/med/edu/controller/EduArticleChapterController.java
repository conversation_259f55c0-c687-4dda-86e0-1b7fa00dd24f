package com.ykl.med.edu.controller;

import com.ykl.med.edu.api.EduArticleChapterFeign;
import com.ykl.med.edu.vo.req.EduArticleChapterReqVO;
import com.ykl.med.edu.vo.resp.EduArticleChapterVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "EduArticleChapterController")
@RestController
@RequestMapping("/eduArticleChapter")
@Validated
public class EduArticleChapterController implements EduArticleChapterFeign {

    @Resource
    private EduArticleChapterService eduArticleChapterService;

    @PostMapping("/saveOrUpdate")
    @Override
    public void saveOrUpdate(@RequestBody @Valid EduArticleChapterReqVO reqVO) {
        eduArticleChapterService.saveOrUpdate(reqVO);
    }

    @PostMapping("/deleteChapter")
    @Override
    public void deleteChapter(@RequestParam(value = "id") Long id) {
        eduArticleChapterService.deleteChapter(id);
    }

    @PostMapping("/queryEduArticleChapter")
    @Override
    public List<EduArticleChapterVO> queryEduArticleChapter(@RequestParam(value = "articleId") Long articleId) {
        return eduArticleChapterService.queryEduArticleChapter(articleId);
    }
}