<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <parent>
        <groupId>com.ykl.med</groupId>
        <artifactId>ykl-edu-service</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>ykl-edu-biz</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-edu-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <!--基础服务引用-->
        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-base-biz</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-patient-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-user-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-doctors-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-push-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <!-- DB 相关 -->
        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-spring-boot-starter-mybatis</artifactId>
            <version>${ykl-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-file-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-master-data-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.ykl.med</groupId>
            <artifactId>ykl-spring-boot-starter-rocketmq</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>