package com.ykl.med.records.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.records.entity.ElectrocardiogramDO;
import com.ykl.med.records.mapper.ElectrocardiogramMapper;
import com.ykl.med.records.service.ElectrocardiogramService;
import com.ykl.med.records.vo.ElectrocardiogramVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ElectrocardiogramServiceImpl extends ServiceImpl<ElectrocardiogramMapper, ElectrocardiogramDO> implements ElectrocardiogramService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(Long patientId, List<ElectrocardiogramVO> electrocardiograms) {
        log.info("保存或更新心电图记录，参数：{}", JSON.toJSONString(electrocardiograms));
        if (electrocardiograms != null) {
            baseMapper.delete(new LambdaQueryWrapper<ElectrocardiogramDO>().eq(ElectrocardiogramDO::getPatientId, patientId));
        }
        if (CollectionUtils.isEmpty(electrocardiograms)) {
            return;
        }
        baseMapper.insertBatch(CopyPropertiesUtil.copyAndConvertList(electrocardiograms, ElectrocardiogramDO::new, (source, target) -> target.setPatientId(patientId)));
    }

    @Override
    public List<ElectrocardiogramVO> listByPatientId(Long patientId) {
        List<ElectrocardiogramDO> electrocardiogramDOList = this.lambdaQuery().eq(ElectrocardiogramDO::getPatientId, patientId).list();
        return CopyPropertiesUtil.copyList(electrocardiogramDOList, ElectrocardiogramVO::new);
    }
}