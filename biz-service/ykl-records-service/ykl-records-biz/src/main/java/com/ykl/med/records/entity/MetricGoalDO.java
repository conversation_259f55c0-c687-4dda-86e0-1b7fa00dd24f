package com.ykl.med.records.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

@Data
@TableName("t_metric_goal")
public class MetricGoalDO extends BaseDO {

    private static final long serialVersionUID = -349795443165096068L;

    /**
     * 唯一值, 用检查检验项目id+,+标本字典值 拼接
     */
    private String searchKey;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 指标值
     */
    private String value;
}
