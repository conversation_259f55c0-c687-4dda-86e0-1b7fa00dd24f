package com.ykl.med.records.service;

import com.ykl.med.records.vo.MedicalHistoryVO;

import java.util.List;

public interface MedicalHistoryService {

    /**
     * 保存或更新病史
     *
     * @param patientId        病人ID
     * @param medicalHistories 病史
     */
    void insertBatch(Long patientId, List<MedicalHistoryVO> medicalHistories);

    /**
     * 根据病人ID查询病史
     *
     * @param patientId 病人ID
     * @return 病史
     */
    List<MedicalHistoryVO> listByPatientId(Long patientId);
}