package com.ykl.med.records.util;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.base.utils.SpringContextUtils;
import com.ykl.med.records.service.DynamicFormService;
import com.ykl.med.records.vo.DischargeMedicationVO;
import com.ykl.med.records.vo.DynamicFormVO;
import com.ykl.med.records.vo.LabTestItemVO;
import com.ykl.med.records.vo.resp.DynamicFormItemRespVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

public class DynamicFormConverter {

    public static List<DynamicFormItemRespVO> convertToJson(Object obj) {
        Class<?> clazz = obj.getClass();
        DynamicFormService dynamicFormService = SpringContextUtils.getBean(DynamicFormService.class);
        Map<String, DynamicFormVO> dynamicFormMap = dynamicFormService.getDynamicFormMap(clazz.getName());

        return Arrays.stream(clazz.getDeclaredFields())
                .filter(field -> {
                    DynamicFormVO dynamicFormVO = dynamicFormMap.get(field.getName());
                    return dynamicFormVO != null && dynamicFormVO.getDisplay();
                })
                .map(field -> {
                    field.setAccessible(true);
                    DynamicFormVO dynamicFormVO = dynamicFormMap.get(field.getName());
                    String alias = dynamicFormVO.getAlias() != null ? dynamicFormVO.getAlias() : field.getName();
                    return convertToFormItem(alias, obj, field, dynamicFormVO, dynamicFormService);
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(DynamicFormItemRespVO::getSort))
                .collect(Collectors.toList());
    }

    private static DynamicFormItemRespVO convertToFormItem(String alias, Object obj, Field field, DynamicFormVO dynamicFormVO, DynamicFormService dynamicFormService) {
        try {
            Object value = field.get(obj);
            Map<String, DynamicFormVO> dynamicFormMap = dynamicFormService.getDynamicFormMap(field.getType().getName());
            if (value instanceof List) {
                List<?> list = (List<?>) value;
                if (CollectionUtils.isNotEmpty(list)) {
                    Class<?> genericType = list.get(0).getClass();
                    switch (genericType.getName()) {
                        case "com.ykl.med.records.vo.LabTestItemVO":
                            List<DynamicFormItemRespVO> labTestItems = list.stream()
                                    .map(LabTestItemVO.class::cast)
                                    .map(labTestItemVO -> {
                                        JSONObject jsonObject = new JSONObject();
                                        jsonObject.put("deviation", labTestItemVO.getDeviation());
                                        jsonObject.put("value", labTestItemVO.getValue());
                                        jsonObject.put("unit", labTestItemVO.getUnit());
                                        jsonObject.put("rangeSymbol", labTestItemVO.getRangeSymbol());
                                        return new DynamicFormItemRespVO()
                                                .setKey(labTestItemVO.getTestItem())
                                                .setValue(jsonObject.toString())
                                                .setSort(dynamicFormVO.getSort())
                                                .setReferenceRange(labTestItemVO.getReferenceRange())
                                                .setType("LAB_TEST_ITEM");
                                    })
                                    .collect(Collectors.toList());

                            return new DynamicFormItemRespVO()
                                    .setKey(alias)
                                    .setChildren(Collections.singletonList(labTestItems))
                                    .setSort(dynamicFormVO.getSort())
                                    .setType(dynamicFormVO.getType());
                        case "com.ykl.med.records.vo.DischargeMedicationVO":
                            return new DynamicFormItemRespVO()
                                    .setKey(alias)
                                    .setValue(list.stream()
                                            .map(DischargeMedicationVO.class::cast)
                                            // .map(o -> String.format("%s %s %s 每次%s%s", o.getDrugName(), o.getAdministrationRoute(), o.getFrequency(), o.getSingleDose(), o.getDoseUnit()))
                                            .map(DischargeMedicationVO::getOriginalText)
                                            .filter(StringUtils::isNotBlank)
                                            .collect(Collectors.joining("\n")))
                                    .setSort(dynamicFormVO.getSort())
                                    .setType("TEXT");
                        case "java.lang.String":
                            return new DynamicFormItemRespVO().setKey(alias).setValue(list.stream()
                                    .filter(Objects::nonNull)
                                    .map(Object::toString)
                                    .filter(StringUtils::isNotBlank)
                                    .map(String::trim)
                                    .collect(Collectors.joining("、"))).setSort(dynamicFormVO.getSort()).setType(dynamicFormVO.getType());
                        default:
                            dynamicFormMap = dynamicFormService.getDynamicFormMap(genericType.getName());
                            if (MapUtils.isNotEmpty(dynamicFormMap)) {
                                List<List<DynamicFormItemRespVO>> children = list.stream()
                                        .map(DynamicFormConverter::convertToJson)
                                        .collect(Collectors.toList());
                                return new DynamicFormItemRespVO().setKey(alias).setChildren(children).setSort(dynamicFormVO.getSort()).setType(dynamicFormVO.getType());
                            }
                            break;
                    }
                }
                return new DynamicFormItemRespVO().setKey(alias).setValue(CollectionUtils.isEmpty(list) ? StringUtils.EMPTY : value.toString()).setSort(dynamicFormVO.getSort()).setType(dynamicFormVO.getType());
            }

            if (dynamicFormMap.containsKey(field.getType().getName())) {
                return new DynamicFormItemRespVO().setKey(alias).setChildren(Collections.singletonList(convertToJson(value))).setSort(dynamicFormVO.getSort()).setType(dynamicFormVO.getType());
            }
            String text;
            switch (field.getType().getName()) {
                case "java.time.LocalDateTime":
                    text = value == null ? StringUtils.EMPTY : String.valueOf(((java.time.LocalDateTime) value).toInstant(java.time.ZoneOffset.of("+8")).toEpochMilli());
                    break;
                case "java.time.LocalDate":
                    text = value == null ? StringUtils.EMPTY : String.valueOf(((java.time.LocalDate) value).atStartOfDay(java.time.ZoneOffset.of("+8")).toInstant().toEpochMilli());
                    break;
                case "com.ykl.med.records.enums.ReportCategory":
                    text = value == null ? StringUtils.EMPTY : ((com.ykl.med.records.enums.ReportCategory) value).getDesc();
                    break;
                default:
                    text = value == null ? StringUtils.EMPTY : value.toString();
                    break;
            }
            return new DynamicFormItemRespVO().setKey(alias).setValue(text).setSort(dynamicFormVO.getSort()).setType(dynamicFormVO.getType());
        } catch (IllegalAccessException ignore) {
            return null;
        }
    }
}