package com.ykl.med.records.service.impl;

import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.records.service.ImageMetricService;
import com.ykl.med.records.service.ImagingExaminationService;
import com.ykl.med.records.service.ReportService;
import com.ykl.med.records.vo.ExaminationFindingsVO;
import com.ykl.med.records.vo.ImagingExaminationVO;
import com.ykl.med.records.vo.metric.image.ImageMetricGroupVO;
import com.ykl.med.records.vo.metric.image.ImageMetricVO;
import com.ykl.med.records.vo.resp.ReportListRespVO;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 影像学检查指标服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ImageMetricServiceImpl implements ImageMetricService {

    private final ImagingExaminationService imagingExaminationService;

    private final ReportService reportService;


    @Override
    public List<ImageMetricVO> list(Long patientId) {
        return list(patientId, null);
    }

    private List<ImageMetricVO> list(Long patientId, List<String> examinationType) {
        List<ImagingExaminationVO> imagingExaminations = imagingExaminationService.listByPatientId(patientId, examinationType);
        imagingExaminations.sort((o1, o2) -> {
            if (o1.getExaminationLocalDateTime() == null && o2.getExaminationLocalDateTime() == null) {
                return 0;
            }
            if (o1.getExaminationLocalDateTime() == null) {
                return 1;
            }
            if (o2.getExaminationLocalDateTime() == null) {
                return -1;
            }
            return o2.getExaminationLocalDateTime().compareTo(o1.getExaminationLocalDateTime());
        });
        List<FindingWithReport> findingsWithReportId = imagingExaminations.stream()
                .flatMap(exam -> exam.getExaminationFindings().stream()
                        .map(finding -> new FindingWithReport(exam.getReportId(), exam.getExaminationLocalDateTime(), finding)))
                .collect(Collectors.toList());

        return CopyPropertiesUtil.copyAndConvertList(findingsWithReportId, ImageMetricVO::new, (s, t) -> {
            t.setReportId(s.getReportId());
            t.setExaminationTime(s.getExaminationTime());
            t.setName(s.getFinding().getLesion());
            t.setPart(s.getFinding().getLesionLocation());
            t.setSituation(s.getFinding().getLesionMorphology());
            t.setSize(s.getFinding().getLesionSize());
            t.setDensity(s.getFinding().getLesionDensity());
            t.setBorder(s.getFinding().getLesionMargin());
            t.setLevel(s.getFinding().getLesionGrading());
        });
    }

    @Override
    public List<ImageMetricGroupVO> listGroup(Long patientId, List<String> examinationType) {
        List<ImageMetricVO> list = list(patientId, examinationType);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        Set<Long> reportIds = list.stream().map(ImageMetricVO::getReportId).collect(Collectors.toSet());
        Map<Long, ReportListRespVO> reportListRespVOMap = reportService.groupingByIds(reportIds);

        // 按照报告id分组
        Map<Long, List<ImageMetricVO>> collect = list.stream().collect(Collectors.groupingBy(ImageMetricVO::getReportId));
        List<ImageMetricGroupVO> result = new ArrayList<>();
        for (Long reportId : collect.keySet()) {
            List<ImageMetricVO> imageMetricVOS = collect.get(reportId);
            if (CollectionUtils.isEmpty(imageMetricVOS)) {
                continue;
            }

            ReportListRespVO reportListRespVO = reportListRespVOMap.get(reportId);
            if (reportListRespVO == null) {
                continue;
            }

            ImageMetricGroupVO imageMetricGroupVO = new ImageMetricGroupVO();
            imageMetricGroupVO.setReportId(reportId);
            imageMetricGroupVO.setSubCategory(reportListRespVO.getSubCategory());
            imageMetricGroupVO.setSubCategoryName(reportListRespVO.getSubCategoryName());
            imageMetricGroupVO.setExaminationTime(reportListRespVO.getRecordsTime());
            imageMetricGroupVO.setImageMetric(imageMetricVOS);
            result.add(imageMetricGroupVO);
        }

        if (CollectionUtils.isNotEmpty(result)) {
            return result.stream().sorted(Comparator.comparing(ImageMetricGroupVO::getExaminationTime).reversed()).collect(Collectors.toList());
        }

        return result;
    }

    @Getter
    private static class FindingWithReport {
        private final Long reportId;
        private final LocalDateTime examinationTime;
        private final ExaminationFindingsVO finding;

        FindingWithReport(Long reportId, LocalDateTime examinationTime, ExaminationFindingsVO finding) {
            this.reportId = reportId;
            this.examinationTime = examinationTime;
            this.finding = finding;
        }

    }
}
