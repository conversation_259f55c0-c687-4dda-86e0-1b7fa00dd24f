package com.ykl.med.records.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.records.entity.PulmonaryFunctionDO;
import com.ykl.med.records.mapper.PulmonaryFunctionMapper;
import com.ykl.med.records.service.PulmonaryFunctionService;
import com.ykl.med.records.vo.PulmonaryFunctionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PulmonaryFunctionServiceImpl extends ServiceImpl<PulmonaryFunctionMapper, PulmonaryFunctionDO> implements PulmonaryFunctionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(Long patientId, List<PulmonaryFunctionVO> pulmonaryFunctions) {
        log.info("保存或更新肺功能，参数：{}", JSON.toJSONString(pulmonaryFunctions));
        if (pulmonaryFunctions != null) {
            baseMapper.delete(new LambdaQueryWrapper<PulmonaryFunctionDO>().eq(PulmonaryFunctionDO::getPatientId, patientId));
        }
        if (CollectionUtils.isEmpty(pulmonaryFunctions)) {
            return;
        }
        baseMapper.insertBatch(CopyPropertiesUtil.copyAndConvertList(pulmonaryFunctions, PulmonaryFunctionDO::new, (source, target) -> target.setPatientId(patientId)));
    }

    @Override
    public List<PulmonaryFunctionVO> listByPatientId(Long patientId) {
        List<PulmonaryFunctionDO> pulmonaryFunctionDOList = this.lambdaQuery().eq(PulmonaryFunctionDO::getPatientId, patientId).list();
        return CopyPropertiesUtil.copyList(pulmonaryFunctionDOList, PulmonaryFunctionVO::new);
    }
}