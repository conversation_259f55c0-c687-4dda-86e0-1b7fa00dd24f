package com.ykl.med.records.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.records.entity.MedicalVisitHistoryDO;
import com.ykl.med.records.mapper.MedicalVisitHistoryMapper;
import com.ykl.med.records.service.MedicalVisitHistoryService;
import com.ykl.med.records.vo.MedicalVisitHistoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MedicalVisitHistoryServiceImpl extends ServiceImpl<MedicalVisitHistoryMapper, MedicalVisitHistoryDO> implements MedicalVisitHistoryService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(Long patientId, List<MedicalVisitHistoryVO> medicalVisitHistories) {
        log.info("保存或更新就诊历史记录，参数：{}", JSON.toJSONString(medicalVisitHistories));
        if (medicalVisitHistories != null) {
            baseMapper.delete(new LambdaQueryWrapper<>(MedicalVisitHistoryDO.class).eq(MedicalVisitHistoryDO::getPatientId, patientId));
        }
        if (CollectionUtils.isEmpty(medicalVisitHistories)) {
            return;
        }
        baseMapper.insertBatch(CopyPropertiesUtil.copyAndConvertList(medicalVisitHistories, MedicalVisitHistoryDO::new, (source, target) -> target.setPatientId(patientId)));
    }

    @Override
    public List<MedicalVisitHistoryVO> listByPatientId(Long patientId) {
        List<MedicalVisitHistoryDO> medicalVisitHistoryDOList = this.lambdaQuery().eq(MedicalVisitHistoryDO::getPatientId, patientId).list();
        return CopyPropertiesUtil.copyList(medicalVisitHistoryDOList, MedicalVisitHistoryVO::new);
    }
}