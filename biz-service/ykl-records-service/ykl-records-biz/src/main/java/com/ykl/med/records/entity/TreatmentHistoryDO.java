package com.ykl.med.records.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.records.enums.TreatmentType;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 治疗历史
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_treatment_history")
public class TreatmentHistoryDO extends BaseDO {

    private static final long serialVersionUID = -2542557927921546206L;

    /**
     * 病人ID
     */
    private Long patientId;

    /**
     * 治疗类型
     */
    private TreatmentType treatmentType;

    /**
     * 治疗方式(目前只有中医治疗需要), 字典值
     */
    private String treatmentMode;

    /**
     * 用法(目前只有中医治疗需要), 字典值
     */
    private String treatmentUsage;

    /**
     * 治疗子类型，当治疗类型为其他时填写
     */
    private String subType;

    /**
     * 治疗名称（手术名称、药物名称、化疗方案等）
     */
    private String treatmentName;

    /**
     * 治疗开始时间
     */
    private LocalDateTime treatmentStartTime;

    /**
     * 治疗结束时间
     */
    private LocalDateTime treatmentEndTime;

    /**
     * 治疗疗效（手术效果、化疗效果、放疗效果、药物疗效等）
     */
    private String treatmentEffect;

    /**
     * 治疗并发症或不良反应
     */
    private String complicationsOrSideEffects;

    /**
     * 主刀医生或主管医生
     */
    private String chiefDoctor;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 医院名称
     */
    private String hospitalName;

    // 以下是特定治疗类型可能需要的额外字段

    /**
     * 是否留置导管（仅化疗相关）
     */
    private Boolean catheterPlacement;

    /**
     * 靶区（仅放疗相关）
     */
    private String targetArea;

    /**
     * 总剂量Gy（仅放疗相关）
     */
    private String totalDoseGy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 治疗部位
     */
    private String treatmentPart;

    /**
     * 治疗方法
     */
    private String treatmentMethod;

}
