package com.ykl.med.records.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_lab_test_item")
public class LabTestItemDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 所属检验ID，关联到LabTestDO
     */
    private Long labTestId;

    /**
     * 检验项目
     */
    private String testItem;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 符号
     */
    private String rangeSymbol;

    /**
     * 项目值（指标值）
     */
    private String value;

    /**
     * 单位
     */
    private String unit;

    /**
     * 参考范围
     */
    private String referenceRange;

    /**
     * 偏差
     */
    private String deviation;

    /**
     * 异常指标名称
     */
    private String abnormalIndicatorName;
}
