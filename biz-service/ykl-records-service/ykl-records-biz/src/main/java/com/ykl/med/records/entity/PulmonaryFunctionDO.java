package com.ykl.med.records.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@TableName("t_pulmonary_function")
public class PulmonaryFunctionDO extends BaseDO {

    private static final long serialVersionUID = -7891948305602349408L;

    /**
     * 病人ID
     */
    private Long patientId;

    /**
     * 检查名称
     */
    private String checkName;

    /**
     * 检查时间
     */
    private String checkTime;

    /**
     * 检查图形
     */
    private String checkGraph;

    /**
     * 诊断意见
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> diagnosisOpinion;

    /**
     * VT预计值
     */
    private BigDecimal expectedVt;
    /**
     * VT实际值
     */
    private BigDecimal actualVt;
    /**
     * VT实际值/VT预计值
     */
    private BigDecimal ratioVt;

    /**
     * ERV预计值
     */
    private BigDecimal expectedErv;
    /**
     * ERV实际值
     */
    private BigDecimal actualErv;
    /**
     * ERV实际值/ERV预计值
     */
    private BigDecimal ratioErv;

    /**
     * BF预计值
     */
    private BigDecimal expectedBf;

    /**
     * BF实际值
     */
    private BigDecimal actualBf;

    /**
     * BF实际值/BF预计值
     */
    private BigDecimal ratioBf;

    /**
     * IC预计值
     */
    private BigDecimal expectedIc;

    /**
     * IC实际值
     */
    private BigDecimal actualIc;

    /**
     * IC实际值/IC预计值
     */
    private BigDecimal ratioIc;

    /**
     * VC MAX预计值
     */
    private BigDecimal expectedVcMax;

    /**
     * VC MAX实际值
     */
    private BigDecimal actualVcMax;

    /**
     * VC MAX实际值/VC MAX预计值
     */
    private BigDecimal ratioVcMax;

    /**
     * FVC预计值
     */
    private BigDecimal expectedFvc;

    /**
     * FVC实测值
     */
    private BigDecimal actualFvc;

    /**
     * FVC实测/预计
     */
    private BigDecimal ratioFvc;

    /**
     * 预期的FEV1值 (第一秒用力呼气量)
     */
    private BigDecimal expectedFev1;

    /**
     * 实测的FEV1值 (第一秒用力呼气量)
     */
    private BigDecimal actualFev1;

    /**
     * FEV1实际测量值与预期值的比率
     */
    private BigDecimal ratioFev1;

    /**
     * 预期的FEV1/FVC值
     */
    private BigDecimal expectedFeV1Fvc;

    /**
     * 实测的FEV1/FVC值
     */
    private BigDecimal actualFeV1Fvc;

    /**
     * FEV1/FVC实际测量值与预期值的比率
     */
    private BigDecimal ratioFeV1Fvc;

    /**
     * 预期的FEV1/VCmax值
     */
    private BigDecimal expectedFeV1VcMax;

    /**
     * 实测的FEV1/VCmax值
     */
    private BigDecimal actualFeV1VcMax;

    /**
     * FEV1/VCmax实测值与预计值的比率
     */
    private BigDecimal ratioFeV1VcMax;

    /**
     * 预期的PEF (最大呼气流速)值
     */
    private BigDecimal expectedPef;

    /**
     * 实测的PEF (最大呼气流速)值
     */
    private BigDecimal actualPef;

    /**
     * PEF实测值与预期值的比率
     */
    private BigDecimal ratioPef;

    /**
     * MEF 75预计值
     */
    private BigDecimal expectedMef75;

    /**
     * MEF 75实测值
     */
    private BigDecimal actualMef75;

    /**
     * MEF 75实测/预计
     */
    private BigDecimal ratioMef75;

    /**
     * MEF 50预计值
     */
    private BigDecimal expectedMef50;

    /**
     * MEF 50实测值
     */
    private BigDecimal actualMef50;

    /**
     * MEF 50实测/预计
     */
    private BigDecimal ratioMef50;

    /**
     * MEF 25预计值
     */
    private BigDecimal expectedMef25;

    /**
     * MEF 25实测值
     */
    private BigDecimal actualMef25;

    /**
     * MEF 25实测/预计
     */
    private BigDecimal ratioMef25;

    /**
     * MMEF75/25预计值
     */
    private BigDecimal expectedMmef7525;

    /**
     * MMEF75/25实测值
     */
    private BigDecimal actualMmef7525;

    /**
     * MMEF75/25实测/预计
     */
    private BigDecimal ratioMmef7525;

    /**
     * MVV预计值
     */
    private BigDecimal expectedMvv;

    /**
     * MVV实测值
     */
    private BigDecimal actualMvv;

    /**
     * MVV实测/预计
     */
    private BigDecimal ratioMvv;

    /**
     * FEV1*30预计值
     */
    private BigDecimal expectedFev130;

    /**
     * FEV1*30实测值
     */
    private BigDecimal actualFev130;

    /**
     * FEV1*30实测/预计
     */
    private BigDecimal ratioFev130;

    /**
     * RV-SB预计值
     */
    private BigDecimal expectedRvSb;

    /**
     * RV-SB实测值
     */
    private BigDecimal actualRvSb;

    /**
     * RV-SB实测/预计
     */
    private BigDecimal ratioRvSb;

    /**
     * RV%TLC-SB预计值
     */
    private BigDecimal expectedRvTlcSb;

    /**
     * RV%TLC-SB实测值
     */
    private BigDecimal actualRvTlcSb;

    /**
     * RV%TLC-SB实测/预计
     */
    private BigDecimal ratioRvTlcSb;

    /**
     * TLC-SB预计值
     */
    private BigDecimal expectedTlcSb;

    /**
     * TLC-SB实测值
     */
    private BigDecimal actualTlcSb;

    /**
     * TLC-SB实测/预计
     */
    private BigDecimal ratioTlcSb;

    /**
     * FRC-SB预计值
     */
    private BigDecimal expectedFrcSb;

    /**
     * FRC-SB实测值
     */
    private BigDecimal actualFrcSb;

    /**
     * FRC-SB实测/预计
     */
    private BigDecimal ratioFrcSb;

    /**
     * FRC%TLC-SB预计值
     */
    private BigDecimal expectedFrcTlcSb;

    /**
     * FRC%TLC-SB实测值
     */
    private BigDecimal actualFrcTlcSb;

    /**
     * FRC%TLC-SB实测/预计
     */
    private BigDecimal ratioFrcTlcSb;

    /**
     * DLCOc SB预计值
     */
    private BigDecimal expectedDlcocSb;

    /**
     * DLCOc SB实测值
     */
    private BigDecimal actualDlcocSb;

    /**
     * DLCOc SB实测/预计
     */
    private BigDecimal ratioDlcocSb;

    /**
     * DLCOc/VA预计值
     */
    private BigDecimal expectedDlcocVa;

    /**
     * DLCOc/VA实测值
     */
    private BigDecimal actualDlcocVa;

    /**
     * DLCOc/VA实测/预计
     */
    private BigDecimal ratioDlcocVa;

}