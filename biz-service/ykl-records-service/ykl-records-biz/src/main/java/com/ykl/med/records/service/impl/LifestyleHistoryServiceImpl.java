package com.ykl.med.records.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.records.entity.LifestyleHistoryDO;
import com.ykl.med.records.mapper.LifestyleHistoryMapper;
import com.ykl.med.records.service.LifestyleHistoryService;
import com.ykl.med.records.vo.LifestyleHistoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class LifestyleHistoryServiceImpl extends ServiceImpl<LifestyleHistoryMapper, LifestyleHistoryDO> implements LifestyleHistoryService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(Long patientId, List<LifestyleHistoryVO> lifestyleHistories) {
        log.info("保存或更新生活方式历史，参数：{}", JSON.toJSONString(lifestyleHistories));
        if (lifestyleHistories != null) {
            baseMapper.delete(new LambdaQueryWrapper<LifestyleHistoryDO>().eq(LifestyleHistoryDO::getPatientId, patientId));
        }
        if (CollectionUtils.isEmpty(lifestyleHistories)) {
            return;
        }
        baseMapper.insertBatch(CopyPropertiesUtil.copyAndConvertList(lifestyleHistories, LifestyleHistoryDO::new, (source, target) -> target.setPatientId(patientId)));
    }

    @Override
    public List<LifestyleHistoryVO> listByPatientId(Long patientId) {
        List<LifestyleHistoryDO> lifestyleHistoryDOList = this.lambdaQuery().eq(LifestyleHistoryDO::getPatientId, patientId).list();
        return CopyPropertiesUtil.copyList(lifestyleHistoryDOList, LifestyleHistoryVO::new);
    }
}