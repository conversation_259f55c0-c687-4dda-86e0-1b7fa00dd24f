package com.ykl.med.records.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.records.entity.AllergyHistoryDO;
import com.ykl.med.records.mapper.AllergyHistoryMapper;
import com.ykl.med.records.service.AllergyHistoryService;
import com.ykl.med.records.vo.AllergyHistoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AllergyHistoryServiceImpl extends ServiceImpl<AllergyHistoryMapper, AllergyHistoryDO> implements AllergyHistoryService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(Long patientId, List<AllergyHistoryVO> allergyHistories) {
        log.info("保存或更新过敏史，参数：{}", JSON.toJSONString(allergyHistories));
        if (allergyHistories != null) {
            baseMapper.delete(new LambdaQueryWrapper<AllergyHistoryDO>().eq(AllergyHistoryDO::getPatientId, patientId));
        }
        if (CollectionUtils.isEmpty(allergyHistories)) {
            return;
        }
        baseMapper.insertBatch(CopyPropertiesUtil.copyAndConvertList(allergyHistories, AllergyHistoryDO::new, (source, target) -> target.setPatientId(patientId)));
    }

    @Override
    public List<AllergyHistoryVO> listByPatientId(Long patientId) {
        List<AllergyHistoryDO> allergyHistoryDOList = this.lambdaQuery().eq(AllergyHistoryDO::getPatientId, patientId).list();
        return CopyPropertiesUtil.copyList(allergyHistoryDOList, AllergyHistoryVO::new);
    }

    @Override
    public List<AllergyHistoryVO> listByPatientIds(List<Long> patientIds) {
        if(CollectionUtils.isEmpty(patientIds)){
            return new ArrayList<>();
        }
        List<AllergyHistoryDO> allergyHistoryDOList = this.lambdaQuery().in(AllergyHistoryDO::getPatientId, patientIds).list();
        return CopyPropertiesUtil.copyList(allergyHistoryDOList, AllergyHistoryVO::new);
    }
}