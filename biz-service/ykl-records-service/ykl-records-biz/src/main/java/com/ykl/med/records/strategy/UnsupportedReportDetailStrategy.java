package com.ykl.med.records.strategy;

import java.util.Collections;
import java.util.List;

import com.ykl.med.base.utils.SpringContextUtils;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.records.entity.ReportDO;
import com.ykl.med.records.mapper.ReportMapper;
import com.ykl.med.records.util.DynamicFormConverter;
import com.ykl.med.records.vo.ImagingExaminationVO;
import com.ykl.med.records.vo.ReportVO;
import com.ykl.med.records.vo.req.ReportConfirmReqVO;
import com.ykl.med.records.vo.resp.DynamicFormItemRespVO;
import com.ykl.med.records.vo.resp.ReportDetailRespVO;

/**
 * 不支持的报告类型的策略
 * 
 * <AUTHOR>
 */
public class UnsupportedReportDetailStrategy implements ReportDetailStrategy {

    @Override
    public ReportDetailRespVO<?> detail(Long id) {
        ReportDO report = SpringContextUtils.getBean(ReportMapper.class).selectById(id);

        if (report == null) {
            return null;
        }

        return new ReportDetailRespVO<ImagingExaminationVO>()
                .setCategory(report.getCategory())
                .setTime(report.getRecordTime())
                .setErrorMsg(report.getErrorMsg())
                .setId(report.getId());
    }

    @Override
    public List<DynamicFormItemRespVO> detailDynamicForm(ReportDO report) {
        return DynamicFormConverter.convertToJson(CopyPropertiesUtil.copy(report, ReportVO::new));
    }

    @Override
    public void confirm(ReportDO report, ReportConfirmReqVO reqVO) {
    }

    @Override
    public void delete(ReportDO report) {
    }

}
