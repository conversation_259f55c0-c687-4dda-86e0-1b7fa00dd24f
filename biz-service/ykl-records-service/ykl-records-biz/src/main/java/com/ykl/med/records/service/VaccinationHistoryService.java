package com.ykl.med.records.service;

import com.ykl.med.records.vo.VaccinationHistoryVO;

import java.util.List;

/**
 * 接种史
 *
 * <AUTHOR>
 */
public interface VaccinationHistoryService {

    /**
     * 批量保存或更新接种史
     *
     * @param patientId          病人ID
     * @param vaccinationHistories 接种史
     */
    void insertBatch(Long patientId, List<VaccinationHistoryVO> vaccinationHistories);

    /**
     * 根据病人ID获取接种史
     *
     * @param patientId 病人ID
     * @return 接种史
     */
    List<VaccinationHistoryVO> listByPatientId(Long patientId);
}
