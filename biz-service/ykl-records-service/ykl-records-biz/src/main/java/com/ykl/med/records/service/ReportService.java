package com.ykl.med.records.service;

import com.ykl.med.framework.common.pojo.IdReqVO;
import com.ykl.med.records.vo.ReportGroupListVO;
import com.ykl.med.records.vo.ReportVO;
import com.ykl.med.records.vo.req.*;
import com.ykl.med.records.vo.resp.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 报告相关的Service接口
 *
 * <AUTHOR>
 */
public interface ReportService {

    /**
     * 识别一批病历档案文件。
     *
     * @param reqVO 包含批量识别详情的请求对象。
     * @return 识别结果。
     */
    ReportVO recognize(ReportUploadReqVO reqVO);


    /**
     * 识别PDF
     *
     * @param reqVO 包含批量识别详情的请求对象。
     * @return 识别结果。
     */
    ReportVO recognizePDF(ReportUploadPDFReqVO reqVO);

    /**
     * 校验PDF是否重复
     *
     * @param patientId 患者id。
     * @param file      文件
     */
    void checkRecognizePDF(Long patientId, MultipartFile file);

    /**
     * 查询病人的病历档案报告，搜索条件：
     * 分类：影像报告、检验报告、出院病历
     * 档案日期：时间范围
     * 上传日期：时间范围
     *
     * @param reqVO 病历档案列表查询条件reqVO
     * @return 病历档案列表
     */
    List<ReportListRespVO> listReport(ReportListReqVO reqVO);

    List<ReportListRespVO> listReportWeb(ReportListReqVO reqVO);

    List<ReportListRespVO> listReportApp(ReportListReqVO reqVO);

    List<ReportGroupListVO> listReportGroup(ReportListReqVO reqVO);

    /**
     * 检索报告的详细信息。
     *
     * @param reqVO 包含报告详细信息请求的请求对象。
     * @return 报告的详细信息。
     */
    ReportDetailRespVO<?> detail(ReportDetailReqVO reqVO);

    /**
     * 检索报告的详细信息。
     *
     * @param reportId 报告ID
     * @return 报告的详细信息。
     */
    List<DynamicFormItemRespVO> detailDynamicForm(Long reportId);

    /**
     * 根据病历档案ID列表，查询病历档案报告
     *
     * @param recordsIds 病历档案ID列表
     * @return 病历档案报告
     */
    Map<Long, ReportListRespVO> groupingByRecordIds(Collection<Long> recordsIds);

    /**
     * 根据报告ID列表，查询报告
     *
     * @param ids 报告ID列表
     * @return 报告
     */
    Map<Long, ReportListRespVO> groupingByIds(Collection<Long> ids);

    /**
     * 获取识别报告的队列信息。
     *
     * @param patientId 病人ID
     * @return 队列信息
     */
    QueueInfoRespVO getQueueInfo(Long patientId);

    /**
     * 处理下一个待处理的报告
     *
     * @return 是否处理成功
     */
    List<ProcessNextPendingReportRespVO> processNextPendingReport();

    /**
     * 确认病历档案文件。
     *
     * @param reqVO 包含确认详情的请求对象。
     */
    void confirm(ReportConfirmReqVO reqVO);

    /**
     * 删除病历档案文件。
     *
     * @param id 要删除的病历档案文件的id。
     */
    void delete(Long id);

    /**
     * 判断病人是否存在病历档案
     *
     * @param patientId 病人ID
     * @return true 存在 false 不存在
     */
    boolean exist(Long patientId);

    /**
     * 关联报告
     *
     * @param reqVO 关联报告请求对象
     */
    void associate(ReportAssociateReqVO reqVO);

    /**
     * 解除关联报告
     *
     * @param reqVO 解除关联报告请求对象
     */
    void disassociate(ReportDisassociateReqVO reqVO);

    ReportMergeCheckRespVO mergeCheck(ReportMergeReqVO reqVO);

    void merge(ReportMergeReqVO reqVO);


    void reload(IdReqVO reqVO);
}
