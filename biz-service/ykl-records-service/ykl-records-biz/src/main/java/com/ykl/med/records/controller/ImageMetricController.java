package com.ykl.med.records.controller;

import com.ykl.med.records.api.ImageMetricFeign;
import com.ykl.med.records.service.ImageMetricService;
import com.ykl.med.records.vo.metric.image.ImageMetricGroupVO;
import com.ykl.med.records.vo.metric.image.ImageMetricVO;
import com.ykl.med.records.vo.req.QueryImageMetricReqVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 影像学检查指标控制器
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class ImageMetricController implements ImageMetricFeign {

    private final ImageMetricService imageMetricService;

    @Override
    @GetMapping("/imageMetric/list")
    public List<ImageMetricVO> list(@RequestParam("patientId") Long patientId) {
        return imageMetricService.list(patientId);
    }

    @Override
    @GetMapping("/imageMetric/list/group")
    public List<ImageMetricGroupVO> listGroup(@RequestParam("patientId") Long patientId) {
        return imageMetricService.listGroup(patientId, null);
    }

    @Override
    @PostMapping("/imageMetric/list/group")
    public List<ImageMetricGroupVO> listGroup(@RequestBody QueryImageMetricReqVO reqVO) {
        return imageMetricService.listGroup(reqVO.getPatientId(),reqVO.getExaminationType());
    }
}
