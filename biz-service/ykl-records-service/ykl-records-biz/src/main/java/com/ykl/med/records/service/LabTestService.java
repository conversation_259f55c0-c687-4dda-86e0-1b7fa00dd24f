package com.ykl.med.records.service;

import com.ykl.med.records.vo.LabTestListVO;
import com.ykl.med.records.vo.LabTestVO;

import java.util.List;

/**
 * 检验服务接口
 *
 * <AUTHOR>
 */
public interface LabTestService {

    /**
     * 批量保存或更新化验检查
     *
     * @param patientId 病人ID
     * @param labTests  化验检查列表
     */
    void insertBatch(Long patientId, List<LabTestVO> labTests);

    /**
     * 根据病人ID获取化验检查列表
     *
     * @param patientId 病人ID
     * @return 化验检查列表
     */
    List<LabTestVO> listByPatientId(Long patientId);


    List<LabTestListVO> listLabsApp(Long patientId);



    /**
     * 通过报告ID检索LabTestVO对象。
     *
     * @param reportId 报告ID
     * @return 指定报告ID的LabTestVO对象
     */
    LabTestVO getByReportId(Long reportId);

    /**
     * 删除具有给定报告ID的化验检查。
     *
     * @param reportId 报告ID
     */
    void deleteByReportId(Long reportId);
}
