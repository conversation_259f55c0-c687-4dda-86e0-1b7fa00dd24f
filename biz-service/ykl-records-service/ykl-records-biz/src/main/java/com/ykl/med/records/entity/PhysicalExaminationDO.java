package com.ykl.med.records.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

@Data
@TableName("t_physical_examination")
public class PhysicalExaminationDO extends BaseDO {

    private static final long serialVersionUID = -4628751685214018497L;

    /**
     * 病人ID
     */
    private Long patientId;

    /**
     * 检查类别
     */
    private String examinationCategory;

    /**
     * 检查名称
     */
    private String examinationName;

    /**
     * 检查部位
     */
    private String examinationPart;

    /**
     * 检查项目, 如心率、体温等
     */
    private String examinationItem;

    /**
     * 体查结果, 指标值数字、有/无、红润、清等
     */
    private String examinationResult;

    /**
     * 结果单位
     */
    private String resultUnit;

    /**
     * 检查时间
     */
    private String examinationTime;

    /**
     * 检查机构, 健康记录的结果也记录在体检检查内，机构为“自查”
     */
    private String examinationOrganization;
}