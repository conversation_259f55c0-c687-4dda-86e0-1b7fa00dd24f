package com.ykl.med.records;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;


/**
 * 档案服务
 *
 * <AUTHOR>
 */
@EnableCaching
@SpringBootApplication
@MapperScan("com.ykl.med.**.mapper")
@ComponentScan(basePackages = {"com.ykl.med"})
@EnableFeignClients(basePackages = {"com.ykl.med"})
public class RecordsBizApplication {

    public static void main(String[] args) {
        SpringApplication.run(RecordsBizApplication.class, args);
    }
}
