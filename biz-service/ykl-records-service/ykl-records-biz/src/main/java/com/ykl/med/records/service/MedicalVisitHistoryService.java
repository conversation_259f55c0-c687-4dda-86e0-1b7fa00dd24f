package com.ykl.med.records.service;

import com.ykl.med.records.vo.MedicalVisitHistoryVO;

import java.util.List;

public interface MedicalVisitHistoryService {

    /**
     * 批量保存或更新就诊史
     *
     * @param patientId             病人ID
     * @param medicalVisitHistories 就诊史
     */
    void insertBatch(Long patientId, List<MedicalVisitHistoryVO> medicalVisitHistories);

    /**
     * 根据病人ID获取就诊史
     *
     * @param patientId 病人ID
     * @return 就诊史
     */
    List<MedicalVisitHistoryVO> listByPatientId(Long patientId);
}