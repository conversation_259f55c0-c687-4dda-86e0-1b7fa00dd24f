package com.ykl.med.records.service;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.records.vo.recognition.RecognitionResultVO;
import com.ykl.med.records.vo.req.ImageRecognizeReqVO;

import java.util.List;

public interface RecognizeService {

    /**
     * 识别病历档案接口
     *
     * @param reqVO 病历档案文件
     * @return 识别结果
     */
    List<RecognitionResultVO<JSONObject>> recognize(ImageRecognizeReqVO reqVO);
}
