package com.ykl.med.records.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.common.enums.LifestyleStatus;
import com.ykl.med.framework.common.enums.LifestyleType;
import com.ykl.med.records.interfaces.FieldMetadata;
import lombok.Data;

/**
 * 生活习惯史DO
 *
 * <AUTHOR>
 */
@Data
@TableName("t_lifestyle_history")
public class LifestyleHistoryDO extends BaseDO {

    private static final long serialVersionUID = -3170962361563302303L;

    /**
     * 病人ID
     */
    private Long patientId;

    /**
     * 生活方式类型，吸烟或饮酒
     */
    private LifestyleType type;

    /**
     * 生活方式年限
     */
    @FieldMetadata
    private String lifestyleYears;

    /**
     * 每日生活方式量
     */
    @FieldMetadata
    private String dailyAmount;

    /**
     * 生活方式状态
     */
    private LifestyleStatus status;

    /**
     * 戒除生活方式年限
     */
    private String quitYears;
}