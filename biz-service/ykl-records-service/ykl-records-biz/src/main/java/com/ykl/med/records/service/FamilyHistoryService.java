package com.ykl.med.records.service;

import com.ykl.med.records.vo.FamilyHistoryVO;

import java.util.List;

public interface FamilyHistoryService {

    /**
     * 保存或更新家族史
     *
     * @param patientId       病人ID
     * @param familyHistories 家族史
     */
    void insertBatch(Long patientId, List<FamilyHistoryVO> familyHistories);

    /**
     * 根据病人ID查询家族史
     *
     * @param patientId 病人ID
     * @return 家族史
     */
    List<FamilyHistoryVO> listByPatientId(Long patientId);

    /**
     * 根据病人ID查询家族史
     *
     * @param patientIds id集合
     * @return 家族史
     */
    List<FamilyHistoryVO> listByPatientIds(List<Long> patientIds);

}