package com.ykl.med.records.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.util.List;

@Data
@TableName("t_other_examination")
public class OtherExaminationDO extends BaseDO {

    private static final long serialVersionUID = -8081975498807779649L;

    /**
     * 病人ID
     */
    private Long patientId;

    /**
     * 检查类别
     */
    private String examinationCategory;

    /**
     * 检查名称
     */
    private String examinationName;

    /**
     * 检查部位
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> examinationPart;

    /**
     * 检查图片
     */
    private String examinationImage;

    /**
     * 检查所见
     */
    private String examinationFindings;

    /**
     * 检查结论
     */
    private String examinationConclusion;

    /**
     * 检查时间
     */
    private String examinationTime;

    /**
     * 检查机构
     */
    private String examinationOrganization;

}