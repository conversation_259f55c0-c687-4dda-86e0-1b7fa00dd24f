package com.ykl.med.records.strategy;

import com.alibaba.fastjson.JSON;
import com.ykl.med.base.utils.SpringContextUtils;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.vo.dict.DictDataRespVO;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.records.entity.ReportDO;
import com.ykl.med.records.enums.RecognitionStatus;
import com.ykl.med.records.factory.RecognitionResultFactory;
import com.ykl.med.records.mapper.ReportMapper;
import com.ykl.med.records.service.ImagingExaminationService;
import com.ykl.med.records.util.DynamicFormConverter;
import com.ykl.med.records.vo.ImagingExaminationVO;
import com.ykl.med.records.vo.ReportVO;
import com.ykl.med.records.vo.recognition.ImageReportVO;
import com.ykl.med.records.vo.req.ReportConfirmReqVO;
import com.ykl.med.records.vo.resp.DynamicFormItemRespVO;
import com.ykl.med.records.vo.resp.ReportDetailRespVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 影像报告详情策略
 *
 * <AUTHOR>
 */
public class ImageReportDetailStrategy implements ReportDetailStrategy {
    private final ImagingExaminationService imagingExaminationService;

    public ImageReportDetailStrategy(ImagingExaminationService imagingExaminationService) {
        this.imagingExaminationService = imagingExaminationService;
    }

    @Override
    public ReportDetailRespVO<ImagingExaminationVO> detail(Long reportId) {
        ReportDO report = SpringContextUtils.getBean(ReportMapper.class).selectById(reportId);

        if (report == null) {
            return null;
        }

        ImagingExaminationVO data = report.getRecognitionStatus() == RecognitionStatus.SUCCESS
                ? imagingExaminationService.getByReportId(reportId)
                : RecognitionResultFactory.createImagingExamination(report, JSON.parseObject(report.getOriginalResult(), ImageReportVO.class));

        return new ReportDetailRespVO<ImagingExaminationVO>()
                .setCategory(report.getCategory())
                .setData(data)
                .setTime(report.getRecordTime())
                .setErrorMsg(report.getErrorMsg())
                .setId(report.getId());
    }

    @Override
    public List<DynamicFormItemRespVO> detailDynamicForm(ReportDO report) {
        ImagingExaminationVO data = report.getRecognitionStatus() == RecognitionStatus.SUCCESS
                ? imagingExaminationService.getByReportId(report.getId())
                : RecognitionResultFactory.createImagingExamination(report, JSON.parseObject(report.getOriginalResult(), ImageReportVO.class));
        if (StringUtils.isNotBlank(data.getExaminationTime())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.parse(data.getExaminationTime(), formatter);
            data.setExaminationLocalDateTime(localDateTime);
        }

        // 字典转value
        data.setExaminationPart(dictToValue(data.getExaminationPart()));

        List<DynamicFormItemRespVO> dynamicFormItems = DynamicFormConverter.convertToJson(CopyPropertiesUtil.copy(report, ReportVO::new));
        dynamicFormItems.addAll(DynamicFormConverter.convertToJson(data));


        return dynamicFormItems;
    }

    private List<String> dictToValue(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        DictDataFeign dictDataFeign = SpringContextUtils.getBean(DictDataFeign.class);
        List<DictDataRespVO> dictDataByValues = dictDataFeign.getDictDataByValues(list);
        if(CollectionUtils.isEmpty(dictDataByValues)){
            return new ArrayList<>();
        }

        Map<String, String> collect = dictDataByValues.stream().collect(Collectors.toMap(DictDataRespVO::getValue, DictDataRespVO::getLabel));
        return list.stream().map(collect::get).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirm(ReportDO report, ReportConfirmReqVO reqVO) {
        ImagingExaminationVO imagingExamination = reqVO.getImagingExamination() != null ? reqVO.getImagingExamination() : new ImagingExaminationVO()
                .setExaminationCategory("影像报告")
                .setExaminationImages(report.getFileUrls())
                .setExaminationType(reqVO.getSubCategory());
        imagingExamination.setReportId(report.getId());
        imagingExaminationService.deleteByReportId(report.getId());
        imagingExaminationService.insertBatch(report.getPatientId(), Collections.singletonList(imagingExamination));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(ReportDO report) {
        imagingExaminationService.deleteByReportId(report.getId());
    }
}