<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--
    注意：
    需要配置系统参数,(java启动参数)
    -DLOG_PATH=/app/java/ -DAPP_NAME=ykl-websocket-biz
    -->
    <jmxConfigurator/>

    <springProfile name="local">
        <appender class="ch.qos.logback.core.ConsoleAppender" name="CONSOLE">
            <encoder>
                <pattern>[%-5level] %d{HH:mm:ss.SSS} [%thread] %logger.%M - %msg%n</pattern>
            </encoder>
        </appender>
    </springProfile>

    <springProfile name="prod">
        <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="LOGFILE">
            <File>${LOG_PATH}/${APP_NAME}/logs/${APP_NAME}.log</File>
            <rollingPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <FileNamePattern>${LOG_PATH}/${APP_NAME}/logs/${APP_NAME}-%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
                <maxHistory>15</maxHistory>
                <maxFileSize>500MB</maxFileSize>
            </rollingPolicy>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>[%-5level] %d{HH:mm:ss.SSS} [%thread] %logger.%M - %msg%n</pattern>
            </layout>
        </appender>
    </springProfile>


    <springProfile name="pre">
        <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="LOGFILE">
            <File>${LOG_PATH}/${APP_NAME}/logs/${APP_NAME}.log</File>
            <rollingPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <FileNamePattern>${LOG_PATH}/${APP_NAME}/logs/${APP_NAME}-%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
                <maxHistory>15</maxHistory>
                <maxFileSize>500MB</maxFileSize>
            </rollingPolicy>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>[%-5level] %d{HH:mm:ss.SSS} [%thread] %logger.%M - %msg%n</pattern>
            </layout>
        </appender>
    </springProfile>

    <springProfile name="test">
        <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="LOGFILE">
            <File>${LOG_PATH}/${APP_NAME}/logs/${APP_NAME}.log</File>
            <rollingPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <FileNamePattern>${LOG_PATH}/${APP_NAME}/logs/${APP_NAME}-%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
                <maxHistory>15</maxHistory>
                <maxFileSize>500MB</maxFileSize>
            </rollingPolicy>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>[%-5level] %d{HH:mm:ss.SSS} [%thread] %logger.%M - %msg%n</pattern>
            </layout>
        </appender>
    </springProfile>

    <root level="info">
        <springProfile name="local">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <springProfile name="prod">
            <appender-ref ref="LOGFILE"/>
        </springProfile>
        <springProfile name="pre">
            <appender-ref ref="LOGFILE"/>
        </springProfile>
        <springProfile name="test">
            <appender-ref ref="LOGFILE"/>
        </springProfile>
    </root>

</configuration>
