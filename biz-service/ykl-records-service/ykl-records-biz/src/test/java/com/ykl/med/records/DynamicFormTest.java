package com.ykl.med.records;

import com.alibaba.fastjson.JSON;
import com.ykl.med.records.service.ReportService;
import com.ykl.med.records.vo.resp.DynamicFormItemRespVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DynamicFormTest {

    @Resource
    private ReportService reportService;

    @Test
    public void testConvert() {
        List<DynamicFormItemRespVO> dynamicFormItems = reportService.detailDynamicForm(654090749278351360L);
        System.out.println(JSON.toJSONString(dynamicFormItems, true));
    }

}
