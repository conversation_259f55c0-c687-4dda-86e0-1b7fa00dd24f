package com.ykl.med.records.api;

import com.ykl.med.records.vo.BasicReportVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ykl-records-service", path = "/ykl-records-service")
public interface BasicReportFeign {
    @GetMapping("/basicReport/getByPatientId")
    BasicReportVO getByPatientId(@RequestParam("patientId") Long patientId);

    @PostMapping("/basicReport/listByPatientIds")
    List<BasicReportVO> listByPatientIds(@RequestParam("patientIds") List<Long> patientIds);

    @PostMapping("/basicReport/initBasicReportStage")
    void initBasicReportStage();
}
