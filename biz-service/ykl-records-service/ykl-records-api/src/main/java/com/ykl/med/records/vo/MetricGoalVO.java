package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "MetricGoalVO", description = "指标目标")
public class MetricGoalVO implements Serializable {

    private static final long serialVersionUID = 4089324241206653702L;

    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "检查检验项目id")
    @Stringify
    private Long examinationTestProjectId;

    @Schema(description = "标本字典值")
    private String specimen;

    @Schema(description = "患者id")
    @Stringify
    private Long patientId;

    @Schema(description = "指标值")
    private String value;

}
