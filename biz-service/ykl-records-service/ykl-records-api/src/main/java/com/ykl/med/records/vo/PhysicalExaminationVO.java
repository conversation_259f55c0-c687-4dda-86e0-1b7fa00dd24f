package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "PhysicalExaminationVO", description = "体检")
public class PhysicalExaminationVO implements Serializable {

    private static final long serialVersionUID = -6518650446547381444L;

    @Schema(description = "体检ID")
    @Stringify
    private Long id;

    @Schema(description = "患者ID")
    @Stringify
    private Long patientId;

    @Schema(description = "检查类别")
    private String examinationCategory;

    @Schema(description = "检查名称")
    private String examinationName;

    @Schema(description = "检查部位")
    private String examinationPart;

    @Schema(description = "检查项目")
    private String examinationItem;

    @Schema(description = "体检结果")
    private String examinationResult;

    @Schema(description = "结果单位")
    private String resultUnit;

    @Schema(description = "检查时间")
    private String examinationTime;

    @Schema(description = "检查机构")
    private String examinationOrganization;
}