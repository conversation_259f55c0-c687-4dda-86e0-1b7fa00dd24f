package com.ykl.med.records.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "DischargeMedicationVO", description = "出院用药VO")
public class DischargeMedicationVO implements Serializable {

    private static final long serialVersionUID = 2299342755974283083L;

    @Schema(description = "药品名称")
    private String drugName;

    @Schema(description = "单次剂量")
    private String singleDose;

    @Schema(description = "剂量单位")
    private String doseUnit;

    @Schema(description = "给药途径")
    private String administrationRoute;

    @Schema(description = "频次")
    private String frequency;

    @Schema(description = "用药周期")
    private String medicationCycle;

    @Schema(description = "原始文本")
    private String originalText;

}
