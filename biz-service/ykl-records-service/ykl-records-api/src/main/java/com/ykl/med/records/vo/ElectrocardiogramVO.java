package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "ElectrocardiogramVO", description = "心电图")
public class ElectrocardiogramVO implements Serializable {

    private static final long serialVersionUID = -3940882510719370527L;

    @Schema(description = "心电图ID")
    @Stringify
    private Long id;

    @Schema(description = "检查名称")
    private String examinationName;

    @Schema(description = "心电图诊断")
    private String ekgDiagnosis;

    @Schema(description = "心率")
    private String heartRate;

    @Schema(description = "P时限")
    private String pTimeInterval;

    @Schema(description = "PR间期")
    private String prInterval;

    @Schema(description = "QRS时限")
    private String qrsDuration;

    @Schema(description = "QT间期")
    private String qtInterval;

    @Schema(description = "QCT间期")
    private String qtcInterval;

    @Schema(description = "P电轴")
    private String pAxis;

    @Schema(description = "QRS电轴")
    private String qrsAxis;

    @Schema(description = "T电轴")
    private String tAxis;

    @Schema(description = "RV5振幅")
    private String rv5Amplitude;

    @Schema(description = "SV1振幅")
    private String sv1Amplitude;

    @Schema(description = "RV6振幅")
    private String rv6Amplitude;

    @Schema(description = "SV2振幅")
    private String sv2Amplitude;

    @Schema(description = "心电图图片")
    private String ekgImage;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "年龄")
    private String age;

    @Schema(description = "检查时间")
    private String examinationTime;
}