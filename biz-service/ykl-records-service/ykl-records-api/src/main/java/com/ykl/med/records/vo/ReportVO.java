package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.records.enums.RecognitionStatus;
import com.ykl.med.records.enums.ReportCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 识别病历档案接口响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "识别病历档案接口响应")
public class ReportVO implements Serializable {

    private static final long serialVersionUID = -5233144797706179986L;

    @Schema(description = "病历档案ID")
    @Stringify
    private Long id;

    /**
     * 患者id
     */
    @Schema(description = "患者id")
    @Stringify
    private Long patientId;

    /**
     * 病历档案id
     */
    @Schema(description = "病历档案id")
    @Stringify
    private Long recordId;

    /**
     * 病历档案类型
     */
    @Schema(description = "病历档案类型", allowableValues = {"IMAGE", "LAB", "DISCHARGE"}, example = "IMAGE")
    private ReportCategory category;

    /**
     * 识别状态
     */
    @Schema(description = "识别状态", allowableValues = {"SUCCESS", "FAIL", "PROCESSING"}, example = "SUCCESS")
    private RecognitionStatus recognitionStatus;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 文件当前页码
     */
    @Schema(description = "文件当前页码")
    private Integer page;

    /**
     * 档案信息
     */
    @Schema(description = "档案信息")
    private String recordsInfo;

    /**
     * 档案时间
     */
    @Schema(description = "档案时间")
    @TimestampConvert
    private LocalDateTime recordTime;

    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    @TimestampConvert
    private LocalDateTime uploadTime;

    /**
     * 文件地址
     */
    @Schema(description = "文件地址")
    private List<String> fileUrls;

    /**
     * 关联的报告id集合
     */
    @Schema(description = "关联的报告id集合")
    private List<String> relatedReportIds;

    /**
     * 批次id
     */
    @Stringify
    @Schema(description = "批次id")
    private Long batchId;

    /**
     * OCR结果
     */
    @Schema(description = "OCR结果")
    private String ocrResult;


}
