package com.ykl.med.records.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 治疗类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TreatmentType {
    /**
     * 手术史
     */
    SURGERY("手术史"),

    /**
     * 化疗史
     */
    CHEMOTHERAPY("化疗史"),

    /**
     * 放疗史
     */
    RADIOTHERAPY("放疗史"),

    /**
     * 靶向药物史
     */
    TARGETED_THERAPY("靶向药物史"),

    /**
     * 免疫治疗史
     */
    IMMUNOTHERAPY("免疫治疗史"),

    /**
     * 质子治疗史
     */
    PROTON_THERAPY("质子治疗史"),

    /**
     * 生物治疗史
     */
    BIOLOGICAL_THERAPY("生物治疗史"),

    /**
     * 中医治疗史
     */
    TRADITIONAL_CHINESE_MEDICINE("中医治疗史"),

    /**
     * 其他治疗史
     */
    OTHER_THERAPY("其他治疗史");


    private final String desc;
}