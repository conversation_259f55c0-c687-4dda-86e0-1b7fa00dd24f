package com.ykl.med.records.vo.req;

import com.ykl.med.records.enums.ReportCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 批量识别报告请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "批量识别报告请求")
public class BatchRecognizeReportReqVO implements Serializable {

    private static final long serialVersionUID = 2602084838192434416L;

    @Schema(description = "患者id")
    private Long patientId;

    @Schema(description = "报告分类")
    private ReportCategory category;

    @Schema(description = "报告子分类")
    private String subCategory;

    @Schema(description = "关联的报告id")
    private Long relatedReportId;

    @Schema(description = "上传类型()")
    private Integer uploaderType;

    @Schema(description = "上传者id")
    private Long uploaderId;

    @Schema(description = "上传者名称")
    private String uploaderName;

    @Schema(description = "上传者关系")
    private String uploaderRelation;

}
