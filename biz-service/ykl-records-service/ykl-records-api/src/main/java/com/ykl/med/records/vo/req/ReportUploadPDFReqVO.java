package com.ykl.med.records.vo.req;

import com.ykl.med.records.enums.ReportCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025/1/6
 */
@Data
public class ReportUploadPDFReqVO {

    private static final long serialVersionUID = -5415446180300634236L;

    @Schema(description = "患者id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "患者id不能为空")
    private Long patientId;

    @Schema(description = "报告分类")
    private ReportCategory category;

    @Schema(description = "报告子分类")
    private String subCategory;

    @Schema(description = "关联的报告id")
    private Long relatedReportId;

    @Schema(description = "报告文件", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报告文件不能为空")
    private MultipartFile file;

    @Schema(description = "上传用户类型（1-医生、2-患者）")
    private Integer uploaderType;

    @Schema(description = "上传者id")
    private Long uploaderId;

    @Schema(description = "上传者名称")
    private String uploaderName;

    @Schema(description = "上传者关系")
    private String uploaderRelation;

}

