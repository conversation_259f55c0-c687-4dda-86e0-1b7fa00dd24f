package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.records.enums.MetricRecognitionExceptionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 病历档案异常指标
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "RecordsExceptionMetricsVO", description = "病历档案异常指标")
public class ExceptionMetricVO implements Serializable {

    private static final long serialVersionUID = 3907769726667213276L;

    @Schema(description = "异常指标id")
    @Stringify
    private Long id;

    @Schema(description = "创建时间")
    @TimestampConvert
    private LocalDateTime createTime;

    @Schema(description = "指标数据id")
    @Stringify
    private Long metricDataId;

    @Schema(description = "患者id")
    @Stringify
    private Long patientId;

    @Schema(description = "预警等级")
    private Integer level;

    @Schema(description = "预警信息")
    private String message;

    @Schema(description = "自动识别异常类型")
    private MetricRecognitionExceptionType type;

    @Schema(description = "报告id")
    @Stringify
    private Long reportId;
}
