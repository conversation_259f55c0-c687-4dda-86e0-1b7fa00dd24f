package com.ykl.med.records.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.records.enums.TreatmentType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 治疗历史
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "TreatmentHistoryVO", description = "治疗历史")
public class TreatmentHistoryVO implements Serializable {

    private static final long serialVersionUID = -2544545491624891350L;

    @Schema(description = "治疗历史ID")
    @Stringify
    private Long id;

    @Schema(description = "档案报告id")
    @Stringify
    private Long reportId;

    @Schema(description = "病人ID")
    @Stringify
    private Long patientId;

    @Schema(description = "治疗类型")
    private TreatmentType treatmentType;

    @Schema(description = "治疗类型名称")
    private String treatmentTypeName;

    @Schema(description = "治疗方式(目前只有中医治疗需要), 字典值")
    private String treatmentMode;

    @Schema(description = "用法(目前只有中医治疗需要), 字典值")
    private String treatmentUsage;

    @Schema(description = "治疗子类型，当治疗类型为其他时填写")
    private String subType;

    @Schema(description = "治疗名称（手术名称、药物名称、化疗方案等）")
    private String treatmentName;

    @Schema(description = "治疗开始时间")
    @TimestampConvert
    private LocalDateTime treatmentStartTime;

    @Schema(description = "治疗结束时间")
    @TimestampConvert
    private LocalDateTime treatmentEndTime;

    @Schema(description = "治疗疗效（手术效果、化疗效果、放疗效果、药物疗效等）")
    private String treatmentEffect;

    @Schema(description = "治疗并发症或不良反应")
    private String complicationsOrSideEffects;

    @Schema(description = "主刀医生或主管医生")
    private String chiefDoctor;

    @Schema(description = "医院Id")
    @Stringify
    private Long hospitalId;

    @Schema(description = "医院名称")
    private String hospitalName;

    @Schema(description = "是否留置导管（仅化疗相关）")
    private Boolean catheterPlacement;

    @Schema(description = "靶区（仅放疗相关）")
    private String targetArea;

    @Schema(description = "总剂量Gy（仅放疗相关）")
    private String totalDoseGy;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "治疗部位")
    private String treatmentPart;

    @Schema(description = "治疗方法")
    private String treatmentMethod;

    @Schema(description = "厂商名称")
    private String producerName;

    @Schema(description = "单次用量")
    private String onceDosage;

    @Schema(description = "单次用量单位")
    private String onceDosageUnit;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "使用周期（天）")
    private Long usageCycleDays;

    @Schema(description = "用药状态")
    private String medicalOrderStatus;

    @Schema(description = "停药原因")
    private String stopReason;

}