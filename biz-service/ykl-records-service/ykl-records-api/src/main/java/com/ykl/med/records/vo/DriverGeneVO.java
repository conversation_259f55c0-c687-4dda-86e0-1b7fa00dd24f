package com.ykl.med.records.vo;

import com.ykl.med.records.enums.TestResult;
import com.ykl.med.records.interfaces.FieldMetadata;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 驱动基因VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "DriverGeneVO", description = "驱动基因VO")
public class DriverGeneVO implements Serializable {

    private static final long serialVersionUID = 82846042523626718L;

    @Schema(description = "驱动基因字典值")
    @FieldMetadata
    private String dictValue;

    @Schema(description = "驱动基因检查结果", example = "NEGATIVE", allowableValues = {"NEGATIVE", "POSITIVE"})
    private TestResult result;
}