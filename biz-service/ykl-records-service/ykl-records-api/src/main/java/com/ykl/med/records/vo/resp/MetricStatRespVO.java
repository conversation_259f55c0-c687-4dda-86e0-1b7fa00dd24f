package com.ykl.med.records.vo.resp;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.records.vo.MetricDataVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 指标统计
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "MetricStatRespVO", description = "指标统计")
public class MetricStatRespVO implements Serializable {

    private static final long serialVersionUID = -854817083687706074L;

    @Schema(description = "指标id", example = "1")
    @Stringify
    private Long id;

    @Schema(description = "指标名称", example = "血压")
    private String name;

    @Schema(description = "指标单位", example = "mmHg")
    private String unit;

    @Schema(description = "指标参考值", example = "120/80")
    private String referenceValue;

    @Schema(description = "指标数据", example = "[]")
    private List<MetricDataVO> data;

    @Schema(description = "目标值", example = "120/80")
    private String goalValue;

}
