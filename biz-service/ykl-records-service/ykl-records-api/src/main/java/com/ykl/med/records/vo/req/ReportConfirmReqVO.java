package com.ykl.med.records.vo.req;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.records.enums.ReportCategory;
import com.ykl.med.records.vo.DischargeCertificateVO;
import com.ykl.med.records.vo.ImagingExaminationVO;
import com.ykl.med.records.vo.LabTestVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 病历档案确认reqVO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "ReportConfirmReqVO", description = "病历档案确认reqVO")
public class ReportConfirmReqVO implements Serializable {

    private static final long serialVersionUID = 8949083673811223821L;

    @Schema(description = "病历档案id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "档案类型", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private ReportCategory category;

    @Schema(description = "档案子类型")
    private String subCategory;

    @Schema(description = "档案日期(时间戳)")
    @TimestampConvert
    private LocalDateTime time;

    @Schema(description = "影像报告结果")
    private ImagingExaminationVO imagingExamination;

    @Schema(description = "检验检查结果")
    private LabTestVO labTest;

    @Schema(description = "出院证明")
    private DischargeCertificateVO dischargeCertificate;

    @Schema(description = "确认用户id")
    private Long confirmerId;

    @Schema(description = "确认用户名称")
    private String confirmerName;
}
