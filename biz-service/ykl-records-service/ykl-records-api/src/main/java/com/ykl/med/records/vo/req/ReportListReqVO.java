package com.ykl.med.records.vo.req;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.records.enums.RecognitionStatus;
import com.ykl.med.records.enums.ReportCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(name = "PatientRecordsReportReqVO", description = "病历档案列表查询条件reqVO")
public class ReportListReqVO implements Serializable {

    private static final long serialVersionUID = -8267449395261558920L;

    @Schema(description = "患者id")
    private Long patientId;

    @Schema(description = "患者ids")
    private List<Long> patientIds;

    @Schema(description = "分类: 影像报告、检验报告、出院病历, 字典值", example = "影像报告")
    private ReportCategory category;

    @Schema(description = "档案子分类")
    private String subCategory;

    @Schema(description = "识别状态, 字典值", example = "识别成功")
    private RecognitionStatus recognitionStatus;

    @Schema(description = "档案分类列表")
    private List<ReportCategory> categories;

    @Schema(description = "档案子分类列表")
    private List<String> subCategories;

    @Deprecated
    @Schema(description = "是否档案日期升序")
    private Boolean recordTimeAsc;

    @Schema(description = "档案日期范围开始")
    @TimestampConvert
    private LocalDateTime recordTimeStart;

    @Schema(description = "档案日期范围结束")
    @TimestampConvert
    private LocalDateTime recordTimeEnd;

    @Schema(description = "上传日期范围开始")
    @TimestampConvert
    private LocalDateTime uploadTimeStart;

    @Schema(description = "上传日期范围结束")
    @TimestampConvert
    private LocalDateTime uploadTimeEnd;

    @Schema(description = "排序类型(1-档案时间、2-上传时间)")
    private Integer sortType;

    @Schema(description = "排序(ASC-正序正序、DESC-倒序)")
    private String sort;

}