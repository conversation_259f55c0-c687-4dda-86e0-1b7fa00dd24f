package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 出院证明结构化信息
 * 2025-3-13 11:33:28
 */
@Data
public class DischargeHospitalProveDataVO {

    @Schema(description = "出院证明ID")
    @Stringify
    private Long id;

    @Schema(description = "报告id")
    private Long reportId;
    
    @Schema(description = "病人id")
    private Long patientId;
    
    @Schema(description = "患者姓名")
    private String patientName;
    
    @Schema(description = "性别")
    private String gender;
    
    @Schema(description = "年龄")
    private String age;
    
    @Schema(description = "科室")
    private String department;
    
    @Schema(description = "住院号")
    private String admissionNumber;
    
    @Schema(description = "床号")
    private String bedNumber;
    
    @Schema(description = "身份证号")
    private String idCardNumber;
    
    @Schema(description = "联系电话")
    private String contactNumber;
    
    @Schema(description = "入院日期")
    private String admissionDate;
    
    @Schema(description = "出院日期")
    private String dischargeDate;
    
    @Schema(description = "住院天数")
    private String lengthOfStay;
    
    @Schema(description = "入院诊断原文")
    private String admissionDiagnosesRawText;
    
    @Schema(description = "出院诊断原文")
    private String dischargeDiagnosesRawText;
    
    @Schema(description = "入院情况原始文本")
    private String admissionConditionRawText;
    
    @Schema(description = "诊疗经过原始文本")
    private String diagnosisTreatmentProcessRawText;
    
    @Schema(description = "出院情况原始文本")
    private String dischargeConditionRawText;

    @Schema(description = "出院医嘱原始文本")
    private String dischargeInstructionsRawText;

    @Schema(description = "手术记录")
    private List<DischargeSurgeriesVO> surgeries;

    @Schema(description = "诊断信息")
    private DischargeDiagnosesVO diagnosis;

    @Schema(description = "注意事项列表，如：建议出院后休息1月; 注意休息，加强营养")
    private List<String> precautions;

    @Schema(description = "随访建议列表，如：手术后4周返院复查，需咨询后续治疗方案(包括放化疗、免疫治疗、靶向治疗等); 术后定期复査,3-6个月一次")
    private List<String> followUp;

    @Schema(description = "出院带药列表")
    private List<DischargeMedicationVO> dischargeMedications;

}
