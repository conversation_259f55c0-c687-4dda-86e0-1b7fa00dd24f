package com.ykl.med.records.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 取消关联报告请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "取消关联报告请求")
public class ReportDisassociateReqVO implements Serializable {

    private static final long serialVersionUID = -6156546689751742905L;

    @Schema(description = "报告id")
    private Long reportId;

    @Schema(description = "取消关联的报告id")
    private List<String> relatedReportIds;
}
