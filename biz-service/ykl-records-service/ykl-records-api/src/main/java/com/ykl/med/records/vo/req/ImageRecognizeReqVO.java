package com.ykl.med.records.vo.req;

import com.ykl.med.records.enums.OcrTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "识别病历档案接口请求")
public class ImageRecognizeReqVO implements Serializable {

    private static final long serialVersionUID = -7512482367961404837L;

    @Schema(description = "病历档案文件", requiredMode = Schema.RequiredMode.REQUIRED)
    private MultipartFile[] multipartFiles;

    @Schema(description = "病历档案文件", hidden = true)
    private File[] files;

    @Schema(description = "图片上传地址", hidden = true)
    private List<String> fileUrls;

    @Schema(description = "ocr识别类型", hidden = true)
    private OcrTypeEnum ocrType;

}
