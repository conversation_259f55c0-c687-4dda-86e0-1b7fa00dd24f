package com.ykl.med.records.vo.recognition;

import com.ykl.med.records.vo.ReportVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "识别病历档案接口响应")
public class ReportRecognitionRespVO implements Serializable {

    private static final long serialVersionUID = 7241777660135711443L;

    @Schema(description = "病历档案ID")
    private String md5;

    @Schema(description = "病历档案信息")
    private ReportVO report;

}
