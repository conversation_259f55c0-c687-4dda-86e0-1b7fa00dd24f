package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.records.interfaces.FieldMetadata;
import com.ykl.med.records.vo.recognition.InspectDataVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 检验指标VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "LabTestVO", description = "检验指标")
public class LabTestVO implements Serializable {

    private static final long serialVersionUID = 3062766254163086226L;

    @Schema(description = "检验指标ID")
    @Stringify
    private Long id;

    @Schema(description = "患者id")
    @Stringify
    private Long patientId;

    @Schema(description = "检验分类，固定为：检验报告")
    private String testCategory;

    @Schema(description = "检验子类id")
    @FieldMetadata
    @Stringify
    private Long testType;

    @Schema(description = "检验子类名称")
    private String testTypeName;

    @Schema(description = "诊疗项目，取类别为检验的诊疗项目")
    @FieldMetadata
    private String diagnosticItem;

    @Schema(description = "检验标本，取对应检验项目的标本")
    @FieldMetadata
    private String specimen;

    @Schema(description = "检验时间")
    @TimestampConvert
    private LocalDateTime testTime;

    @Schema(description = "档案时间")
    @TimestampConvert
    private LocalDateTime recordTime;

    @Schema(description = "检验指标项")
    private List<LabTestItemVO> labTestItems;

    @Schema(description = "医疗机构")
    private String institution;

    @Schema(description = "检验报告ID")
    @Stringify
    private Long reportId;

    @Schema(description = "不存在于我们数据库里面的，由AI提取到的指标信息")
    private List<InspectDataVO.RemainingTestProVO> allLastRemainingTestPro;

    @Schema(description = "可能存在于我们数据库里面的其他子类的指标信息")
    private List<InspectDataVO.RemainingTestProVO> remainingTestPro;


}