package com.ykl.med.records.vo;

import com.ykl.med.records.vo.resp.ReportListRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@Data
public class ReportGroupListVO {

    @Schema(description = "类型名称")
    private String name;

    @Schema(description = "报告集合")
    private List<ReportListRespVO> data;

    public ReportGroupListVO() {
    }

    public ReportGroupListVO(String name, List<ReportListRespVO> data) {
        this.name = name;
        this.data = data;
    }
}
