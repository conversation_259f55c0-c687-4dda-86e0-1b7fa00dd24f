package com.ykl.med.records.constants;

import com.ykl.med.framework.common.exception.ErrorCode;

public interface RecordErrorCode {

    ErrorCode METRIC_GOAL_DUPLICATE = new ErrorCode(280001, "指标目标已存在");
    ErrorCode RECOGNIZE_FAILED = new ErrorCode(280002, "识别失败");
    ErrorCode ILLIGAL_ARGUMENT = new ErrorCode(280003, "非法参数");
    ErrorCode REPORT_FILE_URL_DUPLICATED_FAIL = new ErrorCode(280004, "您已上传过此文件，请勿重复上传，识别失败的文件如果要重新识别，请先删除原档案文件");
    ErrorCode REPORT_FILE_URL_DUPLICATED_SUCCESS = new ErrorCode(280005, "您已上传过此文件，请勿重复上传");
    ErrorCode ONLY_LAB_REPORT_CAN_BE_MERGE = new ErrorCode(280006, "只有检验报告才能合并");
    ErrorCode ONLY_SUCCESS_OR_WAITING_CONFIRM_CAN_BE_MERGE = new ErrorCode(280007, "只有识别成功/待确认才能合并");
    ErrorCode REPORT_MERGE_FILE_MAX_TEN = new ErrorCode(280008, "合并选择文件最多只能选择10个");
    ErrorCode PDF_SPLIT_IMG_IS_NULL = new ErrorCode(280009, "PDF拆分图片失败");
    ErrorCode PDF_REPORT_FILE_DUPLICATED_FAIL = new ErrorCode(280010, "PDF中有图片和之前上传的重复，是否确认继续上传？");
    ErrorCode SUBCATEGORY_IS_NULL = new ErrorCode(280011, "请填写档案子类");
    ErrorCode RELOAD_STATUS_ERROR = new ErrorCode(280012, "只有【待确认】或【识别失败】的报告可以重新识别");
    ErrorCode REPORT_IS_NULL = new ErrorCode(280013, "档案报告不存在");



}
