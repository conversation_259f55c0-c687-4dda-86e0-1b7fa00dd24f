package com.ykl.med.records.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "DiagnosisOpinionVO", description = "诊断意见")
public class DiagnosisOpinionVO implements Serializable {

    private static final long serialVersionUID = 7372760112081752131L;

    /**
     * 诊断
     */
    @Schema(description = "诊断")
    private String diagnosis;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String state;

    /**
     * 变化
     */
    @Schema(description = "变化")
    private String change;

}