package com.ykl.med.records.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/13
 */
@Data
public class DischargeMedicationsVO {

    @Schema(description = "药物名称")
    private String drugName;

    @Schema(description = "单次剂量")
    private String singleDose;

    @Schema(description = "剂量单位")
    private String doseUnit;

    @Schema(description = "给药途径")
    private String administrationRoute;

    @Schema(description = "频次")
    private String frequency;

    @Schema(description = "用药周期")
    private String medicationCycle;

}
