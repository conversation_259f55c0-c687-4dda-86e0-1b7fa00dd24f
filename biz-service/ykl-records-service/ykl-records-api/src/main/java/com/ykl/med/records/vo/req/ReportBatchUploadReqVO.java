package com.ykl.med.records.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Data
@Schema(description = "批量上传报告接口请求")
public class ReportBatchUploadReqVO implements Serializable {

    private static final long serialVersionUID = 7533764501722804789L;

    @Schema(description = "患者id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long patientId;

    @Schema(description = "报告文件", requiredMode = Schema.RequiredMode.REQUIRED)
    private MultipartFile[] files;
}