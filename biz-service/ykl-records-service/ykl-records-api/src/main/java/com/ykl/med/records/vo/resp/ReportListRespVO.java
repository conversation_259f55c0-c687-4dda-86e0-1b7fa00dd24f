package com.ykl.med.records.vo.resp;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.records.enums.RecognitionStatus;
import com.ykl.med.records.enums.ReportCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 病历档案列表响应结果VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "ReportListRespVO", description = "病历档案列表响应结果VO")
public class ReportListRespVO implements Serializable {

    private static final long serialVersionUID = 1242470511830208714L;

    @Schema(description = "病历档案ID", example = "1")
    @Stringify
    private Long id;

    @Stringify
    @Schema(description = "患者码", defaultValue = "100043")
    private Long patientId;

    @Schema(description = "识别状态, 字典值", example = "识别成功")
    private RecognitionStatus recognitionStatus;

    @Schema(description = "分类: 影像报告、检验报告、出院病历, 字典值", example = "影像报告")
    private ReportCategory category;

    @Schema(description = "分类: 影像报告、检验报告、出院病历, 字典值")
    private String categoryName;

    @Schema(description = "子分类，CT，磁共振等的字典值")
    private String subCategory;

    @Schema(description = "子分类名称")
    private String subCategoryName;

    @Schema(description = "文件格式", example = "pdf")
    private String fileType;

    @Schema(description = "文件总页数", example = "3")
    private Integer totalPages;

    @Schema(description = "档案信息")
    private String recordsInfo;

    @Schema(hidden = true)
    private Boolean recordsTimeExistFlag;

    @Schema(description = "档案时间", example = "2021-01-01 00:00:00")
    @TimestampConvert
    private LocalDateTime recordsTime;

    @Schema(description = "上传时间", example = "2021-01-01 00:00:00")
    @TimestampConvert
    private LocalDateTime uploadTime;

    @Schema(description = "文件地址", example = "http://www.baidu.com")
    private List<String> fileUrls;

    @Schema(description = "消息")
    private String errorMsg;

    @Schema(description = "批次")
    @Stringify
    private Long batchId;

    @Schema(description = "原始文件，PDF识别，保存PDF原文件")
    private String originalFile;

    @Schema(description = "检查机构（医疗机构）")
    private String examinationOrganization;

    @Schema(description = "上传用户Id")
    @Stringify
    private Long uploaderId;

    @Schema(description = "上传用户名称")
    private String uploaderName;

    @Schema(description = "关系（字典）")
    private String uploaderRelation;

    @Schema(description = "确认用户id")
    @Stringify
    private Long confirmerId;

    @Schema(description = "确认用户名称")
    private String confirmerName;

    @Schema(description = "OCR结果")
    private String ocrResult;

}
