package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.records.enums.AllergyCategory;
import com.ykl.med.records.interfaces.FieldMetadata;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@Schema(name = "AllergyHistoryVO", description = "过敏史")
public class AllergyHistoryVO implements Serializable {

    private static final long serialVersionUID = 1838091148797899604L;

    @Schema(description = "过敏史ID")
    @Stringify
    private Long id;

    /**
     * 病人ID
     */
    @Schema(description = "病人ID")
    @Stringify
    private Long patientId;

    /**
     * 过敏类别
     */
    @Schema(description = "过敏类别", example = "FOOD_ALLERGY")
    private AllergyCategory category;

    /**
     * 过敏物质，药物/食物/粉尘等
     */
    @Schema(description = "过敏物质", example = "花生")
    @FieldMetadata
    private String allergen;

    /**
     * 过敏发生时间
     */
    @Schema(description = "过敏发生时间(时间戳)", example = "1617235200000")
    @TimestampConvert
    private LocalDate allergyDate;

    /**
     * 过敏表现，皮肤瘙痒、咳嗽等
     */
    @Schema(description = "过敏表现", example = "皮肤瘙痒、咳嗽")
    @FieldMetadata
    private List<AllergySymptomsVO> allergySymptoms;

    @Data
    public static class AllergySymptomsVO{
        /*** 症状id */
        @Stringify
        private Long id;
        /*** 症状名称 */
        private String name;
    }

}