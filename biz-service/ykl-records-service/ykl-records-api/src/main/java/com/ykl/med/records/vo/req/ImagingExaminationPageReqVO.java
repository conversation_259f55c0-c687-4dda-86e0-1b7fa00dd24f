package com.ykl.med.records.vo.req;

import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Schema(name = "ImagingExaminationPageReqVO", description = "影像学检查分页查询请求VO")
public class ImagingExaminationPageReqVO extends PageParam {

    private static final long serialVersionUID = 8144980184806962547L;

    @Schema(description = "患者id", example = "100004", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "患者id不能为空")
    private Long patientId;

    @Schema(description = "检查类别", example = "CT")
    private String examinationCategory;

    @Schema(description = "开始日期(时间戳)", example = "1629993600000")
    private LocalDateTime startTime;

    @Schema(description = "结束日期(时间戳)", example = "1630684800000")
    private LocalDateTime endTime;

    @Schema(description = "报告时间升序", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报告时间升序不能为空")
    private Boolean reportTimeAsc;

}
