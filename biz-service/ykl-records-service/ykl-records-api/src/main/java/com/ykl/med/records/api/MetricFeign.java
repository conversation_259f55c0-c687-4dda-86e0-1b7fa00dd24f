package com.ykl.med.records.api;

import com.ykl.med.records.vo.ExceptionMetricVO;
import com.ykl.med.records.vo.FollowedMetricStatisticsVO;
import com.ykl.med.records.vo.MetricDataVO;
import com.ykl.med.records.vo.MetricGoalVO;
import com.ykl.med.records.vo.req.*;
import com.ykl.med.records.vo.resp.MetricDefinitionRespVO;
import com.ykl.med.records.vo.resp.MetricStatRespVO;
import com.ykl.med.records.vo.resp.MultiMetricDataListRespVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 指标管理Feign接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "ykl-records-service", path = "/ykl-records-service")
public interface MetricFeign {
    @PostMapping("/metric/followedDefinition")
    List<MetricDefinitionRespVO> followedDefinition(@RequestBody FollowedMetricDefinitionReqVO reqVO);

    @PostMapping("/metric/allDefinition")
    List<MetricDefinitionRespVO> allDefinition(@RequestBody MetricDefinitionReqVO reqVO);

    @PostMapping("/metric/listFollowed")
    List<MetricDataVO> listFollowed(@RequestBody MetricListReqVO reqVO);

    @PostMapping("/metric/setFollowedMetrics")
    void setFollowedMetrics(@RequestBody RecordsMetricsFollowedSaveReqVO reqVO);

    @PostMapping("/metric/listException")
    List<MetricDataVO> listException(@RequestBody MetricListReqVO reqVO);

    @PostMapping("/metric/listOther")
    List<MetricDataVO> listOther(@RequestBody MetricListReqVO reqVO);

    // 查询记录了数据的指标
    @PostMapping("/metric/listRecorded")
    List<MetricDataVO> listRecorded(@RequestBody MetricListReqVO reqVO);

    @PostMapping("/metric/record")
    void record(@RequestBody MetricRecordReqVO reqVO);

    @PostMapping("/metric/stat")
    MetricStatRespVO stat(@RequestBody MetricStatReqVO reqVO);

    @PostMapping("/metric/unfollowMetric")
    void unfollowMetric(@RequestBody UnfollowMetricReqVO reqVO);

    @PostMapping("/metric/followMetric")
    void followMetric(@RequestBody PatientMetricReqVO reqVO);

    @PostMapping("/metric/batchFollowMetrics")
    void batchFollowMetrics(@RequestBody BatchFollowMetricsReqVO reqVO);

    @PostMapping("/metric/data/delete")
    void delete(@RequestParam("metricDataId") Long metricDataId);

    @PostMapping("/metric/data/detail")
    MetricDataVO detail(@RequestParam("metricDataId") Long metricDataId);

    @PostMapping("/metric/data/list")
    List<MetricDataVO> listData(@RequestBody MetricDataListReqVO reqVO);

    @PostMapping("/metric/data/multiList")
    List<MultiMetricDataListRespVO> multiListData(@RequestBody MultiMetricDataListReqVO reqVO);

    @PostMapping("/metric/goal/saveOrUpdate")
    void saveOrUpdateGoal(@RequestBody MetricGoalVO reqVO);

    @PostMapping("/metric/goal/detail")
    MetricGoalVO detailGoal(@RequestBody PatientMetricReqVO reqVO);

    @PostMapping("/metric/listRecordedDailyMonitor")
    List<MetricDataVO> listRecordedDailyMonitor(@RequestBody MetricListReqVO reqVO);

    @PostMapping("/metric/updateExceptionMetric")
    void updateExceptionMetric(@RequestBody ExceptionMetricVO reqVO);

    @PostMapping("/followed/metric/statistics")
    List<FollowedMetricStatisticsVO> followedMetricStatistics(@RequestParam(name = "patientId") Long patientId);
}
