package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Schema(name = "Surgery<PERSON>", description = "手术VO")
public class SurgeryVO implements Serializable {

    private static final long serialVersionUID = -250032895831960737L;

    @Schema(description = "手术名称")
    private String name;

    @Schema(description = "手术时间")
    @TimestampConvert
    private LocalDateTime time;

    @Schema(description = "术中冰冻，如：右肺上叶尖段结节>冰冻切片评估至少微浸润性腺癌")
    private String intraoperativeFrozenSection;

    @Schema(description = "术中情况，如：手术顺利")
    private String intraoperativeCondition;

    @Schema(description = "术后情况，如：术后给予抗感染化痰、止痛、抗凝等对症支持治疗。")
    private String postoperativeCondition;
}
