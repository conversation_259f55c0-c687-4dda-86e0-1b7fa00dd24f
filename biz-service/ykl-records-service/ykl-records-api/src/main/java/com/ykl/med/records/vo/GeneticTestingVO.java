package com.ykl.med.records.vo;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(name = "GeneticTestingVO", description = "基因检测")
public class GeneticTestingVO implements Serializable {

    private static final long serialVersionUID = -9032088271759168172L;

    @Schema(description = "基因检测ID")
    @Stringify
    private Long id;

    @Schema(description = "病人ID")
    @Stringify
    private Long patientId;

    @Schema(description = "诊疗项目")
    private String diagnosticItem;

    @Schema(description = "样本类型")
    private String sampleType;

    @Schema(description = "取样方式")
    private String samplingMethod;

    @Schema(description = "取材部位")
    private String samplingSite;

    @Schema(description = "PD-L1表达")
    private String pdL1Expression;

    @Schema(description = "MMR")
    private String mmr;

    @Schema(description = "靶向药物列表")
    private String targetDrugs;

    @Schema(description = "疗效可能较好的药物")
    private String likelyEffectiveDrugs;

    @Schema(description = "毒副作用可能较小的药物")
    private String likelyLessSideEffectsDrugs;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "年龄")
    private String age;

    @Schema(description = "测试日期")
    private String testDate;

    @Schema(description = "驱动基因")
    private List<DriverGeneVO> driverGenes;

    @Schema(description = "非驱动基因")
    private List<DriverGeneVO> nonDriverGenes;

}