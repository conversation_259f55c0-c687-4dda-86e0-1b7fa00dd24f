package com.ykl.med.records.vo.req;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.records.enums.TreatmentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 诊疗日志分页查询请求VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "TreatmentHistoryPageReqVO", description = "诊疗日志分页查询请求VO")
public class TreatmentHistoryPageReqVO extends PageParam {

    private static final long serialVersionUID = -3871659705174956020L;

    @Schema(description = "患者id", example = "100004", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "患者id不能为空")
    private Long patientId;

    @Schema(description = "诊疗日志类型", example = "SURGERY")
    private TreatmentType treatmentType;

    @Schema(description = "开始日期(时间戳)", example = "1629993600000")
    @TimestampConvert
    private LocalDateTime startTime;

    @Schema(description = "结束日期(时间戳)", example = "1630684800000")
    @TimestampConvert
    private LocalDateTime endTime;

    @Schema(description = "诊疗时间升序", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "诊疗时间升序不能为空")
    private Boolean treatmentTimeAsc;

}
