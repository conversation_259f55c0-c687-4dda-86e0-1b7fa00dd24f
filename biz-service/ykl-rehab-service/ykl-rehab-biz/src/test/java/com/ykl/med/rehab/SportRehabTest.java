package com.ykl.med.rehab;

import com.alibaba.fastjson.JSON;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.service.sport.SportRehabPlanItemExecuteService;
import com.ykl.med.rehab.service.sport.SportRehabPlanService;
import com.ykl.med.rehab.vo.req.ItemExecStatReqVO;
import com.ykl.med.rehab.vo.req.RehabPlanExecuteQueryReqVO;
import com.ykl.med.rehab.vo.req.RehabPlanStatReqVO;
import com.ykl.med.rehab.vo.req.sport.SportRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanExecuteRecordRespVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SportRehabTest {

    @Resource
    private SportRehabPlanService sportRehabPlanService;

    @Resource
    private SportRehabPlanItemExecuteService sportRehabPlanItemExecuteService;

    @Test
    public void testListPlan() {
        System.out.println(JSON.toJSONString(sportRehabPlanService.list(100004L), true));
    }

    @Test
    public void testGenerateExecute() {
        sportRehabPlanItemExecuteService.generateExecuteBatch(Arrays.asList(713166371526344704L, 713162449889865728L));
    }

    @Test
    public void testCountExecute() {
        System.out.println(JSON.toJSONString(sportRehabPlanItemExecuteService.count(new RehabPlanStatReqVO()
                .setPlanId(578399635354324992L).setDate("2023-12-05")), true));
    }

    @Test
    public void testSaveOrUpdatePlan() {
        ArrayList<SportRehabPlanSaveOrUpdateReqVO.SportRehabPlanItemSaveOrUpdateReqVO> items = new ArrayList<>();
        items.add(new SportRehabPlanSaveOrUpdateReqVO.SportRehabPlanItemSaveOrUpdateReqVO()
                .setTemplateId(1L)
                .setSort(1)
                .setFrequencyId(1L)
                .setMaxFrequencyId(1L)
                .setActionsPerSession(9)
                .setSetsPerSession(3)
                .setSort(1)
                .setIntensity("非常强")
        );

        sportRehabPlanService.saveOrUpdate(new SportRehabPlanSaveOrUpdateReqVO()
                .setPatientId(1L)
                .setTemplateId(1L)
                .setGoal("通过训练提高肺功能能力、运动耐力")
                .setDuration(3)
                .setDurationUnit("月")
                .setStartTime(LocalDateTime.now())
                .setEndTime(LocalDateTime.now().plusMonths(3))
                .setItems(items)
                .setSuitable(true)
        );
        testListPlan();
    }

    @Test
    public void testGetWeeklyStatistics() {
        RehabPlanWeeklyStatRespWrapperVO weeklyStatistics = sportRehabPlanItemExecuteService.getWeeklyStatistics(
                new ItemExecStatReqVO().setPatientId(100004L)
        );
        System.out.println(JSON.toJSONString(weeklyStatistics, true));
    }

    @Test
    public void testQueryExecuteRecords() {
        RehabPlanExecuteQueryReqVO reqVO = new RehabPlanExecuteQueryReqVO()
                .setPlanId(577408250673344512L);
        reqVO.setPageNo(1);
        reqVO.setPageSize(10);
        PageResult<SportRehabPlanExecuteRecordRespVO> pageResult = sportRehabPlanItemExecuteService.queryExecuteRecords(reqVO);
        System.out.println(JSON.toJSONString(pageResult, true));
    }

}