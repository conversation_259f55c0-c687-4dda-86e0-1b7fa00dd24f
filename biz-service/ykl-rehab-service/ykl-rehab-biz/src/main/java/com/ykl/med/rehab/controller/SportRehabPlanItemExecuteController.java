package com.ykl.med.rehab.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.api.SportRehabPlanItemExecuteFeign;
import com.ykl.med.rehab.service.sport.SportRehabPlanItemExecuteService;
import com.ykl.med.rehab.vo.req.ItemExecStatReqVO;
import com.ykl.med.rehab.vo.req.RehabPlanExecuteQueryReqVO;
import com.ykl.med.rehab.vo.req.RehabPlanExecuteTimeRangeQueryReqVO;
import com.ykl.med.rehab.vo.req.RehabPlanItemExecSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanExecuteRecordRespVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequiredArgsConstructor
public class SportRehabPlanItemExecuteController implements SportRehabPlanItemExecuteFeign {

    private final SportRehabPlanItemExecuteService sportRehabPlanItemExecuteService;

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/weeklyStat")
    public RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(@RequestBody ItemExecStatReqVO reqVO) {
        return sportRehabPlanItemExecuteService.getWeeklyStatistics(reqVO);
    }

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/queryExecuteRecords")
    public PageResult<SportRehabPlanExecuteRecordRespVO> queryExecuteRecords(@Valid @RequestBody RehabPlanExecuteQueryReqVO queryReqVO) {
        return sportRehabPlanItemExecuteService.queryExecuteRecords(queryReqVO);
    }

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/update")
    public void updateExecuteRecord(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        sportRehabPlanItemExecuteService.updateExecuteRecord(reqVO);
    }

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/delete")
    public void deleteExecuteRecord(@RequestParam("id") Long id) {
        sportRehabPlanItemExecuteService.deleteExecuteRecord(id);
    }

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/queryByTimeRange")
    public Map<String, List<SportRehabPlanExecuteRecordRespVO>> queryExecuteRecordsByTimeRange(@Valid @RequestBody RehabPlanExecuteTimeRangeQueryReqVO reqVO) {
        return sportRehabPlanItemExecuteService.queryExecuteRecordsByTimeRange(reqVO);
    }

}