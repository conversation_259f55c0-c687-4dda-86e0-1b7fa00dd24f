package com.ykl.med.rehab.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.api.FuncRehabFeign;
import com.ykl.med.rehab.service.func.FuncRehabBasePlanService;
import com.ykl.med.rehab.service.func.FuncRehabPlanItemExecuteService;
import com.ykl.med.rehab.service.func.FuncRehabPlanService;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.func.FuncRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.FormAndSnapshotRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanDetailRespVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

@RestController
@RequiredArgsConstructor
public class FuncRehabController implements FuncRehabFeign {

    private final FuncRehabPlanService funcRehabPlanService;

    private final FuncRehabPlanItemExecuteService funcRehabPlanItemExecuteService;

    private final FuncRehabBasePlanService funcRehabBasePlanService;

    @PostMapping("/funcRehab/plan/getDefaultForm")
    public Long getDefaultForm() {
        return funcRehabBasePlanService.getDefaultForm();
    }

    @PostMapping("/funcRehab/plan/getBasePlanReq")
    public FuncRehabPlanSaveOrUpdateReqVO getBasePlanReq(@RequestParam("patientId") Long patientId){
        return funcRehabBasePlanService.getBasePlanReq(patientId);
    }

    @PostMapping("/funcRehab/plan/list")
    public List<FuncRehabPlanDetailRespVO> listPlan(@RequestParam("patientId") Long patientId) {
        return funcRehabPlanService.list(patientId);
    }


    @Override
    @PostMapping("/funcRehab/plan/saveOrUpdate")
    public Long saveOrUpdate(@Valid @RequestBody FuncRehabPlanSaveOrUpdateReqVO reqVO) {
        return funcRehabPlanService.saveOrUpdate(reqVO);
    }

    @Override
    @PostMapping("/funcRehab/plan/getCurrentPlan")
    public FuncRehabPlanDetailRespVO getCurrentPlan(@RequestParam("patientId") Long patientId) {
        return funcRehabPlanService.getCurrentPlan(patientId);
    }

    @Override
    @PostMapping("/funcRehab/plan/itemExecute/count")
    public List<RehabPlanExecuteCountRespVO> count(@Valid @RequestBody RehabPlanStatReqVO reqVO) {
        return funcRehabPlanItemExecuteService.count(reqVO);
    }

    @Override
    @PostMapping("/funcRehab/plan/itemExecute/execute")
    public void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        funcRehabPlanItemExecuteService.execute(reqVO);
    }

    @Override
    @PostMapping("/funcRehab/plan/itemExecute/generateExecute")
    public void generateExecute(@RequestParam("planId") Long planId) {
        funcRehabPlanItemExecuteService.generateExecute(planId, false);
    }

    @Override
    @PostMapping("/funcRehab/plan/itemExecute/generateExecuteBatch")
    public void generateExecuteBatch(@RequestBody Collection<Long> planIds) {
        funcRehabPlanItemExecuteService.generateExecuteBatch(planIds);
    }

    @Override
    @PostMapping("/funcRehab/plan/getHistoryPlans")
    public List<FuncRehabPlanDetailRespVO> getHistoryPlans(@RequestBody HistoryPlanReqVO reqVO) {
        return funcRehabPlanService.getHistoryPlans(reqVO);
    }

    @Override
    @PostMapping("/funcRehab/plan/extend")
    public void extendPlan(@RequestBody ExtendPlanReqVO reqVO) {
        funcRehabPlanService.extendPlan(reqVO);
    }

    @Override
    @PostMapping("/funcRehab/plan/reEvaluate")
    public void reEvaluate(@RequestParam("patientId") Long patientId) {
        funcRehabPlanService.reEvaluate(patientId);
    }

    @Override
    @PostMapping("/funcRehab/plan/getDraftPlan")
    public FuncRehabPlanDetailRespVO getDraftPlan(@RequestParam("patientId") Long patientId) {
        return funcRehabPlanService.getDraftPlan(patientId);
    }

    @Override
    @PostMapping("/funcRehab/plan/getFormAndSnapshots")
    public List<FormAndSnapshotRespVO> getFormAndSnapshots(@RequestBody FormAndSnapshotReqVO reqVO) {
        return funcRehabPlanService.getFormAndSnapshots(reqVO);
    }

    @Override
    @PostMapping("/funcRehab/plan/getPlanById")
    public FuncRehabPlanDetailRespVO getPlanById(@RequestBody PatientIdAndPlanIdVO reqVO) {
        return funcRehabPlanService.getPlanById(reqVO);
    }
    @PostMapping("/funcRehab/plan/getById")
    @Override
    public FuncRehabPlanDetailRespVO getById(@RequestParam("id") Long id){
        return funcRehabPlanService.getById(id);
    }

    @Override
    @PostMapping("/funcRehab/plan/getAllCurrentPlanIds")
    public PageResult<Long> getAllCurrentPlanIds(@RequestBody PageParam pageParam) {
        IPage<Long> page = funcRehabPlanService.getAllCurrentPlanIds(pageParam);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @PostMapping("/funcRehab/plan/getAllHasPlanPatientIds")
    @Override
    public List<Long> getAllHasPlanPatientIds(){
        return funcRehabPlanService.getAllHasPlanPatientIds();
    }

    @PostMapping("/funcRehab/plan/getAllIds")
    @Override
    public List<Long> getAllIds(){
        return funcRehabPlanService.getAllIds();
    }

    @Override
    @PostMapping("/funcRehab/plan/planItemDetails")
    public FuncRehabPlanDetailRespVO.PlanItem planItemDetails(@RequestParam(name = "planItemId") Long planItemId) {
        return funcRehabPlanService.planItemDetails(planItemId);
    }
}