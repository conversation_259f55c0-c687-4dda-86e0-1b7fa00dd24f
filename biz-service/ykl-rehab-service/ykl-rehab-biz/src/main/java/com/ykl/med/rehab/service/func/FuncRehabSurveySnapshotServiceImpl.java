package com.ykl.med.rehab.service.func;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.constants.topic.RehabMangeTopic;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.masterdata.api.FormFeign;
import com.ykl.med.masterdata.vo.FormVO;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.ToDoMessageFeign;
import com.ykl.med.push.constans.MessageConstants;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.enums.ToDoMessageType;
import com.ykl.med.push.vo.message.BizMessageBaseVO;
import com.ykl.med.push.vo.message.MessageSendPatientReqVO;
import com.ykl.med.push.vo.todo.ToDoMessageReqVO;
import com.ykl.med.rehab.entity.FuncRehabSurveySnapshotDO;
import com.ykl.med.rehab.enums.CompleteStatus;
import com.ykl.med.rehab.enums.RehabType;
import com.ykl.med.rehab.mapper.FuncRehabPlanMapper;
import com.ykl.med.rehab.mapper.FuncRehabSurveySnapshotMapper;
import com.ykl.med.rehab.util.FormUtils;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabSurveySnapshotVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 患者功能康复问卷快照Service实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FuncRehabSurveySnapshotServiceImpl
        extends ServiceImpl<FuncRehabSurveySnapshotMapper, FuncRehabSurveySnapshotDO>
        implements FuncRehabSurveySnapshotService {

    @Resource
    private MessageFeign messageFeign;

    @Resource
    private FormFeign formFeign;

    private final IdServiceImpl idService;

    private final FuncRehabPlanMapper funcRehabPlanMapper;

    private final ToDoMessageFeign toDoMessageFeign;
    private final RocketMQTemplate rocketMqTemplate;
    private final FuncRehabSurveySnapshotMapper funcRehabSurveySnapshotMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void send(RehabSurveySnapshotSendReqVO reqVO) {
        log.info("发送功能康复问卷快照并发送消息通知患者及其家属，reqVO: {}", JSON.toJSONString(reqVO));
        FormVO formVO = fetchFormDetails(reqVO);
        Long patientId = reqVO.getPatientId();
        long snapshotId = createAndSaveSnapshot(formVO, patientId);

        MessageSendPatientReqVO messageSendReqVO = new MessageSendPatientReqVO()
                .setMsg(StringUtils.EMPTY)
                .setType(MessageType.FUNCTIONAL_FORM)
                .setExtra(new BizMessageBaseVO()
                        .setBizId(String.valueOf(snapshotId))
                        .setBizName(formVO.getLabel())
                        .setBizType(formVO.getCategory()))
                .setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString());
        messageSendReqVO.setCurrentUserId(MessageConstants.SYSTEM_USER);
        messageFeign.sendMessagePatient(messageSendReqVO);

        toDoMessageFeign.addToDoMessage(new ToDoMessageReqVO()
                .setContent(formVO.getLabel())
                .setOutBizId(String.valueOf(snapshotId))
                .setPatientId(patientId)
                .setType(ToDoMessageType.FUNCTIONAL_FORM));

        //康复管理es变动
        RehabManageMqMessageVO rehabManageMessage = new RehabManageMqMessageVO()
                .setPatientId(patientId)
                .setRehabType(RehabType.SPORT);
        rocketMqTemplate.syncSend(RehabMangeTopic.TOPIC, rehabManageMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void complete(RehabSurveySnapshotCompleteReqVO reqVO) {
        log.info("填写表单快照，reqVO: {}", JSON.toJSONString(reqVO));
        FormUtils.calcScoreAndResult(reqVO.getSnapshot());
        FuncRehabSurveySnapshotDO snapshot = super.getById(reqVO.getId());
        snapshot.setStatus(CompleteStatus.COMPLETED);
        snapshot.setSnapshot(JSON.parseObject(JSON.toJSONString(reqVO.getSnapshot())));
        super.updateById(snapshot);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchComplete(RehabSurveySnapshotsBatchSaveReqVO reqVO) {
        log.info("批量填写表单快照，reqVO: {}", JSON.toJSONString(reqVO));
        for (FormVO formVO : reqVO.getSnapshots()) {
            FormUtils.calcScoreAndResult(formVO);
        }
        List<FuncRehabSurveySnapshotDO> entities = reqVO.getSnapshots().stream().map(snapshot -> {
            FuncRehabSurveySnapshotDO entity = new FuncRehabSurveySnapshotDO()
                    .setOnline(true)
                    .setStatus(CompleteStatus.COMPLETED)
                    .setSnapshot(JSON.parseObject(JSON.toJSONString(snapshot)))
                    .setPatientId(reqVO.getPatientId());
            snapshot.setId(idService.nextId());
            return entity;
        }).collect(Collectors.toList());
        super.saveBatch(entities);
    }

    @Override
    public List<FuncRehabSurveySnapshotVO> list(SurveySnapshotListReqVO reqVO) {
        LambdaQueryWrapperX<FuncRehabSurveySnapshotDO> wrapper = new LambdaQueryWrapperX<FuncRehabSurveySnapshotDO>()
                .eq(FuncRehabSurveySnapshotDO::getPatientId, reqVO.getPatientId())
                .eqIfPresent(FuncRehabSurveySnapshotDO::getOnline, reqVO.getOnline())
                .eqIfPresent(FuncRehabSurveySnapshotDO::getStatus, reqVO.getStatus());

        if (reqVO.getPlanId() == null) {
            Long currentPlanId = funcRehabPlanMapper.getCurrentPlanId(reqVO.getPatientId());
            wrapper.and(w -> w.eq(FuncRehabSurveySnapshotDO::getPlanId, currentPlanId).or()
                    .isNull(FuncRehabSurveySnapshotDO::getPlanId));
        } else {
            wrapper.eq(FuncRehabSurveySnapshotDO::getPlanId, reqVO.getPlanId());
        }

        List<FuncRehabSurveySnapshotDO> snapshots = super.list(wrapper);
        return CopyPropertiesUtil.copyList(snapshots, FuncRehabSurveySnapshotVO::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(SurveySnapshotSaveOrUpdateReqVO reqVO) {
        log.info("医护端填写问卷接口，reqVO: {}", JSON.toJSONString(reqVO));
        FormUtils.calcScoreAndResult(reqVO.getSnapshot());
        FuncRehabSurveySnapshotDO snapshot = super.getById(reqVO.getId());
        if (snapshot == null) {
            snapshot = new FuncRehabSurveySnapshotDO();
            snapshot.setId(reqVO.getId());
        }
        JSONObject snapshotJson = CopyPropertiesUtil.copy(reqVO.getSnapshot(), JSONObject::new);
        snapshot.setSnapshot(snapshotJson);
        snapshot.setStatus(CompleteStatus.COMPLETED);
        snapshot.setPatientId(reqVO.getPatientId());
        snapshot.setOnline(reqVO.getSnapshot().getOnline());
        super.saveOrUpdate(snapshot);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<FuncRehabSurveySnapshotVO> copySnapshots(Long sourcePlanId, Long targetPlanId) {
        List<FuncRehabSurveySnapshotDO> sourceSnapshots = super.list(
                new LambdaQueryWrapperX<FuncRehabSurveySnapshotDO>()
                        .eq(FuncRehabSurveySnapshotDO::getPlanId, sourcePlanId));

        if (sourceSnapshots.isEmpty()) {
            return Collections.emptyList();
        }

        List<FuncRehabSurveySnapshotDO> targetSnapshots = sourceSnapshots.stream()
                .map(snapshot -> createTargetSnapshot(snapshot, targetPlanId))
                .collect(Collectors.toList());

        super.saveBatch(targetSnapshots);

        return targetSnapshots.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public FuncRehabSurveySnapshotVO details(Long id) {
        FuncRehabSurveySnapshotDO funcRehabSurveySnapshotDO = this.getById(id);
        if (funcRehabSurveySnapshotDO == null) {
            return null;
        }
        FuncRehabSurveySnapshotVO funcRehabSurveySnapshotVO = CopyPropertiesUtil.normalCopyProperties(funcRehabSurveySnapshotDO, FuncRehabSurveySnapshotVO.class);
        funcRehabSurveySnapshotVO.setSnapshot(funcRehabSurveySnapshotDO.getSnapshot().toJavaObject(FormVO.class));
        return funcRehabSurveySnapshotVO;
    }


    @Override
    public List<Long> getPatientIdNullPlanId() {
        return funcRehabSurveySnapshotMapper.getPatientIdNullPlanId();
    }

    @Override
    public Integer countCompleteByPatientId(Long patientId){
        return baseMapper.countCompleteByPatientId(patientId);
    }

    private FuncRehabSurveySnapshotDO createTargetSnapshot(FuncRehabSurveySnapshotDO source, Long targetPlanId) {
        FuncRehabSurveySnapshotDO target = new FuncRehabSurveySnapshotDO();
        CopyPropertiesUtil.copy(source, () -> target);
        target.setId(idService.nextId());
        target.setPlanId(targetPlanId);
        return target;
    }

    private FuncRehabSurveySnapshotVO convertToVO(FuncRehabSurveySnapshotDO source) {
        FuncRehabSurveySnapshotVO target = new FuncRehabSurveySnapshotVO();
        CopyPropertiesUtil.copy(source, () -> target);
        target.setSnapshot(JSON.toJavaObject(source.getSnapshot(), FormVO.class));
        return target;
    }

    /**
     * 从表格详细请求 VO 中获取表格详细信息
     *
     * @param reqVO 功能康复快照发送请求 VO
     * @return FormVO (表格详细信息)
     */
    private FormVO fetchFormDetails(RehabSurveySnapshotSendReqVO reqVO) {
        return formFeign.detail(reqVO.getFormId());
    }

    /**
     * 创建并保存快照
     *
     * @param formVO 表格详细信息
     * @return snapshotId (快照 ID)
     */
    private long createAndSaveSnapshot(FormVO formVO, Long patientUserId) {
        FuncRehabSurveySnapshotDO snapshot = new FuncRehabSurveySnapshotDO()
                .setOnline(true)
                .setStatus(CompleteStatus.UNCOMPLETED)
                .setSnapshot(JSON.parseObject(JSON.toJSONString(formVO)))
                .setPatientId(patientUserId);
        long id = idService.nextId();
        snapshot.setId(id);
        super.save(snapshot);
        return id;
    }

}