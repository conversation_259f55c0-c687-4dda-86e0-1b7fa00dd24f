package com.ykl.med.rehab.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.framework.mybatis.core.mapper.BaseMapperX;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.rehab.entity.PsychoRehabPlanItemExecuteDO;
import com.ykl.med.rehab.vo.req.PlanItemTimeVO;
import com.ykl.med.rehab.vo.resp.stat.PsychoStatVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

public interface PsychoRehabPlanItemExecuteMapper extends BaseMapperX<PsychoRehabPlanItemExecuteDO> {

    @Select({
            "<script>",
            "SELECT * FROM t_psycho_rehab_plan_item_execute",
            "WHERE",
            "<foreach collection='planItemTimes' item='item' separator=' OR '>",
            "(",
            "plan_item_id = #{item.planItemId}",
            "AND required_time IN",
            "<foreach collection='item.requiredTimes' item='time' open='(' separator=',' close=')'>",
            "#{time}",
            "</foreach>",
            ")",
            "</foreach>",
            "</script>"
    })
    List<PsychoRehabPlanItemExecuteDO> findPlanItems(
            @Param("planItemTimes") List<PlanItemTimeVO> planItemTimes
    );

    @Select("SELECT COUNT(tie.id) AS loveItemExecCount, " +
            "SUM(tie.duration) AS loveItemTotalDuration, " +
            "ti.template_id AS loveItemId FROM t_psycho_rehab_plan_item_execute as tie  " +
            "left join t_psycho_rehab_plan as plan on tie.plan_id = plan.id " +
            "left join t_psycho_rehab_plan_item as ti on ti.id = tie.plan_item_id " +
            "WHERE plan.patient_id=#{patientId} AND tie.`status`='COMPLETED' " +
            "GROUP BY ti.template_id ORDER BY SUM(tie.duration) desc limit 1")
    PsychoStatVO selectLoveItemStat(@Param("patientId") Long patientId);


    @Select({
            "SELECT",
            "  COUNT(CASE WHEN ti.status = 'COMPLETED' THEN 1 END) AS totalExecCount,",
            "  COUNT(CASE WHEN ti.status = 'COMPLETED' AND ti.create_time >= #{startTime} THEN 1 END) AS threeMonthCompletedCount,",
            "  COUNT(CASE WHEN ti.create_time >= #{startTime} THEN 1 END) AS threeMonthTotalCount,",
            "  SUM(ti.duration) AS totalDuration",
            "FROM t_psycho_rehab_plan_item_execute ti",
            "LEFT JOIN t_psycho_rehab_plan p ON ti.plan_id = p.id",
            "WHERE p.patient_id = #{patientId}"
    })
    PsychoStatVO aggregatePsychoStats(@Param("patientId") Long patientId, @Param("startTime") LocalDateTime startTime);

    default PsychoRehabPlanItemExecuteDO firstExecution(Long planId) {
        return selectOne(new LambdaQueryWrapperX<PsychoRehabPlanItemExecuteDO>()
                .eq(PsychoRehabPlanItemExecuteDO::getPlanId, planId)
                .orderByAsc(PsychoRehabPlanItemExecuteDO::getRequiredTime)
                .last("limit 1"));
    }

    default List<PsychoRehabPlanItemExecuteDO> getExecution(List<Long> planItemIds, LocalDateTime startOfDay, LocalDateTime endOfDay) {
        LambdaQueryWrapper<PsychoRehabPlanItemExecuteDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PsychoRehabPlanItemExecuteDO::getPlanItemId, planItemIds);
        lambdaQueryWrapper.between(PsychoRehabPlanItemExecuteDO::getRequiredTime, startOfDay, endOfDay);
        return selectList(lambdaQueryWrapper);
    }
}
