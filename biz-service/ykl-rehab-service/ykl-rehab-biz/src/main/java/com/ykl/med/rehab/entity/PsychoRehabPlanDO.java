package com.ykl.med.rehab.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.rehab.constants.RehabErrorCode;
import com.ykl.med.rehab.vo.req.psycho.PsychoRehabPlanSaveOrUpdateReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 康复计划
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_psycho_rehab_plan", autoResultMap = true)
public class PsychoRehabPlanDO extends BaseDO {

    private static final long serialVersionUID = -8318118493894429957L;

    /**
     * 患者id
     */
    private Long patientId;

//    /**
//     * 模板id
//     */
//    private Long templateId;

    /**
     * 目标
     */
    private String goal;

    /**
     * 持续时间
     */
    private Integer duration;

    /**
     * 持续时间单位
     */
    private String durationUnit;

    /**
     * 开始日期
     */
    private LocalDateTime startTime;

    /**
     * 结束日期
     */
    private LocalDateTime endTime;

    /**
     * 状态
     */
    private CommonStatusEnum status;


    @Schema(description = "问诊周期", example = "7")
    private Integer consultationCycle;

    /**
     * 创建人
     */
    private Long creatorDoctorId;
//
//    /**
//     * 评估结果
//     */
//    @TableField(typeHandler = FastjsonTypeHandler.class)
//    private List<AssessmentResultRespVO> assessmentResults;


    public static void validatePlanDuration(PsychoRehabPlanSaveOrUpdateReqVO reqVO, PsychoRehabPlanDO currentPlan) {
        if (("年".equals(reqVO.getDurationUnit()) && reqVO.getDuration() > 1) ||
                ("月".equals(reqVO.getDurationUnit()) && reqVO.getDuration() > 12) ||
                ("周".equals(reqVO.getDurationUnit()) && reqVO.getDuration() > 52) ||
                ("天".equals(reqVO.getDurationUnit()) && reqVO.getDuration() > 365)) {
            throw new ServiceException(RehabErrorCode.PLAN_DURATION_EXCEEDS_ONE_YEAR);
        }
        if (currentPlan != null && !currentPlan.getId().equals(reqVO.getId())) {
            throw new ServiceException(RehabErrorCode.PLAN_ALREADY_EXISTS);
        }
//        if (currentPlan != null && currentPlan.getStatus() == CommonStatusEnum.ENABLE) {
//            throw new ServiceException(RehabErrorCode.PLAN_IS_CURRENT_CANNOT_SET_DRAFT);
//        }
    }
}