package com.ykl.med.rehab.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.rehab.enums.NutritionalStatusEnum;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPatientSnapshotVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPlanMealsVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalRecordsAssessmentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 营养方案 Data Object
 *
 * <AUTHOR>
 * 2025-4-22 10:20:52
 */
@Data
@TableName(value = "t_nutritional_plan", autoResultMap = true)
public class NutritionalPlanDO extends BaseDO {
    /*** 状态 */
    private NutritionalStatusEnum status;
    /*** 患者ID */
    private Long patientId;
    /*** 目标 */
    private String goal;
    /*** 方案时间 */
    private Integer planTime;
    /*** 方案时间单位，字典 */
    private String planTimeUnit;
    /*** 开始时间 */
    private LocalDateTime planStartTime;
    /*** 结束时间 */
    private LocalDateTime planEndTime;
    /*** 患者基本信息快照 */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private NutritionalPatientSnapshotVO patientSnapshot;
    /*** 推荐膳食模型*/
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> recommendedDietaryModel;
    /*** 计算系数-热量 */
    private BigDecimal caloriesCoefficient;
    /*** 计算系数-蛋白质 */
    private BigDecimal proteinCoefficient;
    /*** 热量 */
    private BigDecimal calories;
    /*** 碳水化合物 */
    private BigDecimal carbohydrate;
    /*** 蛋白质 */
    private BigDecimal protein;
    /*** 脂肪 */
    private BigDecimal fat;
    /*** 膳食纤维 */
    private BigDecimal dietaryFiber;
    /*** 水 */
    private String water;
    /*** 油 */
    private String oil;
    /*** 食盐 */
    private String salt;
    /*** 餐食-早*/
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private NutritionalPlanMealsVO mealsMorning;
    /*** 餐食-午 */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private NutritionalPlanMealsVO mealsNoon;
    /*** 餐食-晚 */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private NutritionalPlanMealsVO mealsEvening;
    /*** 餐食-加 */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private NutritionalPlanMealsVO mealsAdditional;
    /*** 餐食建议 */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> mealsSuggestion;
    /*** 备注 */
    private String remark;
    /*** 档案评估 */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<NutritionalRecordsAssessmentVO> recordsAssessment;
    /*** 删除标志；0未删，1已删 */
    private Boolean deleteFlag;

}