package com.ykl.med.rehab.service.func;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.google.common.collect.Maps;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.masterdata.api.FrequencyDataFeign;
import com.ykl.med.masterdata.api.FuncRehabItemTemplateFeign;
import com.ykl.med.masterdata.entiry.dto.FrequencyParamDTO;
import com.ykl.med.masterdata.entiry.vo.FrequencyUtilVO;
import com.ykl.med.masterdata.vo.resp.FuncRehabItemTemplateDetailRespVO;
import com.ykl.med.rehab.constants.RehabErrorCode;
import com.ykl.med.rehab.entity.FuncRehabPlanDO;
import com.ykl.med.rehab.entity.FuncRehabPlanItemDO;
import com.ykl.med.rehab.entity.FuncRehabPlanItemExecuteDO;
import com.ykl.med.rehab.enums.CompleteStatus;
import com.ykl.med.rehab.mapper.FuncRehabPlanItemExecuteMapper;
import com.ykl.med.rehab.mapper.FuncRehabPlanItemMapper;
import com.ykl.med.rehab.mapper.FuncRehabPlanMapper;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanExecuteRecordRespVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 功能康复计划项执行服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FuncRehabPlanItemExecuteServiceImpl extends ServiceImpl<FuncRehabPlanItemExecuteMapper, FuncRehabPlanItemExecuteDO> implements FuncRehabPlanItemExecuteService {

    private final FuncRehabPlanItemService funcRehabPlanItemService;
    private final FrequencyDataFeign frequencyDataFeign;
    private final FuncRehabItemTemplateFeign funcRehabItemTemplateFeign;
    private final FuncRehabPlanMapper funcRehabPlanMapper;


    @Override
    public List<RehabPlanExecuteCountRespVO> count(RehabPlanStatReqVO reqVO) {
        LocalDateTime startOfDay = getStartOfDay(reqVO.getDate());
        LocalDateTime endOfDay = getEndOfDay(startOfDay);

        Map<Long, List<FuncRehabPlanItemExecuteDO>> groupedItems = fetchAndGroupItems(reqVO, startOfDay,
                endOfDay);
        if (groupedItems.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Long, FuncRehabPlanDetailRespVO.PlanItem> planItems = fetchPlanItems(reqVO);
        Map<Long, FuncRehabItemTemplateDetailRespVO> templates = fetchTemplates(planItems);

        return createResponseList(groupedItems, planItems, templates);
    }

    private LocalDateTime getStartOfDay(String date) {
        return LocalDate.parse(date).atStartOfDay();
    }

    private LocalDateTime getEndOfDay(LocalDateTime startOfDay) {
        return startOfDay.plusDays(1).minusSeconds(1);
    }

    private Map<Long, List<FuncRehabPlanItemExecuteDO>> fetchAndGroupItems(RehabPlanStatReqVO reqVO,
                                                                           LocalDateTime startOfDay, LocalDateTime endOfDay) {
        return super.lambdaQuery()
                .eq(FuncRehabPlanItemExecuteDO::getPlanId, reqVO.getPlanId())
                .between(FuncRehabPlanItemExecuteDO::getRequiredTime, startOfDay, endOfDay)
                .list()
                .stream()
                .collect(Collectors.groupingBy(FuncRehabPlanItemExecuteDO::getPlanItemId));
    }

    private Map<Long, FuncRehabPlanDetailRespVO.PlanItem> fetchPlanItems(RehabPlanStatReqVO reqVO) {
        return funcRehabPlanItemService.list(reqVO.getPlanId()).stream()
                .collect(Collectors.toMap(FuncRehabPlanDetailRespVO.PlanItem::getId,
                        Function.identity()));
    }

    private Map<Long, FuncRehabItemTemplateDetailRespVO> fetchTemplates(
            Map<Long, FuncRehabPlanDetailRespVO.PlanItem> planItems) {
        return funcRehabItemTemplateFeign.list(planItems.values().stream()
                        .map(FuncRehabPlanDetailRespVO.PlanItem::getTemplateId)
                        .collect(Collectors.toSet()))
                .stream()
                .collect(Collectors.toMap(FuncRehabItemTemplateDetailRespVO::getId,
                        Function.identity()));
    }

    private List<RehabPlanExecuteCountRespVO> createResponseList(
            Map<Long, List<FuncRehabPlanItemExecuteDO>> groupedItems,
            Map<Long, FuncRehabPlanDetailRespVO.PlanItem> planItems,
            Map<Long, FuncRehabItemTemplateDetailRespVO> templates) {
        return groupedItems.entrySet().stream()
                .map(entry -> createResponse(entry.getKey(), entry.getValue(), planItems, templates))
                .collect(Collectors.toList());
    }

    private RehabPlanExecuteCountRespVO createResponse(Long itemId, List<FuncRehabPlanItemExecuteDO> itemList,
                                                       Map<Long, FuncRehabPlanDetailRespVO.PlanItem> idItemMap,
                                                       Map<Long, FuncRehabItemTemplateDetailRespVO> idTemplateMap) {
        int executedCount = (int) itemList.stream()
                .filter(item -> Objects.equals(CompleteStatus.COMPLETED, item.getStatus()))
                .count();

        FuncRehabPlanDetailRespVO.PlanItem planItem = idItemMap.get(itemId);
        FuncRehabItemTemplateDetailRespVO template = idTemplateMap.get(planItem.getTemplateId());

        RehabPlanExecuteCountRespVO respVO = new RehabPlanExecuteCountRespVO();
        respVO.setPlanItemId(itemId);
        respVO.setPlanItemName(template.getName());
        respVO.setExecutedCount(executedCount);
        respVO.setUnit(template.getUnit());
        respVO.setExecutedActions(planItem.getActionsPerSession() * executedCount);
        respVO.setTotalCount(itemList.size());
        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        log.info("Executing rehab plan, reqVO: {}", JSON.toJSONString(reqVO));

        LocalDateTime begin, end;
        if (reqVO.getTargetDate() != null) {
            begin = reqVO.getTargetDate().atStartOfDay();
            end = begin.plusDays(1).minusSeconds(1);
        } else {
            begin = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
            end = begin.plusDays(1).minusSeconds(1);
        }

        FuncRehabPlanItemExecuteDO itemExecute = super.lambdaQuery()
                .between(FuncRehabPlanItemExecuteDO::getRequiredTime, begin, end)
                .eq(FuncRehabPlanItemExecuteDO::getPlanId, reqVO.getPlanId())
                .eq(FuncRehabPlanItemExecuteDO::getPlanItemId, reqVO.getPlanItemId())
                .eq(FuncRehabPlanItemExecuteDO::getStatus, CompleteStatus.UNCOMPLETED)
                .list().stream()
                .findFirst()
                .orElseThrow(() -> new ServiceException(RehabErrorCode.EXECUTE_RECORD_NOT_EXISTS));

        itemExecute.setStatus(CompleteStatus.COMPLETED);
        itemExecute.setExecuteTime(reqVO.getExecuteTime() != null ? reqVO.getExecuteTime() : LocalDateTime.now());
        itemExecute.setDuration(reqVO.getDuration());
        itemExecute.setStartTime(reqVO.getStartTime());
        itemExecute.setEndTime(reqVO.getEndTime());
        itemExecute.setEvaluation(reqVO.getEvaluation());
        itemExecute.setSnapshot(reqVO.getFormVO());
        super.updateById(itemExecute);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateExecute(Long planId, boolean immediatelyFlag) {
        // Get start times for plan ID
        LocalDateTime startTime = funcRehabPlanMapper.selectById(planId).getStartTime();

        // Get items for plan ID
        List<FuncRehabPlanDetailRespVO.PlanItem> planItems = funcRehabPlanItemService.list(planId);
        if (planItems.isEmpty()) {
            return;
        }

        // 获取所有频率的执行时间，并按频率id转map
        Map<Long, List<LocalDateTime>> frequencyIdTimeMap = getTodayTime(startTime, planItems, immediatelyFlag);
        if (frequencyIdTimeMap.isEmpty()) {
            return;
        }

        // Build all potential executed items for all plan
        List<FuncRehabPlanItemExecuteDO> potentialExecutedItems = planItems.stream()
                .flatMap(item -> buildExecutedItemsFor(item, frequencyIdTimeMap))
                .collect(Collectors.toList());

        // Get already existing execution items to filter out
        Set<String> existingItemsKeys = getExistingExecutionItemsKeys(potentialExecutedItems);

        // Filter out already existing execution items
        List<FuncRehabPlanItemExecuteDO> newExecutedItems = potentialExecutedItems.stream()
                .filter(item -> !existingItemsKeys.contains(buildExecutionItemKey(item)))
                .collect(Collectors.toList());

        // Save new execution items if any
        if (!newExecutedItems.isEmpty()) {
            saveBatch(newExecutedItems);
        }
    }


    @Override
    public RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(ItemExecStatReqVO reqVO) {
        LocalDate startDate, endDate;
        endDate = reqVO.getEndDate() == null ? LocalDate.now() : reqVO.getEndDate();
        startDate = reqVO.getStartDate() == null ? endDate.minusDays(6) : reqVO.getStartDate();
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

        // 查询所有方案
        List<FuncRehabPlanDO> plans = funcRehabPlanMapper.selectList(new LambdaQueryWrapperX<FuncRehabPlanDO>()
                .eq(FuncRehabPlanDO::getStatus, CommonStatusEnum.ENABLE)
                .eq(FuncRehabPlanDO::getPatientId, reqVO.getPatientId()));
        List<Long> planIds = plans.stream().map(FuncRehabPlanDO::getId).collect(Collectors.toList());
        if (planIds.isEmpty()) {
            throw new ServiceException(RehabErrorCode.PLAN_NOT_EXISTS);
        }

        // 一次性查询过去7天的所有执行记录
        List<FuncRehabPlanItemExecuteDO> executes = super.lambdaQuery()
                .in(FuncRehabPlanItemExecuteDO::getPlanId, planIds)
                .eq(FuncRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                .between(FuncRehabPlanItemExecuteDO::getRequiredTime, startDateTime, endDateTime)
                .list();

        // 根据planIds获取所有计划项并以templateId为键组成的map
        Map<Long, List<FuncRehabPlanDetailRespVO.PlanItem>> templateToItemsMap = funcRehabPlanItemService
                .listByPlanIds(planIds)
                .values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(FuncRehabPlanDetailRespVO.PlanItem::getTemplateId));
        // 如果没有计划项，直接返回空的响应
        if (templateToItemsMap.isEmpty()) {
            return new RehabPlanWeeklyStatRespWrapperVO();
        }

        Set<Long> templateIds = templateToItemsMap.keySet();
        Map<Long, Long> itemIdToTemplateId = templateToItemsMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(FuncRehabPlanDetailRespVO.PlanItem::getId,
                        FuncRehabPlanDetailRespVO.PlanItem::getTemplateId));

        // 获取模板详细信息
        Map<Long, FuncRehabItemTemplateDetailRespVO> templateDetailsMap = funcRehabItemTemplateFeign.list(templateIds)
                .stream().collect(Collectors.toMap(FuncRehabItemTemplateDetailRespVO::getId, Function.identity()));

        // 构建每个模板的统计序列
        List<RehabPlanWeeklyStatRespWrapperVO.Series> allSeries = templateIds.stream()
                .map(templateId -> createSeriesByTemplate(templateId, templateDetailsMap.get(templateId),
                        templateToItemsMap.get(templateId)))
                .collect(Collectors.toList());

        // 构建每一天的统计数据
        List<RehabPlanWeeklyStatRespWrapperVO.DailyData> dailyDataList = Stream
                .iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .map(date -> createDailyData(date, executes, allSeries, itemIdToTemplateId))
                .collect(Collectors.toList());

        RehabPlanWeeklyStatRespWrapperVO wrapperVO = new RehabPlanWeeklyStatRespWrapperVO();
        wrapperVO.setSeriesList(allSeries);
        wrapperVO.setDailyDataList(dailyDataList);
        return wrapperVO;
    }

    private RehabPlanWeeklyStatRespWrapperVO.Series createSeriesByTemplate(Long templateId,
                                                                           FuncRehabItemTemplateDetailRespVO template,
                                                                           List<FuncRehabPlanDetailRespVO.PlanItem> items) {
        RehabPlanWeeklyStatRespWrapperVO.Series series = new RehabPlanWeeklyStatRespWrapperVO.Series();
        series.setPlanItemId(templateId);
        series.setPlanItemName(template.getName());
        series.setPlanItemMethod(Collections.singletonList(template.getMethod()));
        series.setUnit(template.getUnit());
        // Assume all items associated with this template have the same actions per
        // session
        series.setActionsPerSession(
                items.isEmpty() ? 0 : items.stream().findFirst()
                        .map(FuncRehabPlanDetailRespVO.PlanItem::getActionsPerSession).orElse(0)
        );
        return series;
    }

    private RehabPlanWeeklyStatRespWrapperVO.DailyData createDailyData(LocalDate date,
                                                                       List<FuncRehabPlanItemExecuteDO> executes,
                                                                       List<RehabPlanWeeklyStatRespWrapperVO.Series> allSeries,
                                                                       Map<Long, Long> itemIdToTemplateId) {
        List<FuncRehabPlanItemExecuteDO> dailyExecutes = executes.stream()
                .filter(execute -> execute.getRequiredTime().toLocalDate().equals(date))
                .collect(Collectors.toList());

        Map<Long, Long> templateIdToExecutedDurationSum = dailyExecutes.stream()
                .filter(e -> e.getDuration() != null)
                .collect(Collectors.groupingBy(
                        funcRehabPlanItemExecuteDO -> itemIdToTemplateId
                                .get(funcRehabPlanItemExecuteDO.getPlanItemId()),
                        Collectors.summingLong(FuncRehabPlanItemExecuteDO::getDuration)));

        List<Integer> executedCounts = allSeries.stream()
                .map(series -> templateIdToExecutedDurationSum.getOrDefault(series.getPlanItemId(), 0L).intValue())
                .collect(Collectors.toList());

        RehabPlanWeeklyStatRespWrapperVO.DailyData dailyData = new RehabPlanWeeklyStatRespWrapperVO.DailyData();
        dailyData.setDate(date.atStartOfDay());
        dailyData.setExecutedCounts(executedCounts);
        return dailyData;
    }

    private List<FuncRehabPlanExecuteRecordRespVO> fillExecuteRecordData(List<FuncRehabPlanItemExecuteDO> items) {
        if (items.isEmpty()) {
            return Collections.emptyList();
        }
        // 提取所有的planItemIds
        List<Long> planItemIds = items.stream()
                .map(FuncRehabPlanItemExecuteDO::getPlanItemId)
                .collect(Collectors.toList());

        // 通过planItemIds获取所有相关的FuncRehabPlanItemDO
        Map<Long, FuncRehabPlanItemDO> rehabPlanItemMap = SpringContentUtils
                .getBean(FuncRehabPlanItemMapper.class)
                .selectBatchIds(planItemIds)
                .stream()
                .collect(Collectors.toMap(FuncRehabPlanItemDO::getId, Function.identity()));

        // 提取所有的itemTemplateIds
        Set<Long> itemTemplateIds = rehabPlanItemMap.values().stream()
                .map(FuncRehabPlanItemDO::getTemplateId).collect(Collectors.toSet());

        // 通过itemTemplateIds获取所有相关的模板详情
        Map<Long, FuncRehabItemTemplateDetailRespVO> itemTemplateMap = funcRehabItemTemplateFeign
                .list(itemTemplateIds)
                .stream()
                .collect(Collectors.toMap(FuncRehabItemTemplateDetailRespVO::getId,
                        Function.identity()));

        // 转换执行记录到响应对象
        return items.stream()
                .map(item -> {
                    FuncRehabPlanItemDO planItem = rehabPlanItemMap.get(item.getPlanItemId());
                    FuncRehabItemTemplateDetailRespVO templateDetail = itemTemplateMap
                            .get(planItem.getTemplateId());

                    FuncRehabPlanExecuteRecordRespVO respVO = CopyPropertiesUtil
                            .normalCopyProperties(item,
                                    FuncRehabPlanExecuteRecordRespVO.class);
                    respVO.setPlanItemName(templateDetail.getName());
                    respVO.setPlanItemMethod(templateDetail.getMethod());
                    return respVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<FuncRehabPlanExecuteRecordRespVO> queryExecuteRecords(RehabPlanExecuteQueryReqVO reqVO) {
        PageResult<FuncRehabPlanItemExecuteDO> pageResult = baseMapper.selectPage(reqVO,
                new LambdaQueryWrapperX<FuncRehabPlanItemExecuteDO>()
                        .eq(FuncRehabPlanItemExecuteDO::getPlanId, reqVO.getPlanId())
                        .eq(FuncRehabPlanItemExecuteDO::getPlanItemId, reqVO.getPlanItemId())
                        .eq(FuncRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                        .orderByDesc(FuncRehabPlanItemExecuteDO::getExecuteTime));

        if (pageResult.getList().isEmpty()) {
            return PageResult.empty();
        }

        // 调用新的方法来填充数据
        List<FuncRehabPlanExecuteRecordRespVO> records = fillExecuteRecordData(pageResult.getList());

        return new PageResult<>(records, pageResult.getTotal());
    }

    @Override
    public Map<String, List<FuncRehabPlanExecuteRecordRespVO>> queryExecuteRecordsByTimeRange(
            RehabPlanExecuteTimeRangeQueryReqVO reqVO) {
        // 查询所有方案
        List<Long> planIds;
        if (reqVO.getPlanId() != null) {
            planIds = Collections.singletonList(reqVO.getPlanId());
        } else {
            List<FuncRehabPlanDO> plans = funcRehabPlanMapper.selectList(new LambdaQueryWrapperX<FuncRehabPlanDO>()
                    .eq(FuncRehabPlanDO::getPatientId, reqVO.getPatientId()));
            planIds = plans.stream().map(FuncRehabPlanDO::getId).collect(Collectors.toList());
        }
        if (planIds.isEmpty()) {
            throw new ServiceException(RehabErrorCode.PLAN_NOT_EXISTS);
        }

        List<FuncRehabPlanItemExecuteDO> executeDOs = baseMapper
                .selectList(new LambdaQueryWrapperX<FuncRehabPlanItemExecuteDO>()
                        .in(FuncRehabPlanItemExecuteDO::getPlanId, planIds)
                        .eqIfPresent(FuncRehabPlanItemExecuteDO::getPlanItemId,
                                reqVO.getPlanItemId())
                        .eq(FuncRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                        .between(FuncRehabPlanItemExecuteDO::getExecuteTime,
                                reqVO.getStartTime(), reqVO.getEndTime())
                        .orderByDesc(FuncRehabPlanItemExecuteDO::getExecuteTime));

        // 调用新的方法来填充数据
        List<FuncRehabPlanExecuteRecordRespVO> executeRecords = fillExecuteRecordData(executeDOs);

        Map<String, List<FuncRehabPlanExecuteRecordRespVO>> unsortedMap = executeRecords.stream()
                .collect(Collectors.groupingBy(
                        record -> record.getExecuteTime().toLocalDate().toString(),
                        Collectors.toList()));

        Map<String, List<FuncRehabPlanExecuteRecordRespVO>> sortedMap = new TreeMap<>(
                Comparator.reverseOrder());
        sortedMap.putAll(unsortedMap);

        return sortedMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateExecuteBatch(Collection<Long> planIds) {
        log.info("Batch generating FuncRehabPlanItemExecutes, planIds: {}", planIds);

        planIds.forEach(e -> this.generateExecute(e, false));
    }

    private Map<Long, List<LocalDateTime>> getTodayTime(LocalDateTime localDateTime, List<FuncRehabPlanDetailRespVO.PlanItem> items, boolean immediatelyFlag) {
        LocalDate nowDay = DateTimeUtils.getNowDay();
        LocalDateTime startTime = DateTimeUtils.someTimeStart(nowDay);
        LocalDateTime endTime = DateTimeUtils.someTimeEnd(nowDay);
        List<FrequencyParamDTO> frequencyParamDTOList = new ArrayList<>();
        Map<Long, Long> frequencyIdMap = new HashMap<>();
        for (FuncRehabPlanDetailRespVO.PlanItem planItem : items) {
            Long frequencyId = planItem.getFrequencyId();
            if (frequencyIdMap.get(frequencyId) != null) {
                continue;
            }
            frequencyIdMap.put(frequencyId, frequencyId);
            FrequencyParamDTO frequencyParamDTO = new FrequencyParamDTO();
            frequencyParamDTO.setId(frequencyId.toString());
            frequencyParamDTO.setFrequencyId(frequencyId.toString());
            frequencyParamDTO.setFrequencyStartTime(Collections.singletonList(localDateTime.toString()));
            frequencyParamDTO.setFrequencyFixDate(immediatelyFlag && localDateTime.isBefore(startTime) ? DateTimeUtils.getNowDayTimeString() : localDateTime.toString());
            frequencyParamDTO.setStartTime(startTime);
            frequencyParamDTO.setEndTime(endTime);
            frequencyParamDTOList.add(frequencyParamDTO);
        }
        PageResult<FrequencyUtilVO> frequencyBizPageResult = frequencyDataFeign.genFrequencySetTime(frequencyParamDTOList);
        if (CollectionUtils.isEmpty(frequencyBizPageResult.getList())) {
            return new HashMap<>();
        }

        return frequencyBizPageResult.getList().stream()
                .collect(Collectors.toMap(e -> Long.valueOf(e.getId()), FrequencyUtilVO::getGenExecTimeList));
    }

    private Set<String> getExistingExecutionItemsKeys(List<FuncRehabPlanItemExecuteDO> items) {
        Map<Long, List<LocalDateTime>> planItemIdToTimes = items.stream()
                .collect(Collectors.groupingBy(FuncRehabPlanItemExecuteDO::getPlanItemId,
                        Collectors.mapping(FuncRehabPlanItemExecuteDO::getRequiredTime, Collectors.toList())));
        List<PlanItemTimeVO> planItemTimes = planItemIdToTimes.entrySet().stream()
                .map(entry -> new PlanItemTimeVO()
                        .setPlanItemId(entry.getKey())
                        .setRequiredTimes(entry.getValue()))
                .collect(Collectors.toList());
        if (planItemTimes.isEmpty()) {
            return Collections.emptySet();
        }
        List<FuncRehabPlanItemExecuteDO> executes = baseMapper.findPlanItems(planItemTimes);
        return executes.stream()
                .map(this::buildExecutionItemKey)
                .collect(Collectors.toSet());
    }

    private String buildExecutionItemKey(FuncRehabPlanItemExecuteDO item) {
        return item.getPlanId() + ":" + item.getPlanItemId() + ":" + item.getRequiredTime();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExecuteRecord(RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        FuncRehabPlanItemExecuteDO executeDO = baseMapper.selectById(reqVO.getId());
        if (executeDO != null) {
            baseMapper.updateById(CopyPropertiesUtil.copy(reqVO, () -> executeDO, true));
        } else {
            throw new RuntimeException("未找到对应的执行记录: " + reqVO.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteExecuteRecord(Long id) {
        log.info("删除功能康复计划项执行记录，id: {}", id);
        baseMapper.deleteById(id);
    }

    private Stream<FuncRehabPlanItemExecuteDO> buildExecutedItemsFor(
            FuncRehabPlanDetailRespVO.PlanItem item,
            Map<Long, List<LocalDateTime>> frequencyIdTimeMap) {
        return frequencyIdTimeMap.getOrDefault(item.getFrequencyId(), new ArrayList<>())
                .stream().map(e -> new FuncRehabPlanItemExecuteDO()
                        .setPlanId(item.getPlanId())
                        .setPlanItemId(item.getId())
                        .setStatus(CompleteStatus.UNCOMPLETED)
                        .setSnapshot(item.getFormVO())
                        .setRequiredTime(e));
    }


}
