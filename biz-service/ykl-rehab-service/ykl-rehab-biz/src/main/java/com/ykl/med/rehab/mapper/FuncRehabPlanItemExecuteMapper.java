package com.ykl.med.rehab.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.ykl.med.framework.mybatis.core.mapper.BaseMapperX;
import com.ykl.med.rehab.entity.FuncRehabPlanItemExecuteDO;
import com.ykl.med.rehab.vo.req.PlanItemTimeVO;

/**
 * 功能康复计划项执行Mapper
 *
 * <AUTHOR>
 */
public interface FuncRehabPlanItemExecuteMapper extends BaseMapperX<FuncRehabPlanItemExecuteDO> {

    @Select({
        "<script>",
        "SELECT * FROM t_func_rehab_plan_item_execute",
        "WHERE",
        "<foreach collection='planItemTimes' item='item' separator=' OR '>",
        "(",
        "plan_item_id = #{item.planItemId}",
        "AND required_time IN",
        "<foreach collection='item.requiredTimes' item='time' open='(' separator=',' close=')'>",
        "#{time}",
        "</foreach>",
        ")",
        "</foreach>",
        "</script>"
    })
    List<FuncRehabPlanItemExecuteDO> findPlanItems(
        @Param("planItemTimes") List<PlanItemTimeVO> planItemTimes
    );
}
