package com.ykl.med.rehab.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.rehab.enums.NutritionalSupplementsUseStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 营养-营养补充剂 Data Object
 *
 * <AUTHOR>
 * 2025-4-22 10:20:52
 */
@Data
@TableName(value = "t_nutritional_supplements", autoResultMap = true)
public class NutritionalSupplementsDO extends BaseDO {

    /*** 营养方案ID */
    private Long nutritionalPlanId;
    /*** 项目ID */
    private Long itemId;
    /*** 项目类别（特医食品/功能营养素） */
    private String itemCategory;
    /*** 项目类型（全营养配方/补充营养配方） */
    private String itemType;
    /*** 项目名称 */
    private String itemName;
    /*** 包装规格（20mg*5袋/盒） */
    private String itemPackageSpec;
    /*** 项目厂商名称 */
    private String itemProducerName;
    /*** 项目用法名称 */
    private String usageName;
    /*** 单次用量 */
    private Integer onceDosage;
    /*** 单次用量单位 */
    private String onceDosageUnit;
    /*** 频次ID */
    private Long frequencyId;
    /*** 频次名称 */
    private String frequencyName;
    /*** 频次时间 */
    private String frequencyTimes;
    /*** 开立数量 */
    private Integer quantity;
    /*** 开立数量单位 */
    private String quantityUnit;
    /*** 备注（项目说明） */
    private String remark;
    /*** 使用状态,NutritionalSupplementsUseStatusEnum枚举 */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private NutritionalSupplementsUseStatusEnum useStatus;
    /*** 开始使用时间 */
    private LocalDateTime useStartTime;
    /*** 结束使用时间 */
    private LocalDateTime useEndTime;

}