package com.ykl.med.rehab.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.api.PsychoRehabFeign;
import com.ykl.med.rehab.service.psycho.PsychoRehabBasePlanService;
import com.ykl.med.rehab.service.psycho.PsychoRehabPlanItemExecuteService;
import com.ykl.med.rehab.service.psycho.PsychoRehabPlanService;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.psycho.PsychoRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

@RestController
@RequiredArgsConstructor
public class PsychoRehabController implements PsychoRehabFeign {

    private final PsychoRehabPlanService psychoRehabPlanService;

    private final PsychoRehabPlanItemExecuteService psychoRehabPlanItemExecuteService;

    private final PsychoRehabBasePlanService psychoRehabBasePlanService;


    @PostMapping("/psychoRehab/plan/getBasePlanReq")
    @Override
    public PsychoRehabPlanSaveOrUpdateReqVO getBasePlanReq(@RequestParam("patientId") Long patientId) {
        return psychoRehabBasePlanService.getBasePlanReq(patientId);
    }

    @PostMapping("/psychoRehab/plan/getCurrentOrNextPlanId")
    @Override
    public Long getCurrentOrNextPlanId(@RequestParam("patientId") Long patientId) {
        return psychoRehabPlanService.getCurrentOrNextPlanId(patientId);
    }

    @Override
    @PostMapping("/psychoRehab/plan/saveOrUpdate")
    public Long saveOrUpdate(@Valid @RequestBody PsychoRehabPlanSaveOrUpdateReqVO reqVO) {
        return psychoRehabPlanService.saveOrUpdate(reqVO);
    }

    @Override
    @PostMapping("/psychoRehab/plan/getCurrentPlan")
    public PsychoRehabPlanDetailRespVO getCurrentPlan(@RequestParam("patientId") Long patientId) {
        return psychoRehabPlanService.getCurrentPlan(patientId);
    }

    @Override
    @PostMapping("/psychoRehab/plan/itemExecute/count")
    public List<RehabPlanExecuteCountRespVO> count(@Valid @RequestBody RehabPlanStatReqVO reqVO) {
        return psychoRehabPlanItemExecuteService.count(reqVO);
    }

    @Override
    @PostMapping("/psychoRehab/plan/itemExecute/execute")
    public void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        psychoRehabPlanItemExecuteService.execute(reqVO);
    }

    @Override
    @PostMapping("/psychoRehab/plan/itemExecute/generateExecute")
    public void generateExecute(@RequestParam("planId") Long planId) {
        psychoRehabPlanItemExecuteService.generateExecute(planId, false);
    }

    @Override
    @PostMapping("/psychoRehab/plan/getHistoryPlans")
    public List<PsychoRehabPlanVO> getHistoryPlans(@RequestBody HistoryPlanReqVO reqVO) {
        return psychoRehabPlanService.getHistoryPlans(reqVO);
    }

    @Override
    @PostMapping("/psychoRehab/plan/extend")
    public void extendPlan(@RequestBody ExtendPlanReqVO reqVO) {
        psychoRehabPlanService.extendPlan(reqVO);
    }


    @Override
    @PostMapping("/psychoRehab/plan/getPlanById")
    public PsychoRehabPlanDetailRespVO getPlanById(@RequestBody PatientIdAndPlanIdVO reqVO) {
        return psychoRehabPlanService.getPlanById(reqVO);
    }

    @PostMapping("/psychoRehab/plan/getById")
    @Override
    public PsychoRehabPlanVO getById(@RequestParam("id") Long id) {
        return psychoRehabPlanService.getById(id);
    }

    @Override
    @PostMapping("/psychoRehab/plan/getAllCurrentPlanIds")
    public PageResult<Long> getAllCurrentPlanIds(@RequestBody PageParam pageParam) {
        IPage<Long> page = psychoRehabPlanService.getAllCurrentPlanIds(pageParam);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @PostMapping("/psychoRehab/plan/getAllHasPlanPatientIds")
    @Override
    public List<Long> getAllHasPlanPatientIds() {
        return psychoRehabPlanService.getAllHasPlanPatientIds();
    }

    @PostMapping("/psychoRehab/plan/getAllIds")
    @Override
    public List<Long> getAllIds() {
        return psychoRehabPlanService.getAllIds();
    }

    @PostMapping("/psychoRehab/plan/getLastPlan")
    @Override
    public PsychoRehabPlanDetailRespVO getLastPlan(@RequestParam("patientId") Long patientId) {
        return psychoRehabPlanService.getLastPlan(patientId);
    }

    @Override
    @PostMapping("/psychoRehab/plan/itemExecute/generateExecuteBatch")
    public void generateExecuteBatch(@RequestBody Collection<Long> planIds) {
        psychoRehabPlanItemExecuteService.generateExecuteBatch(planIds);
    }

    @PostMapping("/psychoRehab/plan/isExistPlanExcludeDraft")
    @Override
    public boolean isExistPlanExcludeDraft(@RequestParam("patientId") Long patientId) {
        return psychoRehabPlanService.isExistPlanExcludeDraft(patientId);
    }
}