package com.ykl.med.rehab.service.nutritional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.masterdata.vo.FormManageVO;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.rehab.constants.RehabErrorCode;
import com.ykl.med.rehab.entity.NutritionalPlanDO;
import com.ykl.med.rehab.enums.NutritionalStatusEnum;
import com.ykl.med.rehab.mapper.NutritionalPlanMapper;
import com.ykl.med.rehab.vo.req.nutritional.*;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalHistoryListVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPlanVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NutritionalServiceImpl implements NutritionalService {

    private final NutritionalPlanMapper nutritionalPlanMapper;
    private final NutritionalExecuteService nutritionalExecuteService;
    private final NutritionalFormService nutritionalFormService;
    private final StringRedisTemplate stringRedisTemplate;
    private final IdServiceImpl idService;

    private final static String NUTRITIONAL_RESET_LOCK = "nutritional:reset:lock:";

    @Override
    public NutritionalPlanVO current(Long patientId) {
        LambdaQueryWrapper<NutritionalPlanDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NutritionalPlanDO::getDeleteFlag, false);
        wrapper.eq(NutritionalPlanDO::getPatientId, patientId);
        wrapper.ne(NutritionalPlanDO::getStatus, NutritionalStatusEnum.FINISH);
        wrapper.orderByDesc(NutritionalPlanDO::getCreateTime);
        List<NutritionalPlanDO> nutritionalPlanDOS = nutritionalPlanMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(nutritionalPlanDOS)) {
            return null;
        }
        return buildNutritionalPlanVO(nutritionalPlanDOS.get(0));
    }


    @Override
    public NutritionalPlanVO details(Long id) {
        NutritionalPlanDO nutritionalPlanDO = nutritionalPlanMapper.selectById(id);
        AssertUtils.notNull(nutritionalPlanDO, RehabErrorCode.NUTRITIONAL_IS_NULL);
        return buildNutritionalPlanVO(nutritionalPlanDO);
    }

    @Override
    public List<NutritionalPlanVO> list(QueryNutritionalPlanReqVO reqVO) {
        LambdaQueryWrapper<NutritionalPlanDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NutritionalPlanDO::getDeleteFlag, false);
        if (reqVO.getPatientId() != null) {
            wrapper.eq(NutritionalPlanDO::getPatientId, reqVO.getPatientId());
        }
        if (!CollectionUtils.isEmpty(reqVO.getStatus())) {
            wrapper.in(NutritionalPlanDO::getStatus, reqVO.getStatus());
        }
        if (reqVO.getStartTime() != null) {
            wrapper.eq(NutritionalPlanDO::getPlanStartTime, reqVO.getStartTime());
        }
        if (reqVO.getEndTime() != null) {
            wrapper.eq(NutritionalPlanDO::getPlanEndTime, reqVO.getEndTime());
        }
        List<NutritionalPlanDO> nutritionalPlanDOS = nutritionalPlanMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(nutritionalPlanDOS)) {
            return new ArrayList<>();
        }
        return CopyPropertiesUtil.normalCopyProperties(nutritionalPlanDOS, NutritionalPlanVO.class);
    }

    @Override
    public List<NutritionalHistoryListVO> historyList(QueryNutritionalHistoryReqVO reqVO) {
        LambdaQueryWrapper<NutritionalPlanDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NutritionalPlanDO::getDeleteFlag, false);
        wrapper.eq(NutritionalPlanDO::getPatientId, reqVO.getPatientId());
        wrapper.eq(NutritionalPlanDO::getStatus, NutritionalStatusEnum.FINISH);
        if (reqVO.getTimeType() != null) {
            if (reqVO.getTimeType() == 1) {
                if (reqVO.getStartTime() != null) {
                    wrapper.ge(NutritionalPlanDO::getPlanStartTime, reqVO.getStartTime());
                }
                if (reqVO.getEndTime() != null) {
                    wrapper.le(NutritionalPlanDO::getPlanStartTime, reqVO.getEndTime());
                }
            }
            if (reqVO.getTimeType() == 2) {
                if (reqVO.getStartTime() != null) {
                    wrapper.ge(NutritionalPlanDO::getPlanEndTime, reqVO.getStartTime());
                }
                if (reqVO.getEndTime() != null) {
                    wrapper.le(NutritionalPlanDO::getPlanEndTime, reqVO.getEndTime());
                }
            }
        }
        if (reqVO.getSort() != null) {
            if (reqVO.getSort().equals("ASC")) {
                wrapper.orderByAsc(NutritionalPlanDO::getPlanStartTime);
            } else {
                wrapper.orderByDesc(NutritionalPlanDO::getPlanStartTime);
            }
        } else {
            wrapper.orderByDesc(NutritionalPlanDO::getPlanStartTime);
        }
        List<NutritionalPlanDO> nutritionalPlanDOS = nutritionalPlanMapper.selectList(wrapper);
        Map<Long, PatientFormVO> patientFormVOMap = queryNutritionalPlanFormMap(nutritionalPlanDOS);
        List<NutritionalHistoryListVO> nutritionalHistoryListVOS = new ArrayList<>();
        nutritionalPlanDOS.forEach(e -> {
            NutritionalHistoryListVO convert = CopyPropertiesUtil.normalCopyProperties(e, NutritionalHistoryListVO.class);
            PatientFormVO patientFormVO = patientFormVOMap.get(e.getId());
            if (patientFormVO != null && patientFormVO.getScore() != null) {
                convert.setResult(String.format("营养评估%s分,%s", patientFormVO.getScore(), patientFormVO.getResult()));
            } else {
                convert.setResult("无");
            }
            nutritionalHistoryListVOS.add(convert);
        });
        return nutritionalHistoryListVOS;
    }


    @Override
    public Long formFinish(NutritionalFormFinishVO reqVO) {
        NutritionalPlanDO nutritionalPlanDO = nutritionalPlanMapper.selectById(reqVO.getId());
        AssertUtils.notNull(nutritionalPlanDO, RehabErrorCode.NUTRITIONAL_IS_NULL);
        nutritionalPlanDO.setStatus(NutritionalStatusEnum.FROM_FINISH);
        nutritionalPlanDO.setPatientSnapshot(reqVO.getPatientSnapshot());
        nutritionalPlanDO.setRecordsAssessment(reqVO.getRecordsAssessment());
        nutritionalPlanDO.setUpdateTime(DateTimeUtils.getNow());
        nutritionalPlanMapper.updateById(nutritionalPlanDO);
        return nutritionalPlanDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long reset(NutritionalResetVO reqVO) {
        Long patientId = reqVO.getPatientId();
        String patientIdStr = patientId + "";
        String lockKey = NUTRITIONAL_RESET_LOCK + patientIdStr;
        try {
            Boolean lockAcquired = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, patientIdStr, 30, TimeUnit.SECONDS);
            if (lockAcquired != null && lockAcquired) {
                log.info("成功获取锁: lock_key:{}", lockKey);
                NutritionalPlanVO current = current(patientId);
                if (current != null) {
                    nutritionalPlanMapper.currentPlanFail(current.getId(), NutritionalStatusEnum.FINISH.name(), DateTimeUtils.getNow());
                }
                // 初始化方案表
                NutritionalPlanDO nutritionalPlanDO = new NutritionalPlanDO();
                long id = idService.nextId();
                nutritionalPlanDO.setId(id);
                nutritionalPlanDO.setPatientId(reqVO.getPatientId());
                nutritionalPlanDO.setStatus(NutritionalStatusEnum.INIT);
                nutritionalPlanDO.setDeleteFlag(false);
                nutritionalPlanDO.setPatientSnapshot(reqVO.getPatientSnapshot());
                nutritionalPlanDO.setRecordsAssessment(reqVO.getRecordsAssessment());
                nutritionalPlanMapper.insert(nutritionalPlanDO);
                // 初始化量表
                nutritionalFormService.initPatientForm(patientId, id, reqVO.getRegisterFlag());
                return id;
            } else {
                log.info("营养-重新评估：枷锁失败");
                throw new ServiceException(RehabErrorCode.LOCK_FAIL);
            }
        } finally {
            // 释放锁
            if (patientIdStr.equals(stringRedisTemplate.opsForValue().get(lockKey))) {
                stringRedisTemplate.delete(lockKey);
                log.info("成功释放锁: {}", lockKey);
            }
        }
    }


    @Override
    public Long saveOrUpdate(NutritionalSaveOrUpdateReqVO reqVO) {
        Long id = reqVO.getId();
        NutritionalPlanDO nutritionalPlanDO = nutritionalPlanMapper.selectById(id);
        AssertUtils.notNull(nutritionalPlanDO, RehabErrorCode.NUTRITIONAL_IS_NULL);
        NutritionalPlanDO updateDO = CopyPropertiesUtil.normalCopyProperties(reqVO, NutritionalPlanDO.class);
        updateDO.setStatus(NutritionalStatusEnum.STARTING);
        updateDO.setRecordsAssessment(nutritionalPlanDO.getRecordsAssessment());
        updateDO.setDeleteFlag(false);
        updateDO.setUpdateTime(DateTimeUtils.getNow());
        nutritionalPlanMapper.updateById(updateDO);
        nutritionalExecuteService.saveSupplements(id, reqVO.getSupplements());
        nutritionalExecuteService.saveSuggestions(id, reqVO.getSuggestions());
        return id;
    }

    @Override
    public Long extraTime(NutritionalExtraTimeReqVO reqVO) {
        NutritionalPlanDO nutritionalPlanDO = nutritionalPlanMapper.selectById(reqVO.getId());
        AssertUtils.notNull(nutritionalPlanDO, RehabErrorCode.NUTRITIONAL_IS_NULL);
        AssertUtils.isTrue(nutritionalPlanDO.getStatus().equals(NutritionalStatusEnum.STARTING), RehabErrorCode.NUTRITIONAL_NOT_START);
        AssertUtils.isTrue(nutritionalPlanDO.getPlanStartTime().isBefore(reqVO.getEndTime()), RehabErrorCode.START_TIME_IS_BEFORE_END_TIME);
        nutritionalPlanDO.setPlanEndTime(reqVO.getEndTime());
        nutritionalPlanDO.setUpdateTime(DateTimeUtils.getNow());
        nutritionalPlanMapper.updateById(nutritionalPlanDO);
        return nutritionalPlanDO.getId();
    }

    @Override
    public List<Long> getAllIds() {
        return nutritionalPlanMapper.getAllIds();
    }

    private NutritionalPlanVO buildNutritionalPlanVO(NutritionalPlanDO nutritionalPlanDO) {
        Long id = nutritionalPlanDO.getId();
        NutritionalPlanVO nutritionalPlanVO = CopyPropertiesUtil.normalCopyProperties(nutritionalPlanDO, NutritionalPlanVO.class);
        nutritionalPlanVO.setSuggestions(nutritionalExecuteService.querySuggestions(id));
        nutritionalPlanVO.setSupplements(nutritionalExecuteService.querySupplements(id));
        nutritionalPlanVO.setPatientForm(nutritionalFormService.queryPatientForm(id));
        return nutritionalPlanVO;
    }

    private Map<Long, PatientFormVO> queryNutritionalPlanFormMap(List<NutritionalPlanDO> nutritionalPlanDOS) {
        List<Long> nutritionalPlanIds = nutritionalPlanDOS.stream().map(NutritionalPlanDO::getId).collect(Collectors.toList());
        // 只查PG_SGA表单的数据
        FormManageVO formManageVO = nutritionalFormService.queryForm_PG_SGA();
        if (formManageVO == null) {
            return new HashMap<>();
        }
        List<PatientFormVO> patientFormVOS = nutritionalFormService.queryPatientForm(nutritionalPlanIds, formManageVO.getId());
        if (CollectionUtils.isEmpty(patientFormVOS)) {
            return new HashMap<>();
        }
        return patientFormVOS.stream().collect(Collectors.toMap(PatientFormVO::getBizId, Function.identity()));
    }
}
