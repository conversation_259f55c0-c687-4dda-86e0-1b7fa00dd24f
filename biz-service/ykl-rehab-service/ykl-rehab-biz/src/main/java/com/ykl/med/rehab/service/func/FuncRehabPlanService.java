package com.ykl.med.rehab.service.func;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.rehab.vo.req.ExtendPlanReqVO;
import com.ykl.med.rehab.vo.req.FormAndSnapshotReqVO;
import com.ykl.med.rehab.vo.req.HistoryPlanReqVO;
import com.ykl.med.rehab.vo.req.PatientIdAndPlanIdVO;
import com.ykl.med.rehab.vo.req.func.FuncRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.FormAndSnapshotRespVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanDetailRespVO;

import java.util.List;

/**
 * 功能康复计划服务接口。
 * 本接口负责处理功能康复计划的保存、更新和查询细节。
 * 主要用于管理患者的康复计划，包括计划的创建、修改和查看。
 *
 * <AUTHOR>
 */
public interface FuncRehabPlanService {

    /**
     * 保存或更新功能康复计划。
     * 此方法用于创建新的功能康复计划或更新现有计划。
     * 它处理来自前端的康复计划数据，并将其保存或更新到数据库中。
     *
     * @param reqVO 包含康复计划保存或更新所需数据的请求对象。
     */
    Long saveOrUpdate(FuncRehabPlanSaveOrUpdateReqVO reqVO);

    /**
     * 获取特定患者的功能康复计划详细信息。
     * 此方法用于根据患者的唯一标识（例如用户ID）来检索其功能康复计划的详细信息。
     * 它返回包含康复计划详细内容的响应对象。
     *
     * @param patientId 患者的唯一ID。
     * @return 指定患者的功能康复计划详细信息。
     */
    FuncRehabPlanDetailRespVO getCurrentPlan(Long patientId);

    /**
     * 查询具体患者的所有功能康复计划
     *
     * @param patientId 患者的唯一ID
     * @return 指定患者的所有功能康复计划
     */
    List<FuncRehabPlanDetailRespVO> list(Long patientId);

    /**
     * 为特定病人检索功能康复计划的历史记录。
     *
     * @param reqVO 请求对象
     * @return 包含功能康复计划详细信息的FuncRehabPlanDetailRespVO对象列表。
     */
    List<FuncRehabPlanDetailRespVO> getHistoryPlans(HistoryPlanReqVO reqVO);

    /**
     * 续开方案
     *
     * @param reqVO 请求对象
     */
    void extendPlan(ExtendPlanReqVO reqVO);

    /**
     * 重新评估
     *
     * @param patientId 患者的唯一ID
     */
    void reEvaluate(Long patientId);

    /**
     * 查看康复方案草稿
     *
     * @param patientId 患者的唯一ID
     */
    FuncRehabPlanDetailRespVO getDraftPlan(Long patientId);

    /**
     * 获取表单和快照
     *
     * @param reqVO 请求对象
     * @return 表单和快照
     */
    List<FormAndSnapshotRespVO> getFormAndSnapshots(FormAndSnapshotReqVO reqVO);

    /**
     * 根据患者ID和计划ID获取计划详情
     *
     * @param reqVO 请求对象
     * @return 计划详情
     */
    FuncRehabPlanDetailRespVO getPlanById(PatientIdAndPlanIdVO reqVO);

    FuncRehabPlanDetailRespVO getById(Long id);

    boolean isExistPlanExcludeDraft(Long patientId);

    /**
     * 分页获取所有当前计划ID
     * 
     * @param pageParam 分页参数
     * @return 当前计划ID分页列表
     */
    IPage<Long> getAllCurrentPlanIds(PageParam pageParam);

    List<Long> getAllHasPlanPatientIds();

    List<Long> getAllIds();

    /**
     * 查询功能康复项目id
     *
     * @param id 功能康复项目ID
     * @return 功能康复计划项
     */
    FuncRehabPlanDetailRespVO.PlanItem planItemDetails(Long id);

}
