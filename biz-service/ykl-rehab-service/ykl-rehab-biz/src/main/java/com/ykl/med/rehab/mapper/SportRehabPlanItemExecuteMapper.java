package com.ykl.med.rehab.mapper;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ykl.med.framework.mybatis.core.mapper.BaseMapperX;
import com.ykl.med.rehab.entity.SportRehabPlanDO;
import com.ykl.med.rehab.entity.SportRehabPlanItemExecuteDO;
import com.ykl.med.rehab.enums.CompleteStatus;
import com.ykl.med.rehab.vo.req.PlanItemTimeVO;
import com.ykl.med.rehab.vo.resp.stat.SportStatVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

public interface SportRehabPlanItemExecuteMapper extends BaseMapperX<SportRehabPlanItemExecuteDO> {

    @Select({
            "<script>",
            "SELECT * FROM t_sport_rehab_plan_item_execute",
            "WHERE",
            "<foreach collection='planItemTimes' item='item' separator=' OR '>",
            "(",
            "plan_item_id = #{item.planItemId}",
            "AND required_time IN",
            "<foreach collection='item.requiredTimes' item='time' open='(' separator=',' close=')'>",
            "#{time}",
            "</foreach>",
            ")",
            "</foreach>",
            "</script>"
    })
    List<SportRehabPlanItemExecuteDO> findPlanItems(
            @Param("planItemTimes") List<PlanItemTimeVO> planItemTimes);


    @Select("SELECT distinct ti.evaluation FROM t_sport_rehab_plan_item_execute as ti " +
            "left join t_sport_rehab_plan as plan on ti.plan_id = plan.id " +
            "WHERE plan.patient_id = #{patientId} and ti.evaluation is not null")
    List<String> getEvaluationByPatientId(@Param("patientId") Long patientId);

    @Select({
            "SELECT",
            "  COUNT(CASE WHEN ti.status = 'COMPLETED' THEN 1 END) AS totalExecCount,",
            "  COUNT(CASE WHEN ti.status = 'COMPLETED' AND ti.create_time >= #{startTime} THEN 1 END) AS threeMonthCompletedCount,",
            "  COUNT(CASE WHEN ti.create_time >= #{startTime} THEN 1 END) AS threeMonthTotalCount,",
            "  SUM(ti.duration) AS totalDuration",
            "FROM t_sport_rehab_plan_item_execute ti",
            "LEFT JOIN t_sport_rehab_plan p ON ti.plan_id = p.id",
            "WHERE p.patient_id = #{patientId}"
    })
    SportStatVO aggregateSportStats(@Param("patientId") Long patientId, @Param("startTime") LocalDateTime startTime);
}
