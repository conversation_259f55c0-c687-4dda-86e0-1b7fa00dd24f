package com.ykl.med.rehab.service.stat;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.util.CrudUtils;
import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.api.PsychotherapyFeign;
import com.ykl.med.masterdata.entiry.vo.PsychotherapyVO;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.vo.form.PatientFormQueryVO;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.rehab.service.psycho.PsychoRehabPlanItemExecuteService;
import com.ykl.med.rehab.vo.resp.stat.PsychoStatVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PsychoStatService {
    private static final String PSYCHO_STAT_REDIS_KEY = "rehab:psycho:stat:";
    @Resource
    private PsychoRehabPlanItemExecuteService psychoRehabPlanItemExecuteService;
    @Resource
    private PatientFormFeign patientFormFeign;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private PsychotherapyFeign psychotherapyFeign;

    private PsychoStatVO statByPatientId(Long patientId) {
        PsychoStatVO stat = psychoRehabPlanItemExecuteService.selectLoveItemStat(patientId);
        if (stat == null) {
            stat = new PsychoStatVO();
        } else {
            if (stat.getLoveItemId() != null) {
                PsychotherapyVO psychotherapyVO = CrudUtils.getById(psychotherapyFeign::crud, stat.getLoveItemId());
                if (psychotherapyVO != null) {
                    stat.setLoveItemName(psychotherapyVO.getName());
                }
            }
        }
        PsychoStatVO statExec = psychoRehabPlanItemExecuteService.aggregatePsychoStats(patientId, LocalDateTime.now().minusMonths(3));
        List<PatientFormVO> patientForms = patientFormFeign.query(new PatientFormQueryVO()
                .setPatientId(patientId)
                .setWriteStatus(true)
                .setType(PatientFormBizType.PSYCHOLOGY));
        stat.setCountForm(patientForms == null ? 0 : patientForms.size());
        stat.setTotalDuration(statExec.getTotalDuration());
        stat.setThreeMonthTotalCount(statExec.getThreeMonthTotalCount());
        stat.setThreeMonthCompletedCount(statExec.getThreeMonthCompletedCount());
        stat.setTotalExecCount(statExec.getTotalExecCount());
        stat.setThreeMonthExecRate(stat.getThreeMonthTotalCount() == 0 ? 100 : stat.getThreeMonthCompletedCount() * 100 / stat.getThreeMonthTotalCount());
        return stat;
    }

    public PsychoStatVO getStatByPatientId(Long patientId) {
        String psychoStatJson = stringRedisTemplate.opsForValue().get(PSYCHO_STAT_REDIS_KEY + patientId);
        if (psychoStatJson != null) {
            return JSONObject.parseObject(psychoStatJson, PsychoStatVO.class);
        }
        PsychoStatVO psychoStat = this.statByPatientId(patientId);
        stringRedisTemplate.opsForValue().set(PSYCHO_STAT_REDIS_KEY + patientId, JSONObject.toJSONString(psychoStat), 1, TimeUnit.DAYS);
        return psychoStat;
    }

    public void refreshStat(Long patientId) {
        stringRedisTemplate.delete(PSYCHO_STAT_REDIS_KEY + patientId);
    }
}
