package com.ykl.med.rehab.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.rehab.api.SportRehabFeign;
import com.ykl.med.rehab.service.sport.SportRehabBasePlanService;
import com.ykl.med.rehab.service.sport.SportRehabPlanItemExecuteService;
import com.ykl.med.rehab.service.sport.SportRehabPlanService;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.sport.SportRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.FormAndSnapshotRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanDetailRespVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
public class SportRehabController implements SportRehabFeign {

    private final SportRehabPlanService sportRehabPlanService;

    private final SportRehabPlanItemExecuteService sportRehabPlanItemExecuteService;

    private final SportRehabBasePlanService sportRehabBasePlanService;
    private final EventTaskFeign eventTaskFeign;

    @PostMapping("/sportRehab/plan/getDefaultForm")
    public Long getDefaultForm() {
        return sportRehabBasePlanService.getDefaultForm();
    }

    @PostMapping("/sportRehab/plan/getBasePlanReq")
    public SportRehabPlanSaveOrUpdateReqVO getBasePlanReq(@RequestParam("patientId") Long patientId) {
        return sportRehabBasePlanService.getBasePlanReq(patientId);
    }

    @PostMapping("/sportRehab/plan/list")
    public List<SportRehabPlanDetailRespVO> listPlan(@RequestParam("patientId") Long patientId) {
        return sportRehabPlanService.list(patientId);
    }


    @Override
    @PostMapping("/sportRehab/plan/saveOrUpdate")
    public Long saveOrUpdate(@Valid @RequestBody SportRehabPlanSaveOrUpdateReqVO reqVO) {
        Long id = sportRehabPlanService.saveOrUpdate(reqVO);

        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO().setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString())
                .setBizId(id.toString())
                .setBizType(EventTaskType.SPORTS_REHAB_PLAN_CHANGE);
        eventTaskAddVO.setUserId(reqVO.getCurrentUserId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
        return id;
    }

    @Override
    @PostMapping("/sportRehab/plan/getCurrentPlan")
    public SportRehabPlanDetailRespVO getCurrentPlan(@RequestParam("patientId") Long patientId) {
        return sportRehabPlanService.getCurrentPlan(patientId);
    }

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/count")
    public List<RehabPlanExecuteCountRespVO> count(@Valid @RequestBody RehabPlanStatReqVO reqVO) {
        return sportRehabPlanItemExecuteService.count(reqVO);
    }

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/execute")
    public void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        sportRehabPlanItemExecuteService.execute(reqVO);
    }

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/generateExecute")
    public void generateExecute(@RequestParam("planId") Long planId) {
        sportRehabPlanItemExecuteService.generateExecute(planId, false);
    }

    @Override
    @PostMapping("/sportRehab/plan/getHistoryPlans")
    public List<SportRehabPlanDetailRespVO> getHistoryPlans(@RequestBody HistoryPlanReqVO reqVO) {
        return sportRehabPlanService.getHistoryPlans(reqVO);
    }

    @Override
    @PostMapping("/sportRehab/plan/extend")
    public void extendPlan(@RequestBody ExtendPlanReqVO reqVO) {
        sportRehabPlanService.extendPlan(reqVO);
    }

    @Override
    @PostMapping("/sportRehab/plan/reEvaluate")
    public void reEvaluate(@RequestParam("patientId") Long patientId) {
        sportRehabPlanService.reEvaluate(patientId);
    }

    @Override
    @PostMapping("/sportRehab/plan/getDraftPlan")
    public SportRehabPlanDetailRespVO getDraftPlan(@RequestParam("patientId") Long patientId) {
        return sportRehabPlanService.getDraftPlan(patientId);
    }

    @Override
    @PostMapping("/sportRehab/plan/getFormAndSnapshots")
    public List<FormAndSnapshotRespVO> getFormAndSnapshots(@RequestBody FormAndSnapshotReqVO reqVO) {
        return sportRehabPlanService.getFormAndSnapshots(reqVO);
    }

    @Override
    @PostMapping("/sportRehab/plan/getPlanById")
    public SportRehabPlanDetailRespVO getPlanById(@RequestBody PatientIdAndPlanIdVO reqVO) {
        return sportRehabPlanService.getPlanById(reqVO);
    }

    @PostMapping("/sportRehab/plan/getById")
    @Override
    public SportRehabPlanDetailRespVO getById(@RequestParam("id") Long id) {
        return sportRehabPlanService.getById(id);
    }

    @Override
    @PostMapping("/sportRehab/plan/getAllCurrentPlanIds")
    public PageResult<Long> getAllCurrentPlanIds(@RequestBody PageParam pageParam) {
        IPage<Long> page = sportRehabPlanService.getAllCurrentPlanIds(pageParam);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @PostMapping("/sportRehab/plan/getAllHasPlanPatientIds")
    @Override
    public List<Long> getAllHasPlanPatientIds() {
        return sportRehabPlanService.getAllHasPlanPatientIds();
    }

    @PostMapping("/sportRehab/plan/getAllIds")
    @Override
    public List<Long> getAllIds() {
        return sportRehabPlanService.getAllIds();
    }

    @Override
    @PostMapping("/sportRehab/plan/itemExecute/generateExecuteBatch")
    public void generateExecuteBatch(@RequestBody Collection<Long> currentPlanIds) {
        sportRehabPlanItemExecuteService.generateExecuteBatch(currentPlanIds);
    }

    @Override
    @GetMapping("/sportRehab/plan/isExistPlanExcludeDraft")
    public boolean isExistPlanExcludeDraft(@RequestParam("patientId") Long patientId) {
        return sportRehabPlanService.isExistPlanExcludeDraft(patientId);
    }
}