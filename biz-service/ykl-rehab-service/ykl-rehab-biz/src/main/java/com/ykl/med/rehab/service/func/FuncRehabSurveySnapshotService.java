package com.ykl.med.rehab.service.func;

import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.resp.func.FuncRehabSurveySnapshotVO;

import java.util.List;

/**
 * 功能康复问卷快照服务接口。
 * 本接口提供了功能康复问卷快照的创建、完成、列表查询等功能。
 * 主要用于处理患者功能康复问卷数据的快照操作。
 *
 * <AUTHOR>
 */
public interface FuncRehabSurveySnapshotService {

    /**
     * 发送功能康复问卷快照并发送消息通知患者及其家属。
     * 此方法用于生成功能康复问卷的快照，并将其发送给相关的患者和家属。
     *
     * @param reqVO 包含问卷快照发送所需数据的请求对象。
     */
    void send(RehabSurveySnapshotSendReqVO reqVO);

    /**
     * 完成功能康复问卷快照的处理。
     * 此方法用于标记功能康复问卷快照的完成状态，并进行相应的处理。
     *
     * @param reqVO 包含问卷快照完成所需数据的请求对象。
     */
    void complete(RehabSurveySnapshotCompleteReqVO reqVO);

    /**
     * 批量完成功能康复问卷快照的处理。
     * 此方法用于标记功能康复问卷快照的完成状态，并进行相应的处理。
     *
     * @param reqVO 包含问卷快照完成所需数据的请求对象。
     */
    void batchComplete(RehabSurveySnapshotsBatchSaveReqVO reqVO);

    /**
     * 获取功能康复问卷快照列表。
     * 此方法用于查询和返回功能康复问卷快照的列表。
     *
     * @param reqVO 请求对象。
     * @return 功能康复问卷快照的列表。
     */
    List<FuncRehabSurveySnapshotVO> list(SurveySnapshotListReqVO reqVO);


    /**
     * 医护端填写问卷接口
     * 如果没有id, 则创建快照，如果有id, 则更新快照
     *
     * @param reqVO 请求对象
     */
    void saveOrUpdate(SurveySnapshotSaveOrUpdateReqVO reqVO);


    /**
     * 复制快照
     *
     * @param sourcePlanId 源计划id
     * @param targetPlanId 目标计划id
     * @return 复制后的快照列表
     */
    List<FuncRehabSurveySnapshotVO> copySnapshots(Long sourcePlanId, Long targetPlanId);


    FuncRehabSurveySnapshotVO details(Long id);


    List<Long> getPatientIdNullPlanId();

    Integer countCompleteByPatientId(Long patientId);
}
