package com.ykl.med.rehab.service.sport;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.google.common.collect.Maps;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.framework.trx.TransactionalAfterCommitExecutor;
import com.ykl.med.masterdata.api.FrequencyDataFeign;
import com.ykl.med.masterdata.api.sport.SportProjectFeign;
import com.ykl.med.masterdata.entiry.dto.FrequencyParamDTO;
import com.ykl.med.masterdata.entiry.vo.FrequencyUtilVO;
import com.ykl.med.masterdata.vo.SportProjectVO;
import com.ykl.med.masterdata.vo.SportTrainVO;
import com.ykl.med.rehab.constants.RehabErrorCode;
import com.ykl.med.rehab.entity.SportRehabPlanDO;
import com.ykl.med.rehab.entity.SportRehabPlanItemDO;
import com.ykl.med.rehab.entity.SportRehabPlanItemExecuteDO;
import com.ykl.med.rehab.enums.CompleteStatus;
import com.ykl.med.rehab.mapper.SportRehabPlanItemExecuteMapper;
import com.ykl.med.rehab.mapper.SportRehabPlanItemMapper;
import com.ykl.med.rehab.mapper.SportRehabPlanMapper;
import com.ykl.med.rehab.service.stat.SportStatService;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanExecuteRecordRespVO;
import com.ykl.med.rehab.vo.resp.stat.SportStatVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 运动康复计划项执行服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SportRehabPlanItemExecuteServiceImpl
        extends ServiceImpl<SportRehabPlanItemExecuteMapper, SportRehabPlanItemExecuteDO>
        implements SportRehabPlanItemExecuteService {
    @Resource
    private SportRehabPlanItemService sportRehabPlanItemService;
    @Resource
    private FrequencyDataFeign frequencyDataFeign;
    @Resource
    private SportProjectFeign sportProjectFeign;
    @Resource
    private SportStatService sportStatService;
    @Resource
    private SportRehabPlanMapper sportRehabPlanMapper;

    @Override
    public List<RehabPlanExecuteCountRespVO> count(RehabPlanStatReqVO reqVO) {
        LocalDateTime startOfDay = getStartOfDay(reqVO.getDate());
        LocalDateTime endOfDay = getEndOfDay(startOfDay);

        Map<Long, List<SportRehabPlanItemExecuteDO>> groupedItems = fetchAndGroupItems(reqVO, startOfDay, endOfDay);
        if (groupedItems.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Long, SportRehabPlanDetailRespVO.PlanItem> planItems = fetchPlanItems(reqVO);
        Map<Long, SportProjectVO> templates = fetchTemplates(planItems);

        return createResponseList(groupedItems, planItems, templates);
    }

    private LocalDateTime getStartOfDay(String date) {
        return LocalDate.parse(date).atStartOfDay();
    }

    private LocalDateTime getEndOfDay(LocalDateTime startOfDay) {
        return startOfDay.plusDays(1).minusSeconds(1);
    }

    private Map<Long, List<SportRehabPlanItemExecuteDO>> fetchAndGroupItems(RehabPlanStatReqVO reqVO,
                                                                            LocalDateTime startOfDay, LocalDateTime endOfDay) {
        return super.lambdaQuery()
                .eq(SportRehabPlanItemExecuteDO::getPlanId, reqVO.getPlanId())
                .between(SportRehabPlanItemExecuteDO::getRequiredTime, startOfDay, endOfDay)
                .list()
                .stream()
                .collect(Collectors.groupingBy(SportRehabPlanItemExecuteDO::getPlanItemId));
    }

    private Map<Long, SportRehabPlanDetailRespVO.PlanItem> fetchPlanItems(RehabPlanStatReqVO reqVO) {
        return sportRehabPlanItemService.list(reqVO.getPlanId()).stream()
                .collect(Collectors.toMap(SportRehabPlanDetailRespVO.PlanItem::getId, Function.identity()));
    }

    private Map<Long, SportProjectVO> fetchTemplates(Map<Long, SportRehabPlanDetailRespVO.PlanItem> planItems) {
        return sportProjectFeign.querySportProjectByIds(
                        planItems.values().stream()
                                .map(SportRehabPlanDetailRespVO.PlanItem::getTemplateId)
                                .distinct()
                                .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(SportProjectVO::getId, Function.identity()));
    }

    private List<RehabPlanExecuteCountRespVO> createResponseList(
            Map<Long, List<SportRehabPlanItemExecuteDO>> groupedItems,
            Map<Long, SportRehabPlanDetailRespVO.PlanItem> planItems, Map<Long, SportProjectVO> templates) {
        return groupedItems.entrySet().stream()
                .map(entry -> createResponse(entry.getKey(), planItems, templates, entry.getValue()))
                .collect(Collectors.toList());
    }

    private RehabPlanExecuteCountRespVO createResponse(Long itemId,
                                                       Map<Long, SportRehabPlanDetailRespVO.PlanItem> idItemMap, Map<Long, SportProjectVO> idTemplateMap,
                                                       List<SportRehabPlanItemExecuteDO> itemList) {
        int executedCount = (int) itemList.stream()
                .filter(item -> Objects.equals(CompleteStatus.COMPLETED, item.getStatus()))
                .count();

        SportRehabPlanDetailRespVO.PlanItem planItem = idItemMap.get(itemId);
        SportProjectVO template = idTemplateMap.get(planItem.getTemplateId());

        RehabPlanExecuteCountRespVO respVO = new RehabPlanExecuteCountRespVO();
        respVO.setPlanItemId(itemId);
        respVO.setPlanItemName(template.getName());
        respVO.setExecutedCount(executedCount);
        respVO.setUnit(template.getUnit());
        respVO.setExecutedActions(planItem.getActionsPerSession() * executedCount);
        respVO.setTotalCount(itemList.size());
        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        log.info("Executing rehab plan, reqVO: {}", JSON.toJSONString(reqVO));

        LocalDateTime begin, end;
        if (reqVO.getTargetDate() != null) {
            begin = reqVO.getTargetDate().atStartOfDay();
            end = begin.plusDays(1).minusSeconds(1);
        } else {
            begin = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
            end = begin.plusDays(1).minusSeconds(1);
        }

        SportRehabPlanItemExecuteDO itemExecute = super.lambdaQuery()
                .between(SportRehabPlanItemExecuteDO::getRequiredTime, begin, end)
                .eq(SportRehabPlanItemExecuteDO::getPlanId, reqVO.getPlanId())
                .eq(SportRehabPlanItemExecuteDO::getPlanItemId, reqVO.getPlanItemId())
                .eq(SportRehabPlanItemExecuteDO::getStatus, CompleteStatus.UNCOMPLETED)
                .list().stream()
                .findFirst()
                .orElseThrow(() -> new ServiceException(RehabErrorCode.EXECUTE_RECORD_NOT_EXISTS));

        itemExecute.setStatus(CompleteStatus.COMPLETED);
        itemExecute.setExecuteTime(reqVO.getExecuteTime() != null ? reqVO.getExecuteTime() : LocalDateTime.now());
        itemExecute.setDuration(reqVO.getDuration());
        itemExecute.setStartTime(reqVO.getStartTime());
        itemExecute.setEndTime(reqVO.getEndTime());
        itemExecute.setEvaluation(reqVO.getEvaluation());
        super.updateById(itemExecute);
        TransactionalAfterCommitExecutor.executeAfterCommit(() -> sportStatService.refreshStat(reqVO.getCurrentPatientId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateExecute(Long planId, boolean immediatelyFlag) {
        // Get start times for plan ID
        SportRehabPlanDO sportRehabPlanDO = sportRehabPlanMapper.selectById(planId);
        LocalDateTime localDateTime = sportRehabPlanDO.getStartTime();
        // Get items for plan ID
        List<SportRehabPlanDetailRespVO.PlanItem> itemsMap = sportRehabPlanItemService
                .list(planId);
        if (itemsMap.isEmpty()) {
            return;
        }

        // 获取所有频率的执行时间，并按频率id转map
        Map<Long, List<LocalDateTime>> frequencyIdTimeMap = getTodayTime(localDateTime, itemsMap, immediatelyFlag);
        if (frequencyIdTimeMap.isEmpty()) {
            return;
        }

        // Build all potential executed items for all plan
        List<SportRehabPlanItemExecuteDO> potentialExecutedItems = itemsMap.stream()
                .flatMap(item -> buildExecutedItemsFor(item, frequencyIdTimeMap))
                .collect(Collectors.toList());

        // Get already existing execution items to filter out
        Set<String> existingItemsKeys = getExistingExecutionItemsKeys(potentialExecutedItems);

        // Filter out already existing execution items
        List<SportRehabPlanItemExecuteDO> newExecutedItems = potentialExecutedItems.stream()
                .filter(item -> !existingItemsKeys.contains(buildExecutionItemKey(item)))
                .collect(Collectors.toList());

        // Save new execution items if any
        if (!newExecutedItems.isEmpty()) {
            saveBatch(newExecutedItems);
        }
        TransactionalAfterCommitExecutor.executeAfterCommit(() -> sportStatService.refreshStat(sportRehabPlanDO.getPatientId()));
    }

    @Override
    public RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(ItemExecStatReqVO reqVO) {
        LocalDate startDate, endDate;
        endDate = reqVO.getEndDate() == null ? LocalDate.now() : reqVO.getEndDate();
        startDate = reqVO.getStartDate() == null ? endDate.minusDays(6) : reqVO.getStartDate();
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

        // 查询所有方案
        List<SportRehabPlanDO> plans = sportRehabPlanMapper.selectList(new LambdaQueryWrapperX<SportRehabPlanDO>()
                .eq(SportRehabPlanDO::getStatus, CommonStatusEnum.ENABLE)
                .eq(SportRehabPlanDO::getPatientId, reqVO.getPatientId()));
        List<Long> planIds = plans.stream().map(SportRehabPlanDO::getId).collect(Collectors.toList());
        if (planIds.isEmpty()) {
            throw new ServiceException(RehabErrorCode.PLAN_NOT_EXISTS);
        }
        // 一次性查询过去7天的所有执行记录
        List<SportRehabPlanItemExecuteDO> executes = super.lambdaQuery()
                .in(SportRehabPlanItemExecuteDO::getPlanId, planIds)
                .eq(SportRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                .between(SportRehabPlanItemExecuteDO::getRequiredTime, startDateTime, endDateTime)
                .list();

        // 根据planIds获取所有计划项并以templateId为键组成的map
        Map<Long, List<SportRehabPlanDetailRespVO.PlanItem>> templateToItemsMap = sportRehabPlanItemService
                .listByPlanIds(planIds)
                .values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(SportRehabPlanDetailRespVO.PlanItem::getTemplateId));
        // 如果没有计划项，直接返回空的响应
        if (templateToItemsMap.isEmpty()) {
            return new RehabPlanWeeklyStatRespWrapperVO();
        }

        List<Long> templateIds = new ArrayList<>(templateToItemsMap.keySet());
        Map<Long, Long> itemIdToTemplateId = templateToItemsMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(SportRehabPlanDetailRespVO.PlanItem::getId,
                        SportRehabPlanDetailRespVO.PlanItem::getTemplateId));

        // 获取模板详细信息
        Map<Long, SportProjectVO> templateDetailsMap = sportProjectFeign.querySportProjectByIds(templateIds).stream()
                .collect(Collectors.toMap(SportProjectVO::getId, Function.identity()));

        // 构建每个模板的统计序列
        List<RehabPlanWeeklyStatRespWrapperVO.Series> allSeries = templateIds.stream()
                .map(templateId -> createSeriesByTemplate(templateId, templateDetailsMap.get(templateId),
                        templateToItemsMap.get(templateId)))
                .collect(Collectors.toList());

        // 构建每一天的统计数据
        List<RehabPlanWeeklyStatRespWrapperVO.DailyData> dailyDataList = Stream
                .iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .map(date -> createDailyData(date, executes, allSeries, itemIdToTemplateId))
                .collect(Collectors.toList());

        RehabPlanWeeklyStatRespWrapperVO wrapperVO = new RehabPlanWeeklyStatRespWrapperVO();
        wrapperVO.setSeriesList(allSeries);
        wrapperVO.setDailyDataList(dailyDataList);
        return wrapperVO;
    }

    @Override
    public List<String> getEvaluationByPatientId(Long patientId) {
        return baseMapper.getEvaluationByPatientId(patientId);
    }

    @Override
    public SportStatVO aggregateSportStats(Long patientId, LocalDateTime startTime) {
        return baseMapper.aggregateSportStats(patientId, startTime);
    }


    private RehabPlanWeeklyStatRespWrapperVO.Series createSeriesByTemplate(Long templateId,
                                                                           SportProjectVO template,
                                                                           List<SportRehabPlanDetailRespVO.PlanItem> items) {
        RehabPlanWeeklyStatRespWrapperVO.Series series = new RehabPlanWeeklyStatRespWrapperVO.Series();
        series.setPlanItemId(templateId);
        series.setPlanItemName(template.getName());
        series.setPlanItemMethod(
                template.getTrains().stream().map(SportTrainVO::getName).collect(Collectors.toList()));
        series.setUnit(template.getUnit());
        // Assume all items associated with this template have the same actions per
        // session
        series.setActionsPerSession(
                items.isEmpty() ? 0 : items.stream().findFirst()
                        .map(SportRehabPlanDetailRespVO.PlanItem::getActionsPerSession).orElse(0)
        );
        return series;
    }

    private RehabPlanWeeklyStatRespWrapperVO.DailyData createDailyData(LocalDate date,
                                                                       List<SportRehabPlanItemExecuteDO> executes,
                                                                       List<RehabPlanWeeklyStatRespWrapperVO.Series> allSeries,
                                                                       Map<Long, Long> itemIdToTemplateId) {
        List<SportRehabPlanItemExecuteDO> dailyExecutes = executes.stream()
                .filter(execute -> execute.getRequiredTime().toLocalDate().equals(date))
                .collect(Collectors.toList());

        Map<Long, Long> templateIdToExecutedDurationSum = dailyExecutes.stream()
                .collect(Collectors.groupingBy(
                        sportRehabPlanItemExecuteDO -> itemIdToTemplateId
                                .get(sportRehabPlanItemExecuteDO.getPlanItemId()),
                        Collectors.summingLong(SportRehabPlanItemExecuteDO::getDuration)));

        List<Integer> executedCounts = allSeries.stream()
                .map(series -> templateIdToExecutedDurationSum.getOrDefault(series.getPlanItemId(), 0L).intValue())
                .collect(Collectors.toList());

        RehabPlanWeeklyStatRespWrapperVO.DailyData dailyData = new RehabPlanWeeklyStatRespWrapperVO.DailyData();
        dailyData.setDate(date.atStartOfDay());
        dailyData.setExecutedCounts(executedCounts);
        return dailyData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExecuteRecord(RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        SportRehabPlanItemExecuteDO executeDO = baseMapper.selectById(reqVO.getId());
        if (executeDO != null) {
            baseMapper.updateById(CopyPropertiesUtil.copy(reqVO, () -> executeDO, true));
        } else {
            throw new RuntimeException("未找到对应的执行记录: " + reqVO.getId());
        }
        TransactionalAfterCommitExecutor.executeAfterCommit(() -> sportStatService.refreshStat(reqVO.getCurrentPatientId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteExecuteRecord(Long id) {
        log.info("删除运动康复计划项执行记录，id: {}", id);
        baseMapper.deleteById(id);
    }

    @Override
    public PageResult<SportRehabPlanExecuteRecordRespVO> queryExecuteRecords(RehabPlanExecuteQueryReqVO reqVO) {
        PageResult<SportRehabPlanItemExecuteDO> pageResult = baseMapper.selectPage(reqVO,
                new LambdaQueryWrapperX<SportRehabPlanItemExecuteDO>()
                        .eq(SportRehabPlanItemExecuteDO::getPlanId, reqVO.getPlanId())
                        .eq(SportRehabPlanItemExecuteDO::getPlanItemId, reqVO.getPlanItemId())
                        .eq(SportRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                        .orderByDesc(SportRehabPlanItemExecuteDO::getExecuteTime));

        if (pageResult.getList().isEmpty()) {
            return PageResult.empty();
        }

        // 调用新的方法来填充数据
        List<SportRehabPlanExecuteRecordRespVO> records = fillExecuteRecordData(pageResult.getList());

        return new PageResult<>(records, pageResult.getTotal());
    }

    @Override
    public Map<String, List<SportRehabPlanExecuteRecordRespVO>> queryExecuteRecordsByTimeRange(
            RehabPlanExecuteTimeRangeQueryReqVO reqVO) {
        // 查询所有方案
        List<Long> planIds;
        if (reqVO.getPlanId() != null) {
            planIds = Collections.singletonList(reqVO.getPlanId());
        } else {
            List<SportRehabPlanDO> plans = sportRehabPlanMapper.selectList(new LambdaQueryWrapperX<SportRehabPlanDO>()
                    .eq(SportRehabPlanDO::getPatientId, reqVO.getPatientId()));
            planIds = plans.stream().map(SportRehabPlanDO::getId).collect(Collectors.toList());
        }
        if (planIds.isEmpty()) {
            throw new ServiceException(RehabErrorCode.PLAN_NOT_EXISTS);
        }

        List<SportRehabPlanItemExecuteDO> executeDOs = baseMapper
                .selectList(new LambdaQueryWrapperX<SportRehabPlanItemExecuteDO>()
                        .in(SportRehabPlanItemExecuteDO::getPlanId, planIds)
                        .eqIfPresent(SportRehabPlanItemExecuteDO::getPlanItemId, reqVO.getPlanItemId())
                        .eq(SportRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                        .between(SportRehabPlanItemExecuteDO::getExecuteTime, reqVO.getStartTime(), reqVO.getEndTime())
                        .orderByDesc(SportRehabPlanItemExecuteDO::getExecuteTime));

        // 调用新的方法来填充数据
        List<SportRehabPlanExecuteRecordRespVO> executeRecords = fillExecuteRecordData(executeDOs);

        Map<String, List<SportRehabPlanExecuteRecordRespVO>> unsortedMap = executeRecords.stream()
                .collect(Collectors.groupingBy(record -> record.getExecuteTime().toLocalDate().toString(),
                        Collectors.toList()));

        Map<String, List<SportRehabPlanExecuteRecordRespVO>> sortedMap = new TreeMap<>(Comparator.reverseOrder());
        sortedMap.putAll(unsortedMap);

        return sortedMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateExecuteBatch(Collection<Long> planIds) {
        log.info("Batch generating SportRehabPlanItemExecutes, planIds: {}", planIds);
        planIds.forEach(e -> this.generateExecute(e, false));
    }

    private Map<Long, List<LocalDateTime>> getTodayTime(LocalDateTime localDateTime, List<SportRehabPlanDetailRespVO.PlanItem> items, boolean immediatelyFlag) {
        LocalDate nowDay = DateTimeUtils.getNowDay();
        LocalDateTime startTime = DateTimeUtils.someTimeStart(nowDay);
        LocalDateTime endTime = DateTimeUtils.someTimeEnd(nowDay);
        List<FrequencyParamDTO> frequencyParamDTOList = new ArrayList<>();
        Map<Long, Long> frequencyIdMap = new HashMap<>();
        for (SportRehabPlanDetailRespVO.PlanItem planItem : items) {
            Long frequencyId = planItem.getFrequencyId();
            if (frequencyIdMap.get(frequencyId) != null) {
                continue;
            }
            frequencyIdMap.put(frequencyId, frequencyId);
            FrequencyParamDTO frequencyParamDTO = new FrequencyParamDTO();
            frequencyParamDTO.setId(planItem.getFrequencyId().toString());
            frequencyParamDTO.setFrequencyId(planItem.getFrequencyId().toString());
            frequencyParamDTO.setFrequencyStartTime(Collections.singletonList(localDateTime.toString()));
            frequencyParamDTO.setFrequencyFixDate(immediatelyFlag && localDateTime.isBefore(startTime) ? DateTimeUtils.getNowDayTimeString() : localDateTime.toString());
            frequencyParamDTO.setStartTime(startTime);
            frequencyParamDTO.setEndTime(endTime);
            frequencyParamDTOList.add(frequencyParamDTO);
        }
        PageResult<FrequencyUtilVO> frequencyBizPageResult = frequencyDataFeign.genFrequencySetTime(frequencyParamDTOList);
        if (CollectionUtils.isEmpty(frequencyBizPageResult.getList())) {
            return new HashMap<>();
        }

        return frequencyBizPageResult.getList().stream()
                .collect(Collectors.toMap(e -> Long.valueOf(e.getId()), FrequencyUtilVO::getGenExecTimeList));
    }

    private Set<String> getExistingExecutionItemsKeys(List<SportRehabPlanItemExecuteDO> items) {
        Map<Long, List<LocalDateTime>> planItemIdToTimes = items.stream()
                .collect(Collectors.groupingBy(SportRehabPlanItemExecuteDO::getPlanItemId,
                        Collectors.mapping(SportRehabPlanItemExecuteDO::getRequiredTime, Collectors.toList())));
        List<PlanItemTimeVO> planItemTimes = planItemIdToTimes.entrySet().stream()
                .map(entry -> new PlanItemTimeVO()
                        .setPlanItemId(entry.getKey())
                        .setRequiredTimes(entry.getValue()))
                .collect(Collectors.toList());
        if (planItemTimes.isEmpty()) {
            return Collections.emptySet();
        }
        List<SportRehabPlanItemExecuteDO> executes = baseMapper.findPlanItems(planItemTimes);
        return executes.stream()
                .map(this::buildExecutionItemKey)
                .collect(Collectors.toSet());
    }

    private String buildExecutionItemKey(SportRehabPlanItemExecuteDO item) {
        return item.getPlanId() + ":" + item.getPlanItemId() + ":" + item.getRequiredTime();
    }

    private List<SportRehabPlanExecuteRecordRespVO> fillExecuteRecordData(List<SportRehabPlanItemExecuteDO> items) {
        if (items.isEmpty()) {
            return Collections.emptyList();
        }
        // 提取所有的planItemIds
        List<Long> planItemIds = items.stream()
                .map(SportRehabPlanItemExecuteDO::getPlanItemId)
                .collect(Collectors.toList());

        // 通过planItemIds获取所有相关的SportRehabPlanItemDO
        Map<Long, SportRehabPlanItemDO> rehabPlanItemMap = SpringContentUtils.getBean(SportRehabPlanItemMapper.class)
                .selectBatchIds(planItemIds)
                .stream()
                .collect(Collectors.toMap(SportRehabPlanItemDO::getId, Function.identity()));

        // 提取所有的itemTemplateIds
        List<Long> itemTemplateIds = rehabPlanItemMap.values().stream()
                .map(SportRehabPlanItemDO::getTemplateId).distinct().collect(Collectors.toList());

        // 通过itemTemplateIds获取所有相关的模板详情
        Map<Long, SportProjectVO> itemTemplateMap = sportProjectFeign.querySportProjectByIds(itemTemplateIds)
                .stream()
                .collect(Collectors.toMap(SportProjectVO::getId, Function.identity()));

        // 转换执行记录到响应对象
        return items.stream()
                .map(item -> {
                    SportRehabPlanItemDO planItem = rehabPlanItemMap.get(item.getPlanItemId());
                    SportProjectVO templateDetail = itemTemplateMap.get(planItem.getTemplateId());

                    SportRehabPlanExecuteRecordRespVO respVO = CopyPropertiesUtil.normalCopyProperties(item,
                            SportRehabPlanExecuteRecordRespVO.class);
                    respVO.setPlanItemName(templateDetail.getName());
                    respVO.setPlanItemMethod(templateDetail.getTrains().stream().map(SportTrainVO::getName)
                            .collect(Collectors.joining(",")));
                    return respVO;
                })
                .collect(Collectors.toList());
    }

    private Stream<SportRehabPlanItemExecuteDO> buildExecutedItemsFor(
            SportRehabPlanDetailRespVO.PlanItem item,
            Map<Long, LocalDateTime> idToStartTime,
            Map<Long, Map<LocalDateTime, List<LocalDateTime>>> todayTimes) {
        return Optional
                .ofNullable(todayTimes.getOrDefault(item.getFrequencyId(), Maps.newHashMap()).get(idToStartTime.get(item.getPlanId())))
                .orElse(Collections.emptyList()).stream()
                .map(time -> new SportRehabPlanItemExecuteDO()
                        .setPlanId(item.getPlanId())
                        .setPlanItemId(item.getId())
                        .setStatus(CompleteStatus.UNCOMPLETED)
                        .setRequiredTime(time));
    }

    private Stream<SportRehabPlanItemExecuteDO> buildExecutedItemsFor(
            SportRehabPlanDetailRespVO.PlanItem item,
            Map<Long, List<LocalDateTime>> frequencyIdTimeMap) {
        return frequencyIdTimeMap.getOrDefault(item.getFrequencyId(), new ArrayList<>())
                .stream().map(e -> new SportRehabPlanItemExecuteDO()
                        .setPlanId(item.getPlanId())
                        .setPlanItemId(item.getId())
                        .setStatus(CompleteStatus.UNCOMPLETED)
                        .setRequiredTime(e));
    }


}