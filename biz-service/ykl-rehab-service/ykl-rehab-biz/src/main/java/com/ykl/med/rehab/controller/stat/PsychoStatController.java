package com.ykl.med.rehab.controller.stat;

import com.ykl.med.rehab.api.stat.PsychoStatFeign;
import com.ykl.med.rehab.service.stat.PsychoStatService;
import com.ykl.med.rehab.vo.resp.stat.PsychoStatVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class PsychoStatController implements PsychoStatFeign {
    private final PsychoStatService psychoStatService;

    @PostMapping("/psycho/stat/getStatByPatientId")
    @Override
    public PsychoStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId) {
        return psychoStatService.getStatByPatientId(patientId);
    }

    @PostMapping("/psycho/stat/refreshStat")
    @Override
    public void refreshStat(@RequestParam(value = "patientId") Long patientId) {
        psychoStatService.refreshStat(patientId);
    }
}
