package com.ykl.med.rehab.service.stat;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.vo.form.PatientFormQueryVO;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.rehab.vo.resp.stat.NutritionalStatVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class NutritionalStatService {
    private static final String NUTRITIONAL_STAT_REDIS_KEY = "rehab:nutritional:stat:";
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private PatientFormFeign patientFormFeign;

    public NutritionalStatVO getStatByPatientId(Long patientId) {
        String nutritionalStatJson = stringRedisTemplate.opsForValue().get(NUTRITIONAL_STAT_REDIS_KEY + patientId);
        if (nutritionalStatJson != null) {
            return JSONObject.parseObject(nutritionalStatJson, NutritionalStatVO.class);
        }
        NutritionalStatVO nutritionalStat = this.statByPatientId(patientId);
        stringRedisTemplate.opsForValue().set(NUTRITIONAL_STAT_REDIS_KEY + patientId, JSONObject.toJSONString(nutritionalStat), 1, TimeUnit.DAYS);
        return nutritionalStat;
    }

    public void refreshStat(Long patientId) {
        stringRedisTemplate.delete(NUTRITIONAL_STAT_REDIS_KEY + patientId);
    }

    private NutritionalStatVO statByPatientId(Long patientId) {
        NutritionalStatVO stat = new NutritionalStatVO();
        List<PatientFormVO> patientForms = patientFormFeign.query(new PatientFormQueryVO()
                .setPatientId(patientId)
                .setWriteStatus(true)
                .setType(PatientFormBizType.PSYCHOLOGY));
        stat.setCountForm(patientForms == null ? 0 : patientForms.size());
        return new NutritionalStatVO();
    }
}
