package com.ykl.med.rehab.controller.stat;

import com.ykl.med.rehab.api.stat.SportStatFeign;
import com.ykl.med.rehab.service.stat.SportStatService;
import com.ykl.med.rehab.vo.resp.stat.SportStatVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class SportStatController implements SportStatFeign {
    private final SportStatService sportStatService;

    @PostMapping("/sport/stat/getStatByPatientId")
    @Override
    public SportStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId) {
        return sportStatService.getStatByPatientId(patientId);
    }

    @PostMapping("/sport/stat/refreshStat")
    @Override
    public void refreshStat(@RequestParam(value = "patientId") Long patientId) {
        sportStatService.refreshStat(patientId);
    }
}
