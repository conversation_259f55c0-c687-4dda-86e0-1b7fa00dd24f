package com.ykl.med.rehab.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.rehab.enums.CompleteStatus;
import lombok.Data;

/**
 * 患者功能康复问卷快照
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_func_rehab_survey_snapshot", autoResultMap = true)
public class FuncRehabSurveySnapshotDO extends BaseDO {

    private static final long serialVersionUID = -2810660215347709303L;

    /**
     * 患者用户id
     */
    private Long patientId;

    /**
     * 关联的方案id
     */
    private Long planId;

    /**
     * 问卷快照状态
     */
    private CompleteStatus status;

    /**
     * 是否线上
     */
    private Boolean online;

    /**
     * 问卷快照内容
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject snapshot;

}
