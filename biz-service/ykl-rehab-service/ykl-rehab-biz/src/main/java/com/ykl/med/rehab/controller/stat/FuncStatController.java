package com.ykl.med.rehab.controller.stat;

import com.ykl.med.rehab.api.stat.FuncStatFeign;
import com.ykl.med.rehab.service.stat.FuncStatService;
import com.ykl.med.rehab.vo.resp.stat.FuncStatVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class FuncStatController implements FuncStatFeign {
    private final FuncStatService funcStatService;

    @PostMapping("/func/stat/getStatByPatientId")
    @Override
    public FuncStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId) {
        return funcStatService.getStatByPatientId(patientId);
    }

    @PostMapping("/func/stat/refreshStat")
    @Override
    public void refreshStat(@RequestParam(value = "patientId") Long patientId) {
        funcStatService.refreshStat(patientId);
    }
}
