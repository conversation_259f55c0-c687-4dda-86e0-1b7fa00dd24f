package com.ykl.med.rehab.service.psycho;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.google.common.collect.Maps;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.CrudUtils;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.framework.trx.TransactionalAfterCommitExecutor;
import com.ykl.med.masterdata.api.FrequencyDataFeign;
import com.ykl.med.masterdata.api.PsychotherapyFeign;
import com.ykl.med.masterdata.entiry.dto.FrequencyParamDTO;
import com.ykl.med.masterdata.entiry.vo.FrequencyUtilVO;
import com.ykl.med.masterdata.entiry.vo.PsychotherapyVO;
import com.ykl.med.rehab.constants.RehabErrorCode;
import com.ykl.med.rehab.entity.PsychoRehabPlanDO;
import com.ykl.med.rehab.entity.PsychoRehabPlanItemDO;
import com.ykl.med.rehab.entity.PsychoRehabPlanItemExecuteDO;
import com.ykl.med.rehab.enums.CompleteStatus;
import com.ykl.med.rehab.mapper.PsychoRehabPlanItemExecuteMapper;
import com.ykl.med.rehab.mapper.PsychoRehabPlanItemMapper;
import com.ykl.med.rehab.mapper.PsychoRehabPlanMapper;
import com.ykl.med.rehab.service.stat.PsychoStatService;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanExecuteRecordRespVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanItemVO;
import com.ykl.med.rehab.vo.resp.stat.PsychoStatVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 心理康复计划项执行服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PsychoRehabPlanItemExecuteServiceImpl
        extends ServiceImpl<PsychoRehabPlanItemExecuteMapper, PsychoRehabPlanItemExecuteDO>
        implements PsychoRehabPlanItemExecuteService {
    @Resource
    private PsychoRehabPlanItemService psychoRehabPlanItemService;
    @Resource
    private FrequencyDataFeign frequencyDataFeign;
    @Resource
    private PsychotherapyFeign psychotherapyFeign;
    @Resource
    private PsychoRehabPlanMapper psychoRehabPlanMapper;
    @Resource
    private PsychoStatService psychoStatService;

    @Override
    public List<RehabPlanExecuteCountRespVO> count(RehabPlanStatReqVO reqVO) {
        LocalDateTime startOfDay = LocalDate.parse(reqVO.getDate()).atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusSeconds(1);
        List<PsychoRehabPlanItemVO> planItems = psychoRehabPlanItemService.listByPlanId(reqVO.getPlanId());
        if (CollectionUtils.isEmpty(planItems)) {
            return Collections.emptyList();
        }
        List<Long> planItemIds = planItems.stream().map(PsychoRehabPlanItemVO::getId).collect(Collectors.toList());
        List<PsychoRehabPlanItemExecuteDO> executes = baseMapper.getExecution(planItemIds, startOfDay, endOfDay);
        Map<Long, List<PsychoRehabPlanItemExecuteDO>> groupedItems = executes.stream().collect(Collectors.groupingBy(PsychoRehabPlanItemExecuteDO::getPlanItemId));
        if (groupedItems.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> planItemTemplateIds = planItems.stream().map(PsychoRehabPlanItemVO::getTemplateId).collect(Collectors.toList());
        Map<Long, PsychoRehabPlanItemVO> planItemMap = planItems.stream().collect(Collectors.toMap(PsychoRehabPlanItemVO::getId, Function.identity()));
        List<PsychotherapyVO> psychotherapyList = CrudUtils.getByIdsLong(psychotherapyFeign::crud, planItemTemplateIds);
        Map<Long, PsychotherapyVO> templateMap = psychotherapyList.stream().collect(Collectors.toMap(e -> Long.valueOf(e.getId()), Function.identity()));
        return createResponseList(groupedItems, planItemMap, templateMap);
    }


    private List<RehabPlanExecuteCountRespVO> createResponseList(
            Map<Long, List<PsychoRehabPlanItemExecuteDO>> groupedItems,
            Map<Long, PsychoRehabPlanItemVO> planItems, Map<Long, PsychotherapyVO> templates) {
        return groupedItems.entrySet().stream()
                .map(entry -> createResponse(entry.getKey(), entry.getValue(), planItems, templates))
                .collect(Collectors.toList());
    }

    private RehabPlanExecuteCountRespVO createResponse(Long itemId, List<PsychoRehabPlanItemExecuteDO> itemList,
                                                       Map<Long, PsychoRehabPlanItemVO> idItemMap, Map<Long, PsychotherapyVO> idTemplateMap) {
        int executedCount = (int) itemList.stream()
                .filter(item -> Objects.equals(CompleteStatus.COMPLETED, item.getStatus()))
                .count();

        PsychoRehabPlanItemVO planItem = idItemMap.get(itemId);
        PsychotherapyVO template = idTemplateMap.get(planItem.getTemplateId());

        RehabPlanExecuteCountRespVO respVO = new RehabPlanExecuteCountRespVO();
        respVO.setPlanItemId(itemId);
        respVO.setPlanItemName(template.getName());
        respVO.setExecutedCount(executedCount);
        respVO.setUnit(template.getCountUnit());
        respVO.setExecutedActions(planItem.getActionsPerSession() * executedCount);
        respVO.setTotalCount(itemList.size());
        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        log.info("Executing rehab plan, reqVO: {}", JSON.toJSONString(reqVO));

        LocalDateTime begin, end;
        if (reqVO.getTargetDate() != null) {
            begin = reqVO.getTargetDate().atStartOfDay();
            end = begin.plusDays(1).minusSeconds(1);
        } else {
            begin = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
            end = begin.plusDays(1).minusSeconds(1);
        }

        PsychoRehabPlanItemExecuteDO itemExecute = super.lambdaQuery()
                .between(PsychoRehabPlanItemExecuteDO::getRequiredTime, begin, end)
                .eq(PsychoRehabPlanItemExecuteDO::getPlanId, reqVO.getPlanId())
                .eq(PsychoRehabPlanItemExecuteDO::getPlanItemId, reqVO.getPlanItemId())
                .eq(PsychoRehabPlanItemExecuteDO::getStatus, CompleteStatus.UNCOMPLETED)
                .list().stream()
                .findFirst()
                .orElseThrow(() -> new ServiceException(RehabErrorCode.EXECUTE_RECORD_NOT_EXISTS));

        itemExecute.setStatus(CompleteStatus.COMPLETED);
        itemExecute.setExecuteTime(reqVO.getExecuteTime() != null ? reqVO.getExecuteTime() : LocalDateTime.now());
        itemExecute.setDuration(reqVO.getDuration());
        itemExecute.setStartTime(reqVO.getStartTime());
        itemExecute.setEndTime(reqVO.getEndTime());
        itemExecute.setEvaluation(reqVO.getEvaluation());
        super.updateById(itemExecute);

        TransactionalAfterCommitExecutor.executeAfterCommit(() -> psychoStatService.refreshStat(reqVO.getCurrentPatientId()));
        System.out.println(1);
        System.out.println(2);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateExecute(Long planId, boolean immediatelyFlag) {
        // Get start times for plan ID
        PsychoRehabPlanDO planDO = psychoRehabPlanMapper.selectById(planId);
        LocalDateTime startTime = planDO.getStartTime();

        // Get items for plan ID
        List<PsychoRehabPlanDetailRespVO.PlanItem> planItems = psychoRehabPlanItemService
                .listAndExecuted(planId);
        if (planItems.isEmpty()) {
            return;
        }

        // 获取所有频率的执行时间，并按频率id转map
        Map<Long, List<LocalDateTime>> frequencyIdTimeMap = getTodayTime(startTime, planItems, immediatelyFlag);
        if (frequencyIdTimeMap.isEmpty()) {
            return;
        }

        // Build all potential executed items for all plans
        List<PsychoRehabPlanItemExecuteDO> potentialExecutedItems = planItems.stream()
                .flatMap(item -> buildExecutedItemsFor(item, frequencyIdTimeMap))
                .collect(Collectors.toList());

        // Get already existing execution items to filter out
        Set<String> existingItemsKeys = getExistingExecutionItemsKeys(potentialExecutedItems);

        // Filter out already existing execution items
        List<PsychoRehabPlanItemExecuteDO> newExecutedItems = potentialExecutedItems.stream()
                .filter(item -> !existingItemsKeys.contains(buildExecutionItemKey(item)))
                .collect(Collectors.toList());

        // Save new execution items if any
        if (!newExecutedItems.isEmpty()) {
            saveBatch(newExecutedItems);
        }
        TransactionalAfterCommitExecutor.executeAfterCommit(() -> psychoStatService.refreshStat(planDO.getPatientId()));
    }

    @Override
    public RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(ItemExecStatReqVO reqVO) {
        LocalDate startDate, endDate;
        endDate = reqVO.getEndDate() == null ? LocalDate.now() : reqVO.getEndDate();
        startDate = reqVO.getStartDate() == null ? endDate.minusDays(6) : reqVO.getStartDate();
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

        // 查询所有方案
        List<PsychoRehabPlanDO> plans = psychoRehabPlanMapper.selectList(new LambdaQueryWrapperX<PsychoRehabPlanDO>()
                .eq(PsychoRehabPlanDO::getStatus, CommonStatusEnum.ENABLE)
                .eq(PsychoRehabPlanDO::getPatientId, reqVO.getPatientId()));
        List<Long> planIds = plans.stream().map(PsychoRehabPlanDO::getId).collect(Collectors.toList());
        if (planIds.isEmpty()) {
            throw new ServiceException(RehabErrorCode.PLAN_NOT_EXISTS);
        }

        // 一次性查询过去7天的所有执行记录
        List<PsychoRehabPlanItemExecuteDO> executes = super.lambdaQuery()
                .in(PsychoRehabPlanItemExecuteDO::getPlanId, planIds)
                .eq(PsychoRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                .between(PsychoRehabPlanItemExecuteDO::getRequiredTime, startDateTime, endDateTime)
                .list();

        // 根据planIds获取所有计划项并以templateId为键组成的map
        Map<Long, List<PsychoRehabPlanItemVO>> templateToItemsMap = psychoRehabPlanItemService
                .listAllStatusByPlanIds(planIds)
                .values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(PsychoRehabPlanItemVO::getTemplateId));
        // 如果没有计划项，直接返回空的响应
        if (templateToItemsMap.isEmpty()) {
            return new RehabPlanWeeklyStatRespWrapperVO();
        }

        Set<Long> templateIds = templateToItemsMap.keySet();
        Map<Long, Long> itemIdToTemplateId = templateToItemsMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(PsychoRehabPlanItemVO::getId,
                        PsychoRehabPlanItemVO::getTemplateId));

        List<String> templateIdStrList = templateIds.stream().map(String::valueOf).collect(Collectors.toList());

        // 获取模板详细信息
        Map<Long, PsychotherapyVO> templateDetailsMap = CrudUtils.getByIds(psychotherapyFeign::crud, templateIdStrList)
                .stream().collect(Collectors.toMap(
                        psychotherapyVO -> Long.valueOf(psychotherapyVO.getId()), Function.identity()
                ));

        // 构建每个模板的统计序列
        List<RehabPlanWeeklyStatRespWrapperVO.Series> allSeries = templateIds.stream()
                .map(templateId -> createSeriesByTemplate(templateId, templateDetailsMap.get(templateId),
                        templateToItemsMap.get(templateId)))
                .collect(Collectors.toList());

        // 构建每一天的统计数据
        List<RehabPlanWeeklyStatRespWrapperVO.DailyData> dailyDataList = Stream
                .iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .map(date -> createDailyData(date, executes, allSeries, itemIdToTemplateId))
                .collect(Collectors.toList());

        RehabPlanWeeklyStatRespWrapperVO wrapperVO = new RehabPlanWeeklyStatRespWrapperVO();
        wrapperVO.setSeriesList(allSeries);
        wrapperVO.setDailyDataList(dailyDataList);
        return wrapperVO;
    }

    private RehabPlanWeeklyStatRespWrapperVO.Series createSeriesByTemplate(Long templateId,
                                                                           PsychotherapyVO template,
                                                                           List<PsychoRehabPlanItemVO> items) {
        RehabPlanWeeklyStatRespWrapperVO.Series series = new RehabPlanWeeklyStatRespWrapperVO.Series();
        series.setPlanItemId(templateId);
        series.setPlanItemName(template.getName());
        series.setPlanItemMethod(Collections.singletonList(template.getMode()));
        series.setUnit(template.getCountUnit());
        // Assume all items associated with this template have the same actions per
        // session
        series.setActionsPerSession(
                items.isEmpty() ? 0 : items.stream().findFirst()
                        .map(PsychoRehabPlanItemVO::getActionsPerSession).orElse(0)
        );
        return series;
    }

    private RehabPlanWeeklyStatRespWrapperVO.DailyData createDailyData(LocalDate date,
                                                                       List<PsychoRehabPlanItemExecuteDO> executes,
                                                                       List<RehabPlanWeeklyStatRespWrapperVO.Series> allSeries,
                                                                       Map<Long, Long> itemIdToTemplateId) {
        List<PsychoRehabPlanItemExecuteDO> dailyExecutes = executes.stream()
                .filter(execute -> execute.getRequiredTime().toLocalDate().equals(date))
                .collect(Collectors.toList());

        Map<Long, Long> templateIdToExecutedDurationSum = dailyExecutes.stream()
                .collect(Collectors.groupingBy(
                        psychoRehabPlanItemExecuteDO -> itemIdToTemplateId
                                .get(psychoRehabPlanItemExecuteDO.getPlanItemId()),
                        Collectors.summingLong(PsychoRehabPlanItemExecuteDO::getDuration)));

        List<Integer> executedCounts = allSeries.stream()
                .map(series -> templateIdToExecutedDurationSum.getOrDefault(series.getPlanItemId(), 0L).intValue())
                .collect(Collectors.toList());

        RehabPlanWeeklyStatRespWrapperVO.DailyData dailyData = new RehabPlanWeeklyStatRespWrapperVO.DailyData();
        dailyData.setDate(date.atStartOfDay());
        dailyData.setExecutedCounts(executedCounts);
        return dailyData;
    }

    private List<PsychoRehabPlanExecuteRecordRespVO> fillExecuteRecordData(List<PsychoRehabPlanItemExecuteDO> items) {
        if (items.isEmpty()) {
            return Collections.emptyList();
        }
        // 提取所有的planItemIds
        List<Long> planItemIds = items.stream()
                .map(PsychoRehabPlanItemExecuteDO::getPlanItemId)
                .collect(Collectors.toList());

        // 通过planItemIds获取所有相关的PsychoRehabPlanItemDO
        Map<Long, PsychoRehabPlanItemDO> rehabPlanItemMap = SpringContentUtils.getBean(PsychoRehabPlanItemMapper.class)
                .selectBatchIds(planItemIds)
                .stream()
                .collect(Collectors.toMap(PsychoRehabPlanItemDO::getId, Function.identity()));

        // 提取所有的itemTemplateIds
        Set<Long> itemTemplateIds = rehabPlanItemMap.values().stream()
                .map(PsychoRehabPlanItemDO::getTemplateId).collect(Collectors.toSet());

        List<String> templateIds = itemTemplateIds.stream().map(String::valueOf).collect(Collectors.toList());
        // 通过itemTemplateIds获取所有相关的模板详情
        Map<Long, PsychotherapyVO> itemTemplateMap = CrudUtils.getByIds(psychotherapyFeign::crud, templateIds)
                .stream().collect(
                        Collectors.toMap(psychotherapyVO -> Long.valueOf(psychotherapyVO.getId()), Function.identity(), (a, b) -> a)
                );

        // 转换执行记录到响应对象
        return items.stream()
                .map(item -> {
                    PsychoRehabPlanItemDO planItem = rehabPlanItemMap.get(item.getPlanItemId());
                    PsychotherapyVO templateDetail = itemTemplateMap.get(planItem.getTemplateId());

                    PsychoRehabPlanExecuteRecordRespVO respVO = CopyPropertiesUtil.normalCopyProperties(item,
                            PsychoRehabPlanExecuteRecordRespVO.class);
                    respVO.setPlanItemName(templateDetail.getName());
                    respVO.setPlanItemMethod(templateDetail.getMode());
                    return respVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<PsychoRehabPlanExecuteRecordRespVO> queryExecuteRecords(RehabPlanExecuteQueryReqVO reqVO) {
        PageResult<PsychoRehabPlanItemExecuteDO> pageResult = baseMapper.selectPage(reqVO,
                new LambdaQueryWrapperX<PsychoRehabPlanItemExecuteDO>()
                        .eq(PsychoRehabPlanItemExecuteDO::getPlanId, reqVO.getPlanId())
                        .eq(PsychoRehabPlanItemExecuteDO::getPlanItemId, reqVO.getPlanItemId())
                        .eq(PsychoRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                        .orderByDesc(PsychoRehabPlanItemExecuteDO::getExecuteTime));

        if (pageResult.getList().isEmpty()) {
            return PageResult.empty();
        }

        // 调用新的方法来填充数据
        List<PsychoRehabPlanExecuteRecordRespVO> records = fillExecuteRecordData(pageResult.getList());

        return new PageResult<>(records, pageResult.getTotal());
    }

    @Override
    public Map<String, List<PsychoRehabPlanExecuteRecordRespVO>> queryExecuteRecordsByTimeRange(
            RehabPlanExecuteTimeRangeQueryReqVO reqVO) {
        // 查询所有方案
        List<Long> planIds;
        if (reqVO.getPlanId() != null) {
            planIds = Collections.singletonList(reqVO.getPlanId());
        } else {
            List<PsychoRehabPlanDO> plans = psychoRehabPlanMapper
                    .selectList(new LambdaQueryWrapperX<PsychoRehabPlanDO>()
                            .eq(PsychoRehabPlanDO::getPatientId, reqVO.getPatientId()));
            planIds = plans.stream().map(PsychoRehabPlanDO::getId).collect(Collectors.toList());
        }
        if (planIds.isEmpty()) {
            throw new ServiceException(RehabErrorCode.PLAN_NOT_EXISTS);
        }

        List<PsychoRehabPlanItemExecuteDO> executeDOs = baseMapper
                .selectList(new LambdaQueryWrapperX<PsychoRehabPlanItemExecuteDO>()
                        .in(PsychoRehabPlanItemExecuteDO::getPlanId, planIds)
                        .eqIfPresent(PsychoRehabPlanItemExecuteDO::getPlanItemId, reqVO.getPlanItemId())
                        .eq(PsychoRehabPlanItemExecuteDO::getStatus, CompleteStatus.COMPLETED)
                        .between(PsychoRehabPlanItemExecuteDO::getExecuteTime, reqVO.getStartTime(), reqVO.getEndTime())
                        .orderByDesc(PsychoRehabPlanItemExecuteDO::getExecuteTime));

        // 调用新的方法来填充数据
        List<PsychoRehabPlanExecuteRecordRespVO> executeRecords = fillExecuteRecordData(executeDOs);

        Map<String, List<PsychoRehabPlanExecuteRecordRespVO>> unsortedMap = executeRecords.stream()
                .collect(Collectors.groupingBy(record -> record.getExecuteTime().toLocalDate().toString(),
                        Collectors.toList()));

        Map<String, List<PsychoRehabPlanExecuteRecordRespVO>> sortedMap = new TreeMap<>(Comparator.reverseOrder());
        sortedMap.putAll(unsortedMap);

        return sortedMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateExecuteBatch(Collection<Long> planIds) {
        log.info("Batch generating PsychoRehabPlanItemExecutes, planIds: {}", planIds);

        planIds.forEach(e -> this.generateExecute(e, false));
    }

    private Map<Long, List<LocalDateTime>> getTodayTime(LocalDateTime localDateTime, List<PsychoRehabPlanDetailRespVO.PlanItem> items, boolean immediatelyFlag) {
        LocalDate nowDay = DateTimeUtils.getNowDay();
        LocalDateTime startTime = DateTimeUtils.someTimeStart(nowDay);
        LocalDateTime endTime = DateTimeUtils.someTimeEnd(nowDay);
        List<FrequencyParamDTO> frequencyParamDTOList = new ArrayList<>();
        Map<Long, Long> frequencyIdMap = new HashMap<>();
        for (PsychoRehabPlanDetailRespVO.PlanItem planItem : items) {
            Long frequencyId = planItem.getFrequencyId();
            if (frequencyIdMap.get(frequencyId) != null) {
                continue;
            }
            frequencyIdMap.put(frequencyId, frequencyId);
            FrequencyParamDTO frequencyParamDTO = new FrequencyParamDTO();
            frequencyParamDTO.setId(planItem.getFrequencyId().toString());
            frequencyParamDTO.setFrequencyId(planItem.getFrequencyId().toString());
            frequencyParamDTO.setFrequencyStartTime(Collections.singletonList(localDateTime.toString()));
            frequencyParamDTO.setFrequencyFixDate(immediatelyFlag && localDateTime.isBefore(startTime) ? DateTimeUtils.getNowDayTimeString() : localDateTime.toString());
            frequencyParamDTO.setStartTime(startTime);
            frequencyParamDTO.setEndTime(endTime);
            frequencyParamDTOList.add(frequencyParamDTO);
        }
        PageResult<FrequencyUtilVO> frequencyBizPageResult = frequencyDataFeign.genFrequencySetTime(frequencyParamDTOList);
        if (CollectionUtils.isEmpty(frequencyBizPageResult.getList())) {
            return new HashMap<>();
        }

        return frequencyBizPageResult.getList().stream()
                .collect(Collectors.toMap(e -> Long.valueOf(e.getId()), FrequencyUtilVO::getGenExecTimeList));
    }

    private Set<String> getExistingExecutionItemsKeys(List<PsychoRehabPlanItemExecuteDO> items) {
        Map<Long, List<LocalDateTime>> planItemIdToTimes = items.stream()
                .collect(Collectors.groupingBy(PsychoRehabPlanItemExecuteDO::getPlanItemId,
                        Collectors.mapping(PsychoRehabPlanItemExecuteDO::getRequiredTime, Collectors.toList())));
        List<PlanItemTimeVO> planItemTimes = planItemIdToTimes.entrySet().stream()
                .map(entry -> new PlanItemTimeVO()
                        .setPlanItemId(entry.getKey())
                        .setRequiredTimes(entry.getValue()))
                .collect(Collectors.toList());
        if (planItemTimes.isEmpty()) {
            return Collections.emptySet();
        }
        List<PsychoRehabPlanItemExecuteDO> executes = baseMapper.findPlanItems(planItemTimes);
        return executes.stream()
                .map(this::buildExecutionItemKey)
                .collect(Collectors.toSet());
    }

    private String buildExecutionItemKey(PsychoRehabPlanItemExecuteDO item) {
        return item.getPlanId() + ":" + item.getPlanItemId() + ":" + item.getRequiredTime();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExecuteRecord(RehabPlanItemExecSaveOrUpdateReqVO reqVO) {
        PsychoRehabPlanItemExecuteDO executeDO = baseMapper.selectById(reqVO.getId());
        if (executeDO != null) {
            baseMapper.updateById(CopyPropertiesUtil.copy(reqVO, () -> executeDO, true));
        } else {
            throw new RuntimeException("未找到对应的执行记录: " + reqVO.getId());
        }
        TransactionalAfterCommitExecutor.executeAfterCommit(() -> psychoStatService.refreshStat(reqVO.getCurrentPatientId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteExecuteRecord(Long id) {
        log.info("删除心理康复计划项执行记录，id: {}", id);
        baseMapper.deleteById(id);
    }

    @Override
    public PsychoStatVO selectLoveItemStat(Long patientId) {
        return baseMapper.selectLoveItemStat(patientId);
    }

    @Override
    public PsychoStatVO aggregatePsychoStats(Long patientId, LocalDateTime startTime){
        return baseMapper.aggregatePsychoStats(patientId,startTime);
    }

    private Stream<PsychoRehabPlanItemExecuteDO> buildExecutedItemsFor(
            PsychoRehabPlanDetailRespVO.PlanItem item,
            Map<Long, LocalDateTime> idToStartTime,
            Map<Long, Map<LocalDateTime, List<LocalDateTime>>> todayTimes,
            Long planId) {
        return Optional
                .ofNullable(todayTimes.getOrDefault(item.getFrequencyId(), Maps.newHashMap())
                        .get(idToStartTime.get(planId)))
                .orElse(Collections.emptyList()).stream()
                .map(time -> new PsychoRehabPlanItemExecuteDO()
                        .setPlanId(planId)
                        .setPlanItemId(item.getId())
                        .setStatus(CompleteStatus.UNCOMPLETED)
                        .setRequiredTime(time));
    }

    private Stream<PsychoRehabPlanItemExecuteDO> buildExecutedItemsFor(
            PsychoRehabPlanDetailRespVO.PlanItem item,
            Map<Long, List<LocalDateTime>> frequencyIdTimeMap) {
        return frequencyIdTimeMap.getOrDefault(item.getFrequencyId(), new ArrayList<>())
                .stream().map(e -> new PsychoRehabPlanItemExecuteDO()
                        .setPlanId(item.getPlanId())
                        .setPlanItemId(item.getId())
                        .setStatus(CompleteStatus.UNCOMPLETED)
                        .setRequiredTime(e));
    }

}