package com.ykl.med.rehab.service.func;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.constants.topic.PatientFormTopic;
import com.ykl.med.framework.common.constants.topic.RehabMangeTopic;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.enums.PatientDeviceTypeEnum;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.masterdata.api.FormFeign;
import com.ykl.med.masterdata.api.FrequencyFeign;
import com.ykl.med.masterdata.api.FuncRehabItemTemplateFeign;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.enums.QuestionnairesType;
import com.ykl.med.masterdata.vo.FormVO;
import com.ykl.med.masterdata.vo.FrequencyVO;
import com.ykl.med.masterdata.vo.FuncRehabItemDeviceDataVO;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.masterdata.vo.req.FormPageReqVO;
import com.ykl.med.masterdata.vo.resp.FormListRespVO;
import com.ykl.med.masterdata.vo.resp.FuncRehabItemTemplateDetailRespVO;
import com.ykl.med.patient.api.PatientDeviceFeign;
import com.ykl.med.patient.vo.PatientDeviceVO;
import com.ykl.med.patient.vo.QueryPatientDeviceReqVO;
import com.ykl.med.rehab.constants.RehabErrorCode;
import com.ykl.med.rehab.entity.*;
import com.ykl.med.rehab.enums.CompleteStatus;
import com.ykl.med.rehab.enums.PlanStatus;
import com.ykl.med.rehab.enums.RehabType;
import com.ykl.med.rehab.mapper.FuncRehabPlanItemExecuteMapper;
import com.ykl.med.rehab.mapper.FuncRehabPlanItemMapper;
import com.ykl.med.rehab.mapper.FuncRehabPlanMapper;
import com.ykl.med.rehab.mapper.FuncRehabSurveySnapshotMapper;
import com.ykl.med.rehab.utils.WeekdayConverter;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.func.FuncRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import com.ykl.med.rehab.vo.resp.AssessmentResultRespVO;
import com.ykl.med.rehab.vo.resp.FormAndSnapshotRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabSurveySnapshotVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 功能康复计划服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FuncRehabPlanServiceImpl extends ServiceImpl<FuncRehabPlanMapper, FuncRehabPlanDO>
        implements FuncRehabPlanService {

    private final IdServiceImpl idService;
    private final FuncRehabPlanItemService funcRehabPlanItemService;
    private final FuncRehabPlanItemExecuteMapper funcRehabPlanItemExecuteMapper;
    private final FuncRehabPlanCareAdviceService funcRehabPlanCareAdviceService;
    private final FuncRehabSurveySnapshotService funcRehabSurveySnapshotService;
    private final FuncRehabPlanItemExecuteService funcRehabPlanItemExecuteService;
    private final FuncRehabItemTemplateFeign funcRehabItemTemplateFeign;
    private final FuncRehabSurveySnapshotMapper funcRehabSurveySnapshotMapper;
    private final FormFeign formFeign;
    private final FrequencyFeign frequencyFeign;
    private final PatientDeviceFeign patientDeviceFeign;
    private final RocketMQTemplate rocketMqTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(FuncRehabPlanSaveOrUpdateReqVO reqVO) {
        log.info("Saving or updating functional rehab plan, reqVO: {}", JSON.toJSONString(reqVO));

        boolean isDraft = reqVO.getStatus() == CommonStatusEnum.DISABLE;
        Long planId = idService.nextId();
        Optional<Long> currentPlanIdOpt = Optional.ofNullable(getCurrentPlan(reqVO.getPatientId()))
                .map(FuncRehabPlanDetailRespVO::getId);

        // Validate plan duration
        validatePlanDuration(reqVO);

        // Handle draft status
        if (isDraft) {
            deleteDrafts(reqVO.getPatientId());
            saveOrUpdatePlan(reqVO, planId);
            return planId;
        }

        // Handle enable status
        terminatePreviousPlans(currentPlanIdOpt);
        List<FuncRehabSurveySnapshotVO> snapshots;
        if (currentPlanIdOpt.isPresent()) {
            snapshots = getSnapshotsForEnable(currentPlanIdOpt.get(), planId);
        } else {
            snapshots = funcRehabSurveySnapshotService
                    .list(new SurveySnapshotListReqVO().setPatientId(reqVO.getPatientId()));
        }
        saveOrUpdatePlan(reqVO, planId, snapshots);
        deleteDrafts(reqVO.getPatientId());
        bindPlanIdToSnapshots(reqVO.getPatientId(), planId);
        generateExecutionData(planId);
        return planId;
    }

    private void deleteDrafts(Long patientId) {
        baseMapper.delete(new LambdaQueryWrapperX<FuncRehabPlanDO>()
                .eq(FuncRehabPlanDO::getPatientId, patientId)
                .eq(FuncRehabPlanDO::getStatus, CommonStatusEnum.DISABLE));
    }

    private void saveOrUpdatePlan(FuncRehabPlanSaveOrUpdateReqVO reqVO, Long planId) {
        FuncRehabPlanDO funcRehabPlanDO = CopyPropertiesUtil.normalCopyProperties(reqVO, FuncRehabPlanDO.class);
        funcRehabPlanDO.setId(planId);
        funcRehabPlanDO.setEndTime(LocalDateTimeUtil.endOfDay(funcRehabPlanDO.getEndTime(), true));
        super.saveOrUpdate(funcRehabPlanDO);
        saveRelatedData(planId, reqVO);
    }

    private void saveOrUpdatePlan(FuncRehabPlanSaveOrUpdateReqVO reqVO, Long planId,
                                  List<FuncRehabSurveySnapshotVO> snapshots) {
        FuncRehabPlanDO funcRehabPlanDO = CopyPropertiesUtil.normalCopyProperties(reqVO, FuncRehabPlanDO.class);
        funcRehabPlanDO.setId(planId);
        funcRehabPlanDO.setEndTime(LocalDateTimeUtil.endOfDay(funcRehabPlanDO.getEndTime(), true));
        funcRehabPlanDO.setAssessmentResults(
                snapshots.stream()
                        .map(snapshot -> new AssessmentResultRespVO()
                                .setFormName(snapshot.getSnapshot().getLabel())
                                .setScore(snapshot.getSnapshot().getScore())
                                .setResult(snapshot.getSnapshot().getResult()))
                        .collect(Collectors.toList()));
        super.saveOrUpdate(funcRehabPlanDO);
        saveRelatedData(planId, reqVO);
    }

    private void saveRelatedData(Long planId, FuncRehabPlanSaveOrUpdateReqVO reqVO) {
        funcRehabPlanItemService.saveBatch(planId, reqVO);
        funcRehabPlanCareAdviceService.saveBatch(planId, reqVO);
    }

    private void terminatePreviousPlans(Optional<Long> currentPlanIdOpt) {
        currentPlanIdOpt.ifPresent(currentPlanId -> baseMapper.updateEndTime(currentPlanId, LocalDateTime.now()));
    }

    private List<FuncRehabSurveySnapshotVO> getSnapshotsForEnable(Long currentPlanId, Long planId) {
        return funcRehabSurveySnapshotService.copySnapshots(currentPlanId, planId);
    }

    private void bindPlanIdToSnapshots(Long patientId, Long planId) {
        funcRehabSurveySnapshotMapper.bindPlanId(patientId, planId);
    }

    private void generateExecutionData(Long planId) {
        funcRehabPlanItemExecuteService.generateExecute(planId, true);
    }

    public void validatePlanDuration(FuncRehabPlanSaveOrUpdateReqVO reqVO) throws ServiceException {
        int duration = reqVO.getDuration();
        String unit = reqVO.getDurationUnit();

        boolean isDurationInvalid = ("年".equals(unit) && duration > 1) ||
                ("月".equals(unit) && duration > 12) ||
                ("周".equals(unit) && duration > 52) ||
                ("天".equals(unit) && duration > 365);

        if (isDurationInvalid) {
            throw new ServiceException(RehabErrorCode.PLAN_DURATION_EXCEEDS_ONE_YEAR);
        }
    }

    /**
     * 获取列表
     *
     * @param patientId 患者 ID
     * @param reqVO     请求 VO
     * @param status    方案状态
     * @return 详情响应 VO 列表
     */
    public List<FuncRehabPlanDetailRespVO> list(Long patientId, Long planId, HistoryPlanReqVO reqVO,
                                                PlanStatus status) {
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapperX<FuncRehabPlanDO> query = buildQueryWrapper(patientId, planId, reqVO, status, now);

        List<FuncRehabPlanDO> rehabPlans = baseMapper.selectList(query);
        if (rehabPlans.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> planIds = rehabPlans.stream().map(FuncRehabPlanDO::getId).collect(Collectors.toList());

        Map<Long, List<FuncRehabPlanItemDO>> planItemMap = getPlanItemMap(planIds);
        Map<Long, FuncRehabItemTemplateDetailRespVO> itemTemplateMap = getItemTemplateMap(planItemMap);
        Map<Long, List<FuncRehabPlanDetailRespVO.CareAdvice>> careAdviceMap = getCareAdviceMap(planIds);

        return convertToResponseVO(rehabPlans, planItemMap, itemTemplateMap, careAdviceMap, now);

    }

    private LambdaQueryWrapperX<FuncRehabPlanDO> buildQueryWrapper(Long patientId, Long planId, HistoryPlanReqVO reqVO,
                                                                   PlanStatus status, LocalDateTime now) {
        Long effectivePatientId = status == PlanStatus.HISTORY ? reqVO.getPatientId() : patientId;

        LambdaQueryWrapperX<FuncRehabPlanDO> query = new LambdaQueryWrapperX<FuncRehabPlanDO>()
                .eq(FuncRehabPlanDO::getPatientId, effectivePatientId)
                .eqIfPresent(FuncRehabPlanDO::getId, planId)
                .eq(FuncRehabPlanDO::getStatus,
                        status == PlanStatus.DRAFT ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE);

        switch (status) {
            case DRAFT:
                query.last("limit 1");
                break;
            case CURRENT:
                query.ge(FuncRehabPlanDO::getEndTime, now)
                        .orderByDesc(FuncRehabPlanDO::getEndTime)
                        .last("limit 1");
                break;
            case HISTORY:
                boolean isStartTimeRangePresent = reqVO.getPlanStartTimeRange() != null;
                boolean isEndTimeRangePresent = reqVO.getPlanEndTimeRange() != null;
                SFunction<FuncRehabPlanDO, ?> field = isStartTimeRangePresent ? FuncRehabPlanDO::getStartTime
                        : FuncRehabPlanDO::getEndTime;
                if (isStartTimeRangePresent) {
                    query.ge(field, LocalDateTimeUtil.beginOfDay(reqVO.getPlanStartTimeRange()[0].atStartOfDay()))
                            .le(field, LocalDateTimeUtil.endOfDay(reqVO.getPlanStartTimeRange()[1].atStartOfDay()));
                } else if (isEndTimeRangePresent) {
                    query.ge(field, LocalDateTimeUtil.beginOfDay(reqVO.getPlanEndTimeRange()[0].atStartOfDay()))
                            .le(field, LocalDateTimeUtil.endOfDay(reqVO.getPlanEndTimeRange()[1].atStartOfDay()));
                }
                query.lt(FuncRehabPlanDO::getEndTime, now)
                        .orderBy(true, Objects.equals(reqVO.getAsc(), true), field);
                break;
            default:
                break;
        }

        return query;
    }

    private Map<Long, List<FuncRehabPlanItemDO>> getPlanItemMap(List<Long> planIds) {
        return SpringContentUtils.getBean(FuncRehabPlanItemMapper.class)
                .selectList(new LambdaQueryWrapperX<FuncRehabPlanItemDO>()
                        .in(FuncRehabPlanItemDO::getPlanId, planIds))
                .stream()
                .collect(Collectors.groupingBy(FuncRehabPlanItemDO::getPlanId));
    }

    private FuncRehabPlanItemDO getPlanItemDetails(Long id) {
        return SpringContentUtils.getBean(FuncRehabPlanItemMapper.class).selectById(id);
    }

    private Map<Long, FuncRehabItemTemplateDetailRespVO> getItemTemplateMap(
            Map<Long, List<FuncRehabPlanItemDO>> planItemMap) {
        Set<Long> templateIds = planItemMap.values().stream()
                .flatMap(List::stream)
                .map(FuncRehabPlanItemDO::getTemplateId)
                .collect(Collectors.toSet());
        if (templateIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return funcRehabItemTemplateFeign.list(templateIds)
                .stream()
                .collect(Collectors.toMap(FuncRehabItemTemplateDetailRespVO::getId, Function.identity()));
    }

    private Map<Long, List<FuncRehabPlanDetailRespVO.CareAdvice>> getCareAdviceMap(List<Long> planIds) {
        return funcRehabPlanCareAdviceService.listAllByIds(planIds);
    }

    @Override
    public FuncRehabPlanDetailRespVO getCurrentPlan(Long patientId) {
        return list(patientId, null, null, PlanStatus.CURRENT).stream().findFirst().map(currentPlan -> {
            Map<Long, RehabPlanExecuteCountRespVO> countMap = funcRehabPlanItemExecuteService
                    .count(new RehabPlanStatReqVO()
                            .setDate(LocalDate.now().toString())
                            .setPlanId(currentPlan.getId()))
                    .stream()
                    .collect(Collectors.toMap(RehabPlanExecuteCountRespVO::getPlanItemId, Function.identity()));

            if (CollectionUtils.isNotEmpty(currentPlan.getItems())) {
                currentPlan.getItems().forEach(item -> {
                    RehabPlanExecuteCountRespVO countRespVO = countMap.get(item.getId());
                    item.setExecuted(countRespVO == null
                            || Objects.equals(countRespVO.getExecutedCount(), countRespVO.getTotalCount()));
                    item.setNoNeedExecute(countRespVO == null || countRespVO.getTotalCount() == 0);
                });
                FuncRehabPlanItemExecuteDO firstExecution = funcRehabPlanItemExecuteMapper
                        .selectOne(new LambdaQueryWrapperX<FuncRehabPlanItemExecuteDO>()
                                .eq(FuncRehabPlanItemExecuteDO::getPlanId, currentPlan.getId())
                                .orderByAsc(FuncRehabPlanItemExecuteDO::getRequiredTime)
                                .last("limit 1"));
                currentPlan.setFirstExecutionDate(
                        firstExecution == null ? null : firstExecution.getRequiredTime().toLocalDate());
            }
            return currentPlan;
        }).orElse(null);
    }

    @Override
    public List<FuncRehabPlanDetailRespVO> list(Long patientId) {
        return list(patientId, null, null, PlanStatus.ALL);
    }

    @Override
    public List<FuncRehabPlanDetailRespVO> getHistoryPlans(HistoryPlanReqVO reqVO) {
        return list(null, null, reqVO, PlanStatus.HISTORY);
    }

    @Override
    public void extendPlan(ExtendPlanReqVO reqVO) {
        baseMapper.updateEndTime(reqVO.getPlanId(), reqVO.getEndTime());
    }

    @Override
    public void reEvaluate(Long patientId) {
        Optional.ofNullable(getCurrentPlan(patientId))
                .ifPresent(currentPlan -> {
                    baseMapper.updateEndTime(currentPlan.getId(), LocalDateTime.now().minusSeconds(1));
                    rocketMqTemplate.syncSend(RehabMangeTopic.TOPIC, new RehabManageMqMessageVO()
                            .setPatientId(patientId)
                            .setRehabId(currentPlan.getId())
                            .setRehabType(RehabType.FUNC));
                });
        rocketMqTemplate.syncSend(PatientFormTopic.TOPIC, new PatientFormVO().setPatientId(patientId).setType(PatientFormBizType.FUNC));
    }

    @Override
    public FuncRehabPlanDetailRespVO getDraftPlan(Long patientId) {
        return list(patientId, null, null, PlanStatus.DRAFT).stream().findFirst().orElse(null);
    }

    @Override
    public List<FormAndSnapshotRespVO> getFormAndSnapshots(FormAndSnapshotReqVO reqVO) {
        // Fetch forms once with the required filters.
        List<FormListRespVO> forms = formFeign.list(new FormPageReqVO()
                .setStatus(CommonStatusEnum.ENABLE)
                .setCategory(QuestionnairesType.FUNC_REHAB.name())
                .setOnline(reqVO.getOnline()));

        if (forms.isEmpty()) {
            return Collections.emptyList();
        }

        // Prepare a single query wrapper for the snapshots.
        LambdaQueryWrapper<FuncRehabSurveySnapshotDO> wrapper = new LambdaQueryWrapper<FuncRehabSurveySnapshotDO>()
                .eq(FuncRehabSurveySnapshotDO::getPatientId, reqVO.getPatientId());
        wrapper = reqVO.getPlanId() != null ? wrapper.eq(FuncRehabSurveySnapshotDO::getPlanId, reqVO.getPlanId())
                : wrapper.isNull(FuncRehabSurveySnapshotDO::getPlanId);

        List<FuncRehabSurveySnapshotDO> currentSnapshots = funcRehabSurveySnapshotMapper.selectList(wrapper);

        // Map snapshots by their form ID for quick access.
        Map<Long, FuncRehabSurveySnapshotDO> snapshotMap = currentSnapshots.stream()
                .collect(Collectors.toMap(snapshot -> snapshot.getSnapshot().getLong("id"), Function.identity()));

        // Fetch form details once in bulk.
        Set<Long> formIds = forms.stream().map(FormListRespVO::getId).collect(Collectors.toSet());
        Map<Long, FormVO> formMap = formFeign.listDetail(new ArrayList<>(formIds))
                .stream().collect(Collectors.toMap(FormVO::getId, Function.identity()));

        return forms.stream().map(form -> {
            FuncRehabSurveySnapshotDO snapshot = snapshotMap.get(form.getId());
            FormVO formDetails = formMap.get(form.getId());
            Optional<FuncRehabSurveySnapshotDO> rehabSnapshot = Optional.ofNullable(snapshot);
            Optional<FormVO> snapshotForm = rehabSnapshot.map(snap -> FormVO.from(snap.getSnapshot()));

            FormAndSnapshotRespVO response = new FormAndSnapshotRespVO()
                    .setFormId(form.getId())
                    .setFormLabel(form.getLabel())
                    .setFormRemark(form.getRemark())
                    .setFormType(form.getType())
                    .setRequired(form.getRequired())
                    .setSent(snapshot != null)
                    .setOnline(form.getOnline())
                    .setCompleted(snapshot != null && Objects.equals(CompleteStatus.COMPLETED, snapshot.getStatus()))
                    .setSnapshot(snapshotForm.orElse(formDetails));

            snapshotForm.ifPresent(sForm -> response.setFormResult(sForm.getResult())
                    .setSnapshotId(rehabSnapshot.get().getId()));

            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public FuncRehabPlanDetailRespVO getPlanById(PatientIdAndPlanIdVO reqVO) {
        return list(reqVO.getPatientId(), reqVO.getPlanId(), null, PlanStatus.ALL).stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public FuncRehabPlanDetailRespVO getById(Long id) {
        FuncRehabPlanDO planDO = baseMapper.selectById(id);
        return CopyPropertiesUtil.normalCopyProperties(planDO, FuncRehabPlanDetailRespVO.class);
    }

    @Override
    public boolean isExistPlanExcludeDraft(Long patientId) {
        return super.lambdaQuery().eq(FuncRehabPlanDO::getPatientId, patientId)
                .ne(FuncRehabPlanDO::getStatus, CommonStatusEnum.DISABLE)
                .count() > 0;
    }

    private FuncRehabPlanDetailRespVO createResponseVO(FuncRehabPlanDO rehabPlan,
                                                       Map<Long, List<FuncRehabPlanItemDO>> mapOfPlanItems,
                                                       Map<Long, FuncRehabItemTemplateDetailRespVO> mapOfItemTemplates,
                                                       Map<Long, List<FuncRehabPlanDetailRespVO.CareAdvice>> careAdvices,
                                                       LocalDateTime currentTime) {
        FuncRehabPlanDetailRespVO responseVO = createResponseVOFromPlan(rehabPlan);

        // Creating care advice details
        List<FuncRehabPlanDetailRespVO.CareAdvice> careAdviceList = createCareAdviceList(rehabPlan.getId(),
                careAdvices);
        responseVO.setCareAdvices(careAdviceList);

        // Creating plan items details
        List<FuncRehabPlanDetailRespVO.PlanItem> planItems = createPlanItems(mapOfPlanItems, rehabPlan.getId());
        if (CollectionUtils.isNotEmpty(planItems)) {
            populatePlanItemsDetails(responseVO, planItems, mapOfItemTemplates);
        }
        responseVO.setItems(planItems);

        responseVO.setCurrent(isCurrentPlan(rehabPlan, currentTime));

        return responseVO;
    }

    private FuncRehabPlanDetailRespVO createResponseVOFromPlan(FuncRehabPlanDO rehabPlan) {
        return CopyPropertiesUtil.normalCopyProperties(rehabPlan, FuncRehabPlanDetailRespVO.class);
    }

    private List<FuncRehabPlanDetailRespVO.CareAdvice> createCareAdviceList(Long planId,
                                                                            Map<Long, List<FuncRehabPlanDetailRespVO.CareAdvice>> mapOfCareAdvice) {
        return mapOfCareAdvice.getOrDefault(planId, Collections.emptyList());
    }

    private List<FuncRehabPlanDetailRespVO.PlanItem> createPlanItems(
            Map<Long, List<FuncRehabPlanItemDO>> mapOfPlanItems, Long rehabPlanId) {
        return CopyPropertiesUtil.normalCopyProperties(mapOfPlanItems.get(rehabPlanId),
                FuncRehabPlanDetailRespVO.PlanItem.class);
    }

    private void populatePlanItemsDetails(FuncRehabPlanDetailRespVO
                                                  responseVO, List<FuncRehabPlanDetailRespVO.PlanItem> planItems,
                                          Map<Long, FuncRehabItemTemplateDetailRespVO> mapOfItemTemplates) {
        Map<Long, FrequencyVO> frequencyMap = frequencyFeign
                .getByIds(planItems.stream().map(FuncRehabPlanDetailRespVO.PlanItem::getFrequencyId)
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(FrequencyVO::getId, Function.identity()));

        planItems.forEach(planItem -> {
            FuncRehabItemTemplateDetailRespVO itemTemplate = mapOfItemTemplates.get(planItem.getTemplateId());
            FrequencyVO frequencyVO = frequencyMap.get(planItem.getFrequencyId());
            setPlanItemProperties(responseVO.getPatientId(), planItem, itemTemplate, frequencyVO);
        });
    }

    private void setPlanItemProperties(Long patientId, FuncRehabPlanDetailRespVO.PlanItem planItem,
                                       FuncRehabItemTemplateDetailRespVO itemTemplate, FrequencyVO frequency) {
        planItem.setName(itemTemplate.getName());
        planItem.setMethod(itemTemplate.getMethod());
        planItem.setOperationMethod(itemTemplate.getOperationMethod());
        planItem.setObjective(itemTemplate.getObjective());
        if (frequency != null) {
            planItem.setFrequencyName(frequency.getName());
            if (Objects.equals(frequency.getQuantityUnit(), "WEEK")) {
                planItem.setTrainingDays(WeekdayConverter.checkAndConvertWeekdays(frequency.getTimes()));
            }
        }
        planItem.setRestTimePerSet(itemTemplate.getRestTimePerSet());
        planItem.setMediaType(itemTemplate.getMediaType());
        planItem.setMediaFiles(itemTemplate.getMediaFiles());
        planItem.setUnit(itemTemplate.getUnit());
        planItem.setAttention(itemTemplate.getAttention());

        List<FuncRehabItemDeviceDataVO> deviceData = itemTemplate.getDeviceData();
        if (CollectionUtils.isNotEmpty(deviceData)) {
            List<PatientDeviceTypeEnum> patientDeviceTypeEnums = deviceData.stream().map(FuncRehabItemDeviceDataVO::getDeviceType).filter(Objects::nonNull).collect(Collectors.toList());
            List<PatientDeviceVO> patientDeviceVOS = patientDeviceFeign.list(new QueryPatientDeviceReqVO().setPatientId(patientId).setDeviceType(patientDeviceTypeEnums));
            planItem.setDeviceData(CopyPropertiesUtil.normalCopyProperties(patientDeviceVOS, FuncRehabItemDeviceDataVO.class));
            planItem.setProjectDeviceData(deviceData);
        }
    }

    private boolean isCurrentPlan(FuncRehabPlanDO rehabPlan, LocalDateTime currentTime) {
        return currentTime.isAfter(rehabPlan.getStartTime()) && currentTime.isBefore(rehabPlan.getEndTime());
    }

    private List<FuncRehabPlanDetailRespVO> convertToResponseVO(List<FuncRehabPlanDO> rehabPlans,
                                                                Map<Long, List<FuncRehabPlanItemDO>> rehabPlanItemMap,
                                                                Map<Long, FuncRehabItemTemplateDetailRespVO> itemTemplateMap,
                                                                Map<Long, List<FuncRehabPlanDetailRespVO.CareAdvice>> careAdvices, LocalDateTime now) {
        return rehabPlans.stream()
                .map(rehabPlan -> createResponseVO(rehabPlan, rehabPlanItemMap, itemTemplateMap, careAdvices, now))
                .collect(Collectors.toList());
    }

    @Override
    public IPage<Long> getAllCurrentPlanIds(PageParam pageParam) {
        return baseMapper.getAllCurrentPlanIds(new Page<>(pageParam.getPageNo(), pageParam.getPageSize()))
                .convert(BaseDO::getId);
    }

    @Override
    public List<Long> getAllHasPlanPatientIds() {
        return baseMapper.getAllHasPlanPatientIds();
    }

    @Override
    public List<Long> getAllIds(){
        return baseMapper.getAllIds();
    }

    @Override
    public FuncRehabPlanDetailRespVO.PlanItem planItemDetails(Long id) {
        FuncRehabPlanItemDO funcRehabPlanItemDO = getPlanItemDetails(id);
        AssertUtils.notNull(funcRehabPlanItemDO, RehabErrorCode.PLAN_ITEM_NOT_EXISTS);

        FuncRehabPlanDO funcRehabPlanDO = super.getById(funcRehabPlanItemDO.getPlanId());
        AssertUtils.notNull(funcRehabPlanItemDO, RehabErrorCode.PLAN_NOT_EXISTS);

        FuncRehabPlanDetailRespVO.PlanItem planItem = CopyPropertiesUtil.normalCopyProperties(funcRehabPlanItemDO, FuncRehabPlanDetailRespVO.PlanItem.class);
        FrequencyVO frequencyVO = frequencyFeign.getById(funcRehabPlanItemDO.getFrequencyId());
        FuncRehabItemTemplateDetailRespVO funcRehabItemTemplateDetailRespVO = funcRehabItemTemplateFeign.detail(funcRehabPlanItemDO.getTemplateId());
        setPlanItemProperties(funcRehabPlanDO.getPatientId(), planItem, funcRehabItemTemplateDetailRespVO, frequencyVO);
        planItem.setFormVO(funcRehabPlanItemDO.getSnapshot());
        planItem.setVolumeInspiratory(funcRehabPlanDO.getVolumeInspiratory());
        planItem.setVolumeExpiratory(funcRehabPlanDO.getVolumeExpiratory());
        return planItem;
    }

}
