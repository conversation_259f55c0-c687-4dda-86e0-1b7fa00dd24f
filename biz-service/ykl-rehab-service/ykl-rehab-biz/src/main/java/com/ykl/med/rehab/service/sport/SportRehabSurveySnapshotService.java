package com.ykl.med.rehab.service.sport;

import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.resp.sport.SportRehabSurveySnapshotVO;

import java.util.List;

/**
 * 运动康复问卷快照服务接口。
 * 本接口提供了运动康复问卷快照的创建、完成、列表查询等功能。
 * 主要用于处理患者运动康复问卷数据的快照操作。
 *
 * <AUTHOR>
 */
public interface SportRehabSurveySnapshotService {

    /**
     * 完成运动康复问卷快照的处理。
     * 此方法用于标记运动康复问卷快照的完成状态，并进行相应的处理。
     *
     * @param reqVO 包含问卷快照完成所需数据的请求对象。
     */
    void complete(RehabSurveySnapshotCompleteReqVO reqVO);

    /**
     * 批量完成功能康复问卷快照的处理。
     * 此方法用于标记功能康复问卷快照的完成状态，并进行相应的处理。
     *
     * @param reqVO 包含问卷快照完成所需数据的请求对象。
     */
    void batchComplete(RehabSurveySnapshotsBatchSaveReqVO reqVO);

    /**
     * 获取运动康复问卷快照列表。
     * 此方法用于查询和返回运动康复问卷快照的列表。
     *
     * @param reqVO 包含问卷快照列表查询所需数据的请求对象。
     * @return 运动康复问卷快照的列表。
     */
    List<SportRehabSurveySnapshotVO> list(SurveySnapshotListReqVO reqVO);

    SportRehabSurveySnapshotVO details(Long id);

    /**
     * 保存或更新运动康复问卷快照。
     * 此方法用于保存或更新运动康复问卷快照。
     *
     * @param reqVO 包含问卷快照保存或更新所需数据的请求对象。
     */
    void saveOrUpdate(SurveySnapshotSaveOrUpdateReqVO reqVO);

    /**
     * 发送运动康复问卷快照。
     *
     * @param reqVO 包含问卷快照发送所需数据的请求对象。
     */
    void send(RehabSurveySnapshotSendReqVO reqVO);

    /**
     * 复制快照
     *
     * @param sourcePlanId 源计划id
     * @param targetPlanId 目标计划id
     * @return 复制后的快照列表
     */
    List<SportRehabSurveySnapshotVO> copySnapshots(Long sourcePlanId, Long targetPlanId);

    List<Long> getPatientIdNullPlanId();
}