package com.ykl.med.rehab.mapper;

import com.ykl.med.framework.mybatis.core.mapper.BaseMapperX;
import com.ykl.med.rehab.entity.NutritionalPlanDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 营养方案 数据访问层
 *
 * <AUTHOR>
 * 2025-4-22 10:20:52
 */
@Mapper
public interface NutritionalPlanMapper extends BaseMapperX<NutritionalPlanDO> {

    @Update("update t_nutritional_plan SET `status`=#{status},update_time=#{now},plan_end_time=#{now} WHERE id=#{id}")
    void currentPlanFail(@Param("id") Long id, @Param("status") String status, @Param("now") LocalDateTime now);

    @Select("select id from t_nutritional_plan")
    List<Long> getAllIds();
}