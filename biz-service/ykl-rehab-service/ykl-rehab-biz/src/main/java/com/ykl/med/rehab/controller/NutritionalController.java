package com.ykl.med.rehab.controller;

import com.ykl.med.masterdata.vo.form.PatientFormVO;
import com.ykl.med.rehab.api.NutritionalFeign;
import com.ykl.med.rehab.service.nutritional.NutritionalFormService;
import com.ykl.med.rehab.service.nutritional.NutritionalService;
import com.ykl.med.rehab.vo.req.nutritional.*;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalHistoryListVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPlanVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/nutritional")
@RequiredArgsConstructor
public class NutritionalController implements NutritionalFeign {

    private final NutritionalService nutritionalService;
    private final NutritionalFormService nutritionalFormService;


    @Override
    @PostMapping("/list")
    public List<NutritionalPlanVO> list(@RequestBody QueryNutritionalPlanReqVO reqVO) {
        return nutritionalService.list(reqVO);
    }

    @Override
    @PostMapping("/current")
    public NutritionalPlanVO current(@RequestParam("patientId") Long patientId) {
        return nutritionalService.current(patientId);
    }


    @Override
    @PostMapping("/details")
    public NutritionalPlanVO details(@RequestParam("id") Long id) {
        return nutritionalService.details(id);
    }


    @Override
    @PostMapping("/history/list")
    public List<NutritionalHistoryListVO> historyList(@Valid @RequestBody QueryNutritionalHistoryReqVO reqVO) {
        return nutritionalService.historyList(reqVO);
    }


    @Override
    @PostMapping("/form/list")
    public List<PatientFormVO> formList(@RequestParam("id") Long id) {
        return nutritionalFormService.queryPatientForm(id);
    }


    @Override
    @PostMapping("/formFinish")
    public Long formFinish(@Valid @RequestBody NutritionalFormFinishVO reqVO) {
        return nutritionalService.formFinish(reqVO);
    }


    @Override
    @PostMapping("/reset")
    public Long reset(@Valid @RequestBody NutritionalResetVO reqVO) {
        return nutritionalService.reset(reqVO);
    }


    @Override
    @PostMapping("/saveOrUpdate")
    public Long saveOrUpdate(@Valid @RequestBody NutritionalSaveOrUpdateReqVO reqVO) {
        return nutritionalService.saveOrUpdate(reqVO);
    }


    @Override
    @PostMapping("/extraTime")
    public Long extraTime(@Valid @RequestBody NutritionalExtraTimeReqVO reqVO) {
        return nutritionalService.extraTime(reqVO);
    }

    @PostMapping("/getAllIds")
    @Override
    public List<Long> getAllIds(){
        return nutritionalService.getAllIds();
    }

}