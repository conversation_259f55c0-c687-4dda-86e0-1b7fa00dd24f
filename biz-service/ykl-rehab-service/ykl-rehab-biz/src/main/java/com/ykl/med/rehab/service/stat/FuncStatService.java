package com.ykl.med.rehab.service.stat;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.rehab.service.func.FuncRehabPlanItemExecuteService;
import com.ykl.med.rehab.service.func.FuncRehabSurveySnapshotService;
import com.ykl.med.rehab.vo.resp.stat.FuncStatVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class FuncStatService {
    private static final String FUNC_STAT_REDIS_KEY = "rehab:func:stat:";
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private FuncRehabPlanItemExecuteService funcRehabPlanItemExecuteService;
    @Resource
    private FuncRehabSurveySnapshotService funcRehabSurveySnapshotService;


    public void refreshStat(Long patientId) {
        stringRedisTemplate.delete(FUNC_STAT_REDIS_KEY + patientId);
    }

    public FuncStatVO getStatByPatientId(Long patientId) {
        String funcStatJson = stringRedisTemplate.opsForValue().get(FUNC_STAT_REDIS_KEY + patientId);
        if (funcStatJson != null) {
            return JSONObject.parseObject(funcStatJson, FuncStatVO.class);
        }
        FuncStatVO funcStat = this.statByPatientId(patientId);
        stringRedisTemplate.opsForValue().set(FUNC_STAT_REDIS_KEY + patientId, JSONObject.toJSONString(funcStat), 1, TimeUnit.DAYS);
        return funcStat;
    }

    private FuncStatVO statByPatientId(Long patientId) {
        FuncStatVO stat = funcRehabPlanItemExecuteService.aggregateFuncStats(patientId, LocalDateTime.now().minusMonths(3));
        stat.setEvaluations(funcRehabPlanItemExecuteService.getEvaluationByPatientId(patientId));
        stat.setCountForm(funcRehabSurveySnapshotService.countCompleteByPatientId(patientId));
        if (stat.getThreeMonthTotalCount() > 0) {
            stat.setThreeMonthExecRate(stat.getThreeMonthCompletedCount() * 100 / stat.getThreeMonthTotalCount());
        }
        return stat;
    }
}
