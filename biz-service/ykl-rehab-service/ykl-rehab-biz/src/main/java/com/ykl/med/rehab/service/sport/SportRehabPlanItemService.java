package com.ykl.med.rehab.service.sport;

import com.ykl.med.rehab.vo.req.sport.SportRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanDetailRespVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 运动康复计划项服务
 *
 * <AUTHOR>
 */
public interface SportRehabPlanItemService {

    /**
     * 批量保存运动康复计划项
     *
     * @param planId 运动康复计划ID
     * @param reqVO  运动康复计划保存或更新请求VO
     */
    void saveBatch(Long planId, SportRehabPlanSaveOrUpdateReqVO reqVO);

    /**
     * 查询运动康复计划项
     *
     * @param planId 运动康复计划ID
     * @return 运动康复计划项
     */
    List<SportRehabPlanDetailRespVO.PlanItem> list(Long planId);

    /**
     * 根据运动康复计划ID列表查询运动康复计划项
     *
     * @param planIds 运动康复计划ID列表
     * @return 运动康复计划项
     */
    Map<Long, List<SportRehabPlanDetailRespVO.PlanItem>> listByPlanIds(Collection<Long> planIds);
}