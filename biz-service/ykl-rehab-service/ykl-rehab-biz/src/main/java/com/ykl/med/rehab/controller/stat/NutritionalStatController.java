package com.ykl.med.rehab.controller.stat;

import com.ykl.med.rehab.api.stat.NutritionalStatFeign;
import com.ykl.med.rehab.service.stat.NutritionalStatService;
import com.ykl.med.rehab.vo.resp.stat.NutritionalStatVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class NutritionalStatController implements NutritionalStatFeign {
    private final NutritionalStatService nutritionalStatService;

    @PostMapping("/nutritional/stat/getStatByPatientId")
    @Override
    public NutritionalStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId) {
        return nutritionalStatService.getStatByPatientId(patientId);
    }

    @PostMapping("/nutritional/stat/refreshStat")
    @Override
    public void refreshStat(@RequestParam(value = "patientId") Long patientId) {
        nutritionalStatService.refreshStat(patientId);
    }
}
