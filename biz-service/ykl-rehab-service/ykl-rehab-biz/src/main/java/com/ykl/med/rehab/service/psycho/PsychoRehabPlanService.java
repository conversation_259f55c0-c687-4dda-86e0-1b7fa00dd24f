package com.ykl.med.rehab.service.psycho;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.masterdata.api.PatientFormFeign;
import com.ykl.med.masterdata.enums.PatientFormBizType;
import com.ykl.med.masterdata.vo.form.PatientFormQueryVO;
import com.ykl.med.rehab.entity.PsychoRehabPlanDO;
import com.ykl.med.rehab.entity.PsychoRehabPlanItemExecuteDO;
import com.ykl.med.rehab.mapper.PsychoRehabPlanItemExecuteMapper;
import com.ykl.med.rehab.mapper.PsychoRehabPlanMapper;
import com.ykl.med.rehab.vo.req.ExtendPlanReqVO;
import com.ykl.med.rehab.vo.req.HistoryPlanReqVO;
import com.ykl.med.rehab.vo.req.PatientIdAndPlanIdVO;
import com.ykl.med.rehab.vo.req.psycho.PsychoRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PsychoRehabPlanService extends ServiceImpl<PsychoRehabPlanMapper, PsychoRehabPlanDO> {
    @Resource
    private PsychoRehabPlanItemService psychoRehabPlanItemService;
    @Resource
    private PsychoRehabPlanCareAdviceService psychoRehabPlanCareAdviceService;
    @Resource
    private IdServiceImpl idService;
    @Resource
    private PsychoRehabPlanItemExecuteMapper psychoRehabPlanItemExecuteMapper;
    @Resource
    private PsychoRehabPlanItemExecuteService psychoRehabPlanItemExecuteService;
    @Resource
    private PsychoRehabPlanAttachmentService psychoRehabPlanAttachmentService;
    @Resource
    private PsychoRehabPlanMapper psychoRehabPlanMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private PatientFormFeign patientFormFeign;
    @Resource
    private UserFeign userFeign;

    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(PsychoRehabPlanSaveOrUpdateReqVO reqVO) {
        log.info("Saving or updating psycho rehab plan, reqVO: {}", JSON.toJSONString(reqVO));
        PsychoRehabPlanDO currentPlan = psychoRehabPlanMapper.getCurrentPlan(reqVO.getPatientId());
        // 检验
        PsychoRehabPlanDO.validatePlanDuration(reqVO, currentPlan);
        PsychoRehabPlanDO PsychoRehabPlanDO = CopyPropertiesUtil.normalCopyProperties(reqVO, PsychoRehabPlanDO.class);
        PsychoRehabPlanDO.setEndTime(LocalDateTimeUtil.endOfDay(PsychoRehabPlanDO.getEndTime(), true));
        super.saveOrUpdate(PsychoRehabPlanDO);
        psychoRehabPlanItemService.saveBatch(reqVO.getId(), reqVO);
        psychoRehabPlanCareAdviceService.saveBatch(reqVO.getId(), reqVO);
        psychoRehabPlanAttachmentService.saveBatch(reqVO.getId(), reqVO.getAttachmentIds());
        psychoRehabPlanItemExecuteService.generateExecute(reqVO.getId(), true);
        return reqVO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void extendPlan(ExtendPlanReqVO reqVO) {
        baseMapper.updateEndTime(reqVO.getPlanId(), reqVO.getEndTime());
    }

    /**
     * 获取当前或者下一次的id
     *
     * @param patientId 患者id
     * @return 当前方案id或者下一次的方案id
     */
    public Long getCurrentOrNextPlanId(Long patientId) {
        PsychoRehabPlanDO currentPlan = psychoRehabPlanMapper.getCurrentPlan(patientId);
        if (currentPlan == null) {
            String redisKey = "PsychoRehabNextPlanId:" + patientId;
            String planIdStr = stringRedisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isEmpty(planIdStr)) {
                Long planId = idService.nextId();
                stringRedisTemplate.opsForValue().set(redisKey, planId.toString());
                return planId;
            } else {
                long planId = Long.parseLong(planIdStr);
                PsychoRehabPlanDO old = psychoRehabPlanMapper.selectById(planId);
                if (old != null) {
                    //redis 存的还是过期了的方案，要刷掉
                    planId = idService.nextId();
                    stringRedisTemplate.opsForValue().set(redisKey, Long.toString(planId));
                    return planId;
                } else {
                    return planId;
                }
            }
        } else {
            return currentPlan.getId();
        }
    }

    public PsychoRehabPlanDetailRespVO getCurrentPlan(Long patientId) {
        PsychoRehabPlanDO currentPlan = baseMapper.getCurrentPlan(patientId);
        return buildDetail(currentPlan);
    }

    public List<PsychoRehabPlanVO> getHistoryPlans(HistoryPlanReqVO reqVO) {
        List<PsychoRehabPlanDO> plans = baseMapper.query(reqVO);
        List<Long> creatorDoctorIds = plans.stream().map(PsychoRehabPlanDO::getCreatorDoctorId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, String> doctorMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(creatorDoctorIds)) {
            List<UserSimpleVO> users = userFeign.listByUserIds(new IdListReqVO().setIdList(creatorDoctorIds));
            doctorMap = users.stream().collect(Collectors.toMap(UserSimpleVO::getId, UserSimpleVO::getName));
        }
        List<PsychoRehabPlanVO> psychoRehabPlanVOS = new ArrayList<>();
        for (PsychoRehabPlanDO plan : plans) {
            PsychoRehabPlanVO psychoRehabPlanVO = CopyPropertiesUtil.normalCopyProperties(plan, PsychoRehabPlanVO.class);
            psychoRehabPlanVO.setCreatorDoctorName(doctorMap.get(plan.getCreatorDoctorId()));
            psychoRehabPlanVOS.add(psychoRehabPlanVO);
        }
        return psychoRehabPlanVOS;
    }


    public PsychoRehabPlanDetailRespVO getPlanById(PatientIdAndPlanIdVO reqVO) {
        PsychoRehabPlanDO plan = baseMapper.selectById(reqVO.getPlanId());
        return buildDetail(plan);
    }

    public PsychoRehabPlanVO getById(Long id) {
        PsychoRehabPlanDO plan = baseMapper.selectById(id);
        return CopyPropertiesUtil.normalCopyProperties(plan, PsychoRehabPlanVO.class);
    }

    public IPage<Long> getAllCurrentPlanIds(PageParam pageParam) {
        return baseMapper.getAllCurrentPlanIds(new Page<>(pageParam.getPageNo(), pageParam.getPageSize()))
                .convert(BaseDO::getId);
    }

    public List<Long> getAllHasPlanPatientIds() {
        return baseMapper.getAllHasPlanPatientIds();
    }

    public List<Long> getAllIds() {
        List<Long> planIds = baseMapper.getAllIds();
//        String redisKey = "PsychoRehabNextPlanId:";
//        //把这个redis key的所有患者的计划ID 查出来
//        Set<String> keys = stringRedisTemplate.keys(redisKey + "*");
//        if (CollectionUtils.isEmpty(keys)) {
//            return planIds;
//        }
//        List<String> values = stringRedisTemplate.opsForValue().multiGet(keys);
//        if (CollectionUtils.isEmpty(values)) {
//            return planIds;
//        }
//        for (String value : values) {
//            planIds.add(Long.parseLong(value));
//        }
        return planIds;
    }


    public PsychoRehabPlanDetailRespVO getLastPlan(Long patientId) {
        LambdaQueryWrapper<PsychoRehabPlanDO> wrapper = new LambdaQueryWrapper<PsychoRehabPlanDO>()
                .eq(PsychoRehabPlanDO::getPatientId, patientId)
                .orderByDesc(PsychoRehabPlanDO::getEndTime)
                .last("limit 1");
        PsychoRehabPlanDO plan = baseMapper.selectOne(wrapper);
        return CopyPropertiesUtil.normalCopyProperties(plan, PsychoRehabPlanDetailRespVO.class);
    }

    private PsychoRehabPlanDetailRespVO buildDetail(PsychoRehabPlanDO plan) {
        if (plan == null) {
            return null;
        }
        PsychoRehabPlanDetailRespVO resp = CopyPropertiesUtil.normalCopyProperties(plan, PsychoRehabPlanDetailRespVO.class);
        resp.setItems(psychoRehabPlanItemService.listAndExecuted(plan.getId()));
        resp.setAttachments(psychoRehabPlanAttachmentService.getAttachmentByPlanId(plan.getId()));
        resp.setCareAdvices(psychoRehabPlanCareAdviceService.list(plan.getId()));
        resp.setPatientForms(patientFormFeign.query(new PatientFormQueryVO().setPatientId(plan.getPatientId()).setType(PatientFormBizType.PSYCHOLOGY).setBizId(plan.getId())));
        PsychoRehabPlanItemExecuteDO firstExecution = psychoRehabPlanItemExecuteMapper.firstExecution(plan.getId());
        resp.setFirstExecutionDate(firstExecution == null ? null : firstExecution.getRequiredTime().toLocalDate());
        return resp;
    }
}