package com.ykl.med.rehab.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ykl.med.framework.mybatis.core.mapper.BaseMapperX;
import com.ykl.med.rehab.entity.SportRehabPlanDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 运动康复计划Mapper
 *
 * <AUTHOR>
 */
public interface SportRehabPlanMapper extends BaseMapperX<SportRehabPlanDO> {

    @Update("UPDATE t_sport_rehab_plan SET end_time=#{endTime} WHERE id=#{planId}")
    void updateEndTime(@Param("planId") Long planId, @Param("endTime") LocalDateTime endTime);

    @Select("SELECT id FROM t_sport_rehab_plan WHERE patient_id=#{patientId} AND end_time > NOW() ORDER BY id DESC LIMIT 1")
    Long getCurrentPlanId(@Param("patientId") Long patientId);

    @Select("SELECT DISTINCT patient_id, FIRST_VALUE(id) OVER (PARTITION BY patient_id ORDER BY end_time DESC) AS id " +
            "FROM t_sport_rehab_plan WHERE end_time > NOW() AND status = 'ENABLE'")
    Page<SportRehabPlanDO> getAllCurrentPlanIds(Page<SportRehabPlanDO> page);

    @Select("SELECT DISTINCT patient_id FROM t_sport_rehab_plan WHERE end_time > NOW()")
    List<Long> getAllHasPlanPatientIds();

    @Select("SELECT id FROM t_sport_rehab_plan")
    List<Long> getAllIds();
}
