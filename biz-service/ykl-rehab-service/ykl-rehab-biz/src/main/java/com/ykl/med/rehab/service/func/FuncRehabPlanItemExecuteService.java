package com.ykl.med.rehab.service.func;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanExecuteRecordRespVO;
import com.ykl.med.rehab.vo.resp.stat.FuncStatVO;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 功能康复计划项执行服务
 *
 * <AUTHOR>
 */
public interface FuncRehabPlanItemExecuteService {

    /**
     * 统计功能康复计划项执行次数
     *
     * @param reqVO 功能康复计划统计请求VO
     * @return 功能康复计划项执行次数
     */
    List<RehabPlanExecuteCountRespVO> count(RehabPlanStatReqVO reqVO);

    /**
     * 执行功能康复计划项
     *
     * @param reqVO 请求对象
     */
    void execute(RehabPlanItemExecSaveOrUpdateReqVO reqVO);

    /**
     * 生成功能康复计划项待执行项
     *
     * @param planId 功能康复计划ID
     * @param immediatelyFlag 是否立即生成
     *                        true-立即生成，创建项目的开始时间是当前，
     *                        false-定时生成，创建的项目开始时间是项目的开始时间
     */
    void generateExecute(Long planId, boolean immediatelyFlag);

    /**
     * 获取功能康复计划项周统计数据
     *
     * @param reqVO 请求对象
     * @return 周统计数据
     */
    RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(ItemExecStatReqVO reqVO);

    /**
     * 查询功能康复计划项执行记录
     *
     * @param reqVO 查询请求VO
     * @return 功能康复计划项执行记录
     */
    PageResult<FuncRehabPlanExecuteRecordRespVO> queryExecuteRecords(RehabPlanExecuteQueryReqVO reqVO);

    /**
     * 更新执行记录
     *
     * @param reqVO 包含要更新的执行记录详细信息的请求对象
     */
    void updateExecuteRecord(RehabPlanItemExecSaveOrUpdateReqVO reqVO);

    /**
     * 删除执行记录
     *
     * @param id 要删除的执行记录的ID
     */
    void deleteExecuteRecord(Long id);

    /**
     * 根据时间范围查询执行记录
     *
     * @param reqVO 包含开始时间和结束时间的请求对象
     * @return 执行记录列表
     */
    Map<String, List<FuncRehabPlanExecuteRecordRespVO>> queryExecuteRecordsByTimeRange(RehabPlanExecuteTimeRangeQueryReqVO reqVO);

    /**
     * 批量生成功能康复计划项待执行项
     *
     * @param planIds 功能康复计划ID列表
     */
    void generateExecuteBatch(Collection<Long> planIds);

    List<String> getEvaluationByPatientId(Long patientId);

    FuncStatVO aggregateFuncStats(Long patientId, LocalDateTime startTime);
}
