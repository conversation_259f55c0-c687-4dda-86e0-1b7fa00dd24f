package com.ykl.med.rehab.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.util.List;

/**
 * 营养-指导与建议 Data Object
 *
 * <AUTHOR>
 * 2025-4-22 10:20:52
 */
@Data
@TableName(value = "t_nutritional_suggestions", autoResultMap = true)
public class NutritionalSuggestionsDO extends BaseDO {
    /*** 营养方案ID */
    private Long nutritionalPlanId;
    /*** 项目ID */
    private Long itemId;
    /*** 项目名称 */
    private String itemName;
    /*** 项目类别 */
    private String itemCategory;
    /*** 项目内容 */
    private String itemContent;
    /*** 备注（项目说明） */
    private String remark;
    /*** 附件文件,格式：Json数组：["/ykl/13123123123123.pdf","/ykl/13123123123124.pdf"] */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String> mediaFiles;
    /*** 序号 */
    private Integer sort;

}