package com.ykl.med.rehab.service.stat;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.rehab.service.sport.SportRehabPlanItemExecuteService;
import com.ykl.med.rehab.service.sport.SportRehabSurveySnapshotService;
import com.ykl.med.rehab.vo.resp.stat.SportStatVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SportStatService {
    private static final String SPORT_STAT_REDIS_KEY = "rehab:sport:stat:";
    @Resource
    private SportRehabPlanItemExecuteService sportRehabPlanItemExecuteService;
    @Resource
    private SportRehabSurveySnapshotService sportRehabSurveySnapshotService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private SportStatVO statByPatientId(Long patientId) {
        SportStatVO stat = sportRehabPlanItemExecuteService.aggregateSportStats(patientId, LocalDateTime.now().minusMonths(3));
        stat.setEvaluations(sportRehabPlanItemExecuteService.getEvaluationByPatientId(patientId));
        stat.setCountForm(sportRehabSurveySnapshotService.countCompleteByPatientId(patientId));
        if (stat.getThreeMonthTotalCount() > 0) {
            stat.setThreeMonthExecRate(stat.getThreeMonthCompletedCount() * 100 / stat.getThreeMonthTotalCount());
        }
        return stat;
    }

    public SportStatVO getStatByPatientId(Long patientId) {
        String sportStatJson = stringRedisTemplate.opsForValue().get(SPORT_STAT_REDIS_KEY + patientId);
        if (sportStatJson != null) {
            return JSONObject.parseObject(sportStatJson, SportStatVO.class);
        }
        SportStatVO sportStat = this.statByPatientId(patientId);
        stringRedisTemplate.opsForValue().set(SPORT_STAT_REDIS_KEY + patientId, JSONObject.toJSONString(sportStat), 1, TimeUnit.DAYS);
        return sportStat;
    }

    public void refreshStat(Long patientId) {
        stringRedisTemplate.delete(SPORT_STAT_REDIS_KEY + patientId);
    }

}
