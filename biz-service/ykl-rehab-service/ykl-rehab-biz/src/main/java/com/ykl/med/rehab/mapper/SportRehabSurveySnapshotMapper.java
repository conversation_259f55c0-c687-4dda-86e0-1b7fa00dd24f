package com.ykl.med.rehab.mapper;

import com.ykl.med.framework.mybatis.core.mapper.BaseMapperX;
import com.ykl.med.rehab.entity.SportRehabSurveySnapshotDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 运动康复问卷快照Mapper
 *
 * <AUTHOR>
 */
public interface SportRehabSurveySnapshotMapper extends BaseMapperX<SportRehabSurveySnapshotDO> {

    @Update("UPDATE t_sport_rehab_survey_snapshot SET plan_id=#{planId} WHERE patient_id=#{patientId} and plan_id is null")
    void bindPlanId(@Param("patientId") Long patientId, @Param("planId") Long planId);

    @Select("SELECT distinct patient_id FROM t_func_rehab_survey_snapshot WHERE plan_id is null")
    List<Long> getPatientIdNullPlanId();

    @Select("SELECT count(1) FROM t_sport_rehab_survey_snapshot WHERE patient_id = #{patientId} and status = 'COMPLETED'")
    Integer countCompleteByPatientId(@Param("patientId") Long patientId);
}
