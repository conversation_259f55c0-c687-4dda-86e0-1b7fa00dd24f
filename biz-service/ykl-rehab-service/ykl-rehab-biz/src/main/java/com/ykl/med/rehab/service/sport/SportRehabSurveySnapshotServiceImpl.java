package com.ykl.med.rehab.service.sport;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.constants.topic.RehabMangeTopic;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.ykl.med.masterdata.api.FormFeign;
import com.ykl.med.masterdata.vo.FormVO;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.ToDoMessageFeign;
import com.ykl.med.push.constans.MessageConstants;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.enums.ToDoMessageType;
import com.ykl.med.push.vo.message.BizMessageBaseVO;
import com.ykl.med.push.vo.message.MessageSendPatientReqVO;
import com.ykl.med.push.vo.todo.ToDoMessageReqVO;
import com.ykl.med.rehab.entity.SportRehabSurveySnapshotDO;
import com.ykl.med.rehab.enums.CompleteStatus;
import com.ykl.med.rehab.enums.RehabType;
import com.ykl.med.rehab.mapper.SportRehabPlanMapper;
import com.ykl.med.rehab.mapper.SportRehabSurveySnapshotMapper;
import com.ykl.med.rehab.util.FormUtils;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.manage.RehabManageMqMessageVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabSurveySnapshotVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 患者运动康复问卷快照Service实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SportRehabSurveySnapshotServiceImpl
        extends ServiceImpl<SportRehabSurveySnapshotMapper, SportRehabSurveySnapshotDO>
        implements SportRehabSurveySnapshotService {

    private final SportRehabPlanMapper sportRehabPlanMapper;

    private final IdServiceImpl idService;

    private final MessageFeign messageFeign;

    private final FormFeign formFeign;

    private final ToDoMessageFeign toDoMessageFeign;
    private final RocketMQTemplate rocketMqTemplate;
    private final SportRehabSurveySnapshotMapper sportRehabSurveySnapshotMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void complete(RehabSurveySnapshotCompleteReqVO reqVO) {
        log.info("填写表单快照，reqVO: {}", JSON.toJSONString(reqVO));
        FormUtils.calcScoreAndResult(reqVO.getSnapshot());
        SportRehabSurveySnapshotDO snapshot = super.getById(reqVO.getId());
        snapshot.setStatus(CompleteStatus.COMPLETED);
        snapshot.setSnapshot(JSON.parseObject(JSON.toJSONString(reqVO.getSnapshot())));
        super.updateById(snapshot);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchComplete(RehabSurveySnapshotsBatchSaveReqVO reqVO) {
        log.info("批量填写表单快照，reqVO: {}", JSON.toJSONString(reqVO));
        for (FormVO formVO : reqVO.getSnapshots()) {
            FormUtils.calcScoreAndResult(formVO);
        }
        List<SportRehabSurveySnapshotDO> entities = reqVO.getSnapshots().stream().map(snapshot -> {
            SportRehabSurveySnapshotDO entity = new SportRehabSurveySnapshotDO()
                    .setStatus(CompleteStatus.COMPLETED)
                    .setSnapshot(JSON.parseObject(JSON.toJSONString(snapshot)))
                    .setPatientId(reqVO.getPatientId());
            snapshot.setId(idService.nextId());
            return entity;
        }).collect(Collectors.toList());
        super.saveBatch(entities);
    }

    @Override
    public List<SportRehabSurveySnapshotVO> list(SurveySnapshotListReqVO reqVO) {
        LambdaQueryWrapperX<SportRehabSurveySnapshotDO> wrapper = new LambdaQueryWrapperX<SportRehabSurveySnapshotDO>()
                .eq(SportRehabSurveySnapshotDO::getPatientId, reqVO.getPatientId())
                .eqIfPresent(SportRehabSurveySnapshotDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SportRehabSurveySnapshotDO::getOnline, reqVO.getOnline());

        if (reqVO.getPlanId() == null) {
            Long currentPlanId = sportRehabPlanMapper.getCurrentPlanId(reqVO.getPatientId());
            wrapper.and(w -> w.eq(SportRehabSurveySnapshotDO::getPlanId, currentPlanId).or()
                    .isNull(SportRehabSurveySnapshotDO::getPlanId));
        } else {
            wrapper.eq(SportRehabSurveySnapshotDO::getPlanId, reqVO.getPlanId());
        }

        List<SportRehabSurveySnapshotDO> snapshots = super.list(wrapper);
        return CopyPropertiesUtil.copyList(snapshots, SportRehabSurveySnapshotVO::new);
    }

    @Override
    public SportRehabSurveySnapshotVO details(Long id) {
        SportRehabSurveySnapshotDO sportRehabSurveySnapshotDO = this.getById(id);
        if (sportRehabSurveySnapshotDO == null) {
            return null;
        }
        SportRehabSurveySnapshotVO funcRehabSurveySnapshotVO = CopyPropertiesUtil.normalCopyProperties(sportRehabSurveySnapshotDO, SportRehabSurveySnapshotVO.class);
        funcRehabSurveySnapshotVO.setSnapshot(sportRehabSurveySnapshotDO.getSnapshot().toJavaObject(FormVO.class));
        return funcRehabSurveySnapshotVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(SurveySnapshotSaveOrUpdateReqVO reqVO) {
        log.info("医护端填写问卷接口，reqVO: {}", JSON.toJSONString(reqVO));
        FormUtils.calcScoreAndResult(reqVO.getSnapshot());
        SportRehabSurveySnapshotDO snapshot = super.getById(reqVO.getId());
        if (snapshot == null) {
            snapshot = new SportRehabSurveySnapshotDO();
            snapshot.setId(reqVO.getId());
        }
        JSONObject snapshotJson = CopyPropertiesUtil.copy(reqVO.getSnapshot(), JSONObject::new);
        snapshot.setSnapshot(snapshotJson);
        snapshot.setStatus(CompleteStatus.COMPLETED);
        snapshot.setPatientId(reqVO.getPatientId());
        super.saveOrUpdate(snapshot);
    }

    @Override
    public void send(RehabSurveySnapshotSendReqVO reqVO) {
        log.info("发送运动康复问卷快照并发送消息通知患者及其家属，reqVO: {}", JSON.toJSONString(reqVO));
        FormVO formVO = fetchFormDetails(reqVO);
        Long patientId = reqVO.getPatientId();
        long snapshotId = createAndSaveSnapshot(formVO, patientId);

        MessageSendPatientReqVO messageSendReqVO = new MessageSendPatientReqVO()
                .setMsg(StringUtils.EMPTY)
                .setType(MessageType.SPORTS_FORM)
                .setExtra(new BizMessageBaseVO()
                        .setBizId(String.valueOf(snapshotId))
                        .setBizName(formVO.getLabel())
                        .setBizType(formVO.getCategory()))
                .setPatientId(reqVO.getPatientId())
                .setRequestId(UUID.randomUUID().toString());
        messageSendReqVO.setCurrentUserId(MessageConstants.SYSTEM_USER);
        messageFeign.sendMessagePatient(messageSendReqVO);

        toDoMessageFeign.addToDoMessage(new ToDoMessageReqVO()
                .setContent(formVO.getLabel())
                .setPatientId(patientId)
                .setType(ToDoMessageType.SPORTS_FORM)
                .setOutBizId(String.valueOf(snapshotId)));

        //康复管理es变动
        RehabManageMqMessageVO rehabManageMessage = new RehabManageMqMessageVO()
                .setPatientId(patientId)
                .setRehabType(RehabType.SPORT);
        rocketMqTemplate.syncSend(RehabMangeTopic.TOPIC, rehabManageMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SportRehabSurveySnapshotVO> copySnapshots(Long sourcePlanId, Long targetPlanId) {
        List<SportRehabSurveySnapshotDO> sourceSnapshots = super.list(
                new LambdaQueryWrapperX<SportRehabSurveySnapshotDO>()
                        .eq(SportRehabSurveySnapshotDO::getPlanId, sourcePlanId));

        if (sourceSnapshots.isEmpty()) {
            return Collections.emptyList();
        }

        List<SportRehabSurveySnapshotDO> targetSnapshots = sourceSnapshots.stream()
                .map(snapshot -> createTargetSnapshot(snapshot, targetPlanId))
                .collect(Collectors.toList());

        super.saveBatch(targetSnapshots);

        return targetSnapshots.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getPatientIdNullPlanId() {
        return sportRehabSurveySnapshotMapper.getPatientIdNullPlanId();
    }

    @Override
    public Integer countCompleteByPatientId(Long patientId){
        return sportRehabSurveySnapshotMapper.countCompleteByPatientId(patientId);
    }


    private SportRehabSurveySnapshotDO createTargetSnapshot(SportRehabSurveySnapshotDO source, Long targetPlanId) {
        SportRehabSurveySnapshotDO target = new SportRehabSurveySnapshotDO();
        CopyPropertiesUtil.copy(source, () -> target);
        target.setId(idService.nextId());
        target.setPlanId(targetPlanId);
        return target;
    }

    private SportRehabSurveySnapshotVO convertToVO(SportRehabSurveySnapshotDO source) {
        SportRehabSurveySnapshotVO target = new SportRehabSurveySnapshotVO();
        CopyPropertiesUtil.copy(source, () -> target);
        target.setSnapshot(JSON.toJavaObject(source.getSnapshot(), FormVO.class));
        return target;
    }

    private FormVO fetchFormDetails(RehabSurveySnapshotSendReqVO reqVO) {
        return formFeign.detail(reqVO.getFormId());
    }

    private long createAndSaveSnapshot(FormVO formVO, Long patientUserId) {
        SportRehabSurveySnapshotDO snapshot = new SportRehabSurveySnapshotDO()
                .setOnline(true)
                .setStatus(CompleteStatus.UNCOMPLETED)
                .setSnapshot(JSON.parseObject(JSON.toJSONString(formVO)))
                .setPatientId(patientUserId);
        long id = idService.nextId();
        snapshot.setId(id);
        super.save(snapshot);
        return id;
    }

}