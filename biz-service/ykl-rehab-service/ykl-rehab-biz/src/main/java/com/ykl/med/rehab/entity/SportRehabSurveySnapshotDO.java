package com.ykl.med.rehab.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.rehab.enums.CompleteStatus;
import lombok.Data;

/**
 * 运动康复问卷快照
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_sport_rehab_survey_snapshot", autoResultMap = true)
public class SportRehabSurveySnapshotDO extends BaseDO {

    private static final long serialVersionUID = -8875318334065860570L;

    /**
     * 患者用户id
     */
    private Long patientId;

    /**
     * 是否线上
     */
    private Boolean online;

    /**
     * 康复方案id
     */
    private Long planId;

    /**
     * 问卷快照状态
     */
    private CompleteStatus status;

    /**
     * 问卷快照内容
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject snapshot;
}
