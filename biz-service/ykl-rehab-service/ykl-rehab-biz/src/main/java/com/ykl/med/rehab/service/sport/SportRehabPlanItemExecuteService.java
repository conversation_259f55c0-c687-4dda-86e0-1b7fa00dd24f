package com.ykl.med.rehab.service.sport;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.enums.CompleteStatus;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanExecuteRecordRespVO;
import com.ykl.med.rehab.vo.resp.stat.SportStatVO;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 运动康复计划项执行服务
 *
 * <AUTHOR>
 */
public interface SportRehabPlanItemExecuteService {

    /**
     * 统计运动康复计划项执行次数
     *
     * @param reqVO 统计运动康复计划项执行次数请求
     * @return 运动康复计划项执行次数
     */
    List<RehabPlanExecuteCountRespVO> count(RehabPlanStatReqVO reqVO);

    /**
     * 执行康复计划
     *
     * @param reqVO 请求对象
     */
    void execute(RehabPlanItemExecSaveOrUpdateReqVO reqVO);

    /**
     * 生成功能康复计划项待执行项
     *
     * @param planId          运动康复计划ID
     * @param immediatelyFlag 是否立即生成
     *                        true-立即生成，创建项目的开始时间是当前，
     *                        false-定时生成，创建的项目开始时间是项目的开始时间
     */
    void generateExecute(Long planId, boolean immediatelyFlag);

    /**
     * 获取运动康复计划项周统计数据
     *
     * @param reqVO 请求对象
     * @return 周统计数据
     */
    RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(ItemExecStatReqVO reqVO);

    /**
     * 查询运动康复计划项执行记录
     *
     * @param reqVO 查询请求VO
     * @return 运动康复计划项执行记录
     */
    PageResult<SportRehabPlanExecuteRecordRespVO> queryExecuteRecords(RehabPlanExecuteQueryReqVO reqVO);

    List<String> getEvaluationByPatientId(Long patientId);

    SportStatVO aggregateSportStats(Long patientId, LocalDateTime startTime);
    /**
     * 更新执行记录
     *
     * @param reqVO 包含要更新的执行记录详细信息的请求对象
     */
    void updateExecuteRecord(RehabPlanItemExecSaveOrUpdateReqVO reqVO);

    /**
     * 删除执行记录
     *
     * @param id 要删除的执行记录的ID
     */
    void deleteExecuteRecord(Long id);

    /**
     * 根据时间范围查询执行记录
     *
     * @param reqVO 包含开始时间和结束时间的请求对象
     * @return 执行记录列表
     */
    Map<String, List<SportRehabPlanExecuteRecordRespVO>> queryExecuteRecordsByTimeRange(RehabPlanExecuteTimeRangeQueryReqVO reqVO);

    /**
     * 批量生成运动康复计划项待执行项
     *
     * @param currentPlanIds 当前计划id列表
     */
    void generateExecuteBatch(Collection<Long> currentPlanIds);
}