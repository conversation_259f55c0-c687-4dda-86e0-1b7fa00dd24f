package com.ykl.med.rehab.api.stat;

import com.ykl.med.rehab.vo.resp.stat.SportStatVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-rehab-service", path = "/ykl-rehab-service")
public interface SportStatFeign {
    @PostMapping("/sport/stat/getStatByPatientId")
    SportStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId);

    @PostMapping("/sport/stat/refreshStat")
    void refreshStat(@RequestParam(value = "patientId") Long patientId);
}
