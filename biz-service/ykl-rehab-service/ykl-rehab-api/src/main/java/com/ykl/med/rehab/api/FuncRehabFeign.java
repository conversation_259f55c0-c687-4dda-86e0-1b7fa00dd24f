package com.ykl.med.rehab.api;

import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.func.FuncRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.FormAndSnapshotRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.func.FuncRehabPlanDetailRespVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

@FeignClient(name = "ykl-rehab-service", path = "/ykl-rehab-service")
public interface FuncRehabFeign extends RehabFeign {

    @PostMapping("/funcRehab/plan/getDefaultForm")
    List<Long> getDefaultForm();

    @PostMapping("/funcRehab/plan/getBasePlanReq")
    FuncRehabPlanSaveOrUpdateReqVO getBasePlanReq(@RequestParam("patientId") Long patientId);

    @PostMapping("/funcRehab/plan/saveOrUpdate")
    Long saveOrUpdate(@Valid @RequestBody FuncRehabPlanSaveOrUpdateReqVO reqVO);

    @PostMapping("/funcRehab/plan/getCurrentPlan")
    FuncRehabPlanDetailRespVO getCurrentPlan(@RequestParam("patientId") Long patientId);

    @PostMapping("/funcRehab/plan/itemExecute/count")
    List<RehabPlanExecuteCountRespVO> count(@Valid @RequestBody RehabPlanStatReqVO reqVO);

    @PostMapping("/funcRehab/plan/itemExecute/execute")
    void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO);

    @PostMapping("/funcRehab/plan/itemExecute/generateExecute")
    void generateExecute(@RequestParam("planId") Long planId);

    @PostMapping("/funcRehab/plan/itemExecute/generateExecuteBatch")
    void generateExecuteBatch(@RequestBody Collection<Long> planIds);

    @PostMapping("/funcRehab/plan/getHistoryPlans")
    List<FuncRehabPlanDetailRespVO> getHistoryPlans(@RequestBody HistoryPlanReqVO reqVO);

    @PostMapping("/funcRehab/plan/extend")
    void extendPlan(@RequestBody ExtendPlanReqVO reqVO);

    @PostMapping("/funcRehab/plan/reEvaluate")
    void reEvaluate(@RequestParam("patientId") Long patientId);

    @PostMapping("/funcRehab/plan/getDraftPlan")
    FuncRehabPlanDetailRespVO getDraftPlan(@RequestParam("patientId") Long patientId);

    @PostMapping("/funcRehab/plan/getFormAndSnapshots")
    List<FormAndSnapshotRespVO> getFormAndSnapshots(@RequestBody FormAndSnapshotReqVO reqVO);

    @PostMapping("/funcRehab/plan/getPlanById")
    FuncRehabPlanDetailRespVO getPlanById(@RequestBody PatientIdAndPlanIdVO reqVO);

    @PostMapping("/funcRehab/plan/getById")
    FuncRehabPlanDetailRespVO getById(@RequestParam("id") Long id);

    @PostMapping("/funcRehab/plan/getAllCurrentPlanIds")
    PageResult<Long> getAllCurrentPlanIds(@RequestBody PageParam pageParam);

    @PostMapping("/funcRehab/plan/getAllHasPlanPatientIds")
    List<Long> getAllHasPlanPatientIds();

    @PostMapping("/funcRehab/plan/getAllIds")
    List<Long> getAllIds();

    @PostMapping("/funcRehab/plan/planItemDetails")
    FuncRehabPlanDetailRespVO.PlanItem planItemDetails(@RequestParam(name = "planItemId") Long planItemId);

    @PostMapping("/funcRehab/plan/isExistPlanExcludeDraft")
    boolean isExistPlanExcludeDraft(@RequestParam("patientId") Long patientId);
}