package com.ykl.med.rehab.api;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.vo.req.ItemExecStatReqVO;
import com.ykl.med.rehab.vo.req.RehabPlanExecuteQueryReqVO;
import com.ykl.med.rehab.vo.req.RehabPlanExecuteTimeRangeQueryReqVO;
import com.ykl.med.rehab.vo.req.RehabPlanItemExecSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.RehabPlanWeeklyStatRespWrapperVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanExecuteRecordRespVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@FeignClient(name = "ykl-rehab-service", path = "/ykl-rehab-service")
public interface PsychoRehabPlanItemExecuteFeign {

    @PostMapping("/psychoRehab/plan/itemExecute/weeklyStat")
    RehabPlanWeeklyStatRespWrapperVO getWeeklyStatistics(@RequestBody ItemExecStatReqVO reqVO);

    @PostMapping("/psychoRehab/plan/itemExecute/queryExecuteRecords")
    PageResult<PsychoRehabPlanExecuteRecordRespVO> queryExecuteRecords(@Valid @RequestBody RehabPlanExecuteQueryReqVO queryReqVO);

    @PostMapping("/psychoRehab/plan/itemExecute/update")
    void updateExecuteRecord(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO);

    @PostMapping("/psychoRehab/plan/itemExecute/delete")
    void deleteExecuteRecord(@RequestParam("id") Long id);

    @PostMapping("/psychoRehab/plan/itemExecute/queryByTimeRange")
    Map<String, List<PsychoRehabPlanExecuteRecordRespVO>> queryExecuteRecordsByTimeRange(@Valid @RequestBody RehabPlanExecuteTimeRangeQueryReqVO reqVO);
}