package com.ykl.med.rehab.vo.req.nutritional;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Data
public class QueryNutritionalRecipeReqVO {

    @NotNull(message = "nutritionalId can not null")
    @Schema(description = "营养方案id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long nutritionalId;


}
