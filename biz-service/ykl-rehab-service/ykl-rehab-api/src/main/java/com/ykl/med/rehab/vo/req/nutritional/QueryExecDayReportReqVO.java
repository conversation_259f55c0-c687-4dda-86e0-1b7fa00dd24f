package com.ykl.med.rehab.vo.req.nutritional;

import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/24
 */
@Data
public class QueryExecDayReportReqVO extends PageParam {

    @Schema(description = "患者id")
    private Long patientId;

    @Schema(description = "营养id")
    private Long nutritionalId;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

}
