package com.ykl.med.rehab.vo.resp.stat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "运动康复统计")
public class SportStatVO {
    @Schema(description = "评价")
    private List<String> evaluations;
    @Schema(description = "执行次数")
    private Integer totalExecCount = 0;
    @Schema(description = "近三个月执行率，百分比")
    private Integer threeMonthExecRate = 100;
    @Schema(description = "填写量表数量")
    private Integer countForm = 0;
    @Schema(description = "总时长")
    private Integer totalDuration = 0;
}
