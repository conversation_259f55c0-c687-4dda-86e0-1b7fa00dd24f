package com.ykl.med.rehab.vo.resp.nutritional;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.masterdata.vo.attachment.AttachmentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/23
*/
@Data
public class NutritionalSuggestionsVO {
    
    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "项目ID")
    @Stringify
    private Long itemId;

    @Schema(description = "项目名称")
    private String itemName;

    @Schema(description = "项目类别")
    private String itemCategory;

    @Schema(description = "项目内容")
    private String itemContent;

    @Schema(description = "备注（项目说明）")
    private String remark;

    @Schema(description = "附件文件")
    private List<String> mediaFiles;

    @Schema(description = "附件列表", example = "[]")
    private List<AttachmentVO> attachments;

    @Schema(description = "序号")
    private Integer sort;

}
