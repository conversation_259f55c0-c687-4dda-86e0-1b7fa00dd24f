package com.ykl.med.rehab.vo.req.nutritional;

import com.ykl.med.rehab.enums.NutritionalStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Data
public class QueryNutritionalPlanReqVO {

    @Schema(description = "患者id")
    private Long patientId;

    @Schema(description = "状态")
    private List<NutritionalStatusEnum> status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;


}
