package com.ykl.med.rehab.vo.req;

import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Schema(name = "RehabPlanExecuteTimeRangeQueryReqVO", description = "康复计划执行记录时间范围查询请求VO")
public class RehabPlanExecuteTimeRangeQueryReqVO {

    @Schema(description = "患者id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long patientId;

    @Schema(description = "开始时间", example = "2021-01-01 00:00:00", requiredMode = Schema.RequiredMode.REQUIRED)
    @TimestampConvert
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2021-12-31 23:59:59", requiredMode = Schema.RequiredMode.REQUIRED)
    @TimestampConvert
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    @Schema(description = "康复计划id", example = "1")
    private Long planId;

    @Schema(description = "康复计划项id", example = "1")
    private Long planItemId;

}