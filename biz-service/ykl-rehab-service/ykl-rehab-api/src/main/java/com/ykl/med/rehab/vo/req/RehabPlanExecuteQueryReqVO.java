package com.ykl.med.rehab.vo.req;

import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(name = "RehabPlanExecuteQueryReqVO", description = "康复计划执行记录查询请求VO")
public class RehabPlanExecuteQueryReqVO extends PageParam {

    private static final long serialVersionUID = 6227388968209564496L;

    @Schema(description = "康复计划id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "康复计划id不能为空")
    private Long planId;

    @Schema(description = "康复计划项id", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "康复计划项id不能为空")
    private Long planItemId;

}