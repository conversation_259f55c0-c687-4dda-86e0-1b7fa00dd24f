package com.ykl.med.rehab.vo.resp.stat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "功能康复统计")
public class FuncStatVO {
    @Schema(description = "评价")
    private List<String> evaluations;
    @Schema(description = "总执行次数")
    private Integer totalExecCount = 0;
    @Schema(description = "近三个月完成次数", hidden = true)
    private Integer threeMonthCompletedCount;
    @Schema(description = "近三个月总次数", hidden = true)
    private Integer threeMonthTotalCount;
    @Schema(description = "近三个月执行率，百分比")
    private Integer threeMonthExecRate = 100;
    @Schema(description = "填写量表数量")
    private Integer countForm = 0;
    @Schema(description = "总时长")
    private Integer totalDuration = 0;

    @Schema(description = "最大吸气量")
    private Integer maxVolumeInspiratory;
    @Schema(description = "平均吸气量")
    private Integer averageVolumeInspiratory;
    @Schema(description = "最大呼气量")
    private Integer maxVolumeExpiratory;
    @Schema(description = "平均呼气量")
    private Integer averageVolumeExpiratory;
    @Schema(description = "呼吸训练器总训练次数")
    private Integer countBreathingTraining;
    @Schema(description = "呼吸训练器总训练时长")
    private Integer totalDurationBreathingTraining;
}
