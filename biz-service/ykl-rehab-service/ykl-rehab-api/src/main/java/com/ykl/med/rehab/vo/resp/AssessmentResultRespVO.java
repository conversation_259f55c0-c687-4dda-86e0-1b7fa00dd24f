package com.ykl.med.rehab.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "AssessmentResultRespVO", description = "评估结果响应对象")
public class AssessmentResultRespVO implements Serializable {

    private static final long serialVersionUID = 8498453451308042138L;

    @Schema(description = "表单名称")
    private String formName;

    @Schema(description = "得分")
    private Integer score;

    @Schema(description = "评估结果")
    private String result;
}
