package com.ykl.med.rehab.vo.resp.func;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/2
 */
@Data
public class FuncBreathingTrainingRecordsVO {

    @Schema(description = "吸气量")
    private List<BreathingTrainingRecordsVO> volumeInspiratory;

    @Schema(description = "呼气量")
    private List<BreathingTrainingRecordsVO> volumeExpiratory;

}
