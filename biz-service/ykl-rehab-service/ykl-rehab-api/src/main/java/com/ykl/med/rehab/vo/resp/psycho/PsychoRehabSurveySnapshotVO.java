package com.ykl.med.rehab.vo.resp.psycho;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.masterdata.vo.FormVO;
import com.ykl.med.rehab.enums.CompleteStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "PsychoRehabSurveySnapshotVO", description = "康复问卷快照响应VO")
public class PsychoRehabSurveySnapshotVO implements Serializable {

    private static final long serialVersionUID = 5390394719003404854L;

    @Schema(description = "快照id", example = "1")
    @Stringify
    private Long id;

    @Schema(description = "患者用户id", example = "1")
    private Long patientId;

    @Schema(description = "问卷快照状态", example = "COMPLETED")
    private CompleteStatus status;

    @Schema(description = "是否线上", example = "true")
    private Boolean online;

    @Schema(description = "问卷快照内容", example = "{}")
    private FormVO snapshot;
}