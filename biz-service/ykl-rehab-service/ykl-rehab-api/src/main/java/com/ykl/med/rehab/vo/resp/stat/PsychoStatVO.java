package com.ykl.med.rehab.vo.resp.stat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "心理康复统计")
public class PsychoStatVO {
    @Schema(description = "最爱的执行项目")
    private String loveItemName;
    @Schema(description = "最爱的执行项目Id", hidden = true)
    private Long loveItemId;
    @Schema(description = "最爱的执行项目执行次数")
    private Integer loveItemExecCount = 0;
    @Schema(description = "最爱的执行项目总时长")
    private Integer loveItemTotalDuration = 0;

    @Schema(description = "总执行次数")
    private Integer totalExecCount = 0;
    @Schema(description = "近三个月总执行次数", hidden = true)
    private Integer threeMonthTotalCount = 0;
    @Schema(description = "近三个月已完成执行次数", hidden = true)
    private Integer threeMonthCompletedCount = 0;
    @Schema(description = "近三个月执行率，百分比")
    private Integer threeMonthExecRate = 100;
    @Schema(description = "总时长")
    private Integer totalDuration = 0;

    @Schema(description = "填写量表数量")
    private Integer countForm = 0;
}
