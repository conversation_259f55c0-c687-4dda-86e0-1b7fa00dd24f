package com.ykl.med.rehab.vo.req.nutritional;

import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPatientSnapshotVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalRecordsAssessmentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Data
public class NutritionalFormFinishVO {

    @Schema(description = "营养id")
    @NotNull(message = "营养id 不能为空")
    private Long id;

    @Schema(description = "患者基本信息快照", hidden = true)
    private NutritionalPatientSnapshotVO patientSnapshot;

    @Schema(description = "档案评估")
    private List<NutritionalRecordsAssessmentVO> recordsAssessment;

}
