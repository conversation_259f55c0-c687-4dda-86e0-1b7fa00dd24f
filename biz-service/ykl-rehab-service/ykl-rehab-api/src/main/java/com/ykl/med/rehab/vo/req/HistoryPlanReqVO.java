package com.ykl.med.rehab.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

@Data
@Schema(name = "HistoryPlanReqVO", description = "历史方案请求视图对象")
public class HistoryPlanReqVO implements Serializable {

    private static final long serialVersionUID = 6770137309071301904L;

    @Schema(description = "病人ID")
    @NotNull(message = "病人ID不能为空")
    private Long patientId;

    @Schema(description = "计划开始时间范围", example = "['2021-08-01', '2021-12-31']")
    private LocalDate[] planStartTimeRange;

    @Schema(description = "计划结束时间范围", example = "['2021-08-01', '2021-12-31']")
    private LocalDate[] planEndTimeRange;

    @Schema(description = "是否按照所选时间降序排序")
    @NotNull(message = "是否按照所选时间降序排序不能为空")
    private Boolean asc;
}