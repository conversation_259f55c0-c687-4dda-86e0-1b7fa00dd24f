package com.ykl.med.rehab.vo.resp.psycho;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.masterdata.entiry.vo.PsychotherapyVO;
import com.ykl.med.masterdata.vo.attachment.AttachmentVO;
import com.ykl.med.masterdata.vo.form.PatientFormVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "PsychologyFormResultVO", description = "心理康复 评估页面结果")
public class PsychologyFormResultVO extends PsychoRehabRecommendVO{

    @Stringify
    @Schema(description = "预生成的康复计划id", example = "1")
    private Long planId;

    @Schema(description = "流程状态")
    private PsychologyFlowStatus flowStatus;

    @Schema(description = "量表")
    private List<PatientFormVO> forms;

    /**
     * 心理康复流程状态枚举
     */
    public enum PsychologyFlowStatus {
        /**
         * 未完成
         */
        FORM_NOT_COMPLETED,
        /**
         * 需要问诊
         */
        NEED_CONSULTATION,
        /**
         * 问诊中
         */
        CONSULTATION,
        /**
         * 完成
         */
        COMPLETED,
        /**
         * 已生成草稿
         */
        DRAFT,
        /**
         * 已生成方案
         */
        PLAN_GENERATED,
    }
}
