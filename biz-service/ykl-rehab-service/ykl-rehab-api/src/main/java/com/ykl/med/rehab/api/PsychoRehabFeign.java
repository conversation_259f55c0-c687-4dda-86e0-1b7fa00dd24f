package com.ykl.med.rehab.api;

import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.psycho.PsychoRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanDetailRespVO;
import com.ykl.med.rehab.vo.resp.psycho.PsychoRehabPlanVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

@FeignClient(name = "ykl-rehab-service", path = "/ykl-rehab-service")
public interface PsychoRehabFeign extends RehabFeign {

    @PostMapping("/psychoRehab/plan/getBasePlanReq")
    PsychoRehabPlanSaveOrUpdateReqVO getBasePlanReq(@RequestParam("patientId") Long patientId);

    @PostMapping("/psychoRehab/plan/getCurrentOrNextPlanId")
    Long getCurrentOrNextPlanId(@RequestParam("patientId") Long patientId);

    @PostMapping("/psychoRehab/plan/saveOrUpdate")
    Long saveOrUpdate(@Valid @RequestBody PsychoRehabPlanSaveOrUpdateReqVO reqVO);

    @PostMapping("/psychoRehab/plan/getCurrentPlan")
    PsychoRehabPlanDetailRespVO getCurrentPlan(@RequestParam("patientId") Long patientId);

    @PostMapping("/psychoRehab/plan/itemExecute/count")
    List<RehabPlanExecuteCountRespVO> count(@Valid @RequestBody RehabPlanStatReqVO reqVO);

    @PostMapping("/psychoRehab/plan/itemExecute/execute")
    void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO);

    @PostMapping("/psychoRehab/plan/itemExecute/generateExecute")
    void generateExecute(@RequestParam("planId") Long planId);

    @PostMapping("/psychoRehab/plan/getHistoryPlans")
    List<PsychoRehabPlanVO> getHistoryPlans(@RequestBody HistoryPlanReqVO reqVO);

    @PostMapping("/psychoRehab/plan/extend")
    void extendPlan(@RequestBody ExtendPlanReqVO reqVO);

    @PostMapping("/psychoRehab/plan/getPlanById")
    PsychoRehabPlanDetailRespVO getPlanById(@RequestBody PatientIdAndPlanIdVO reqVO);

    @PostMapping("/psychoRehab/plan/getById")
    PsychoRehabPlanVO getById(@RequestParam("id") Long id);

    @PostMapping("/psychoRehab/plan/getAllCurrentPlanIds")
    PageResult<Long> getAllCurrentPlanIds(@RequestBody PageParam pageParam);

    @PostMapping("/psychoRehab/plan/getAllHasPlanPatientIds")
    List<Long> getAllHasPlanPatientIds();

    @PostMapping("/psychoRehab/plan/getAllIds")
    List<Long> getAllIds();

    @PostMapping("/psychoRehab/plan/getLastPlan")
    PsychoRehabPlanDetailRespVO getLastPlan(@RequestParam("patientId") Long patientId);

    @PostMapping("/psychoRehab/plan/itemExecute/generateExecuteBatch")
    void generateExecuteBatch(@RequestBody Collection<Long> currentPlanIds);

    @PostMapping("/psychoRehab/plan/isExistPlanExcludeDraft")
    boolean isExistPlanExcludeDraft(@RequestParam("patientId") Long patientId);
}