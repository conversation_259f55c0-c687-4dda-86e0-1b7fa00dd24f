package com.ykl.med.rehab.vo.req.nutritional;

import com.ykl.med.rehab.vo.resp.nutritional.NutritionalMealsVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Data
public class AddNutritionalRecipeVO {

    @Schema(description = "餐食-早")
    private List<NutritionalMealsVO> mealsMorning;

    @Schema(description = "餐食-早+")
    private List<NutritionalMealsVO> mealsMorningPlus;

    @Schema(description = "餐食-午")
    private List<NutritionalMealsVO> mealsNoon;

    @Schema(description = "餐食-午+")
    private List<NutritionalMealsVO> mealsNoonPlus;

    @Schema(description = "餐食-晚")
    private List<NutritionalMealsVO> mealsEvening;

    @Schema(description = "餐食-晚+")
    private List<NutritionalMealsVO> mealsEveningPlus;

}
