package com.ykl.med.rehab.api;

import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.rehab.vo.req.*;
import com.ykl.med.rehab.vo.req.sport.SportRehabPlanSaveOrUpdateReqVO;
import com.ykl.med.rehab.vo.resp.FormAndSnapshotRespVO;
import com.ykl.med.rehab.vo.resp.RehabPlanExecuteCountRespVO;
import com.ykl.med.rehab.vo.resp.sport.SportRehabPlanDetailRespVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

@FeignClient(name = "ykl-rehab-service", path = "/ykl-rehab-service")
public interface SportRehabFeign extends RehabFeign {

    @PostMapping("/sportRehab/plan/getDefaultForm")
    Long getDefaultForm();

    @PostMapping("/sportRehab/plan/getBasePlanReq")
    SportRehabPlanSaveOrUpdateReqVO getBasePlanReq(@RequestParam("patientId") Long patientId);

    @PostMapping("/sportRehab/plan/saveOrUpdate")
    Long saveOrUpdate(@Valid @RequestBody SportRehabPlanSaveOrUpdateReqVO reqVO);

    @PostMapping("/sportRehab/plan/getCurrentPlan")
    SportRehabPlanDetailRespVO getCurrentPlan(@RequestParam("patientId") Long patientId);

    @PostMapping("/sportRehab/plan/itemExecute/count")
    List<RehabPlanExecuteCountRespVO> count(@Valid @RequestBody RehabPlanStatReqVO reqVO);

    @PostMapping("/sportRehab/plan/itemExecute/execute")
    void execute(@Valid @RequestBody RehabPlanItemExecSaveOrUpdateReqVO reqVO);

    @PostMapping("/sportRehab/plan/itemExecute/generateExecute")
    void generateExecute(@RequestParam("planId") Long planId);

    @PostMapping("/sportRehab/plan/getHistoryPlans")
    List<SportRehabPlanDetailRespVO> getHistoryPlans(@RequestBody HistoryPlanReqVO reqVO);

    @PostMapping("/sportRehab/plan/extend")
    void extendPlan(@RequestBody ExtendPlanReqVO reqVO);

    @PostMapping("/sportRehab/plan/reEvaluate")
    void reEvaluate(@RequestParam("patientId") Long patientId);

    @PostMapping("/sportRehab/plan/getDraftPlan")
    SportRehabPlanDetailRespVO getDraftPlan(@RequestParam("patientId") Long patientId);

    @PostMapping("/sportRehab/plan/getFormAndSnapshots")
    List<FormAndSnapshotRespVO> getFormAndSnapshots(@RequestBody FormAndSnapshotReqVO reqVO);

    @PostMapping("/sportRehab/plan/getPlanById")
    SportRehabPlanDetailRespVO getPlanById(@RequestBody PatientIdAndPlanIdVO reqVO);

    @PostMapping("/sportRehab/plan/getById")
    SportRehabPlanDetailRespVO getById(@RequestParam("id") Long id);

    @PostMapping("/sportRehab/plan/getAllCurrentPlanIds")
    PageResult<Long> getAllCurrentPlanIds(@RequestBody PageParam pageParam);

    @PostMapping("/sportRehab/plan/getAllHasPlanPatientIds")
    List<Long> getAllHasPlanPatientIds();

    @PostMapping("/sportRehab/plan/getAllIds")
    List<Long> getAllIds();

    @PostMapping("/sportRehab/plan/itemExecute/generateExecuteBatch")
    void generateExecuteBatch(@RequestBody Collection<Long> currentPlanIds);

    @GetMapping("/sportRehab/plan/isExistPlanExcludeDraft")
    boolean isExistPlanExcludeDraft(@RequestParam("patientId") Long patientId);
}