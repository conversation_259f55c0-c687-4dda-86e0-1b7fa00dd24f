package com.ykl.med.rehab.util;

import com.alibaba.fastjson.JSON;
import com.ykl.med.masterdata.vo.BaseFormItemVO;
import com.ykl.med.masterdata.vo.FormScoreMappingVO;
import com.ykl.med.masterdata.vo.FormVO;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 表单工具类
 *
 * <AUTHOR>
 */
public class FormUtils {

    public static void calcScoreAndResult(FormVO formVO) {
        BigDecimal score = calculateTotalScore(formVO);
        String result = determineResult(formVO, score);

        formVO.setScore(score.setScale(0, RoundingMode.HALF_UP).intValue());
        formVO.setResult(result);
    }

    private static BigDecimal calculateTotalScore(FormVO formVO) {
        return formVO.getSubForms().stream()
                .flatMap(subForm -> subForm.getItems().stream())
                .map(item -> JSON.parseObject(JSON.toJSONString(item), BaseFormItemVO.class))
                .filter(item -> StringUtils.isNotBlank(item.getValue()))
                .map(item -> new BigDecimal(item.getValue()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static String determineResult(FormVO formVO, BigDecimal score) {
        return formVO.getScoreMappings().stream()
                .filter(mapping -> score.compareTo(BigDecimal.valueOf(mapping.getMinScore())) >= 0
                        && score.compareTo(BigDecimal.valueOf(mapping.getMaxScore())) <= 0)
                .findFirst()
                .map(FormScoreMappingVO::getResult)
                .orElse(StringUtils.EMPTY);
    }
}
