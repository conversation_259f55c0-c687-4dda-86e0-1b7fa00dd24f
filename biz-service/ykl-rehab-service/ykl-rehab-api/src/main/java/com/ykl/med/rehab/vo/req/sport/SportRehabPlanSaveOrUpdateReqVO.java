package com.ykl.med.rehab.vo.req.sport;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 功能康复计划保存或更新请求VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "FuncRehabPlanSaveOrUpdateReqVO", description = "功能康复计划保存或更新请求VO")
public class SportRehabPlanSaveOrUpdateReqVO implements Serializable, AutoBuildUserId {

    private static final long serialVersionUID = -2587519865831800572L;

    @Schema(description = "当前用户id", hidden = true)
    private Long currentUserId;

    @Schema(description = "计划id")
    private Long id;

    @Schema(description = "患者id")
    private Long patientId;

    /**
     * 模板id
     */
    @Schema(description = "模板id")
    private Long templateId;

    /**
     * 目标
     */
    @Schema(description = "目标")
    private String goal;

    /**
     * 持续时间
     */
    @Schema(description = "持续时间", example = "1")
    private Integer duration;

    /**
     * 持续时间单位
     */
    @Schema(description = "持续时间单位", example = "天")
    private String durationUnit;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期(时间戳)", example = "1609430400000")
    @TimestampConvert
    private LocalDateTime startTime;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期(时间戳)", example = "1609430400000")
    @TimestampConvert
    private LocalDateTime endTime;

    @Schema(description = "是否适合运动", example = "true")
    private Boolean suitable;

    @Schema(description = "康复方案状态", example = "ENABLE")
    @NotNull(message = "康复方案状态不能为空")
    private CommonStatusEnum status;

    /**
     * 计划项
     */
    @Schema(description = "计划项", example = "[]")
    private List<SportRehabPlanItemSaveOrUpdateReqVO> items;

    @Schema(description = "指导与建议", example = "[]")
    private List<SportRehabPlanCareAdviceSaveOrUpdateReqVO> careAdvices;

    @Data
    @Schema(name = "SportRehabPlanItemSaveOrUpdateReqVO", description = "运动康复计划项保存或更新请求VO")
    public static class SportRehabPlanItemSaveOrUpdateReqVO implements Serializable {

        private static final long serialVersionUID = -3917840747522474748L;

        /**
         * 功能康复模板方案id
         */
        @Schema(description = "功能康复模板方案id", example = "1")
        private Long planId;

        /**
         * 功能康复模板项id
         */
        @Schema(description = "功能康复模板项id", example = "1")
        private Long templateId;

        /**
         * 训练频次
         */
        @Schema(description = "训练频次", example = "字典值")
        private Long frequencyId;

        /**
         * 最大频次
         */
        @Schema(description = "最大频次", example = "字典值")
        private Long maxFrequencyId;

        /**
         * 序号
         */
        @Schema(description = "序号", example = "1")
        private Integer sort;

        /**
         * 单次数量
         */
        @Schema(description = "单次数量", example = "1")
        private Integer actionsPerSession;

        @Schema(description = "单位", example = "分钟")
        private String unit;

        /**
         * 单次组数
         */
        @Schema(description = "单次组数", example = "1")
        private Integer setsPerSession;

        /**
         * 每组数量
         */
        @Schema(description = "每组数量", example = "1")
        private Integer actionsPerSet;

        /**
         * 周总时长
         */
        @Schema(description = "周总时长", example = "1")
        private Integer totalDurationPerWeek;

        /**
         * 辅助康复器械
         */
        @Schema(description = "运动强度", example = "手动输入")
        private String intensity;
    }

    @Data
    @Schema(name = "SportRehabPlanCareAdviceSaveOrUpdateReqVO", description = "运动康复计划指导与建议保存或更新请求VO")
    public static class SportRehabPlanCareAdviceSaveOrUpdateReqVO implements Serializable {

        private static final long serialVersionUID = -1602554758715391268L;

        @Schema(description = "指导与建议id")
        private Long careAdviceId;

        @Schema(description = "指导与建议", example = "手动输入")
        private String content;

        @Schema(description = "序号", example = "1")
        private Integer sort;
    }
}
