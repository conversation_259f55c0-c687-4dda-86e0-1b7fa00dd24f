package com.ykl.med.rehab.vo.req.manage;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.rehab.enums.RehabStatus;
import com.ykl.med.rehab.enums.RehabType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "康复管理对象")
public class RehabManageVO {
    @Schema(description = "Id")
    private String id;
    @Stringify
    @Schema(description = "康复方案的Id")
    private Long rehabId;
    @Schema(description = "康复方案类型")
    private RehabType rehabType;
    @Schema(description = "问诊表单是否完成")
    private Boolean consultFormCompleted;
    @Schema(description = "方案开始日期")
    @TimestampConvert
    private LocalDateTime startTime;
    @TimestampConvert
    @Schema(description = "方案结束日期")
    private LocalDateTime endTime;
    @Schema(description = "康复方案状态")
    private RehabStatus rehabStatus;
    @Schema(description = "表单是否完成")
    private Boolean formCompleted;

    /**
     * 病史信息
     */
    @Schema(description = "患者病种阶段")
    private String stage;
    @Schema(description = "临床分期")
    private String clinicalStaging;
    @Stringify
    @Schema(description = "主病")
    private Long diseaseId;
    @Schema(description = "主病名称")
    private String diseaseName;
    /**
     * 患者
     */
    @Stringify
    @Schema(description = "患者Id")
    private Long patientId;
    @Schema(description = "患者姓名")
    private String patientName;
    @Schema(description = "患者性别")
    private String sex;
    @Schema(description = "生日")
    @TimestampConvert
    private LocalDate birthday;
    @Schema(description = "年纪", defaultValue = "年纪")
    private Integer age;
    @Stringify
    @Schema(description = "主治医生Id")
    private Long bindDoctorId;
    @Schema(description = "主治医生姓名")
    private String bindDoctorName;
    @Stringify
    @Schema(description = "医疗组Id")
    private Long medicalTeamId;
    @Schema(description = "医疗组名称")
    private String medicalTeamName;
    @Stringify
    @Schema(description = "会员版本Id")
    private Long memberVersionId;
    @Schema(description = "会员版本名称")
    private String memberVersionName;
    @Schema(description = "方案表单")
    private List<FormItemVO> formItems;
    @Schema(description = "方案问诊表单")
    private List<FormItemVO> consultForms;

    @Data
    @Schema(description = "方案表单项")
    public static class FormItemVO {
        @Schema(description = "表单名称")
        private String formName;
        @Stringify
        @Schema(description = "表单Id,用于各个模块打开量表弹窗")
        private Long formId;
        @Schema(description = "是否填写")
        private Boolean writeStatus;
    }
}
