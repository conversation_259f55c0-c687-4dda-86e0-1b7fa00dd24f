package com.ykl.med.rehab.vo.resp.nutritional;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/25
*/
@Data
public class NutritionalRecipeWeekVO {

    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "营养方案id")
    @Stringify
    private Long nutritionalId;

    @Schema(description = "患者id")
    @Stringify
    private Long patientId;

    @Schema(description = "周一的 食谱id")
    @Stringify
    private Long recipeMonday;

    @Schema(description = "周二的 食谱id")
    @Stringify
    private Long recipeTuesday;

    @Schema(description = "周三的 食谱id")
    @Stringify
    private Long recipeWednesday;

    @Schema(description = "周四的 食谱id")
    @Stringify
    private Long recipeThursday;

    @Schema(description = "周五的 食谱id")
    @Stringify
    private Long recipeFriday;

    @Schema(description = "周六的 食谱id")
    @Stringify
    private Long recipeSaturday;

    @Schema(description = "周天的 食谱id")
    @Stringify
    private Long recipeSunday;
    
}
