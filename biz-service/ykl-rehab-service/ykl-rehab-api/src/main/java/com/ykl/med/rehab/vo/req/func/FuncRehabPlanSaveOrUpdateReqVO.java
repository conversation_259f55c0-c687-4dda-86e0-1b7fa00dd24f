package com.ykl.med.rehab.vo.req.func;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.interfaces.AutoBuildUserId;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 功能康复计划保存或更新请求VO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "FuncRehabPlanSaveOrUpdateReqVO", description = "功能康复计划保存或更新请求VO")
public class FuncRehabPlanSaveOrUpdateReqVO implements Serializable, AutoBuildUserId {

    private static final long serialVersionUID = -7495138947025232510L;

    @Schema(description = "当前用户id", hidden = true)
    private Long currentUserId;

    @Schema(description = "计划id")
    private Long id;

    @Schema(description = "患者id")
    private Long patientId;

    /**
     * 模板id
     */
    @Schema(description = "模板id")
    private Long templateId;

    /**
     * 目标
     */
    @Schema(description = "目标")
    private String goal;

    /**
     * 持续时间
     */
    @Schema(description = "持续时间", example = "1")
    @NotNull(message = "持续时间不能为空")
    @Min(value = 1, message = "持续时间必须大于0")
    private Integer duration;

    /**
     * 持续时间单位
     */
    @Schema(description = "持续时间单位", example = "天")
    @NotBlank(message = "持续时间单位不能为空")
    private String durationUnit;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期(时间戳)", example = "1609430400000")
    @TimestampConvert
    private LocalDateTime startTime;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期(时间戳)", example = "1609430400000")
    @TimestampConvert
    @NotNull(message = "结束日期不能为空")
    private LocalDateTime endTime;

    @Schema(description = "吸气量(ml)")
    private Integer volumeInspiratory;

    @Schema(description = "呼气量(ml)")
    private Integer volumeExpiratory;

    /**
     * 计划项
     */
    @Schema(description = "计划项", example = "[]")
    private List<FuncRehabPlanItemSaveOrUpdateReqVO> items;

    @Schema(description = "指导与建议", example = "[]")
    private List<FuncRehabPlanCareAdviceSaveOrUpdateReqVO> careAdvices;

    @Schema(description = "康复计划状态", example = "ENABLE")
    @NotNull(message = "康复计划状态不能为空")
    private CommonStatusEnum status;

    @Data
    @Schema(name = "FuncRehabPlanItemSaveOrUpdateReqVO", description = "功能康复计划项保存或更新请求VO")
    public static class FuncRehabPlanItemSaveOrUpdateReqVO implements Serializable {

        private static final long serialVersionUID = -2476984738942804172L;

        /**
         * 功能康复模板项id
         */
        @Schema(description = "功能康复模板项id", example = "1")
        private Long templateId;

        /**
         * 训练频次
         */
        @Schema(description = "训练频次", example = "频率表ID")
        private Long frequencyId;

        /**
         * 最大频次
         */
        @Schema(description = "最大频次", example = "频率表ID")
        private Long maxFrequencyId;

        /**
         * 序号
         */
        @Schema(description = "序号", example = "1")
        private Integer sort;

        /**
         * 单次数量
         */
        @Schema(description = "单次数量", example = "1")
        private Integer actionsPerSession;

        /**
         * 单次组数
         */
        @Schema(description = "单次组数", example = "1")
        private Integer setsPerSession;

        /**
         * 辅助康复器械
         */
        @Schema(description = "辅助康复器械", example = "手动输入")
        private String equipment;
    }

    @Data
    @Schema(name = "FuncRehabPlanCareAdviceSaveOrUpdateReqVO", description = "功能康复计划指导与建议保存或更新请求VO")
    public static class FuncRehabPlanCareAdviceSaveOrUpdateReqVO implements Serializable {

        private static final long serialVersionUID = -1398372627241803952L;

        @Schema(description = "指导与建议id")
        private Long careAdviceId;

        @Schema(description = "指导与建议", example = "手动输入")
        private String content;

        @Schema(description = "序号", example = "1")
        private Integer sort;
    }
}
