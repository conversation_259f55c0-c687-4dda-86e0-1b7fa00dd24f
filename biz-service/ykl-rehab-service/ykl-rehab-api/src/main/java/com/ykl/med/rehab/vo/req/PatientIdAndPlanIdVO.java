package com.ykl.med.rehab.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(name = "PatientIdAndPlanIdVO", description = "患者ID和方案ID请求对象")
public class PatientIdAndPlanIdVO implements Serializable {

    private static final long serialVersionUID = 2139195141386803719L;

    @Schema(description = "患者ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "康复方案id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "康复方案id不能为空")
    private Long planId;
}
