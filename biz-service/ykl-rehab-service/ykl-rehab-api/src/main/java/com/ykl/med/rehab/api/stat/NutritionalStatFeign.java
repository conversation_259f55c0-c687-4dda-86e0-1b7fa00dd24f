package com.ykl.med.rehab.api.stat;

import com.ykl.med.rehab.vo.resp.stat.NutritionalStatVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-rehab-service", path = "/ykl-rehab-service")
public interface NutritionalStatFeign {
    @PostMapping("/nutritional/stat/getStatByPatientId")
    NutritionalStatVO getStatByPatientId(@RequestParam(value = "patientId") Long patientId);

    @PostMapping("/nutritional/stat/refreshStat")
    void refreshStat(@RequestParam(value = "patientId") Long patientId);
}
