package com.ykl.med.rehab.vo.resp.stat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "营养康复统计")
public class NutritionalStatVO {
    @Schema(description = "营养补充剂")
    private List<String> supplements;

    @Schema(description = "总执行次数")
    private Integer totalExecCount = 0;

    @Schema(description = "近三个月总执行次数", hidden = true)
    private Integer threeMonthTotalCount = 0;
    @Schema(description = "近三个月已完成执行次数", hidden = true)
    private Integer threeMonthCompletedCount = 0;
    @Schema(description = "近三个月执行率，百分比")
    private Integer threeMonthExecRate = 100;

    @Schema(description = "填写量表数量")
    private Integer countForm = 0;
}
