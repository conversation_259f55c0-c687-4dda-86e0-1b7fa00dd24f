package com.ykl.med.rehab.vo.resp.nutritional;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Data
public class NameValue {

    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "值")
    private String value;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "时间")
    @TimestampConvert
    private LocalDateTime time;

}
