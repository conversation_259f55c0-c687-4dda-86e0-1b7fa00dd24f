package com.ykl.med.rehab.vo.req.nutritional;

import com.ykl.med.rehab.vo.resp.nutritional.NutritionalPatientSnapshotVO;
import com.ykl.med.rehab.vo.resp.nutritional.NutritionalRecordsAssessmentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Data
public class NutritionalResetVO {

    @Schema(hidden = true)
    private Long userId;

    @Schema(description = "患者ID")
    @NotNull(message = "患者ID 不能为空")
    private Long patientId;

    @Schema(description = "是否注册过来，注册的入口量表默认发送")
    private Boolean registerFlag;

    @Schema(description = "患者基本信息快照", hidden = true)
    private NutritionalPatientSnapshotVO patientSnapshot;

    @Schema(description = "档案评估", hidden = true)
    private List<NutritionalRecordsAssessmentVO> recordsAssessment;

}
