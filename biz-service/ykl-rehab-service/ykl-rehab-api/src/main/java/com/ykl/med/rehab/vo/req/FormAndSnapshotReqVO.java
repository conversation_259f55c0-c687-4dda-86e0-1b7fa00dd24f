package com.ykl.med.rehab.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(name = "FormAndSnapshotReqVO", description = "康复方案表单和评估状态请求对象")
public class FormAndSnapshotReqVO implements Serializable {

    private static final long serialVersionUID = -2559720072144130314L;

    @Schema(description = "患者id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "患者id不能为空")
    private Long patientId;

    @Schema(description = "是否线上", example = "true")
    private Boolean online;

    @Schema(description = "康复方案id, 不传默认查询当前方案", example = "1")
    private Long planId;

    @Schema(description = "方案分类", example = "1", hidden = true)
    private String category;

}
