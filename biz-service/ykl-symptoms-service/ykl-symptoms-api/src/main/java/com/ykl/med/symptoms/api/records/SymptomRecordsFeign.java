package com.ykl.med.symptoms.api.records;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.symptoms.vo.records.SymptomRecordsColorVO;
import com.ykl.med.symptoms.vo.records.SymptomRecordsQueryVO;
import com.ykl.med.symptoms.vo.records.SymptomRecordsVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "ykl-symptoms-service", path = "ykl-symptoms-service/symptomRecords")
public interface SymptomRecordsFeign {

    @PostMapping("/list")
    List<SymptomRecordsVO> list(@RequestBody List<Long> symptomIds);

    @PostMapping("/query")
    PageResult<SymptomRecordsVO> query(@RequestBody @Valid SymptomRecordsQueryVO queryVO);

    @PostMapping("/querySymptomRecords")
    @Operation(summary = "查询症状日志(包含了颜色等)")
    PageResult<SymptomRecordsColorVO> querySymptomRecords(@RequestBody SymptomRecordsQueryVO queryVO);

    @PostMapping("/delete")
    void delete(@RequestParam("id") Long id);
}