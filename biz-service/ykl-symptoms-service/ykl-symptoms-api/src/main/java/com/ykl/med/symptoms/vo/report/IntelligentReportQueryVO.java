package com.ykl.med.symptoms.vo.report;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.symptoms.enums.IntelligentReportStatus;
import com.ykl.med.symptoms.enums.IntelligentReportType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "智能报告查询对象")
public class IntelligentReportQueryVO extends PageParam {
    @Schema(description = "报告类型", example = "[1,2,3]")
    private List<IntelligentReportType> types;

    @Schema(description = "报告状态,app端不传", example = "[1,2,3]")
    private List<IntelligentReportStatus> statusList;

    @TimestampConvert
    @Schema(description = "开始创建时间,app端不传", example = "157323384532452")
    private LocalDateTime startCreateTime;
    @TimestampConvert
    @Schema(description = "结束创建时间,app端不传", example = "157323384532452")
    private LocalDateTime endCreateTime;

    @TimestampConvert
    @Schema(description = "开始提交时间,app端不传", example = "157323384532452")
    private LocalDateTime startSubmitTime;
    @TimestampConvert
    @Schema(description = "结束提交时间,app端不传", example = "157323384532452")
    private LocalDateTime endSubmitTime;

    @TimestampConvert
    @Schema(description = "报告开始时间,app端不传", example = "157323384532452")
    private LocalDate startTime;
    @TimestampConvert
    @Schema(description = "报告提交时间,app端不传", example = "157323384532452")
    private LocalDate endTime;

    @Schema(description = "患者ID列表,app端不传", example = "[1,2,3]")
    private List<Long> patientIds;

}
