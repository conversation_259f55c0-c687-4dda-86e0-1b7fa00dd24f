package com.ykl.med.symptoms.api.records;

import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.symptoms.vo.records.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "ykl-symptoms-service", path = "ykl-symptoms-service/symptoms")
public interface SymptomFeign {

    @PostMapping("/add")
    @Operation(summary = "添加症状")
    void add(@RequestBody @Valid SymptomAddVO addVO);

    @PostMapping("/stopSymptom")
    @Operation(summary = "停止症状")
    void stopSymptom(@RequestParam("id") Long id);

    @PostMapping("/query")
    @Operation(summary = "查询症状")
    PageResult<SymptomVO> query(@RequestBody @Valid SymptomQueryVO queryVO);

    @PostMapping("/queryDetail")
    @Operation(summary = "查询详细列表", description = "返回列表数据")
    PageResult<SymptomDetailVO> queryDetail(@RequestBody SymptomQueryVO queryVO);

    @PostMapping("/getByPatientId")
    @Operation(summary = "查询症状列表")
    List<SymptomVO> getByPatientId(@RequestParam("patientId") Long patientId);

    @PostMapping("/getById")
    @Operation(summary = "查询症状详情")
    SymptomVO getById(@RequestParam("id") Long id);

    @PostMapping("/getByIds")
    @Operation(summary = "批量查询症状详情")
    List<SymptomVO> getByIds(@RequestBody List<Long> ids);

    @PostMapping("/getPainTotal")
    @Operation(summary = "查询疼痛症状列表")
    SymptomPainTotalVO getPainTotal(@RequestParam("patientId") Long patientId);

    @PostMapping("/queryChart")
    @Operation(summary = "查询图表")
    List<SymptomChartVO> queryChart(@RequestBody @Valid SymptomChartQueryVO symptomChartQueryVO);

    @PostMapping("/queryPainRecord")
    @Operation(summary = "查询疼痛记录")
    PageResult<SymptomPainRecordsVO> queryPainRecord(@RequestParam("patientId") Long patientId, @RequestBody PageParam pageParam);
}