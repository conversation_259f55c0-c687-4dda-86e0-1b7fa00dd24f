package com.ykl.med.symptoms.vo.report;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.symptoms.enums.IntelligentReportStatus;
import com.ykl.med.symptoms.enums.IntelligentReportType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "智能报告列表对象")
public class IntelligentReportListVO {
    @Stringify
    @Schema(description = "患者id", example = "1234567")
    private Long patientId;

    @Stringify
    @Schema(description = "id", example = "1234567")
    private Long id;

    @Schema(description = "报告状态", example = "TO_BE_SUBMITTED")
    private IntelligentReportStatus status;

    @Schema(description = "报告名称", example = "xxxx")
    private String name;

    @TimestampConvert
    @Schema(description = "报告开始时间", example = "2022-01-01")
    private LocalDate startDate;

    @TimestampConvert
    @Schema(description = "报告结束时间", example = "2022-01-01")
    private LocalDate endDate;

    @TimestampConvert
    @Schema(description = "提交时间", example = "157323384532452")
    private LocalDateTime submitTime;

    @TimestampConvert
    @Schema(description = "创建时间", example = "157323384532452")
    private LocalDateTime createTime;


    @Stringify
    @Schema(description = "提交人id", example = "1234567")
    private Long submitUserId;
    @Schema(description = "提交人姓名", example = "张三")
    private String submitUserName;


    @Schema(description = "报告类型", example = "SYMPTOM_REPORT")
    private IntelligentReportType type;
}
