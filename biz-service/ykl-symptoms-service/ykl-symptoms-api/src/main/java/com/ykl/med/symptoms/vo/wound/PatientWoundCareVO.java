package com.ykl.med.symptoms.vo.wound;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "患者伤口护理视图对象")
public class PatientWoundCareVO {
    @Stringify
    @Schema(description = "ID", example = "12345")
    private Long id;

    @Stringify
    @Schema(description = "患者ID", example = "12345")
    private Long patientId;

    @Schema(description = "伤口", example = "后背的一处创伤")
    private String wound;

    @Schema(description = "伤口所见")
    private List<String> woundAppearance;

    @Schema(description = "伤口疼痛", example = "轻度疼痛")
    private String woundPain;

    @Schema(description = "伤口感觉")
    private List<String> woundFeeling;

    @Schema(description = "今日处理", example = "应用药膏")
    private List<String> todayHandle;

    @Schema(description = "备注", example = "无其他特别说明")
    private String remark;

    @Schema(description = "图片")
    private List<String> images;

    @Schema(description = "创建时间")
    @TimestampConvert
    private LocalDateTime createTime;

    @Schema(description = "拍照时间")
    @TimestampConvert
    private LocalDateTime photoTime;
}
