package com.ykl.med.symptoms.vo.data;

import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "症状数据查询视图对象")
public class SymptomDataQueryVO extends PageParam {
    @Schema(description = "词", example = "示例词")
    private String name;

    @Schema(description ="是否量表症状", example = "true")
    private Boolean scaleFlag;

    @Schema(description = "类别（字典）", example = "类型示例")
    private String type;

    @Schema(description = "状态")
    private CommonStatusEnum status;
}