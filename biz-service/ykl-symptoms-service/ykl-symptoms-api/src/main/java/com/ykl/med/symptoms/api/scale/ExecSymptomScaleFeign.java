package com.ykl.med.symptoms.api.scale;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleQueryVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "ykl-symptoms-service", path = "ykl-symptoms-service/execSymptomScale")
public interface ExecSymptomScaleFeign {

    @PostMapping("/query")
    PageResult<ExecSymptomScaleVO> query(@RequestBody @Validated ExecSymptomScaleQueryVO queryVO);

    @PostMapping("/notifyToDoMessage")
    void notifyToDoMessage(@RequestParam(value = "id") Long id);

    @PostMapping("/getAllNeedNotifyToDoMessage")
    List<ExecSymptomScaleVO> getAllNeedNotifyToDoMessage();

    @PostMapping("/getById")
    ExecSymptomScaleVO getById(@RequestParam(value = "id") Long id);

    @PostMapping("/getDetailById")
    ExecSymptomScaleVO getDetailById(@RequestParam(value = "id") Long id);
}
