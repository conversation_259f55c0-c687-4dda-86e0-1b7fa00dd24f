package com.ykl.med.symptoms.vo.data;

import com.ykl.med.base.utils.CustomizeLevelInfo;
import com.ykl.med.framework.common.enums.CommonStatusEnum;
import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;


@Data
@Schema(description = "症状数据请求")
public class SymptomDataReqVO {

    @Stringify
    @Schema(description = "Id", example = "123456")
    private Long id;

    @Schema(description = "名称", example = "名称示例", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "码", example = "码示例")
    private String code;

    @Schema(description = "症状表现")
    private List<String> symptomPerformanceList;

    @Schema(description = "是否量表症状", example = "true")
    private Boolean scaleFlag;

    @Schema(description = "类型（字典）", example = "type_example")
    private String type;

    @Schema(description = "排序", example = "10")
    private Integer sort;

    @Schema(description = "一级的症状描述", example = "一级症状描述例")
    private String remarkLevelOne;

    @Schema(description = "二级的症状描述", example = "二级症状描述例")
    private String remarkLevelTwo;

    @Schema(description = "三级的症状描述", example = "三级症状描述例")
    private String remarkLevelThree;

    @Schema(description = "状态", example = "ENABLE")
    private CommonStatusEnum status;

    @Schema(description = "建议")
    private String suggestion;

    @Schema(description = "是否自定义等级", example = "true")
    private Boolean customizeLevel;

    @Schema(description = "自定义等级信息")
    private List<CustomizeLevelInfo> customizeLevelInfos;
}