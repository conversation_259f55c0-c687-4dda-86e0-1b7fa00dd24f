package com.ykl.med.symptoms.vo.report;

import com.ykl.med.framework.common.json.Stringify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "智能报告重新总结请求")
public class IntelligentReportSummaryReqVO {
    @Schema(description = "报告内容", example = "xxxx")
    private String report;
    @Stringify
    @Schema(description = "患者id", example = "1234567")
    private Long patientId;
}
