package com.ykl.med.symptoms.vo.report.drug;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "智能服务报告与药品的关联关系")
public class IntelligentReportDrugVO {
    @Schema(description = "主键ID")
    private Long id;
    @Schema(description = "药品ID")
    private Long drugId;
    @Schema(description = "智能服务报告ID")
    private Long intelligentReportId;
    @Schema(description = "药品名称")
    private String name;
    @Schema(description = "用药时间")
    private String dosingTime;
    @Schema(description = "用药方法")
    private String administrationMethod;
    @Schema(description = "注意事项")
    private List<String> notes;
    @Schema(description = "漏服药物措施")
    private List<String> missedDose;
    @Schema(description = "药物-患者相互作用")
    private List<DrugInteractionVO> drugInteractions;
    @Schema(description = "药物-患者相互作用")
    private List<PatientInteractionVO> patientInteractions;
    @Schema(description = "药物-食物相互作用")
    private List<FoodInteractionVO> foodInteractions;
}
