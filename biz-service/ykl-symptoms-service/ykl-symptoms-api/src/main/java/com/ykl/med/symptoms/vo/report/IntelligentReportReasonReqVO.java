package com.ykl.med.symptoms.vo.report;

import com.ykl.med.symptoms.vo.report.drug.IntelligentReportDrugVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "智能报告分析结果请求")
public class IntelligentReportReasonReqVO {
    @Schema(description = "id", example = "1234567")
    private Long id;
    @Schema(description = "报告内容", example = "xxxx")
    private String report;
    @Schema(description = "总结", example = "xxxx")
    private String summary;
    @Schema(description = "报告分析过程", example = "xxxx")
    private String reportReasoning;
    @Schema(description = "总结分析过程", example = "xxxx")
    private String summaryReasoning;


    @Schema(description = "指导书药品相关结构化数据", example = "xxxx")
    private List<IntelligentReportDrugVO> drugs;
    @Schema(description = "附加信息", example = "xxxx")
    private IntelligentReportExtra extra;
}
