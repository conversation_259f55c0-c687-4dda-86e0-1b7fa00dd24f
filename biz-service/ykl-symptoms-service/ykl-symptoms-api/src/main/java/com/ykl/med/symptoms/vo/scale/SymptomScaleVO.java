package com.ykl.med.symptoms.vo.scale;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.masterdata.vo.FormVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "患者症状量表")
public class SymptomScaleVO {
    @Stringify
    @Schema(description = "唯一标识", example = "1234567")
    private Long id;

    @Stringify
    @Schema(description = "患者ID", example = "1234567")
    private Long patientId;

    @Stringify
    @Schema(description = "项目ID", example = "1234567")
    private Long itemId;

    @Schema(description = "表单名称", example = "表单1")
    private String itemName;

    @Schema(description = "项目内容（可存表单数据）", example = "项目内容示例")
    private FormVO itemContent;

    @Stringify
    @Schema(description = "频次ID", example = "1234567")
    private Long frequencyId;

    @TimestampConvert
    @Schema(description = "开始时间", example = "1635680303000")
    private LocalDateTime startTime;

    @Schema(description = "频次名称",example = "")
    private String frequencyName ;

    @Schema(description = "是否已经执行",example = "true")
    private Boolean isExecuted;

    @Stringify
    @Schema(description = "执行量表ID", example = "1234567")
    private Long execSymptomScaleId;
}