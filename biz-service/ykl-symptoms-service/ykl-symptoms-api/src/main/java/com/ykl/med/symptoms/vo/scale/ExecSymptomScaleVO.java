package com.ykl.med.symptoms.vo.scale;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.masterdata.vo.FormVO;
import com.ykl.med.symptoms.enums.ExecSymptomScaleStatus;
import com.ykl.med.symptoms.vo.records.SymptomAddVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "症状量表执行表")
public class ExecSymptomScaleVO {
    @Stringify
    private Long id;

    @Schema(description = "生成时间")
    @TimestampConvert
    private LocalDateTime createTime;

    @Schema(description = "患者ID", example = "123456789")
    @Stringify
    private Long patientId;

    @Schema(description = "症状量表ID", example = "987654321")
    @Stringify
    private Long symptomScaleId;

    @Schema(description = "执行时间", example = "1648771200000")
    @TimestampConvert
    private LocalDateTime execTime;

    @Schema(description = "应执行时间")
    @TimestampConvert
    private LocalDateTime shouldExecTime;

    @Schema(description = "填报的内容（症状量表）", example = "这是填报的症状量表内容")
    private FormVO itemContent;

    @Schema(description = "表单名称", example = "表单1")
    private String symptomScaleItemName;

    @Schema(description = "频次名称", example = "一日三次")
    private String frequencyName;

    @Schema(description = "状态")
    private ExecSymptomScaleStatus status;

    @Schema(description = "量表症状集合")
    private List<SymptomAddVO> symptoms;

    @Schema(description = "额外的自选症状信息")
    private List<SymptomAddVO> extraSymptoms;
}