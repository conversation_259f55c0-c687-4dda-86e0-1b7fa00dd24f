package com.ykl.med.symptoms.vo.records;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Schema(description = "图表查询对象")
public class SymptomChartQueryVO {
    @Schema(description = "患者ID", example = "123")
    @Stringify
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "症状ID", example = "456")
    @Stringify
    private Long symptomId;

    @Schema(description = "开始时间", example = "1577865600000", requiredMode = Schema.RequiredMode.REQUIRED)
    @TimestampConvert
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "1577865600000", requiredMode = Schema.RequiredMode.REQUIRED)
    @TimestampConvert
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
}
