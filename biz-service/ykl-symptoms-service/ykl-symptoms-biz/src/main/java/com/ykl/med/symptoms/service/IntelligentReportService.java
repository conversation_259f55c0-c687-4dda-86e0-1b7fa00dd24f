package com.ykl.med.symptoms.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ykl.med.doctors.api.DigitalDoctorFeign;
import com.ykl.med.doctors.api.DoctorInfoFeign;
import com.ykl.med.doctors.api.PersonnelUserFeign;
import com.ykl.med.doctors.entity.dto.PersonnelUserQueryDTO;
import com.ykl.med.doctors.entity.vo.DoctorInfoVO;
import com.ykl.med.doctors.entity.vo.GenerateVideoEnableReqVO;
import com.ykl.med.doctors.entity.vo.PersonnelDataVO;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.patient.api.PatientDoctorFeign;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.vo.message.BizMessageBaseVO;
import com.ykl.med.push.vo.message.MessageSendPatientReqVO;
import com.ykl.med.symptoms.constans.SymptomsErrorCodeConstants;
import com.ykl.med.symptoms.db.entity.IntelligentReportDO;
import com.ykl.med.symptoms.db.mapper.IntelligentReportMapper;
import com.ykl.med.symptoms.enums.IntelligentReportStatus;
import com.ykl.med.symptoms.enums.IntelligentReportType;
import com.ykl.med.symptoms.vo.report.*;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.vo.req.IdListReqVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IntelligentReportService {
    @Resource
    private IntelligentReportMapper intelligentReportMapper;
    @Resource
    private DoctorInfoFeign doctorInfoFeign;
    @Resource
    private PersonnelUserFeign personnelUserFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private PatientDoctorFeign patientDoctorFeign;
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private DigitalDoctorFeign digitalDoctorFeign;
    @Resource
    private IntelligentReportDrugService intelligentReportDrugService;

    @Transactional(rollbackFor = Exception.class)
    public void add(IntelligentReportAddVO addVO) {
        log.info("add:{}", JSON.toJSONString(addVO));
        addVO.setStartDate(IntelligentReportDO.buildStartDate(intelligentReportMapper.getLastByPatientIdAndType(addVO.getPatientId(), addVO.getType()), addVO));
        addVO.setEndDate(addVO.getEndDate() == null ? LocalDate.now() : addVO.getEndDate());
        IntelligentReportDO intelligentReportDO = intelligentReportMapper.getOne(addVO.getPatientId(), addVO.getType(), addVO.getStartDate(), addVO.getEndDate());
        if (intelligentReportDO != null) {
            //如果相同时间段有数据，则更新状态为生成中，不做其他操作
            intelligentReportDO.setStatus(IntelligentReportStatus.REPORT_GENERATING);
            intelligentReportMapper.updateById(intelligentReportDO);
            return;
        }
        intelligentReportMapper.insert(IntelligentReportDO.buildFromAddVO(addVO));
    }

    @Transactional(rollbackFor = Exception.class)
    public IntelligentReportDO reportGenerated(IntelligentReportReasonReqVO reqVO) {
        log.info("reportGenerated:{}", JSON.toJSONString(reqVO));
        IntelligentReportDO intelligentReportDO = intelligentReportMapper.selectById(reqVO.getId());
        AssertUtils.notNull(intelligentReportDO, SymptomsErrorCodeConstants.REPORT_NOT_EXIST);
        intelligentReportDO.setReport(reqVO.getReport());
        intelligentReportDO.setSummary(reqVO.getSummary());
        intelligentReportDO.setReportReasoning(reqVO.getReportReasoning());
        intelligentReportDO.setSummaryReasoning(reqVO.getSummaryReasoning());
        intelligentReportDO.setStatus(IntelligentReportStatus.REPORT_GENERATED);
        intelligentReportDO.setExtra(reqVO.getExtra());
        intelligentReportDrugService.batchAdd(reqVO.getDrugs(), reqVO.getId());
        intelligentReportMapper.updateById(intelligentReportDO);
        return intelligentReportDO;
    }

    @Transactional(rollbackFor = Exception.class)
    public IntelligentReportDO changeStatus(Long id, IntelligentReportStatus status) {
        log.info("changeStatus:id:{},status:{}", id, status);
        IntelligentReportDO intelligentReportDO = intelligentReportMapper.selectById(id);
        AssertUtils.notNull(intelligentReportDO, SymptomsErrorCodeConstants.REPORT_NOT_EXIST);
        intelligentReportDO.setStatus(status);
        intelligentReportMapper.updateById(intelligentReportDO);
        return intelligentReportDO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDigitalDoctor(Long id, String digitalDoctorVideoUrl) {
        log.info("updateDigitalDoctor:id:{},digitalDoctorVideoUrl:{}", id, digitalDoctorVideoUrl);
        IntelligentReportDO intelligentReportDO = intelligentReportMapper.selectById(id);
        AssertUtils.notNull(intelligentReportDO, SymptomsErrorCodeConstants.REPORT_NOT_EXIST);
        intelligentReportDO.setDigitalDoctorVideoUrl(digitalDoctorVideoUrl);
        intelligentReportMapper.updateById(intelligentReportDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public IntelligentReportDO update(IntelligentReportSubmitReqVO reqVO) {
        log.info("update:{}", JSON.toJSONString(reqVO));
        IntelligentReportDO intelligentReportDO = intelligentReportMapper.selectById(reqVO.getId());
        AssertUtils.notNull(intelligentReportDO, SymptomsErrorCodeConstants.REPORT_NOT_EXIST);
        intelligentReportDO.setName(reqVO.getName());
        intelligentReportDO.setReport(reqVO.getReport());
        intelligentReportDO.setSummary(reqVO.getSummary());
        intelligentReportDO.setExtra(reqVO.getExtra());
        intelligentReportDO.setSecondName(reqVO.getSecondName());
        intelligentReportDrugService.batchUpdate(reqVO);
        if (reqVO.getSubmit()) {
            AssertUtils.isTrue(intelligentReportDO.getStatus() != IntelligentReportStatus.REPORT_GENERATING, SymptomsErrorCodeConstants.REPORT_IS_BEING_GENERATED);
            AssertUtils.isTrue(!(intelligentReportDO.getStatus() == IntelligentReportStatus.DIGITAL_DOCTOR_GENERATING && reqVO.getDigitalDoctorStatus()), SymptomsErrorCodeConstants.DIGITAL_DOCTOR_IS_BEING_GENERATED);
            intelligentReportDO.setSubmitTime(LocalDateTime.now());
            intelligentReportDO.setSubmitUserId(reqVO.getCurrentUserId());
            intelligentReportDO.setStatus(IntelligentReportStatus.COMPLETED);
            intelligentReportDO.setDigitalDoctorStatus(reqVO.getDigitalDoctorStatus());
            intelligentReportDO.setDigitalDoctorVideoUrl(reqVO.getDigitalDoctorVideoUrl());
            PersonnelUserQueryDTO queryDTO = new PersonnelUserQueryDTO();
            queryDTO.setUserId(reqVO.getCurrentUserId().toString());
            PersonnelDataVO personnelDataVO = personnelUserFeign.querySingle(queryDTO);
            DoctorInfoVO doctorInfoVO = doctorInfoFeign.querySingle(Long.valueOf(personnelDataVO.getId()));
            intelligentReportDO.setSubmitDoctorSign(doctorInfoVO.getPhotoSign());
            if (reqVO.getDigitalVideoId() != null) {
                digitalDoctorFeign.generateVideoEnable(new GenerateVideoEnableReqVO().setId(reqVO.getDigitalVideoId()).setEnable(true));
            }
        }
        intelligentReportMapper.updateById(intelligentReportDO);
        return intelligentReportDO;
    }

    public PageResult<IntelligentReportListVO> getPage(IntelligentReportQueryVO reqVO) {
        MPJLambdaWrapper<IntelligentReportDO> queryWrapper = intelligentReportMapper.getQueryWrapper(reqVO);
        queryWrapper.orderByDesc(IntelligentReportDO::getCreateTime);
        Page<IntelligentReportDO> page = intelligentReportMapper.selectPage(new Page<>(reqVO.getPageNo(), reqVO.getPageSize()), queryWrapper);
        if (page == null || page.getRecords() == null) {
            return PageResult.empty();
        }
        List<Long> userId = page.getRecords().stream().map(IntelligentReportDO::getSubmitUserId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userId)) {
            return new PageResult<>(CopyPropertiesUtil.normalCopyProperties(page.getRecords(), IntelligentReportListVO.class), page.getTotal());
        }
        List<UserSimpleVO> userSimpleVOS = userFeign.listByUserIds(new IdListReqVO().setIdList(userId));
        Map<Long, String> userMap = userSimpleVOS.stream().collect(Collectors.toMap(UserSimpleVO::getId, UserSimpleVO::getName));
        List<IntelligentReportListVO> list = page.getRecords().stream().map(intelligentReportDO -> {
            IntelligentReportListVO intelligentReportListVO = CopyPropertiesUtil.normalCopyProperties(intelligentReportDO, IntelligentReportListVO.class);
            intelligentReportListVO.setSubmitUserName(userMap.get(intelligentReportDO.getSubmitUserId()));
            return intelligentReportListVO;
        }).collect(Collectors.toList());
        return new PageResult<>(list, page.getTotal());
    }

    public IntelligentReportVO getLastByPatientId(Long patientId, IntelligentReportType type) {
        MPJLambdaWrapper<IntelligentReportDO> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.eq(IntelligentReportDO::getPatientId, patientId);
        queryWrapper.orderByDesc(IntelligentReportDO::getCreateTime);
        queryWrapper.eq(IntelligentReportDO::getStatus, IntelligentReportStatus.COMPLETED);
        queryWrapper.eq(IntelligentReportDO::getType, type);
        queryWrapper.last("limit 1");
        IntelligentReportDO intelligentReportDO = intelligentReportMapper.selectOne(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(intelligentReportDO, IntelligentReportVO.class);
    }

    public IntelligentReportVO getById(Long id) {
        IntelligentReportDO intelligentReportDO = intelligentReportMapper.selectById(id);
        return CopyPropertiesUtil.normalCopyProperties(intelligentReportDO, IntelligentReportVO.class);
    }

    public IntelligentReportDetailVO getDetailById(Long id) {
        IntelligentReportVO vo = this.getById(id);
        return intelligentReportDrugService.buildDrugData(vo);
    }

    public List<IntelligentReportVO> queryList(IntelligentReportQueryVO queryVO) {
        MPJLambdaWrapper<IntelligentReportDO> queryWrapper = intelligentReportMapper.getQueryWrapper(queryVO);
        queryWrapper.orderByAsc(IntelligentReportDO::getCreateTime);
        List<IntelligentReportDO> intelligentReportDOS = intelligentReportMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(intelligentReportDOS, IntelligentReportVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(IntelligentReportDO intelligentReportDO) {
        Long doctorId = patientDoctorFeign.getDoctorIdByPatientId(intelligentReportDO.getPatientId());
        if (doctorId == null) {
            log.error("症状报告发送消息无主管医生，消息：{}", JSON.toJSONString(intelligentReportDO));
            return;
        }
        MessageSendPatientReqVO messageSendReqVO = new MessageSendPatientReqVO();
        messageSendReqVO.setCurrentUserId(doctorId);
        messageSendReqVO.setPatientId(intelligentReportDO.getPatientId());
        messageSendReqVO.setRequestId(intelligentReportDO.getId().toString());
        messageSendReqVO.setType(MessageType.PATIENT_REPORT);
        BizMessageBaseVO bizMessageBaseVO = new BizMessageBaseVO();
        bizMessageBaseVO.setBizId(intelligentReportDO.getId().toString());
        bizMessageBaseVO.setBizName(intelligentReportDO.getName());
        bizMessageBaseVO.setBizType(intelligentReportDO.getType().name());
        messageSendReqVO.setExtra(bizMessageBaseVO);
        messageFeign.sendMessagePatient(messageSendReqVO);
        intelligentReportDO.setSendMessage(true);
        intelligentReportMapper.updateById(intelligentReportDO);
    }
}
