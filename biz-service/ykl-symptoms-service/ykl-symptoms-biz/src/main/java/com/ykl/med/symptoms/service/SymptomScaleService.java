package com.ykl.med.symptoms.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.api.FrequencyDataFeign;
import com.ykl.med.masterdata.api.FrequencyFeign;
import com.ykl.med.masterdata.entiry.dto.FrequencyParamDTO;
import com.ykl.med.masterdata.entiry.vo.FrequencyUtilVO;
import com.ykl.med.masterdata.vo.FormVO;
import com.ykl.med.masterdata.vo.FrequencyVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.api.ToDoMessageFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.enums.ToDoMessageType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.push.vo.todo.ToDoMessageListVO;
import com.ykl.med.push.vo.todo.ToDoMessageQueryVO;
import com.ykl.med.push.vo.todo.ToDoMessageReqVO;
import com.ykl.med.symptoms.constans.SymptomsErrorCodeConstants;
import com.ykl.med.symptoms.db.entity.SymptomScaleDO;
import com.ykl.med.symptoms.db.mapper.SymptomScaleMapper;
import com.ykl.med.symptoms.vo.data.SymptomDataVO;
import com.ykl.med.symptoms.vo.records.SymptomAddVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleAddVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleVO;
import com.ykl.med.symptoms.vo.scale.SymptomScaleAddVO;
import com.ykl.med.symptoms.vo.scale.SymptomScaleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SymptomScaleService extends ServiceImpl<SymptomScaleMapper, SymptomScaleDO> {
    @Resource
    private SymptomScaleMapper symptomScaleMapper;
    @Resource
    private ExecSymptomScaleService execSymptomScaleService;
    @Resource
    private FrequencyFeign frequencyFeign;
    @Resource
    private SymptomService symptomService;
    @Resource
    private ToDoMessageFeign toDoMessageFeign;
    @Resource
    private EventTaskFeign eventTaskFeign;
    @Resource
    private FrequencyDataFeign frequencyDataFeign;
    @Resource
    private SymptomDataService symptomDataService;
    @Resource
    private IntelligentReportService intelligentReportService;

    @Transactional(rollbackFor = Exception.class)
    public SymptomScaleVO add(SymptomScaleAddVO addVO) {
        log.info("add symptom scale: {}", JSON.toJSONString(addVO));
        List<SymptomScaleDO> symptomScaleDOS = symptomScaleMapper.getByPatientId(addVO.getPatientId());
        if (CollectionUtils.isNotEmpty(symptomScaleDOS)) {
            // 一个患者只能有一个量表,其他的都禁用
            for (SymptomScaleDO symptomScaleDO1 : symptomScaleDOS) {
                symptomScaleDO1.setStopFlag(symptomScaleDO1.getId());
            }
        } else {
            symptomScaleDOS = new ArrayList<>();
        }
        FrequencyVO frequencyVO = frequencyFeign.getById(addVO.getFrequencyId());
        AssertUtils.notNull(frequencyVO, SymptomsErrorCodeConstants.FREQUENCY_NOT_EXIST);
        SymptomScaleDO symptomScaleDO = new SymptomScaleDO();
        BeanUtils.copyProperties(addVO, symptomScaleDO);
        symptomScaleDO.setStopFlag(0L);
        symptomScaleDO.setCreateUserId(addVO.getCurrentUserId());
        symptomScaleDO.setFrequencyName(frequencyVO.getName());
        symptomScaleDO.setNotifyTodoTime(LocalDateTime.now().plusDays(-1));
        symptomScaleDO.setStartTime(addVO.getStartExecTime());
        symptomScaleDOS.add(symptomScaleDO);
        saveOrUpdateBatch(symptomScaleDOS);
        //把之前的待办全部删掉，等待新的定时任务去生成新待办
        this.deleteTodo(addVO.getPatientId());
        //添加事件
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setUserId(addVO.getCurrentUserId())
                .setRequestId(UUID.randomUUID().toString())
                .setBizType(EventTaskType.SYMPTOM_FORM_CHANGE)
                .setBizId(symptomScaleDO.getId().toString())
                .setPatientId(addVO.getPatientId());
        //添加量表数据，并加入待办
        SymptomScaleVO notify = this.notifyToDoMessage(symptomScaleDO.getId());
        if (notify != null) {
            //如果今天又需要填写的，就发消息提醒
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("autoDay", true);
            eventTaskAddVO.setExtJson(jsonObject);
        }
        eventTaskFeign.addEventTask(eventTaskAddVO);
        return CopyPropertiesUtil.normalCopyProperties(symptomScaleDO, SymptomScaleVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long execSymptomScale(ExecSymptomScaleAddVO addVO) {
        log.info("exec symptom scale: {}", JSON.toJSONString(addVO));
        Long id = execSymptomScaleService.exec(buildExecSymptomAddVO(addVO));
        if (id == null) {
            return null;
        }
        // 添加症状数据
        if (CollectionUtils.isNotEmpty(addVO.getSymptomAddVOS())) {
            //app 的表单特殊处理，包含了症状的一些其他属性
            for (SymptomAddVO symptomAddVO : addVO.getSymptomAddVOS()) {
                symptomService.add(symptomAddVO);
            }
            if (CollectionUtils.isNotEmpty(addVO.getExtraSymptoms())) {
                for (SymptomAddVO symptomAddVO : addVO.getExtraSymptoms()) {
                    symptomService.add(symptomAddVO);
                }
            }
        } else {
            //普通表单的数据
            symptomService.formAdd(addVO.getItemContent(), addVO.getPatientId(), addVO.getCurrentUserId());
        }

//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("isPatient", addVO.getIsPatient());
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setUserId(addVO.getCurrentUserId())
                .setEventTime(LocalDateTime.now())
                .setRequestId(id.toString())
                .setBizType(EventTaskType.PATIENT_FINISH_SYMPTOM_FORM)
                .setBizId(id.toString())
                .setPatientId(addVO.getPatientId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
        return id;
    }

    public List<SymptomScaleVO> getALlNeedNotifyToDoMessage() {
        //查询今日以前的未通知的量表
        LambdaQueryWrapper<SymptomScaleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomScaleDO::getStopFlag, 0L);
        queryWrapper.le(SymptomScaleDO::getStartTime, LocalDateTimeUtil.endOfDay(LocalDateTime.now(), true));
        queryWrapper.le(SymptomScaleDO::getNotifyTodoTime, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        List<SymptomScaleDO> symptomScaleDOS = symptomScaleMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(symptomScaleDOS, SymptomScaleVO.class);
    }

    public SymptomScaleVO notifyToDoMessage(Long id) {
        SymptomScaleDO symptomScaleDO = getById(id);
        LocalDateTime todayTime = getTodayTime(symptomScaleDO.getFrequencyId(), symptomScaleDO.getStartTime());
        //把之前的清理掉
        this.deleteTodo(symptomScaleDO.getPatientId());
        //把今天未执行的量表记录删掉
        execSymptomScaleService.deleteToday(symptomScaleDO.getId());
        if (todayTime == null) {
            symptomScaleDO.setNotifyTodoTime(LocalDateTime.now());
            updateById(symptomScaleDO);
            return null;
        }
        //添加今天的量表执行记录
        execSymptomScaleService.add(symptomScaleDO.getPatientId(), symptomScaleDO.getId(), todayTime, symptomScaleDO.getItemContent());
        //添加待办
        ToDoMessageReqVO toDoMessageReqVO = new ToDoMessageReqVO();
        toDoMessageReqVO.setPatientId(symptomScaleDO.getPatientId());
        toDoMessageReqVO.setType(ToDoMessageType.SYMPTOM_SCALE);
        toDoMessageReqVO.setOutBizId(DigestUtils.sha1Hex(symptomScaleDO.getId().toString() + todayTime));
        toDoMessageReqVO.setContent(symptomScaleDO.getItemName() + "症状量表填写");
        toDoMessageReqVO.setExpireTime(LocalDateTime.now());
        toDoMessageFeign.addToDoMessage(toDoMessageReqVO);
        //更新通知时间
        symptomScaleDO.setNotifyTodoTime(LocalDateTime.now());
        updateById(symptomScaleDO);
        return CopyPropertiesUtil.normalCopyProperties(symptomScaleDO, SymptomScaleVO.class);
    }

    public SymptomScaleVO getSymptomScaleById(Long id) {
        SymptomScaleDO symptomScaleDO = getById(id);
        return CopyPropertiesUtil.normalCopyProperties(symptomScaleDO, SymptomScaleVO.class);
    }

    public List<SymptomScaleVO> getSymptomScaleByIds(List<Long> ids) {
        List<SymptomScaleDO> symptomScaleDOS = listByIds(ids);
        return CopyPropertiesUtil.normalCopyProperties(symptomScaleDOS, SymptomScaleVO.class);
    }

    public SymptomScaleVO getCurrentSymptomScale(Long patientId) {
        LambdaQueryWrapper<SymptomScaleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomScaleDO::getPatientId, patientId);
        queryWrapper.eq(SymptomScaleDO::getStopFlag, 0L);
        SymptomScaleDO symptomScaleDO = symptomScaleMapper.selectOne(queryWrapper);
        if (symptomScaleDO == null) {
            return null;
        }
        SymptomScaleVO symptomScaleVO = CopyPropertiesUtil.normalCopyProperties(symptomScaleDO, SymptomScaleVO.class);
        ExecSymptomScaleVO execSymptomScaleVO = execSymptomScaleService.getLastByPatient(symptomScaleVO.getPatientId());
        if (execSymptomScaleVO != null && execSymptomScaleVO.getExecTime() != null) {
            symptomScaleVO.setIsExecuted(true);
            symptomScaleVO.setItemContent(execSymptomScaleVO.getItemContent());
            symptomScaleVO.setExecSymptomScaleId(execSymptomScaleVO.getId());
        } else {
            symptomScaleVO.setIsExecuted(false);
            if (execSymptomScaleVO != null){
                symptomScaleVO.setItemContent(execSymptomScaleVO.getItemContent());
            }else {
                symptomScaleVO.setItemContent(JSON.parseObject(symptomScaleDO.getItemContent(), FormVO.class));
            }
        }
        FrequencyVO frequencyVO = frequencyFeign.getById(symptomScaleVO.getFrequencyId());
        symptomScaleVO.setFrequencyName(frequencyVO.getName());
        return symptomScaleVO;
    }


    private LocalDateTime getTodayTime(Long frequencyId, LocalDateTime startTime) {
        FrequencyParamDTO frequencyParamDTO = new FrequencyParamDTO();
        if (startTime == null) {
            frequencyParamDTO.setStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            frequencyParamDTO.setFrequencyFixDate(formatter.format(startTime));
            frequencyParamDTO.setFrequencyStartTime(Lists.newArrayList(formatter.format(startTime)));
            //取当前时间和开始时间的最大值，然后计算当前天的开始时间
            if (startTime.isAfter(LocalDateTime.now())) {
                frequencyParamDTO.setStartTime(LocalDateTimeUtil.beginOfDay(startTime));
            } else {
                frequencyParamDTO.setStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
            }
        }
        frequencyParamDTO.setFrequencyId(frequencyId.toString());
        frequencyParamDTO.setEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now(), true));
        PageResult<FrequencyUtilVO> frequencyBizPageResult = frequencyDataFeign.genFrequencySetTime(Lists.newArrayList(frequencyParamDTO));
        FrequencyUtilVO frequencyUtilVO = frequencyBizPageResult.getList().get(0);
//        Map<Long, Map<LocalDateTime, List<LocalDateTime>>> map = frequencyFeign.batchGetTodayTimes(Lists.newArrayList(reqVO));
//        Map<LocalDateTime, List<LocalDateTime>> localDateTimeListMap = map.get(frequencyId);
//        List<LocalDateTime> execTimeList = localDateTimeListMap.get(reqVO.getLocalDateTime());
        List<LocalDateTime> execTimeList = frequencyUtilVO.getGenExecTimeList();
        if (CollectionUtils.isEmpty(execTimeList)) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        for (LocalDateTime localDateTime : execTimeList) {
            //判断是不是在今天
            if ((localDateTime.isAfter(LocalDateTimeUtil.beginOfDay(now)) || localDateTime.isEqual(LocalDateTimeUtil.beginOfDay(now)))
                    && (localDateTime.isBefore(LocalDateTimeUtil.endOfDay(now, true)) || localDateTime.isEqual(LocalDateTimeUtil.endOfDay(now, true)))) {
                return localDateTime;
            }
        }
        return null;
    }

    private void deleteTodo(Long patientId) {
        //把之前的待办全部删掉，等待新的定时任务去生成新待办
        List<ToDoMessageListVO> toDoMessageListVOS = toDoMessageFeign.queryList(new ToDoMessageQueryVO()
                .setPatientId(patientId)
                .setExecuted(false)
                .setType(ToDoMessageType.SYMPTOM_SCALE));
        if (CollectionUtils.isNotEmpty(toDoMessageListVOS)) {
            List<Long> ids = toDoMessageListVOS.stream().map(ToDoMessageListVO::getId).collect(Collectors.toList());
            toDoMessageFeign.deleteById(ids);
        }
    }


    private ExecSymptomScaleAddVO buildExecSymptomAddVO(ExecSymptomScaleAddVO addVO) {
        List<Long> symptomDataIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addVO.getExtraSymptoms())) {
            for (SymptomAddVO symptomAddVO : addVO.getExtraSymptoms()) {
                symptomDataIds.add(symptomAddVO.getValue());
            }
        }
        if (CollectionUtils.isNotEmpty(addVO.getSymptomAddVOS())) {
            for (SymptomAddVO symptomAddVO : addVO.getSymptomAddVOS()) {
                symptomDataIds.add(symptomAddVO.getValue());
            }
        }
        if (CollectionUtils.isEmpty(symptomDataIds)) {
            return addVO;
        }
        List<SymptomDataVO> symptomDataVOList = symptomDataService.getByIds(symptomDataIds);
        Map<Long, SymptomDataVO> symptomDataVOMap = symptomDataVOList.stream().collect(Collectors.toMap(SymptomDataVO::getId, a -> a));
        if (CollectionUtils.isNotEmpty(addVO.getSymptomAddVOS())) {
            for (SymptomAddVO symptomAddVO : addVO.getSymptomAddVOS()) {
                SymptomDataVO symptomDataVO = symptomDataVOMap.get(symptomAddVO.getValue());
                if (symptomDataVO != null) {
                    symptomAddVO.setSymptomName(symptomDataVO.getName());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(addVO.getExtraSymptoms())) {
            for (SymptomAddVO symptomAddVO : addVO.getExtraSymptoms()) {
                SymptomDataVO symptomDataVO = symptomDataVOMap.get(symptomAddVO.getValue());
                if (symptomDataVO != null) {
                    symptomAddVO.setSymptomName(symptomDataVO.getName());
                }
            }
        }
        return addVO;
    }

}
