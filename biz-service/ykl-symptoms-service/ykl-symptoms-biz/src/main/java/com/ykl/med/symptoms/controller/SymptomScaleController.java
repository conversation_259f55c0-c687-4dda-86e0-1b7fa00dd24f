package com.ykl.med.symptoms.controller;

import com.ykl.med.symptoms.api.scale.SymptomScaleFeign;
import com.ykl.med.symptoms.service.SymptomScaleService;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleAddVO;
import com.ykl.med.symptoms.vo.scale.SymptomScaleAddVO;
import com.ykl.med.symptoms.vo.scale.SymptomScaleVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("symptomScale")
@Validated
public class SymptomScaleController implements SymptomScaleFeign {
    @Resource
    private SymptomScaleService symptomScaleService;

    @Override
    @PostMapping("/add")
    public SymptomScaleVO add(@Valid @RequestBody SymptomScaleAddVO addVO) {
       return symptomScaleService.add(addVO);
    }

    @Override
    @PostMapping("/execSymptomScale")
    public Long execSymptomScale(@Valid @RequestBody ExecSymptomScaleAddVO addVO) {
        return symptomScaleService.execSymptomScale(addVO);
    }

    @Override
    @GetMapping("/getSymptomScaleById")
    public SymptomScaleVO getSymptomScaleById(@RequestParam("id") Long id) {
        return symptomScaleService.getSymptomScaleById(id);
    }

    @Override
    @PostMapping("/getSymptomScaleByIds")
    public List<SymptomScaleVO> getSymptomScaleByIds(@RequestBody List<Long> ids) {
        return symptomScaleService.getSymptomScaleByIds(ids);
    }

    @Override
    @GetMapping("/getCurrentSymptomScale")
    public SymptomScaleVO getCurrentSymptomScale(@RequestParam("patientId") Long patientId) {
        return symptomScaleService.getCurrentSymptomScale(patientId);
    }

    @Override
    @PostMapping("/getALlNeedNotifyToDoMessage")
    public List<SymptomScaleVO> getALlNeedNotifyToDoMessage() {
        return symptomScaleService.getALlNeedNotifyToDoMessage();
    }


    @Override
    @GetMapping("/notifyToDoMessage")
    public SymptomScaleVO notifyToDoMessage(@RequestParam("id") Long id) {
        return symptomScaleService.notifyToDoMessage(id);
    }


}