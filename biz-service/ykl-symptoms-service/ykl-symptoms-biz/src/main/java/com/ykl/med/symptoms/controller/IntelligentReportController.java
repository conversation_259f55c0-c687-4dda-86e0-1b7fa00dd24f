package com.ykl.med.symptoms.controller;

import com.alibaba.fastjson.JSONObject;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.symptoms.api.report.IntelligentReportFeign;
import com.ykl.med.symptoms.db.entity.IntelligentReportDO;
import com.ykl.med.symptoms.enums.IntelligentReportStatus;
import com.ykl.med.symptoms.enums.IntelligentReportType;
import com.ykl.med.symptoms.service.IntelligentReportDrugService;
import com.ykl.med.symptoms.service.IntelligentReportService;
import com.ykl.med.symptoms.vo.report.*;
import com.ykl.med.symptoms.vo.report.drug.IntelligentReportDrugVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("report")
@Validated
public class IntelligentReportController implements IntelligentReportFeign {
    @Resource
    private IntelligentReportService intelligentReportService;
    @Resource
    private EventTaskFeign eventTaskFeign;
    @Resource
    private IntelligentReportDrugService intelligentReportDrugService;

    @Override
    @Operation(summary = "添加报告")
    @PostMapping("/add")
    public void add(@RequestBody @Valid IntelligentReportAddVO addVO) {
        intelligentReportService.add(addVO);
    }

    @Override
    @Operation(summary = "报告生成完成")
    @PostMapping("/reportGenerated")
    public void reportGenerated(@RequestBody @Valid IntelligentReportReasonReqVO reqVO) {
        IntelligentReportDO intelligentReportDO = intelligentReportService.reportGenerated(reqVO);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("report", intelligentReportDO.getType().getDesc());
        EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                .setEventTime(LocalDateTime.now())
                .setRequestId(UUID.randomUUID().toString())
                .setBizType(EventTaskType.REPORT_TO_BE_REVIEWED)
                .setBizId(reqVO.getId().toString())
                .setExtJson(jsonObject)
                .setPatientId(intelligentReportDO.getPatientId());
        eventTaskFeign.addEventTask(eventTaskAddVO);
    }

    @Operation(summary = "修改状态")
    @PostMapping("/changeStatus")
    @Override
    public void changeStatus(@RequestParam(value = "id") Long id, @RequestParam(value = "status") IntelligentReportStatus status) {
        IntelligentReportDO intelligentReportDO = intelligentReportService.changeStatus(id, status);
        if (status == IntelligentReportStatus.REPORT_GENERATED || status == IntelligentReportStatus.DIGITAL_DOCTOR_GENERATED) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("report", intelligentReportDO.getType().getDesc());
            EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                    .setEventTime(LocalDateTime.now())
                    .setRequestId(UUID.randomUUID().toString())
                    .setBizType(EventTaskType.REPORT_TO_BE_REVIEWED)
                    .setBizId(id.toString())
                    .setExtJson(jsonObject)
                    .setPatientId(intelligentReportDO.getPatientId());
            eventTaskFeign.addEventTask(eventTaskAddVO);
        }
    }

    @Operation(summary = "更新数字医生视频地址")
    @PostMapping("/updateDigitalDoctor")
    @Override
    public void updateDigitalDoctor(@RequestParam(value = "id") Long id, @RequestParam(value = "digitalDoctorVideoUrl") String digitalDoctorVideoUrl) {
        intelligentReportService.updateDigitalDoctor(id, digitalDoctorVideoUrl);
    }


    @Override
    @Operation(summary = "更改报告、提交报告")
    @PostMapping("/update")
    public void update(@RequestBody @Valid IntelligentReportSubmitReqVO reqVO) {
        IntelligentReportDO intelligentReportDO = intelligentReportService.update(reqVO);
        if (intelligentReportDO.getStatus() == IntelligentReportStatus.COMPLETED && !intelligentReportDO.getSendMessage()) {
            intelligentReportService.sendMessage(intelligentReportDO);
        }
    }


    @Override
    @Operation(summary = "查询分页")
    @PostMapping("/getPage")
    public PageResult<IntelligentReportListVO> getPage(@RequestBody @Valid IntelligentReportQueryVO reqVO) {
        return intelligentReportService.getPage(reqVO);
    }

    @Operation(summary = "查询患者最近一次报告")
    @PostMapping("/getLastByPatientId")
    @Override
    public IntelligentReportVO getLastByPatientId(@RequestParam(value = "patientId") Long patientId,
                                                  @RequestParam(value = "type") IntelligentReportType type) {
        return intelligentReportService.getLastByPatientId(patientId, type);
    }

    @Override
    @Operation(summary = "查询报告详情")
    @PostMapping("/getById")
    public IntelligentReportVO getById(@RequestParam(value = "id") Long id) {
        return intelligentReportService.getById(id);
    }

    @Operation(summary = "查询报告详细详情")
    @PostMapping("/getDetailById")
    @Override
    public IntelligentReportDetailVO getDetailById(@RequestParam(value = "id") Long id) {
        return intelligentReportService.getDetailById(id);
    }

    @Override
    @Operation(summary = "查询报告列表")
    @PostMapping("/queryList")
    public List<IntelligentReportVO> queryList(@RequestBody @Valid IntelligentReportQueryVO queryVO) {
        return intelligentReportService.queryList(queryVO);
    }

    @Operation(summary = "查询用药建议等")
    @PostMapping("/queryDrugByReportId")
    @Override
    public List<IntelligentReportDrugVO> queryDrugByReportId(@RequestParam(value = "reportId") Long reportId) {
        return intelligentReportDrugService.queryByReportId(reportId);
    }
}
