package com.ykl.med.symptoms.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ykl.med.base.utils.CustomizeLevelInfo;
import com.ykl.med.base.utils.LevelUtils;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.masterdata.api.CommonConfigFeign;
import com.ykl.med.masterdata.enums.RelatedDataCategory;
import com.ykl.med.masterdata.vo.BaseFormItemVO;
import com.ykl.med.masterdata.vo.FormVO;
import com.ykl.med.masterdata.vo.SubFormVO;
import com.ykl.med.push.api.EventTaskFeign;
import com.ykl.med.push.enums.EventTaskOperationType;
import com.ykl.med.push.enums.EventTaskType;
import com.ykl.med.push.vo.event.EventTaskAddVO;
import com.ykl.med.symptoms.constans.SymptomsErrorCodeConstants;
import com.ykl.med.symptoms.db.entity.SymptomDO;
import com.ykl.med.symptoms.db.mapper.SymptomMapper;
import com.ykl.med.symptoms.vo.data.SymptomDataVO;
import com.ykl.med.symptoms.vo.records.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SymptomService extends ServiceImpl<SymptomMapper, SymptomDO> {
    @Resource
    private SymptomDataService symptomDataService;
    @Resource
    private SymptomMapper symptomMapper;
    @Resource
    private SymptomRecordsService symptomRecordsService;
    @Resource
    private CommonConfigFeign commonConfigFeign;
    @Resource
    private EventTaskFeign eventTaskFeign;

    @Transactional(rollbackFor = Exception.class)
    public void formAdd(FormVO formVO, Long patientId, Long currentUserId) {
        if (CollectionUtils.isEmpty(formVO.getSubForms())) {
            return;
        }
        List<SubFormVO> subForms = formVO.getSubForms();
        for (SubFormVO subFormVO : subForms) {
            if (CollectionUtils.isEmpty(subFormVO.getItems())) {
                continue;
            }
            // 获取表单项
            for (Object itemObj : subFormVO.getItems()) {
                BaseFormItemVO baseFormItemVO = JSONObject.parseObject(JSONObject.toJSONString(itemObj), BaseFormItemVO.class);
                if (baseFormItemVO.getRelatedDataCategory() != RelatedDataCategory.SYMPTOM) {
                    continue;
                }
                if (StringUtils.isEmpty(baseFormItemVO.getValue())) {
                    continue;
                }
                SymptomAddVO addVO = new SymptomAddVO();
                addVO.setPatientId(patientId);
                addVO.setCurrentUserId(currentUserId);
                SymptomDataVO symptomDataVO = symptomDataService.getById(baseFormItemVO.getBusinessId());
                if (symptomDataVO == null) {
                    continue;
                }
                addVO.setValue(symptomDataVO.getId());
                addVO.setLevel(Long.valueOf(baseFormItemVO.getValue()));
                addVO.setStartTime(LocalDateTime.now());
                addVO.setSymptomName(symptomDataVO.getName());
                this.add(addVO);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void formAdd(List<SymptomAddVO> symptomAddVOS, Long patientId, Long currentUserId) {
        if (CollectionUtils.isEmpty(symptomAddVOS)) {
            return;
        }

        LocalDateTime now = DateTimeUtils.getNow();
        for (SymptomAddVO addVO : symptomAddVOS) {
            SymptomDataVO symptomDataVO = symptomDataService.getById(addVO.getValue());
            if (symptomDataVO == null) {
                continue;
            }

            addVO.setSymptomName(symptomDataVO.getName());
            addVO.setPatientId(patientId);
            addVO.setCurrentUserId(currentUserId);
            addVO.setStartTime(now);
            this.add(addVO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(SymptomAddVO addVO) {
        log.info("force add symptom: {}", JSON.toJSONString(addVO));
        SymptomDataVO symptomDataVO = symptomDataService.getById(addVO.getValue());
        AssertUtils.notNull(symptomDataVO, SymptomsErrorCodeConstants.SYMPTOM_NOT_EXIST);
        //疼痛
        if (addVO.getValue() == 112829L) {
            Map<String, Long> painSymptomPositionMap = this.getPainSymptomPositionMap();
            Map<Long, Set<String>> symptomPositionMap = new HashMap<>();
            if (CollectionUtils.isEmpty(addVO.getPosition())) {
                this.realAdd(addVO, symptomDataVO);
                return;
            }
            for (String position : addVO.getPosition()) {
                if (!painSymptomPositionMap.containsKey(position)) {
                    if (!symptomPositionMap.containsKey(addVO.getValue())) {
                        Set<String> positionSet = new HashSet<>();
                        positionSet.add(position);
                        symptomPositionMap.put(addVO.getValue(), positionSet);
                    } else {
                        symptomPositionMap.get(addVO.getValue()).add(position);
                    }
                    continue;
                }
                Long symptomDataId = painSymptomPositionMap.get(position);
                if (!symptomPositionMap.containsKey(symptomDataId)) {
                    Set<String> positionSet = new HashSet<>();
                    positionSet.add(position);
                    symptomPositionMap.put(symptomDataId, positionSet);
                } else {
                    symptomPositionMap.get(symptomDataId).add(position);
                }
            }
            for (Map.Entry<Long, Set<String>> entry : symptomPositionMap.entrySet()) {
                Long symptomDataId = entry.getKey();
                Set<String> position = entry.getValue();
                SymptomDataVO symptomDataVO1 = symptomDataService.getById(symptomDataId);
                SymptomAddVO addVO1 = new SymptomAddVO();
                BeanUtils.copyProperties(addVO, addVO1);
                addVO1.setSymptomName(symptomDataVO1.getName());
                addVO1.setValue(symptomDataId);
                addVO1.setPosition(Lists.newArrayList(position));
                this.realAdd(addVO1, symptomDataVO1);
            }
        } else {
            this.realAdd(addVO, symptomDataVO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void realAdd(SymptomAddVO addVO, SymptomDataVO symptomDataVO) {
        log.info("add symptom: {}", JSON.toJSONString(addVO));
        SymptomDO symptomDO = symptomMapper.getSymptom(addVO.getPatientId(), addVO.getValue(), 0L);
        if (symptomDO == null) {
            if (addVO.getLevel() <= 0) {
                return;
            }
            symptomDO = CopyPropertiesUtil.normalCopyProperties(addVO, SymptomDO.class);
            symptomDO.setCreateUserId(addVO.getCurrentUserId());
            symptomDO.setStopFlag(0L);
        } else {
            //更新症状
            symptomDO.setLevel(addVO.getLevel());
            symptomDO.setPerformance(addVO.getPerformance());
            symptomDO.setFrequency(addVO.getFrequency());
            symptomDO.setRelativeFrequency(addVO.getRelativeFrequency());
            symptomDO.setPosition(addVO.getPosition());
            symptomDO.setRemark(addVO.getRemark());
            if (addVO.getLevel() > 0) {
                symptomDO.setStopFlag(0L);
            } else {
                symptomDO.setStopFlag(symptomDO.getId());
                symptomDO.setStopTime(LocalDateTime.now());
            }
            //取请求时间和数据库时间的最小值
            symptomDO.setStartTime(LocalDateTime.now().isBefore(addVO.getStartTime()) ? LocalDateTime.now() : addVO.getStartTime());
        }
        symptomDO.setSymptomName(symptomDataVO.getName());
        symptomDO.setCustomizeLevel(symptomDataVO.getCustomizeLevel());
        symptomDO.setLevelName(LevelUtils.buildLevelName(symptomDO.getLevel(), symptomDataVO.getCustomizeLevel(), symptomDataVO.getCustomizeLevelInfos()));
        this.saveOrUpdate(symptomDO);
        symptomRecordsService.add(addVO, symptomDO.getId(), symptomDO.getCustomizeLevel(), symptomDO.getLevelName());

        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("changeBiz", symptomDO.getSymptomName());
            EventTaskAddVO eventTaskAddVO = new EventTaskAddVO()
                    .setExtJson(jsonObject)
                    .setPatientId(symptomDO.getPatientId())
                    .setEventTime(LocalDateTime.now())
                    .setOperation(addVO.getLevel() == 0 ? EventTaskOperationType.DELETE : EventTaskOperationType.ADD)
                    .setBizType(EventTaskType.PATIENT_SYMPTOM_CHANGE)
                    .setRequestId(UUID.randomUUID().toString())
                    .setBizId(symptomDO.getId().toString())
                    .setUserId(addVO.getCurrentUserId());
            eventTaskFeign.addEventTask(eventTaskAddVO);
        } catch (Exception e) {
            log.error("症状提醒任务失败", e);
        }

    }


    @Transactional(rollbackFor = Exception.class)
    public void stopSymptom(Long id) {
        SymptomDO symptomDO = symptomMapper.selectById(id);
        AssertUtils.notNull(symptomDO, SymptomsErrorCodeConstants.SYMPTOM_NOT_EXIST);
        AssertUtils.isTrue(symptomDO.getStopFlag() == 0L, SymptomsErrorCodeConstants.SYMPTOM_HAS_BEEN_STOP);
        symptomDO.setStopTime(LocalDateTime.now());
        symptomDO.setStopFlag(id);
        symptomMapper.updateById(symptomDO);

        SymptomAddVO addVO = new SymptomAddVO();
        BeanUtils.copyProperties(symptomDO, addVO, "id");
        addVO.setLevel(0L);
        addVO.setStartTime(LocalDateTime.now());
        symptomRecordsService.add(addVO, symptomDO.getId(), symptomDO.getCustomizeLevel(), LevelUtils.DEFAULT_LEVEL_NAME);
    }

    public List<SymptomVO> getByPatientId(Long patientId) {
        LambdaQueryWrapper<SymptomDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomDO::getPatientId, patientId);
        queryWrapper.eq(SymptomDO::getStopFlag, 0L);
        List<SymptomDO> symptomDOS = symptomMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(symptomDOS)) {
            return new ArrayList<>();
        }
        List<Long> symptomDataIds = symptomDOS.stream().map(SymptomDO::getValue).collect(Collectors.toList());
        List<SymptomDataVO> symptomDataVOS = symptomDataService.getByIds(symptomDataIds);
        Map<Long, SymptomDataVO> symptomDataVOMap = symptomDataVOS.stream().collect(Collectors.toMap(SymptomDataVO::getId, a -> a));
        List<SymptomVO> symptomVOS = new ArrayList<>();
        for (SymptomDO symptomDO : symptomDOS) {
            symptomVOS.add(symptomDO.toVO(symptomDataVOMap.get(symptomDO.getValue())));
        }
        return symptomVOS;
    }

    public SymptomVO getById(Long id) {
        SymptomDO symptomDO = symptomMapper.selectById(id);
        AssertUtils.notNull(symptomDO, SymptomsErrorCodeConstants.SYMPTOM_NOT_EXIST);
        SymptomDataVO symptomDataVO = symptomDataService.getById(symptomDO.getValue());
        return symptomDO.toVO(symptomDataVO);
    }

    public List<SymptomVO> getByIds(List<Long> ids) {
        List<SymptomDO> symptomDOS = symptomMapper.selectBatchIds(ids);
        return CopyPropertiesUtil.normalCopyProperties(symptomDOS, SymptomVO.class);
    }

    public SymptomPainTotalVO getPainTotal(Long patientId) {
        List<SymptomDO> symptomDOS = getPainSymptom(patientId);
        if (CollectionUtils.isEmpty(symptomDOS)) {
            return null;
        }
        List<Long> symptomDataIds = symptomDOS.stream().map(SymptomDO::getValue).collect(Collectors.toList());
        List<SymptomDataVO> symptomDataVOS = symptomDataService.getByIds(symptomDataIds);
        Map<Long, SymptomDataVO> symptomDataVOMap = symptomDataVOS.stream().collect(Collectors.toMap(SymptomDataVO::getId, a -> a));
        SymptomPainTotalVO symptomPainTotalVO = new SymptomPainTotalVO();
        for (SymptomDO symptomDO : symptomDOS) {
            if (CollectionUtils.isNotEmpty(symptomDO.getPosition())) {
                SymptomVO symptomVO = symptomDO.toVO(symptomDataVOMap.get(symptomDO.getValue()));
                symptomPainTotalVO.getPosition().addAll(symptomDO.getPosition());
                for (String position : symptomDO.getPosition()) {
                    symptomPainTotalVO.getPositionSymptomMap().put(position, symptomVO);
                }
            }
            symptomPainTotalVO.setMaxLevel(Math.max(symptomPainTotalVO.getMaxLevel(), symptomDO.getLevel()));
        }
        return symptomPainTotalVO;
    }


    public List<SymptomChartVO> queryChart(SymptomChartQueryVO symptomChartQueryVO) {
        List<SymptomDO> result = new ArrayList<>();
        if (symptomChartQueryVO.getSymptomId() != null) {
            result.add(symptomMapper.selectById(symptomChartQueryVO.getSymptomId()));
        } else {
            //默认查询疼痛症状
            result = getPainSymptom(symptomChartQueryVO.getPatientId());
        }
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        List<Long> ids = result.stream().map(SymptomDO::getId).collect(Collectors.toList());
        List<SymptomRecordsVO> symptomRecordsVOS = symptomRecordsService.list(ids, symptomChartQueryVO.getStartTime(), symptomChartQueryVO.getEndTime());
        if (CollectionUtils.isEmpty(symptomRecordsVOS)) {
            return new ArrayList<>();
        }
        Map<Long, List<SymptomRecordsVO>> map = symptomRecordsVOS.stream().collect(Collectors.groupingBy(SymptomRecordsVO::getSymptomId));
        Map<Long, SymptomChartVO> painChartVOMap = new HashMap<>();
        for (SymptomDO symptomDO : result) {
            SymptomChartVO symptomChartVO;
            if (map.containsKey(symptomDO.getValue())) {
                symptomChartVO = painChartVOMap.get(symptomDO.getValue());
            } else {
                symptomChartVO = new SymptomChartVO();
                symptomChartVO.setSymptomName(symptomDO.getSymptomName());
                symptomChartVO.setSymptomDataId(symptomDO.getValue());
                symptomChartVO.setPoints(new ArrayList<>());
            }
            List<SymptomRecordsVO> records = map.get(symptomDO.getId());
            if (CollectionUtils.isEmpty(records)) {
                painChartVOMap.put(symptomDO.getValue(), symptomChartVO);
                continue;
            }
            for (SymptomRecordsVO record : records) {
                SymptomChartVO.PainChartPointVO point = new SymptomChartVO.PainChartPointVO();
                point.setStartTime(record.getStartTime());
                point.setLevel(record.getLevel());
                symptomChartVO.getPoints().add(point);
            }
            //按时间排序
            symptomChartVO.setPoints(symptomChartVO.getPoints().stream().sorted(Comparator.comparing(SymptomChartVO.PainChartPointVO::getStartTime).reversed()).collect(Collectors.toList()));
            painChartVOMap.put(symptomDO.getValue(), symptomChartVO);
        }

        return new ArrayList<>(painChartVOMap.values());
    }


    public PageResult<SymptomPainRecordsVO> queryPainRecord(Long patientId, PageParam pageParam) {
        LambdaQueryWrapper<SymptomDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomDO::getPatientId, patientId);
        queryWrapper.in(SymptomDO::getValue, this.getPainSymptomIds());
        List<SymptomDO> symptomDOS = symptomMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(symptomDOS)) {
            return PageResult.empty();
        }
        Map<Long, SymptomDO> symptomMap = symptomDOS.stream().collect(Collectors.toMap(SymptomDO::getId, a -> a));
        SymptomRecordsQueryVO queryVO = new SymptomRecordsQueryVO();
        queryVO.setPageNo(pageParam.getPageNo());
        queryVO.setPageSize(pageParam.getPageSize());
        queryVO.setSymptomIds(symptomDOS.stream().map(SymptomDO::getId).collect(Collectors.toList()));
        PageResult<SymptomRecordsVO> pageResult = symptomRecordsService.query(queryVO);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }
        List<SymptomPainRecordsVO> painRecordsVOS = new ArrayList<>();
        for (SymptomRecordsVO recordsVO : pageResult.getList()) {
            SymptomPainRecordsVO painRecordsVO = CopyPropertiesUtil.normalCopyProperties(recordsVO, SymptomPainRecordsVO.class);
            SymptomDO symptomDO = symptomMap.get(recordsVO.getSymptomId());
            painRecordsVO.setSymptomName(symptomDO.getSymptomName());
            painRecordsVO.setValue(symptomDO.getValue());
            painRecordsVO.setStopFlag(symptomDO.getStopFlag() == 0 ? 0L : 1L);
            painRecordsVOS.add(painRecordsVO);
        }
        return new PageResult<>(painRecordsVOS, pageResult.getTotal());
    }

    private List<SymptomDO> getPainSymptom(Long patientId) {
        LambdaQueryWrapper<SymptomDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomDO::getPatientId, patientId);
        queryWrapper.eq(SymptomDO::getStopFlag, 0L);
        queryWrapper.in(SymptomDO::getValue, this.getPainSymptomIds());
        return symptomMapper.selectList(queryWrapper);
    }

    public PageResult<SymptomVO> query(SymptomQueryVO queryVO) {
        LambdaQueryWrapper<SymptomDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomDO::getPatientId, queryVO.getPatientId());
        if (queryVO.getStopFlag() != null) {
            if (queryVO.getStopFlag() == 0) {
                queryWrapper.eq(SymptomDO::getStopFlag, 0L);
            } else {
                queryWrapper.ne(SymptomDO::getStopFlag, 0L);
            }
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.isNotEmpty(queryVO.getStartTimeFirstTime()) && StringUtils.isNotEmpty(queryVO.getStartTimeSecondTime())) {
            queryWrapper.between(SymptomDO::getStartTime, LocalDate.parse(queryVO.getStartTimeFirstTime(), formatter), LocalDate.parse(queryVO.getStartTimeSecondTime(), formatter));
        }
        if (StringUtils.isNotEmpty(queryVO.getStopTimeFirstTime()) && StringUtils.isNotEmpty(queryVO.getStopTimeSecondTime())) {
            queryWrapper.between(SymptomDO::getStopTime, LocalDate.parse(queryVO.getStopTimeFirstTime(), formatter), LocalDate.parse(queryVO.getStopTimeSecondTime(), formatter));
        }
        if (queryVO.getPainFlag() != null && queryVO.getPainFlag()) {
            queryWrapper.in(SymptomDO::getValue, this.getPainSymptomIds());
        }

        if ("ASC".equals(queryVO.getStartTimeSort())) {
            queryWrapper.orderByAsc(SymptomDO::getStartTime);
        } else {
            queryWrapper.orderByDesc(SymptomDO::getStartTime);
        }
        Page<SymptomDO> page = symptomMapper.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }
        List<Long> symptomDataIds = page.getRecords().stream().map(SymptomDO::getValue).collect(Collectors.toList());
        List<SymptomDataVO> symptomDataVOS = symptomDataService.getByIds(symptomDataIds);
        Map<Long, SymptomDataVO> symptomDataVOMap = symptomDataVOS.stream().collect(Collectors.toMap(SymptomDataVO::getId, a -> a));
        List<SymptomVO> symptomVOS = new ArrayList<>();
        for (SymptomDO symptomDO : page.getRecords()) {
            symptomVOS.add(symptomDO.toVO(symptomDataVOMap.get(symptomDO.getValue())));
        }
        return new PageResult<>(symptomVOS, page.getTotal());
    }


    public PageResult<SymptomDetailVO> queryDetail(SymptomQueryVO queryVO) {
        PageResult<SymptomVO> result = this.query(queryVO);
        if (CollectionUtils.isEmpty(result.getList())) {
            return new PageResult<>(result.getTotal());
        }

        List<Long> symptomDataIds = result.getList().stream().map(SymptomVO::getValue).collect(Collectors.toList());
        List<SymptomDataVO> symptomDataVOS = symptomDataService.getByIds(symptomDataIds);
        Map<Long, SymptomDataVO> symptomDataVOMap = symptomDataVOS.stream().collect(Collectors.toMap(SymptomDataVO::getId, a -> a));

        //组装颜色
        List<SymptomDetailVO> detailVOs = CopyPropertiesUtil.normalCopyProperties(result.getList(), SymptomDetailVO.class);
        List<Long> ids = result.getList().stream().map(SymptomVO::getId).collect(Collectors.toList());
        List<SymptomRecordsVO> symptomRecordsVOS = symptomRecordsService.list(ids, null, null);
        Map<Long, List<SymptomRecordsVO>> map = symptomRecordsVOS.stream().collect(Collectors.groupingBy(SymptomRecordsVO::getSymptomId));

        for (SymptomDetailVO detailVO : detailVOs) {
            List<SymptomRecordsColorVO> recordsVOS = CopyPropertiesUtil.normalCopyProperties(map.get(detailVO.getId()), SymptomRecordsColorVO.class);
            if (CollectionUtils.isEmpty(recordsVOS)) {
                continue;
            }
            List<CustomizeLevelInfo> customizeLevelInfos = symptomDataVOMap.getOrDefault(detailVO.getValue(), new SymptomDataVO()).getCustomizeLevelInfos();
            for (SymptomRecordsColorVO recordsVO : recordsVOS) {
                recordsVO.setLevelColor(LevelUtils.buildLevelColor(recordsVO.getLevel(), recordsVO.getCustomizeLevel(), customizeLevelInfos));
            }
            detailVO.setRecords(recordsVOS);
        }
        return new PageResult<>(detailVOs, result.getTotal());
    }

    private List<Long> getPainSymptomIds() {
        JSONObject jsonObject = commonConfigFeign.getCommonConfigValueJsonByKey("symptom_config");
        return jsonObject.getJSONArray("painSymptomIds").toJavaList(Long.class);
    }

    private Map<String, Long> getPainSymptomPositionMap() {
        Map<String, Long> painSymptomPositionMap = new HashMap<>();
        JSONObject jsonObject = commonConfigFeign.getCommonConfigValueJsonByKey("symptom_config");
        JSONObject painSymptomPosition = jsonObject.getJSONObject("painSymptomPosition");
        for (String key : painSymptomPosition.keySet()) {
            Object value = painSymptomPosition.get(key);
            if (value instanceof Number) {
                painSymptomPositionMap.put(key, ((Number) value).longValue());
            }
        }
        return painSymptomPositionMap;
    }


}
