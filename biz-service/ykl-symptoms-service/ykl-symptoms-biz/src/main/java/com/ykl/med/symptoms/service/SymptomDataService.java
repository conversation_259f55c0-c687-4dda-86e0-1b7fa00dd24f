package com.ykl.med.symptoms.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.api.DictDataFeign;
import com.ykl.med.masterdata.vo.req.BatchUpdateStatusReqVO;
import com.ykl.med.symptoms.constans.SymptomsErrorCodeConstants;
import com.ykl.med.symptoms.db.entity.SymptomDataDO;
import com.ykl.med.symptoms.db.mapper.SymptomDataMapper;
import com.ykl.med.symptoms.vo.data.SymptomDataQueryVO;
import com.ykl.med.symptoms.vo.data.SymptomDataReqVO;
import com.ykl.med.symptoms.vo.data.SymptomDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SymptomDataService extends ServiceImpl<SymptomDataMapper, SymptomDataDO> {
    @Resource
    private SymptomDataMapper symptomDataMapper;
    @Resource
    private DictDataFeign dictDataFeign;

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(SymptomDataReqVO symptomDataReqVO) {
        log.info("saveOrUpdate symptomDataReqVO:{}", JSON.toJSONString(symptomDataReqVO));
        SymptomDataDO symptomDataDO;
        if (symptomDataReqVO.getId() == null) {
            symptomDataDO = CopyPropertiesUtil.normalCopyProperties(symptomDataReqVO, SymptomDataDO.class);
            if (StringUtils.isEmpty(symptomDataDO.getCode())) {
                symptomDataDO.setCode(SymptomDataDO.getDefaultCode());
            }
        } else {
            symptomDataDO = symptomDataMapper.selectById(symptomDataReqVO.getId());
            BeanUtils.copyProperties(symptomDataReqVO, symptomDataDO, "code");
        }
        try {
            this.saveOrUpdate(symptomDataDO);
        } catch (DuplicateKeyException e) {
            if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("uk_code")) {
                throw new ServiceException(SymptomsErrorCodeConstants.SYMPTOMS_DATA_CODE_EXIST);
            } else if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("uk_name")) {
                throw new ServiceException(SymptomsErrorCodeConstants.SYMPTOMS_DATA_NAME_EXIST);
            }
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(BatchUpdateStatusReqVO reqVO) {
        log.info("changeStatus reqVO:{}", JSON.toJSONString(reqVO));
        List<SymptomDataDO> symptomDataDOList = symptomDataMapper.selectBatchIds(reqVO.getIds());
        symptomDataDOList.forEach(symptomDataDO -> {
            symptomDataDO.setStatus(reqVO.getStatus());
        });
        this.updateBatchById(symptomDataDOList);
    }

    public PageResult<SymptomDataVO> queryPage(SymptomDataQueryVO queryVO) {
        MPJLambdaWrapper<SymptomDataDO> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.eq(queryVO.getStatus() != null, SymptomDataDO::getStatus, queryVO.getStatus());
        if (StringUtils.isNotEmpty(queryVO.getName())) {
            queryWrapper.and(i -> i.like(SymptomDataDO::getName, queryVO.getName()).or().like(SymptomDataDO::getCode, queryVO.getName()));
        }
        queryWrapper.eq(queryVO.getScaleFlag() != null, SymptomDataDO::getScaleFlag, queryVO.getScaleFlag());
        queryWrapper.eq(StringUtils.isNotEmpty(queryVO.getType()), SymptomDataDO::getType, queryVO.getType());
        if (StringUtils.isNotEmpty(queryVO.getName())) {
            queryWrapper.orderByAsc("CHAR_LENGTH(name)", "locate('" + queryVO.getName() + "',name)");
        }
        queryWrapper.orderByAsc(SymptomDataDO::getSort);
        queryWrapper.orderByDesc(SymptomDataDO::getCreateTime);
        Page<SymptomDataDO> adverseReactionDOPage = symptomDataMapper.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), queryWrapper);
        if (CollectionUtils.isEmpty(adverseReactionDOPage.getRecords())) {
            return PageResult.empty();
        }
        List<SymptomDataVO> symptomDataVOList = adverseReactionDOPage.getRecords().stream().map(SymptomDataDO::toVO).collect(Collectors.toList());
        return new PageResult<>(symptomDataVOList, adverseReactionDOPage.getTotal());
    }

    public SymptomDataVO getById(Long id) {
        SymptomDataDO symptomDataDO = symptomDataMapper.selectById(id);
        if (symptomDataDO == null) {
            return null;
        }
        SymptomDataVO vo = SymptomDataDO.toVO(symptomDataDO);
        //疼痛单独处理
        if ("SYMPTOM_NAMES_464".equals(symptomDataDO.getCode())) {
            vo.setPositionFlag(1L);
        } else {
            vo.setPositionFlag(0L);
        }
        vo.setPerformanceFlag(CollectionUtils.isNotEmpty(vo.getSymptomPerformanceList()) ? 1L : 0L);
        return vo;
    }

    public List<SymptomDataVO> getByIds(List<Long> ids) {
        List<SymptomDataDO> symptomDataDOList = symptomDataMapper.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(symptomDataDOList)) {
            return Collections.emptyList();
        }
        return symptomDataDOList.stream().map(SymptomDataDO::toVO).collect(Collectors.toList());
    }

    public SymptomDataVO getByName(String name) {
        LambdaQueryWrapper<SymptomDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomDataDO::getName, name);
        SymptomDataDO symptomDataDO = symptomDataMapper.selectOne(queryWrapper);
        return SymptomDataDO.toVO(symptomDataDO);
    }

    public SymptomDataVO getByCode(String code) {
        SymptomDataDO symptomDataDO = symptomDataMapper.selectOne(new LambdaQueryWrapper<SymptomDataDO>().eq(SymptomDataDO::getCode, code));
        return SymptomDataDO.toVO(symptomDataDO);
    }

    public List<SymptomDataVO> getByCodes(List<String> codes) {
        List<SymptomDataDO> symptomDataDOList = symptomDataMapper.selectList(new LambdaQueryWrapper<SymptomDataDO>().in(SymptomDataDO::getCode, codes));
        if (CollectionUtils.isEmpty(symptomDataDOList)) {
            return Collections.emptyList();
        }
        return symptomDataDOList.stream().map(SymptomDataDO::toVO).collect(Collectors.toList());
    }
}
