package com.ykl.med.symptoms.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@Schema(description = "患者症状量表")
@TableName("t_symptom_scale")
public class SymptomScaleDO extends BaseDO {

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "项目ID")
    private Long itemId;

    @Schema(description = "表单名称")
    private String itemName;

    @Schema(description = "项目内容（可存表单数据）")
    private String itemContent;

    @Schema(description = "频次ID")
    private Long frequencyId;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "创建者ID")
    private Long createUserId;

    @Schema(description = "通知待办时间")
    private LocalDateTime notifyTodoTime;

    @Schema(description = "停止标志；0未停止，其他已停止")
    private Long stopFlag;
}
