package com.ykl.med.symptoms.db.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ykl.med.symptoms.db.entity.SymptomScaleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SymptomScaleMapper extends MPJBaseMapper<SymptomScaleDO> {
    default SymptomScaleDO getSymptomScale(Long patientId, Long itemId) {
        LambdaQueryWrapper<SymptomScaleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomScaleDO::getPatientId, patientId);
        queryWrapper.eq(SymptomScaleDO::getItemId, itemId);
        return selectOne(queryWrapper);
    }

    default List<SymptomScaleDO> getByPatientId(Long patientId) {
        LambdaQueryWrapper<SymptomScaleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SymptomScaleDO::getPatientId, patientId);
        return selectList(queryWrapper);
    }
}