package com.ykl.med.symptoms.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.symptoms.api.records.SymptomRecordsFeign;
import com.ykl.med.symptoms.service.SymptomRecordsService;
import com.ykl.med.symptoms.vo.records.SymptomRecordsColorVO;
import com.ykl.med.symptoms.vo.records.SymptomRecordsQueryVO;
import com.ykl.med.symptoms.vo.records.SymptomRecordsVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("symptomRecords")
@Validated
public class SymptomRecordsController implements SymptomRecordsFeign {
    @Resource
    private SymptomRecordsService symptomRecordsService;

    @Override
    @PostMapping("/list")
    public List<SymptomRecordsVO> list(@RequestBody List<Long> symptomIds) {
        return symptomRecordsService.list(symptomIds, null, null);
    }

    @Override
    @PostMapping("/query")
    public PageResult<SymptomRecordsVO> query(@RequestBody @Valid SymptomRecordsQueryVO queryVO) {
        return symptomRecordsService.query(queryVO);
    }

    @PostMapping("/querySymptomRecords")
    @Operation(summary = "查询症状日志(包含了颜色等)")
    @Override
    public PageResult<SymptomRecordsColorVO> querySymptomRecords(@RequestBody SymptomRecordsQueryVO queryVO) {
        return symptomRecordsService.querySymptomRecords(queryVO);
    }

    @Override
    @PostMapping("/delete")
    public void delete(@RequestParam("id") Long id) {
        symptomRecordsService.delete(id);
    }

}