package com.ykl.med.symptoms.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.NoNullFastjsonTypeHandler;
import com.ykl.med.symptoms.enums.IntelligentReportStatus;
import com.ykl.med.symptoms.enums.IntelligentReportType;
import com.ykl.med.symptoms.vo.report.IntelligentReportAddVO;
import com.ykl.med.symptoms.vo.report.IntelligentReportExtra;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "智能服务报告")
@TableName(value = "t_intelligent_report", autoResultMap = true)
public class IntelligentReportDO extends BaseDO {
    private IntelligentReportType type;

    private String name;
    /**
     * 副标题
     */
    private String secondName;
    private LocalDate startDate;
    private LocalDate endDate;
    private Long submitUserId;
    private LocalDateTime submitTime;
    /**
     * 医生签名
     */
    private String submitDoctorSign;
    private String report;
    private String summary;
    private String reportReasoning;
    private String summaryReasoning;
    private Long patientId;
    /**
     * 数字医生视频地址
     */
    private String digitalDoctorVideoUrl;
    private Boolean digitalDoctorStatus = true;

    private IntelligentReportStatus status;

    private Boolean sendMessage = false;

    /**
     * 额外信息
     */
    @TableField(typeHandler = NoNullFastjsonTypeHandler.class)
    private IntelligentReportExtra extra;


    /**
     * 算新增时的开始时间
     *
     * @param last  上一次的同类型报告
     * @param addVO 添加请求的数据
     * @return 开始时间
     */
    public static LocalDate buildStartDate(IntelligentReportDO last, IntelligentReportAddVO addVO) {
        if (addVO.getStartDate() != null) {
            // 如果前端传了开始时间，就使用前端传的时间
            return addVO.getStartDate();
        }
        if (last == null) {
            // 如果上一次没有数据，就使用当前时间
            return LocalDate.now();
        }
        if (addVO.getType() == IntelligentReportType.SYMPTOM_REPORT) {
            if (last.getEndDate().equals(LocalDate.now())) {
                //上次截止时间是今天，就把之前的数据拿出来重新算即可
                return last.getStartDate();
            }
            //上次截止时间是今天以前，就把截止时间加1天，从这天开始算
            return last.getEndDate().plusDays(1);
        } else {
            // 其他类型，就从今天开始算
            return LocalDate.now();
        }
    }


    /**
     * 从添加请求中构建实体
     *
     * @param addVO 请求
     * @return 实体
     */
    public static IntelligentReportDO buildFromAddVO(IntelligentReportAddVO addVO) {
        IntelligentReportDO intelligentReportDO = CopyPropertiesUtil.normalCopyProperties(addVO, IntelligentReportDO.class);
        intelligentReportDO.setName(addVO.getType().getDesc());
        intelligentReportDO.setStatus(IntelligentReportStatus.REPORT_GENERATING);
        if (addVO.getType() == IntelligentReportType.MEDICATION_INSTRUCTIONS) {
            intelligentReportDO.setSecondName("不良反应综合建议");
        }
        return intelligentReportDO;
    }
}
