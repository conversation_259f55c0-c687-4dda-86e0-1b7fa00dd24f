package com.ykl.med.symptoms.controller;


import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.pojo.PageParam;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.symptoms.api.records.SymptomFeign;
import com.ykl.med.symptoms.constans.SymptomsErrorCodeConstants;
import com.ykl.med.symptoms.db.entity.SymptomDO;
import com.ykl.med.symptoms.db.mapper.SymptomMapper;
import com.ykl.med.symptoms.service.SymptomService;
import com.ykl.med.symptoms.vo.records.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("symptoms")
@Validated
public class SymptomController implements SymptomFeign {

    @Resource
    private SymptomService symptomService;
    @Resource
    private SymptomMapper symptomMapper;

    @Override
    @PostMapping("/add")
    @Operation(summary = "添加症状")
    public void add(@RequestBody @Valid SymptomAddVO addVO) {
        SymptomDO symptomDO = symptomMapper.getSymptom(addVO.getPatientId(), addVO.getValue(), 0L);
        if (symptomDO == null) {
            //新增，必须等级大于1
            AssertUtils.isTrue(addVO.getLevel() > 0, SymptomsErrorCodeConstants.LEVEL_NEED_NOT_ZERO);
        }
        symptomService.add(addVO);
    }

    @Override
    @PostMapping("/stopSymptom")
    @Operation(summary = "停止症状")
    public void stopSymptom(@RequestParam("id") Long id) {
        symptomService.stopSymptom(id);
    }

    @Override
    @PostMapping("/query")
    @Operation(summary = "查询症状")
    public PageResult<SymptomVO> query(@RequestBody @Valid SymptomQueryVO queryVO) {
        return symptomService.query(queryVO);
    }

    @PostMapping("/queryDetail")
    @Operation(summary = "查询详细列表", description = "返回列表数据")
    @Override
    public PageResult<SymptomDetailVO> queryDetail(@RequestBody SymptomQueryVO queryVO) {
        return symptomService.queryDetail(queryVO);
    }

    @Override
    @PostMapping("/getByPatientId")
    @Operation(summary = "查询症状列表")
    public List<SymptomVO> getByPatientId(@RequestParam("patientId") Long patientId) {
        return symptomService.getByPatientId(patientId);
    }

    @Override
    @PostMapping("/getById")
    @Operation(summary = "查询症状详情")
    public SymptomVO getById(@RequestParam("id") Long id) {
        return symptomService.getById(id);
    }


    @Override
    @PostMapping("/getByIds")
    @Operation(summary = "批量查询症状详情")
    public List<SymptomVO> getByIds(@RequestBody List<Long> ids) {
        return symptomService.getByIds(ids);
    }

    @Override
    @PostMapping("/getPainTotal")
    @Operation(summary = "查询疼痛症状列表")
    public SymptomPainTotalVO getPainTotal(@RequestParam("patientId") Long patientId) {
        return symptomService.getPainTotal(patientId);
    }

    @Override
    @PostMapping("/queryChart")
    @Operation(summary = "查询疼痛图表")
    public List<SymptomChartVO> queryChart(@RequestBody @Valid SymptomChartQueryVO symptomChartQueryVO) {
        return symptomService.queryChart(symptomChartQueryVO);
    }


    @Override
    @PostMapping("/queryPainRecord")
    @Operation(summary = "查询疼痛记录")
    public PageResult<SymptomPainRecordsVO> queryPainRecord(@RequestParam("patientId") Long patientId, @RequestBody PageParam pageParam) {
        return symptomService.queryPainRecord(patientId, pageParam);
    }


}