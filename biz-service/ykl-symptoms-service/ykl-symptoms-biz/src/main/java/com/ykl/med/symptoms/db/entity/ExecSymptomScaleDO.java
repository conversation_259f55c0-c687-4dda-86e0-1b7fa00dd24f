package com.ykl.med.symptoms.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import com.ykl.med.framework.mybatis.core.type.NoNullFastjsonTypeHandler;
import com.ykl.med.symptoms.vo.records.SymptomAddVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@Data
@Schema(description = "症状量表执行表")
@TableName(value = "t_exec_symptom_scale", autoResultMap = true)
public class ExecSymptomScaleDO extends BaseDO {
    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "症状量表ID")
    private Long symptomScaleId;

    @Schema(description = "执行时间")
    private LocalDateTime execTime;

    @Schema(description = "应执行时间")
    private LocalDateTime shouldExecTime;

    @Schema(description = "填报的内容（症状量表）")
    private String itemContent;

    @Schema(description = "原始的待填写症状量表")
    private String originItemContent;

    @Schema(description = "创建者ID【实体】")
    private Long createUserId;

    @Schema(description = "通知待办标志")
    private Boolean notifyTodoFlag = false;

    @Schema(description = "删除标志")
    private Boolean deleteFlag = false;

    @Schema(description = "量表症状集合")
    @TableField(typeHandler = NoNullFastjsonTypeHandler.class)
    private List<SymptomAddVO> symptoms;

    @Schema(description = "额外的自选症状信息")
    @TableField(typeHandler = NoNullFastjsonTypeHandler.class)
    private List<SymptomAddVO> extraSymptoms;
}
