package com.ykl.med.symptoms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.symptoms.db.entity.IntelligentReportDrugDO;
import com.ykl.med.symptoms.db.mapper.IntelligentReportDrugMapper;
import com.ykl.med.symptoms.enums.IntelligentReportType;
import com.ykl.med.symptoms.vo.report.IntelligentReportDetailVO;
import com.ykl.med.symptoms.vo.report.IntelligentReportSubmitReqVO;
import com.ykl.med.symptoms.vo.report.IntelligentReportVO;
import com.ykl.med.symptoms.vo.report.drug.IntelligentReportDrugVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class IntelligentReportDrugService extends ServiceImpl<IntelligentReportDrugMapper, IntelligentReportDrugDO> {

    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<IntelligentReportDrugVO> vos, Long intelligentReportId) {
        LambdaQueryWrapper<IntelligentReportDrugDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntelligentReportDrugDO::getIntelligentReportId, intelligentReportId);
        this.remove(queryWrapper);
        if (CollectionUtils.isNotEmpty(vos)) {
            List<IntelligentReportDrugDO> entityList = CopyPropertiesUtil.normalCopyProperties(vos, IntelligentReportDrugDO.class);
            for (IntelligentReportDrugDO entity : entityList) {
                entity.setIntelligentReportId(intelligentReportId);
            }
            this.saveBatch(entityList);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(IntelligentReportSubmitReqVO submitReqVO) {
        LambdaQueryWrapper<IntelligentReportDrugDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntelligentReportDrugDO::getIntelligentReportId, submitReqVO.getId());
        List<IntelligentReportDrugDO> entityList = this.list(queryWrapper);
        for (IntelligentReportDrugDO entity : entityList) {
            for (IntelligentReportDetailVO.DrugDosingTime drugDosingTime : submitReqVO.getDrugDosingTime()) {
                if (drugDosingTime.getDrugId().equals(entity.getDrugId())) {
                    entity.setDosingTime(drugDosingTime.getDosingTime());
                    entity.setAdministrationMethod(drugDosingTime.getAdministrationMethod());
                    entity.setNotes(drugDosingTime.getNotes());
                }
            }
            for (IntelligentReportDetailVO.DrugMissedDoseMeasures missedDoseMeasures : submitReqVO.getDrugMissedDoseMeasures()) {
                if (missedDoseMeasures.getDrugId().equals(entity.getDrugId())) {
                    entity.setMissedDose(missedDoseMeasures.getMissedDose());
                }
            }
            for (IntelligentReportDetailVO.DrugDrugInteractions drugDrugInteractions : submitReqVO.getDrugDrugInteractions()) {
                if (drugDrugInteractions.getDrugId().equals(entity.getDrugId())) {
                    entity.setDrugInteractions(drugDrugInteractions.getDrugInteractions());
                }
            }
            for (IntelligentReportDetailVO.DrugPatientInteractions patientInteractions : submitReqVO.getDrugPatientInteractions()) {
                if (patientInteractions.getDrugId().equals(entity.getDrugId())) {
                    entity.setPatientInteractions(patientInteractions.getPatientInteractions());
                }
            }
            for (IntelligentReportDetailVO.DrugFoodInteractions foodInteractions : submitReqVO.getDrugFoodInteractions()) {
                if (foodInteractions.getDrugId().equals(entity.getDrugId())) {
                    entity.setFoodInteractions(foodInteractions.getFoodInteractions());
                }
            }
        }
        this.updateBatchById(entityList);
    }


    public List<IntelligentReportDrugVO> queryByReportId(Long reportId) {
        LambdaQueryWrapper<IntelligentReportDrugDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntelligentReportDrugDO::getIntelligentReportId, reportId);
        return CopyPropertiesUtil.normalCopyProperties(this.list(queryWrapper), IntelligentReportDrugVO.class);
    }

    public IntelligentReportDetailVO buildDrugData(IntelligentReportVO reportVO) {
        IntelligentReportDetailVO detailVO = CopyPropertiesUtil.normalCopyProperties(reportVO, IntelligentReportDetailVO.class);
        if (reportVO.getType() != IntelligentReportType.MEDICATION_INSTRUCTIONS) {
            return detailVO;
        }
        LambdaQueryWrapper<IntelligentReportDrugDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntelligentReportDrugDO::getIntelligentReportId, reportVO.getId());
        List<IntelligentReportDrugDO> entityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            return detailVO;
        }
        List<IntelligentReportDetailVO.DrugDosingTime> drugDosingTime = new ArrayList<>();
        List<IntelligentReportDetailVO.DrugMissedDoseMeasures> drugMissedDoseMeasures = new ArrayList<>();
        List<IntelligentReportDetailVO.DrugPatientInteractions> drugPatientInteractions = new ArrayList<>();
        List<IntelligentReportDetailVO.DrugFoodInteractions> drugFoodInteractions = new ArrayList<>();
        List<IntelligentReportDetailVO.DrugDrugInteractions> drugDrugInteractions = new ArrayList<>();
        for (IntelligentReportDrugDO entity : entityList) {
            if (entity.getDosingTime() != null || entity.getAdministrationMethod() != null || CollectionUtils.isNotEmpty(entity.getNotes())) {
                drugDosingTime.add(CopyPropertiesUtil.normalCopyProperties(entity, IntelligentReportDetailVO.DrugDosingTime.class));
            }
            if (CollectionUtils.isNotEmpty(entity.getMissedDose())) {
                drugMissedDoseMeasures.add(CopyPropertiesUtil.normalCopyProperties(entity, IntelligentReportDetailVO.DrugMissedDoseMeasures.class));
            }
            if (CollectionUtils.isNotEmpty(entity.getDrugInteractions())) {
                drugDrugInteractions.add(CopyPropertiesUtil.normalCopyProperties(entity, IntelligentReportDetailVO.DrugDrugInteractions.class));
            }
            if (CollectionUtils.isNotEmpty(entity.getPatientInteractions())) {
                drugPatientInteractions.add(CopyPropertiesUtil.normalCopyProperties(entity, IntelligentReportDetailVO.DrugPatientInteractions.class));
            }
            if (CollectionUtils.isNotEmpty(entity.getFoodInteractions())) {
                drugFoodInteractions.add(CopyPropertiesUtil.normalCopyProperties(entity, IntelligentReportDetailVO.DrugFoodInteractions.class));
            }
        }
        detailVO.setDrugDosingTime(drugDosingTime);
        detailVO.setDrugMissedDoseMeasures(drugMissedDoseMeasures);
        detailVO.setDrugPatientInteractions(drugPatientInteractions);
        detailVO.setDrugFoodInteractions(drugFoodInteractions);
        detailVO.setDrugDrugInteractions(drugDrugInteractions);
        return detailVO;
    }
}
