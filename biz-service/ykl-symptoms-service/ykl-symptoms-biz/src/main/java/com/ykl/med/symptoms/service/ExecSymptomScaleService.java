package com.ykl.med.symptoms.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.masterdata.vo.FormVO;
import com.ykl.med.push.api.ToDoMessageFeign;
import com.ykl.med.push.enums.ToDoMessageType;
import com.ykl.med.push.vo.todo.ToDoMessageExecutedReqVO;
import com.ykl.med.push.vo.todo.ToDoMessageListVO;
import com.ykl.med.push.vo.todo.ToDoMessageQueryVO;
import com.ykl.med.symptoms.constans.SymptomsErrorCodeConstants;
import com.ykl.med.symptoms.db.entity.ExecSymptomScaleDO;
import com.ykl.med.symptoms.db.entity.SymptomScaleDO;
import com.ykl.med.symptoms.db.mapper.ExecSymptomScaleMapper;
import com.ykl.med.symptoms.enums.ExecSymptomScaleStatus;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleAddVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleQueryVO;
import com.ykl.med.symptoms.vo.scale.ExecSymptomScaleVO;
import com.ykl.med.symptoms.vo.scale.SymptomScaleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExecSymptomScaleService extends ServiceImpl<ExecSymptomScaleMapper, ExecSymptomScaleDO> {
    @Resource
    private ExecSymptomScaleMapper execSymptomScaleMapper;
    @Resource
    private SymptomScaleService symptomScaleService;
    @Resource
    private ToDoMessageFeign toDoMessageFeign;

    @Transactional(rollbackFor = Exception.class)
    public ExecSymptomScaleDO add(Long patientId, Long symptomScaleId, LocalDateTime shouldExecTime, String originItemContent) {
        log.info("add wait exec symptom scale:{},{},{},{}", patientId, symptomScaleId, shouldExecTime, originItemContent);
        LambdaQueryWrapper<ExecSymptomScaleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExecSymptomScaleDO::getSymptomScaleId, symptomScaleId);
        queryWrapper.eq(ExecSymptomScaleDO::getShouldExecTime, shouldExecTime);
        ExecSymptomScaleDO old = execSymptomScaleMapper.selectOne(queryWrapper);
        if (old != null) {
            execSymptomScaleMapper.deleteById(old);
        }
        ExecSymptomScaleDO execSymptomScaleDO = new ExecSymptomScaleDO();
        execSymptomScaleDO.setPatientId(patientId);
        execSymptomScaleDO.setSymptomScaleId(symptomScaleId);
        execSymptomScaleDO.setShouldExecTime(shouldExecTime);
        execSymptomScaleDO.setItemContent(originItemContent);
        execSymptomScaleDO.setOriginItemContent(originItemContent);
        execSymptomScaleMapper.insert(execSymptomScaleDO);
        return execSymptomScaleDO;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long exec(ExecSymptomScaleAddVO addVO) {
        log.info("add exec symptom scale: {}", JSON.toJSONString(addVO));
        ExecSymptomScaleDO execSymptomScaleDO = execSymptomScaleMapper.getLastByPatientId(addVO.getPatientId());
        if (execSymptomScaleDO == null) {
            throw new ServiceException(SymptomsErrorCodeConstants.SCALE_NOT_EXIST);
        }
        if (execSymptomScaleDO.getExecTime() != null) {
            throw new ServiceException(SymptomsErrorCodeConstants.SCALE_ALREADY_EXEC);
        }
        execSymptomScaleDO.setExecTime(LocalDateTime.now());
        execSymptomScaleDO.setSymptoms(addVO.getSymptomAddVOS());
        execSymptomScaleDO.setExtraSymptoms(addVO.getExtraSymptoms());
        execSymptomScaleDO.setItemContent(JSON.toJSONString(addVO.getItemContent()));
        execSymptomScaleDO.setCreateUserId(addVO.getCurrentUserId());
        execSymptomScaleMapper.updateById(execSymptomScaleDO);
        this.notifyToDoMessage(execSymptomScaleDO.getId());
        return execSymptomScaleDO.getId();
    }

    /**
     * 删除今天的未填报的量表
     *
     * @param symptomScaleId 量表id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteToday(Long symptomScaleId) {
        LambdaQueryWrapper<ExecSymptomScaleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExecSymptomScaleDO::getSymptomScaleId, symptomScaleId);
        queryWrapper.eq(ExecSymptomScaleDO::getDeleteFlag, false);
        queryWrapper.ge(ExecSymptomScaleDO::getShouldExecTime, LocalDateTimeUtil.endOfDay(LocalDateTime.now(), true));
        queryWrapper.le(ExecSymptomScaleDO::getShouldExecTime, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        queryWrapper.isNull(ExecSymptomScaleDO::getExecTime);
        List<ExecSymptomScaleDO> dos = execSymptomScaleMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(dos)) {
            return;
        }
        dos.forEach(e -> e.setDeleteFlag(true));
        this.updateBatchById(dos);
    }


    @Transactional(rollbackFor = Exception.class)
    public void notifyToDoMessage(Long id) {
        log.info("notifyToDoMessage exec symptom scale: {}", id);
        ExecSymptomScaleDO execSymptomScaleDO = execSymptomScaleMapper.selectById(id);
        if (execSymptomScaleDO == null) {
            return;
        }
        List<ToDoMessageListVO> toDoMessageListVOS = toDoMessageFeign.queryList(new ToDoMessageQueryVO()
                .setPatientId(execSymptomScaleDO.getPatientId())
                .setExecuted(false)
                .setType(ToDoMessageType.SYMPTOM_SCALE));
        if (CollectionUtils.isNotEmpty(toDoMessageListVOS)) {
            //把id最大的待办取出来
            Long maxId = toDoMessageListVOS.stream().map(ToDoMessageListVO::getId).max(Long::compareTo).orElse(null);
            ToDoMessageExecutedReqVO toDoMessageExecutedReqVO = new ToDoMessageExecutedReqVO();
            toDoMessageExecutedReqVO.setCurrentPatientId(execSymptomScaleDO.getPatientId());
            toDoMessageExecutedReqVO.setId(maxId);
            toDoMessageExecutedReqVO.setExecutedTime(LocalDateTime.now());
            toDoMessageFeign.toDoMessageExecuted(toDoMessageExecutedReqVO);
            // 把其他id取出来
            List<Long> ids = toDoMessageListVOS.stream().map(ToDoMessageListVO::getId).filter(e -> !Objects.equals(e, maxId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                toDoMessageFeign.deleteById(ids);
            }
        }
        execSymptomScaleDO.setNotifyTodoFlag(true);
        execSymptomScaleMapper.updateById(execSymptomScaleDO);
    }

    public List<ExecSymptomScaleVO> getAllNeedNotifyToDoMessage() {
        LambdaQueryWrapper<ExecSymptomScaleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(ExecSymptomScaleDO::getExecTime);
        queryWrapper.eq(ExecSymptomScaleDO::getNotifyTodoFlag, false);
        List<ExecSymptomScaleDO> execSymptomScaleDOS = execSymptomScaleMapper.selectList(queryWrapper);
        return CopyPropertiesUtil.normalCopyProperties(execSymptomScaleDOS, ExecSymptomScaleVO.class);
    }

//    public ExecSymptomScaleVO getLast(Long symptomScaleId) {
//        ExecSymptomScaleDO execSymptomScaleDO = execSymptomScaleMapper.getLastBySymptomScaleId(symptomScaleId);
//        if (execSymptomScaleDO == null) {
//            return null;
//        }
//        execSymptomScaleDO = execSymptomScaleMapper.selectById(execSymptomScaleDO.getId());
//        ExecSymptomScaleVO execSymptomScaleVO = CopyPropertiesUtil.normalCopyProperties(execSymptomScaleDO, ExecSymptomScaleVO.class);
//        execSymptomScaleVO.setItemContent(JSON.parseObject(execSymptomScaleDO.getItemContent(), FormVO.class));
//        return execSymptomScaleVO;
//    }

    public ExecSymptomScaleVO getLastByPatient(Long patientId) {
        ExecSymptomScaleDO execSymptomScaleDO = execSymptomScaleMapper.getLastByPatientId(patientId);
        if (execSymptomScaleDO == null) {
            return null;
        }
        execSymptomScaleDO = execSymptomScaleMapper.selectById(execSymptomScaleDO.getId());
        ExecSymptomScaleVO execSymptomScaleVO = CopyPropertiesUtil.normalCopyProperties(execSymptomScaleDO, ExecSymptomScaleVO.class);
        execSymptomScaleVO.setItemContent(JSON.parseObject(execSymptomScaleDO.getItemContent(), FormVO.class));
        return execSymptomScaleVO;
    }

    public ExecSymptomScaleVO getById(Long id) {
        return CopyPropertiesUtil.normalCopyProperties(execSymptomScaleMapper.selectById(id), ExecSymptomScaleVO.class);
    }

    public ExecSymptomScaleVO getDetailById(Long id) {
        ExecSymptomScaleDO execSymptomScaleDO = execSymptomScaleMapper.selectById(id);
        ExecSymptomScaleVO execSymptomScaleVO = CopyPropertiesUtil.normalCopyProperties(execSymptomScaleDO, ExecSymptomScaleVO.class);
        SymptomScaleDO symptomScaleDO = symptomScaleService.getById(execSymptomScaleVO.getSymptomScaleId());
        execSymptomScaleVO.setSymptomScaleItemName(symptomScaleDO.getItemName());
        execSymptomScaleVO.setFrequencyName(symptomScaleDO.getFrequencyName());
        // 如果已经执行过，使用execSymptomScaleDO的itemContent，否则使用symptomScaleDO的itemContent
        ExecSymptomScaleDO last = execSymptomScaleMapper.getLastByPatientId(execSymptomScaleDO.getPatientId());
        if (execSymptomScaleVO.getExecTime() != null) {
            execSymptomScaleVO.setItemContent(JSON.parseObject(execSymptomScaleDO.getItemContent(), FormVO.class));
            execSymptomScaleVO.setStatus(ExecSymptomScaleStatus.EXEC);
        } else {
            execSymptomScaleVO.setItemContent(JSON.parseObject(symptomScaleDO.getItemContent(), FormVO.class));
            if (execSymptomScaleDO.getId() < last.getId()) {
                execSymptomScaleVO.setStatus(ExecSymptomScaleStatus.EXPIRE);
            } else {
                execSymptomScaleVO.setStatus(ExecSymptomScaleStatus.NOT_EXEC);
            }
        }
        return execSymptomScaleVO;
    }

    public PageResult<ExecSymptomScaleVO> query(ExecSymptomScaleQueryVO queryVO) {
        ExecSymptomScaleDO last = execSymptomScaleMapper.getLastByPatientId(queryVO.getPatientId());
        LambdaQueryWrapper<ExecSymptomScaleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ExecSymptomScaleDO.class,
                info -> !info.getColumn().equals("item_content") &&
                        !info.getColumn().equals("origin_item_content"));
        queryWrapper.eq(ExecSymptomScaleDO::getPatientId, queryVO.getPatientId());
        queryWrapper.eq(ExecSymptomScaleDO::getDeleteFlag, false);
        queryWrapper.ge(queryVO.getCreateTimeStart() != null, ExecSymptomScaleDO::getCreateTime, queryVO.getCreateTimeStart());
        queryWrapper.le(queryVO.getCreateTimeEnd() != null, ExecSymptomScaleDO::getCreateTime, queryVO.getCreateTimeEnd());
        if (queryVO.getStatus() != null) {
            if (queryVO.getStatus().equals(ExecSymptomScaleStatus.EXEC)) {
                queryWrapper.isNotNull(ExecSymptomScaleDO::getExecTime);
            } else {
                queryWrapper.isNull(ExecSymptomScaleDO::getExecTime);
                if (queryVO.getStatus().equals(ExecSymptomScaleStatus.EXPIRE)) {
                    queryWrapper.lt(ExecSymptomScaleDO::getId, last.getId());
                } else if (queryVO.getStatus().equals(ExecSymptomScaleStatus.NOT_EXEC)) {
                    queryWrapper.ge(ExecSymptomScaleDO::getId, last.getId());
                }
            }
        }
        queryWrapper.orderByDesc(ExecSymptomScaleDO::getCreateTime);
        Page<ExecSymptomScaleDO> page = execSymptomScaleMapper.selectPage(new Page<>(queryVO.getPageNo(), queryVO.getPageSize()), queryWrapper);
        List<Long> symptomScaleIds = page.getRecords().stream().map(ExecSymptomScaleDO::getSymptomScaleId).distinct().collect(Collectors.toList());
        if (symptomScaleIds.isEmpty()) {
            return PageResult.empty();
        }
        List<SymptomScaleVO> symptomScaleVOS = symptomScaleService.getSymptomScaleByIds(symptomScaleIds);
        Map<Long, SymptomScaleVO> symptomScaleMap = symptomScaleVOS.stream().collect(Collectors.toMap(SymptomScaleVO::getId, v -> v));
        List<ExecSymptomScaleVO> execSymptomScaleVOS = new ArrayList<>();

        for (ExecSymptomScaleDO execSymptomScaleDO : page.getRecords()) {
            ExecSymptomScaleVO execSymptomScaleVO = CopyPropertiesUtil.normalCopyProperties(execSymptomScaleDO, ExecSymptomScaleVO.class);
            SymptomScaleVO symptomScaleVO = symptomScaleMap.get(execSymptomScaleDO.getSymptomScaleId());
            if (symptomScaleVO != null) {
                execSymptomScaleVO.setSymptomScaleItemName(symptomScaleVO.getItemName());
                execSymptomScaleVO.setFrequencyName(symptomScaleVO.getFrequencyName());
            }
            if (execSymptomScaleVO.getExecTime() != null) {
                execSymptomScaleVO.setStatus(ExecSymptomScaleStatus.EXEC);
            } else {
                if (execSymptomScaleDO.getId() < last.getId()) {
                    execSymptomScaleVO.setStatus(ExecSymptomScaleStatus.EXPIRE);
                } else {
                    execSymptomScaleVO.setStatus(ExecSymptomScaleStatus.NOT_EXEC);
                }
            }
            execSymptomScaleVOS.add(execSymptomScaleVO);
        }
        return new PageResult<>(execSymptomScaleVOS, page.getTotal());
    }
}
