package com.ykl.med.business.service;

import com.ykl.med.nutritional.entity.vo.NewNutritionalAssessmentVO;
import com.ykl.med.nutritional.entity.vo.NewNutritionalVO;
import com.ykl.med.nutritional.entity.vo.req.QueryNutritionalAssessmentParam;
import com.ykl.med.nutritional.entity.vo.req.QueryNutritionalReqParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
public interface NewNutritionalService {
    NewNutritionalVO getById(Long nutritionalId);

    NewNutritionalVO getByPatientId(Long patientId);

    List<NewNutritionalVO> getAllPlanNutritional();

    List<NewNutritionalVO> getAllPlanNutritionalByEndTime(QueryNutritionalReqParam param);

    List<NewNutritionalAssessmentVO> nutritionalAssessmentList(QueryNutritionalAssessmentParam param);

    NewNutritionalAssessmentVO nutritionalAssessmentDetails(Long id);
}
