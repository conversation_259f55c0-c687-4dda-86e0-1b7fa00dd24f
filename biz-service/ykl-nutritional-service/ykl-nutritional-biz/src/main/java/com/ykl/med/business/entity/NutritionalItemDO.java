package com.ykl.med.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 *  营养方案
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@TableName(value = "t_nutritional_item")
public class NutritionalItemDO extends BaseDO {
    /** 患者ID */
    private Long patientId ;

    /** 营养方案ID */
    private Long nutritionalId ;

    /** 项目ID */
    private Long itemId;

    /** 项目类型（全营养配方/补充营养配方） */
    private String itemType ;

    /** 项目名称 */
    private String itemName ;

    /** 包装规格（20mg*5袋/盒） */
    private String itemPackageSpec ;

    /** 项目厂商名称 */
    private String itemProducerName ;

    /** 项目用法名称 */
    private String usageName ;

    /** 单次用量 */
    private Short onceDosage ;

    /** 单次用量单位 */
    private String onceDosageUnit ;

    /** 频次ID */
    private Long frequencyId ;

    /** 频次名称 */
    private String frequencyName ;

    /** 频次时间 */
    private String frequencyTimes ;

    /** 开立数量 */
    private Short quantity ;

    /** 开立数量单位 */
    private String quantityUnit ;

    /** 备注（项目说明） */
    private String remark ;

    /** 创建者ID【实体】 */
    private Long createUserId ;

    /** 最后一次操作者ID【实体】 */
    private Long lastUserId ;

    /** 状态；启用：ENABLE，禁用：DISABLE */
    private String status ;

    /** 删除标志；0未删，1已删 */
    private Boolean deleteFlag ;

    /** 开始时间 */
    private LocalDateTime startTime ;

    /** 结束时间 */
    private LocalDateTime endTime ;

    /** 最后一次生成执行计划的时间 */
    private LocalDateTime lastExecPlanTime ;
}
