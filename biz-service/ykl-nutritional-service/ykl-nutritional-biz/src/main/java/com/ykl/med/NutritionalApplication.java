package com.ykl.med;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan(basePackages = {"com.ykl.med.**.mapper"})
@EnableFeignClients(basePackages = "com.ykl.med")
public class NutritionalApplication {

    public static void main(String[] args) {

        SpringApplication.run(NutritionalApplication.class, args);

    }

}