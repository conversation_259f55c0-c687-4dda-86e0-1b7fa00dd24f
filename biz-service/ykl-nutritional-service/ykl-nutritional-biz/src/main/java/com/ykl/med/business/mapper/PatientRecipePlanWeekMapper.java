package com.ykl.med.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ykl.med.business.entity.PatientRecipePlanWeekDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;


@Mapper
public interface PatientRecipePlanWeekMapper extends BaseMapper<PatientRecipePlanWeekDO> {

    @Update("UPDATE t_patient_recipe_plan_week SET delete_flag=true WHERE nutritional_id=#{nutritionalId}")
    void delRecommendRecipeWeek(@Param("nutritionalId") Long nutritionalId);
}
