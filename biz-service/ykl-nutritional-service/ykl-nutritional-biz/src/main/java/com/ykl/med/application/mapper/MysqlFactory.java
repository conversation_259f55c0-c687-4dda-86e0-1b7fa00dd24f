package com.ykl.med.application.mapper;

import com.ykl.med.application.entity.ObjectAttribute;
import com.ykl.med.application.entity.PageRecord;
import com.ykl.med.application.entity.ValueObject;
import com.ykl.med.config.SimpleException;
import com.ykl.med.enums.LogicValue;
import com.ykl.med.util.GsonUtil;
import com.ykl.med.util.PublicUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class MysqlFactory {

    /**
     * 处理翻页对象,确保其正确性
     * */
    public PageRecord processPageRecordObject(PageRecord page) {
        PageRecord pageRecord;
        if ( page != null ){
            pageRecord = page;
        } else {
            pageRecord = new PageRecord();
        }

        // 判断pageRecord是否正确
        if ( pageRecord.getPageNo() < 1 ){
            pageRecord.setPageNo(1);
        }
        if( pageRecord.getPageSize() < 1 ){
            pageRecord.setPageSize(100);
        }

        // 不能超过1w条
        if ( pageRecord.getPageSize() > 10000 ){
            pageRecord.setPageSize(10000);
        }

        // 设置返回结果集为[]
        if ( pageRecord.getList() == null ){
            pageRecord.setRecords(new ArrayList<>());
        }

        return pageRecord;
    }

    /**
     * 把增删改查组合
     */
    public String crudToSql(ValueObject valueObject, PageRecord page) throws SimpleException {

        // 对象为空, 直接返回空串
        if ( valueObject == null
                || valueObject.getTableName() == null
                || valueObject.getVObj() == null
                || valueObject.getMethod() == null ){
            return null;
        }

        // 过滤掉虚拟表
        if ( valueObject.getTableName().startsWith("t_virtual") ){
            return null;
        }

        String sqlStr = null;

        switch (valueObject.getMethod()) {
            case "GET":
                sqlStr = select(valueObject.getVObj(), valueObject.getTableName(), page);
                break;
            case "PUT":
                sqlStr = update(valueObject.getVObj(), valueObject.getTableName());
                break;
            case "POST":
                sqlStr = insert(valueObject.getVObj(), valueObject.getTableName());
                break;
            case "DELETE":
                sqlStr = delete(valueObject.getVObj(), valueObject.getTableName());
                break;
            default:
                break;
        }

        // 获取sql失败
        if ( sqlStr == null || sqlStr.equals("") ) {
            return null;
        }

        return sqlStr;
    }

    /**
     * 方法: insert
     * 描述: 生成SQL的插入语句
     * 参数：arr: 值对象, tableName: 表名
     */
    private String insert(Map<String, ObjectAttribute> arr, String tableName) {
        // 插入字段
        StringBuilder addBuffer = new StringBuilder("INSERT INTO "+tableName+"(");
        // 插入数据
        StringBuilder valueBuffer = new StringBuilder(" VALUES(");

        // 非默认值数量
        short segCount = 0;
        for ( Map.Entry<String, ObjectAttribute> m : arr.entrySet() ){

            // 插入时, id没有值, 生成默认默认值
            if ( m.getKey().equals("id") && m.getValue().getLogicActValueMap() != null
                    && (m.getValue().getObj().size() == 0) // 没有传入值
                    && m.getValue().getLogicActValueMap().containsKey(LogicValue.SGV_INSERT)
            ) {
                m.getValue().getObj().add(PublicUtil.uuidGeneratorLong18().toString());
            }

            if ( m.getValue().getObj().size() > 0 ){
                // 组装列字段
                addBuffer.append("`").append(m.getKey()).append("`").append(",");

                // 组装插入值
                //StringBuilder valStr = getObjValue(m.getValue().getObj().get(0), m.getValue().getType());
                StringBuilder valStr = getObjListValue(m.getValue().getObj(), m.getValue().getType());
                valueBuffer.append(valStr).append(",");

                // 非默认值,+1
                if ( !m.getValue().isDefaultValueFlag() ){
                    segCount++;
                }
            }
        }

        // 没有字段
        if ( (segCount == 0) ){
            return "";
        }

        addBuffer.delete(addBuffer.length()-1,addBuffer.length());//删除最后一个逗号
        addBuffer.append(")");

        valueBuffer.delete(valueBuffer.length()-1,valueBuffer.length());//删除最后一个逗号
        valueBuffer.append(")");

        addBuffer.append(valueBuffer);

        return addBuffer.toString();
    }

    /**
     * 方法：update
     * 描述: 生成SQL的更新语句
     * 参数：arr: 值对象, tableName: 表名
     */
    private String update(Map<String, ObjectAttribute> arr, String tableName) throws SimpleException {
        StringBuilder editBuffer = new StringBuilder("UPDATE " + tableName + " SET ");

        // 非生成的字段值的个数
        short segCount = 0;
        for ( Map.Entry<String, ObjectAttribute> m : arr.entrySet() ){
            // 不能被更新设置
            if ( m.getValue().getLogicActValueMap().containsKey(LogicValue.FP_PUT_INVALID)
                    || m.getValue().getLogicActValueMap().containsKey(LogicValue.FP_PUT_WHERE) ){
                continue;
            }

            // 需要更新的内容: 给定对象, 且非唯一主键
            if ( m.getValue().getObj().size() > 0 ){
                //StringBuilder valStr = getObjValue(m.getValue().getObj().get(0),m.getValue().getType());
                StringBuilder valStr = getObjListValue(m.getValue().getObj(), m.getValue().getType());
                editBuffer.append("`").append(m.getKey()).append("`").append("=").append(valStr).append(",");
            }

            // 非生成的字段值的个数
            if ( !m.getValue().isDefaultValueFlag() ){
                segCount++;
            }
        }

        // 没有字段
        if ( (segCount == 0) ){
            return "";
        }

        // 删除最后一个逗号
        editBuffer.delete(editBuffer.length()-1,editBuffer.length());

        // 拼接where条件, 没有条件返回空串
        StringBuilder whereBuffer = assembleWhereString(arr, LogicValue.UPDATE);
        if ( whereBuffer.length() == 0 ){
            editBuffer.setLength(0);
        } else {
            editBuffer.append(whereBuffer);// 条件拼接
        }

        return editBuffer.toString();
    }

    /**
     * 方法: delete
     * 描述: 生成SQL的删除语句
     * 参数：arr: 值对象, tableName: 表名
     */
    private String delete(Map<String, ObjectAttribute> arr, String tableName) {
        StringBuilder deleteBuffer = new StringBuilder("DELETE FROM " + tableName);

        StringBuilder whereBuffer = assembleWhereString(arr, LogicValue.DELETE);
        // 没有条件: 不让执行删除(危险)
        if ( whereBuffer.length() == 0 ){
            deleteBuffer.setLength(0);
        } else {
            deleteBuffer.append(whereBuffer); // 条件拼接
        }

        return deleteBuffer.toString();
    }

    /**
     * 方法: select
     * 描述: 生成SQL的查询语句
     * 参数：arr: 值对象, tableName: 表名
     */
    private String select(Map<String, ObjectAttribute> arr, String tableName, PageRecord page) {
        // 处理翻页对象,保证其正确性
        PageRecord pageRecord = this.processPageRecordObject(page);

        StringBuilder queryBuffer = new StringBuilder("SELECT ");

        short segCount = 0;
        for ( Map.Entry<String, ObjectAttribute> m : arr.entrySet() ){
            // 字段处理: 隐藏字段获取
            if( m.getValue().getLogicActValueMap().containsKey(LogicValue.FP_GET_HIDE) ) {
                continue;
            }

            // 组装列字段
            if ( m.getKey().equals(m.getValue().getKeyName()) ){// 相等
                queryBuffer.append("`").append(m.getKey()).append("`").append(",");
            } else { // 不等,做别名
                queryBuffer.append("`").append(m.getKey()).append("`").append(" AS ").append("`").append(m.getValue().getKeyName()).append("`").append(",");
            }

            segCount++;
        }

        // 没有字段
        if ( segCount == 0){
            return "";
        }

        // 先处理排序字段
        StringBuilder sortSqlStr = assembleSortString(arr, LogicValue.SELECT);

        // 删除最后一个逗号
        queryBuffer.delete(queryBuffer.length()-1,queryBuffer.length());

        queryBuffer.append(" FROM ").append(tableName);

        // 获取条件
        queryBuffer.append(assembleWhereString(arr, LogicValue.SELECT));

        // 获取排序
        queryBuffer.append(sortSqlStr);

        // 条数限制
        long startIndex = (pageRecord.getPageNo()-1) * pageRecord.getPageSize();
        queryBuffer.append(" Limit ").append(startIndex).append(",").append(pageRecord.getPageSize());

        return queryBuffer.toString();
    }

    /**
     * 方法: getObjValue
     * 描述: 根据类型获取对象的值
     * 用于条件判断时, 获取对应的值
     * */
    private StringBuilder getObjValue(Object obj, Type type){
        StringBuilder addBuffer = new StringBuilder("");

        if ( obj == null ){
            addBuffer.append("null");
            return addBuffer;
        }

        if ( type == null ){
            type = obj.getClass();
        }

        String segType = type.getTypeName();
        String objType = obj.getClass().getTypeName();

        // 类型为字符串, 加引号
        if ( segType.endsWith("String") ){
            if ( obj.toString().equals("NULL") ){
                addBuffer.append(obj);
            } else {
                addBuffer.append("'").append(obj).append("'");
            }
        } else if( segType.endsWith("Date") ) {
            if ( objType.endsWith("Date") ){
                addBuffer.append("'").append(DateToString((Date) obj)).append("'");
            } else {
                addBuffer.append("'").append(obj).append("'");
            }
        } else if ( segType.contains(".List<") ) { // 是列表, 转json字符串
            addBuffer.append("'").append(GsonUtil.jsonString(obj)).append("'");
        } else if ( segType.endsWith("Object") ) { // 是对象
            addBuffer.append("'").append(GsonUtil.jsonString(obj)).append("'");
        } else if ( segType.endsWith("BigDecimal")
                || segType.endsWith("Long")
                || segType.endsWith("Integer")
                || segType.endsWith("Short")
                || segType.endsWith("Byte") ){
            if ( obj.toString().isEmpty() ){
                addBuffer.append("NULL");
            } else {
                addBuffer.append(obj);
            }
        } else {
            addBuffer.append("'").append(obj).append("'");
        }

        return addBuffer;
    }

    /**
     * 方法: getObjListValue
     * 描述: 根据类型获取对象的值(对象为列表)
     * 用于插入和更新时, 对传入值进行转换
     * */
    private StringBuilder getObjListValue(List<Object> objectList, Type type){
        StringBuilder addBuffer = new StringBuilder("");

        if ( objectList == null || objectList.size() == 0 ){
            addBuffer.append("null");
            return addBuffer;
        }

        String segType = null;

        if ( type != null ){
            segType = type.getTypeName();
        }

        // 处理字符串: 循环拼接
        if ( segType != null && segType.endsWith("String") ){
            String joinString = StringUtils.join(objectList,",");
            if ( joinString != null && joinString.equals("NULL") ){
                addBuffer.append(joinString);
            } else {
                addBuffer.append("'").append(joinString).append("'");
            }

            return addBuffer;
        }

        /**
         * 只取一个的类型
         * */
        String objType = objectList.get(0).getClass().getTypeName();
        Object obj = objectList.get(0);

        if ( segType == null ){
            segType = objType;
        }

        if ( segType.endsWith("Date") && objType.endsWith("Date") ) {
            addBuffer.append("'").append(DateToString((Date) obj)).append("'");
        } else if (segType.contains(".List<")) { // 是列表, 转json字符串
            addBuffer.append("'").append(GsonUtil.jsonString(obj)).append("'");
        } else if (segType.endsWith("Object")) { // 是对象
            addBuffer.append("'").append(GsonUtil.jsonString(obj)).append("'");
        } else if ( segType.endsWith("BigDecimal")
                || segType.endsWith("Long")
                || segType.endsWith("Integer")
                || segType.endsWith("Short")
                || segType.endsWith("Byte") ){
            if ( obj == null || obj.toString().isEmpty() ){
                addBuffer.append("NULL");
            } else {
                addBuffer.append(obj);
            }
        } else {
            addBuffer.append("'").append(obj).append("'");
        }

        return addBuffer;
    }

    /**
     * 方法: assembleSortString
     * 描述: 获取SQL排序字符串
     * */
    private StringBuilder assembleSortString(Map<String, ObjectAttribute> arr, LogicValue crudMethod) {
        StringBuilder sortBuffer = new StringBuilder();

        // 设置条件
        boolean condFlag = false;

        for ( Map.Entry<String, ObjectAttribute> m : arr.entrySet() ){

            // 查询操作, 且有排序配置
            if ( crudMethod.equals(LogicValue.SELECT)
                    && m.getValue().getLogicActValueMap().containsKey(LogicValue.SGV_SORT)
            ){
                // 查询是否有传入排序字符
                String sortStr = "";
                if ( m.getValue().getObj().size() > 0 ){
                    int i = 0;
                    for ( ; i < m.getValue().getObj().size(); i++ ){
                        if( m.getValue().getObj().get(i).toString().equals("SORT:ASC") ){
                            sortStr = "ASC";
                            break;
                        } else if( m.getValue().getObj().get(i).toString().equals("SORT:DESC") ){
                            sortStr = "DESC";
                            break;
                        }
                    }
                    // 删除排序字段
                    if ( !sortStr.isEmpty() ){
                        m.getValue().getObj().remove(i);
                        sortBuffer.append(" ORDER BY ").append("`").append(m.getValue().getTableKeyName()).append("`").append(" ").append(sortStr).append(" ");

                        // 已设置排序: 可直接跳出
                        condFlag = true;
                    }
                }

                /* // 使用设置的默认排序
                if( m.getValue().getLogicActValueMap().get(LogicValue.SGV_SORT).size() > 0 ){
                    sortBuffer.append(m.getValue().getLogicActValueMap().get(LogicValue.SGV_SORT).get(0)).append(" ");
                } else {
                    sortBuffer.append("DESC ");
                }*/
            }

            if ( condFlag ){
                break;
            }
        }

        return sortBuffer;
    }

    /**
     * 方法: assembleWhereString
     * 描述: 获取值对象的
     * */
    private StringBuilder assembleWhereString(Map<String, ObjectAttribute> arr, LogicValue crudMethod) {
        StringBuilder whereBuffer = new StringBuilder(" WHERE ");

        short methodType = 0;
        if ( crudMethod.equals(LogicValue.SELECT) ){
            methodType = 1;
        } else if ( (crudMethod.equals(LogicValue.UPDATE) || crudMethod.equals(LogicValue.DELETE)) ){
            methodType = 2;
        }

        // 是否有唯一主键作为条件标志
        boolean isExistUniqueFlag = false;
        if ( methodType == 1 ){ // 方法类型为1的情况下: 不需要判断唯一主键是否存在于条件, 直接 赋值为true
            isExistUniqueFlag = true;
        }

        // 设置条件
        int conditionCount = 0;
        for ( Map.Entry<String, ObjectAttribute> m : arr.entrySet() ){

            if ( m.getValue() == null ){
                continue;
            }

            /**
             * 不满足条件的情况
             * 1、查询时,非主键不能作为条件
             * 2、更新字段时,必须是唯一主键才能作为条件
             * 3、删除时,必须是唯一主键才能作为条件
             * */
            if ( ((methodType == 1) && !m.getValue().isKeyFlag())
                    || ((methodType == 2) && !m.getValue().isUniqueFlag()) ){
                continue;
            }

            // 要使用的值对象: 根据情况用默认值或传入值
            List<Object> defObj = new ArrayList<>();

            // 自动填充条件值: 没有传入值时,使用配置默认值
            if ( (m.getValue().getObj().size() == 0) && m.getValue().getLogicActValueMap().containsKey(LogicValue.SGV_WHERE) ){
                int i = 0;
                while (m.getValue().getLogicActValueMap().get(LogicValue.SGV_WHERE).size() > i){
                    defObj.add(m.getValue().getLogicActValueMap().get(LogicValue.SGV_WHERE).get(i));
                    i++;
                }
            } else if ( m.getValue().getObj() != null ) {
                defObj = m.getValue().getObj();
            }

            // 组装条件字符串
            if ( defObj.size() > 0 ){

                // 是唯一主键: 设置存在标志(表示条件中有使用唯一组件进行条件组装)
                if ( m.getValue().isUniqueFlag() ){
                    isExistUniqueFlag = true;
                }

                /**
                 * 根据字段逻辑值实现条件拼装
                 * */
                if (m.getValue().getLogicActValueMap().containsKey(LogicValue.WL_MATCH)
                        && (m.getValue().getLogicActValueMap().get(LogicValue.WL_MATCH).size() == 2)
                ){ // 条件左右通配
                    StringBuilder whStr = new StringBuilder();

                    String leftActValue = m.getValue().getLogicActValueMap().get(LogicValue.WL_MATCH).get(0);
                    String rightActValue = m.getValue().getLogicActValueMap().get(LogicValue.WL_MATCH).get(1);

                    for ( Object obj : defObj ) {
                        whStr.append("`").append(m.getKey()).append("`").append(" LIKE '").append(leftActValue).
                                append(obj).append(rightActValue).append("' OR ");
                    }
                    whStr.delete(whStr.length()-4,whStr.length());// 删除最后的拼接符

                    whereBuffer.append(" (").append(whStr).append(") AND ");
                } else if (m.getValue().getLogicActValueMap().containsKey(LogicValue.WL_RANGE)
                        && (m.getValue().getLogicActValueMap().get(LogicValue.WL_RANGE).size() == 2)
                ) { // 范围匹配
                    StringBuilder whStr = new StringBuilder();

                    String leftActValue = m.getValue().getLogicActValueMap().get(LogicValue.WL_RANGE).get(0);
                    String rightActValue = m.getValue().getLogicActValueMap().get(LogicValue.WL_RANGE).get(1);

                    // 左值
                    if ( ! defObj.get(0).toString().isEmpty() ) {
                        whStr.append("`").append(m.getKey()).append("`").append(" ").append(leftActValue).append(" ")
                                .append(getObjValue(defObj.get(0), m.getValue().getType()))
                                .append(" AND ");
                    }

                    // 右值
                    if ( defObj.size() > 1 ){
                        if ( ! defObj.get(1).toString().isEmpty() ) {
                            whStr.append("`").append(m.getKey()).append("`").append(" ").append(rightActValue).append(" ")
                                    .append(getObjValue(defObj.get(1), m.getValue().getType()))
                                    .append(" AND ");
                        }
                    }
                    whStr.delete(whStr.length()-5,whStr.length());// 删除最后的拼接符

                    whereBuffer.append(" (").append(whStr).append(") AND ");
                } else { // 默认全匹配
                    StringBuilder whStr = new StringBuilder();
                    for ( Object obj : defObj ) {
                        whStr.append(getObjValue(obj, m.getValue().getType())).append(",");
                    }
                    whStr.delete(whStr.length() - 1, whStr.length());// 删除最后一个字符

                    whereBuffer.append("`").append(m.getKey()).append("`").append(" IN (").append(whStr).append(") AND ");
                }

                conditionCount++;
            }
            /*else if ( methodType == 2 ){ // 删除或更新时,必须要有唯一主键输入
                throw new SimpleException(100001, m.getValue().getKeyName()+"不能为空");
            }*/
        }

        // 有条件
        if ( conditionCount > 0 ){
            whereBuffer.delete(whereBuffer.length()-4,whereBuffer.length());// 删除最后一个连接符
        } else { // 没有条件,清空字符串
            whereBuffer.setLength(0);
        }

        // 没有使用到唯一主键作为条件: 条件无效
        if ( ! isExistUniqueFlag ){
            whereBuffer.setLength(0);
        }

        return whereBuffer;
    }

    /** 日期转字符串 */
    private String DateToString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(date);
    }

}
