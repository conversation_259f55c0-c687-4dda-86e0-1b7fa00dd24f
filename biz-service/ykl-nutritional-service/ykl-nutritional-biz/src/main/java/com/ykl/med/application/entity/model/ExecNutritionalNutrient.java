package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import java.util.Date;

/**
 * 营养方案营养素执行表;
 * <AUTHOR> xkli
 * @date : 2023-12-12
 */
@Data
@Schema(description = "营养方案营养素执行表")
@TableName("t_exec_nutritional_nutrient")
public class ExecNutritionalNutrient {

    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long id ;

    @Schema(description = "患者ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,NOTNULL:NN_PUT,FIELD_PROC:FP_PUT_WHERE")
    private Long patientId ;

    @Schema(description = "营养方案ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private Long nutritionalId ;

    @Schema(description = "执行时间（打卡）", example = "")
    @TableId
    @TableLogic("FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date execTime ;

    @Schema(description = "数量（单位：g）", example = "")
    private Integer quantity ;

    @Schema(description = "营养素类型（如：蛋白质，碳水化合物）", example = "")
    @TableId
    private String nutrientType ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT:ENABLE")
    private String status ;

    @Schema(description = "删除标志；0未删，1已删", example = "")
    @TableId
    @TableLogic("FILLED:SGV_DELETE:1,FILLED:SGV_WHERE:0,FIELD_PROC:FP_GET_HIDE")
    private Byte deleteFlag ;

}
