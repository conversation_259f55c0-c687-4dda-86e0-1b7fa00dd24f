package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.common.util.object.ObjectUtils;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import java.util.Date;

/**
 * 营养方案评估表;
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案评估表")
@TableName("t_nutritional_assessment")
public class NutritionalAssessment {

    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long  id ;

    @Schema(description = "患者ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,NOTNULL:NN_PUT,FIELD_PROC:FP_PUT_WHERE")
    private Long patientId ;

    @Schema(description = "营养方案ID（评估完成后，可挂载方案）", example = "")
    @TableId
    private Long nutritionalId ;

    @Schema(description = "表单ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST")
    private Long formId ;

    @Schema(description = "表单名称", example = "")
    @TableId
    @TableLogic("WHERE:WL_MATCH:%:%")
    private String formName ;

    @Schema(description = "表单类别（如：症状/随访/运动康复/营养康复）", example = "")
    private String formCategory ;

    @Schema(description = "表单类型（如：量表/问卷）", example = "")
    @TableId
    private String formType ;

    @Schema(description = "表单内容（格式自定义）", example = "")
    private Object formContent ;

    @Schema(description = "表单问题数量", example = "")
    private Short formQuestionCount ;

    @Schema(description = "表单总分数", example = "")
    private Short formTotalScore ;

    @Schema(description = "是否必选；0非必选，1必选", example = "")
    private Byte required ;

    @Schema(description = "线上标志；0线下，1线上", example = "")
    @TableId
    private Byte online ;

    @Schema(description = "发送标志；0未发送，1已发送",example = "")
    @TableId
    private Byte sendFlag ;

    @Schema(description = "填报标志；0未填报，1已填报", example = "")
    @TableId
    private Byte fillFlag ;

    @Schema(description = "填报时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_UPDATE:fillFlag=1,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date fillTime ;

    @Schema(description = "评估分数", example = "")
    private Integer scores ;

    @Schema(description = "评估结果", example = "")
    private String result ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT:ENABLE")
    private String status ;

}
