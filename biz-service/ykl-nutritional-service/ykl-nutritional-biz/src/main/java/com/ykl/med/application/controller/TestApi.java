package com.ykl.med.application.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
public class TestApi {
    @RequestMapping("/test")
    public String testApi(HttpServletRequest request) {
        // 返回信息
        String str = "I am ok";
        String keyStr = "word";
        if( request.getParameterMap().containsKey(keyStr)
                && (request.getParameter(keyStr).length() > 0)
        ) {
            str += "," + request.getParameter(keyStr);
        }

        return str;
    }
}
