package com.ykl.med.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *  营养方案
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@TableName(value = "t_nutritional")
public class NutritionalDO extends BaseDO {

    /** 患者ID */
    private Long patientId ;

    /** 目标 */
    private String goal ;

    /** 方案时间数值 */
    private Short timeValue ;

    /** 方案时间数值单位（天/周/月/年） */
    private String timeValueUnit ;

    /** 开始时间 */
    private LocalDateTime startTime ;

    /** 结束时间 */
    private LocalDateTime endTime ;

    /** 患者姓名(字段及以下为患者基本信息) */
    private String name ;

    /** 年龄 */
    private Byte age ;

    /** 性别 */
    private String sex ;

    /** 身高 */
    private Double height ;

    /** 体重 */
    private Double weight ;

    /** BMI值 */
    private Short bmi ;

    /** 职业 */
    private String profession ;

    /** 诊断（主病） */
    private String diagnosis ;

    /** 当前劳动等级 */
    private String laborCapacity ;

    /** 理想体重 */
    private Double suitableWeight ;

    /** 饮食情况 */
    private String dietaryStatus ;

    /** 热量（字段及以下为每日营养素摄入） */
    private Integer kilocalorie ;

    /** 碳水化合物（单位：g） */
    private Integer carbohydrate ;

    /** 蛋白质（单位：g） */
    private Integer protein ;

    /** 脂肪（单位：g） */
    private Integer fat ;

    /** 水（单位：ml） */
    private Integer water ;

    /** 主食（单位：g；字段及以下为膳食处方） */
    private Integer stapleFood ;

    /** 蔬菜（单位：g） */
    private Integer vegetable ;

    /** 水果（单位：g） */
    private Integer fruit ;

    /** 肉类（单位：g） */
    private Integer meat ;

    /** 鱼虾（单位：g） */
    private Integer fish ;

    /** 蛋类（单位：g） */
    private Integer egg ;

    /** 牛奶（单位：ml） */
    private Integer milk ;

    /** 豆类（单位：g） */
    private Integer bean ;

    /** 油（单位：g） */
    private Integer oil ;

    /** 食盐（单位：g） */
    private Integer salt ;

    /** 备注 */
    private String remark ;

    /** 创建者ID【实体】 */
    private Long createUserId ;

    /** 最后一次操作者ID【实体】 */
    private Long lastUserId ;

    /** 状态；启用：ENABLE，禁用：DISABLE */
    private String status ;

    /** 删除标志；0未删，1已删 */
    private Byte deleteFlag ;

}
