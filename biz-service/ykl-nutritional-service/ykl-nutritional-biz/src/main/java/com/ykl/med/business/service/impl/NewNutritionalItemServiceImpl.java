package com.ykl.med.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.business.entity.NutritionalItemDO;
import com.ykl.med.business.mapper.NutritionalItemMapper;
import com.ykl.med.business.service.NewNutritionalItemService;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.nutritional.entity.dto.QuerySpecialMedicalFoodExtendParam;
import com.ykl.med.pharmacy.api.SpecialMedicalFoodFeign;
import com.ykl.med.pharmacy.entity.dto.SpecialMedicalFoodQueryDTO;
import com.ykl.med.pharmacy.entity.vo.SpecialMedicalFoodExtendVO;
import com.ykl.med.pharmacy.entity.vo.SpecialMedicalFoodNutrientVO;
import com.ykl.med.pharmacy.entity.vo.SpecialMedicalFoodVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/15
 */
@Slf4j
@Service
public class NewNutritionalItemServiceImpl extends ServiceImpl<NutritionalItemMapper, NutritionalItemDO> implements NewNutritionalItemService {

    @Resource
    private NutritionalItemMapper nutritionalItemMapper;
    @Resource
    private SpecialMedicalFoodFeign specialMedicalFoodFeign;

    @Override
    public List<SpecialMedicalFoodExtendVO> querySpecialMedicalFood(QuerySpecialMedicalFoodExtendParam param) {
        LambdaQueryWrapper<NutritionalItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NutritionalItemDO::getNutritionalId, param.getNutritionalId());
        List<NutritionalItemDO> nutritionalItemDOS = nutritionalItemMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(nutritionalItemDOS)) {
            return new ArrayList<>();
        }

        List<SpecialMedicalFoodExtendVO> list = new ArrayList<>();

        for (NutritionalItemDO nutritionalItemDO : nutritionalItemDOS) {
            PageResult<SpecialMedicalFoodVO> specialMedicalFoodVOPageResult = specialMedicalFoodFeign.crud("GET",
                    new SpecialMedicalFoodQueryDTO().setId(String.valueOf(nutritionalItemDO.getItemId())));
            if (CollectionUtils.isEmpty(specialMedicalFoodVOPageResult.getList())) {
                continue;
            }

            SpecialMedicalFoodVO specialMedicalFoodVO = specialMedicalFoodVOPageResult.getList().get(0);

            SpecialMedicalFoodExtendVO specialMedicalFoodExtendVO = CopyPropertiesUtil.normalCopyProperties(specialMedicalFoodVO,SpecialMedicalFoodExtendVO.class);
            specialMedicalFoodExtendVO.setUsageName(nutritionalItemDO.getUsageName());
            specialMedicalFoodExtendVO.setOnceDosage(nutritionalItemDO.getOnceDosage());
            specialMedicalFoodExtendVO.setFrequency(nutritionalItemDO.getFrequencyName());
            specialMedicalFoodExtendVO.setSpec(nutritionalItemDO.getQuantity());
            specialMedicalFoodExtendVO.setSpecUnit(nutritionalItemDO.getQuantityUnit());
            specialMedicalFoodExtendVO.setNutrient(CopyPropertiesUtil.normalCopyProperties(specialMedicalFoodVO, SpecialMedicalFoodNutrientVO.class));

            list.add(specialMedicalFoodExtendVO);
        }

        return list;
    }
}
