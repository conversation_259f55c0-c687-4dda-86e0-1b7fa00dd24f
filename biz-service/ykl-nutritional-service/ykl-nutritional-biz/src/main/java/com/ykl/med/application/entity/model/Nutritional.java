package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 营养方案表;
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案表")
@TableName("t_nutritional")
public class Nutritional {

    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE,TABLE:TR_O2M:nutritionalItemList:nutritional_id:nutritionalCareAdviceList:nutritional_id")
    private Long id ;

    @Schema(description = "患者ID", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,NOTNULL:NN_PUT,FIELD_PROC:FP_PUT_WHERE,TABLE:TR_O2M:nutritionalItemList:patient_id:nutritionalCareAdviceList:patient_id")
    private Long patientId ;

    @Schema(description = "目标", example = "")
    @TableLogic("NOTNULL:NN_POST")
    private String goal ;

    @Schema(description = "方案时间数值", example = "")
    @TableLogic("NOTNULL:NN_POST")
    private Short timeValue ;

    @Schema(description = "方案时间数值单位（天/周/月/年）", example = "")
    @TableLogic("NOTNULL:NN_POST")
    private String timeValueUnit ;

    @Schema(description = "开始时间", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date startTime ;

    @Schema(description = "结束时间", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date endTime ;

    @Schema(description = "患者姓名(字段及以下为患者基本信息)", example = "")
    private String name ;

    @Schema(description = "年龄", example = "")
    private Byte age ;

    @Schema(description = "性别", example = "")
    private String sex ;

    @Schema(description = "身高", example = "")
    private Double height ;

    @Schema(description = "体重", example = "")
    private Double weight ;

    @Schema(description = "BMI值", example = "")
    private Short bmi ;

    @Schema(description = "职业",example = "")
    private String profession ;

    @Schema(description = "诊断（主病）", example = "")
    private String diagnosis ;

    @Schema(description = "当前劳动等级", example = "")
    private String laborCapacity ;

    @Schema(description = "理想体重", example = "")
    private Double suitableWeight ;

    @Schema(description = "饮食情况", example = "")
    private String dietaryStatus ;

    @Schema(description = "热量（字段及以下为每日营养素摄入）", example = "")
    private Integer kilocalorie ;

    @Schema(description = "碳水化合物（单位：g）", example = "")
    private Integer carbohydrate ;

    @Schema(description = "蛋白质（单位：g）", example = "")
    private Integer protein ;

    @Schema(description = "脂肪（单位：g）", example = "")
    private Integer fat ;

    @Schema(description = "水（单位：ml）", example = "")
    private Integer water ;

    @Schema(description = "主食（单位：g；字段及以下为膳食处方）", example = "")
    private Integer stapleFood ;

    @Schema(description = "蔬菜（单位：g）", example = "")
    private Integer vegetable ;

    @Schema(description = "水果（单位：g）", example = "")
    private Integer fruit ;

    @Schema(description = "肉类（单位：g）", example = "")
    private Integer meat ;

    @Schema(description = "鱼虾（单位：g）", example = "")
    private Integer fish ;

    @Schema(description = "蛋类（单位：g）", example = "")
    private Integer egg ;

    @Schema(description = "牛奶（单位：ml）", example = "")
    private Integer milk ;

    @Schema(description = "豆类（单位：g）", example = "")
    private Integer bean ;

    @Schema(description = "油（单位：g）", example = "")
    private Integer oil ;

    @Schema(description = "食盐（单位：g）", example = "")
    private Integer salt ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT:ENABLE")
    private String status ;

    @Schema(description = "删除标志；0未删，1已删", example = "")
    @TableId
    @TableLogic("FILLED:SGV_DELETE:1,FILLED:SGV_WHERE:0,FIELD_PROC:FP_GET_HIDE")
    private Byte deleteFlag ;

    @Schema(description = "营养康复项目（特医食品）列表", example = "")
    private List<NutritionalItem> nutritionalItemList;

    @Schema(description = "指导建议项目列表", example = "")
    private List<NutritionalCareAdvice> nutritionalCareAdviceList;

}
