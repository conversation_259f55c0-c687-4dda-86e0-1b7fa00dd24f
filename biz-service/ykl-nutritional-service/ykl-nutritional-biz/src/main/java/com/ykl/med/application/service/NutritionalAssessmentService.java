package com.ykl.med.application.service;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.ykl.med.application.entity.HttpRequestData;
import com.ykl.med.application.entity.ResponseCode;
import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.application.entity.ValueObject;
import com.ykl.med.application.entity.model.NutritionalAssessment;
import com.ykl.med.application.interfaces.IService;
import com.ykl.med.application.repository.MapperRepository;
import com.ykl.med.config.SimpleException;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class NutritionalAssessmentService implements IService {
    @Override
    public String serviceName() {
        return "nutritionalAssessment";
    }

    @Override
    public ResponseModel crud(HashMap requestObj)
            throws IllegalAccessException, SimpleException, InstantiationException, ParseException, NoSuchFieldException {

        // 定义值对象
        NutritionalAssessment SObj = new NutritionalAssessment();

        ResponseModel responseModel = new ResponseModel();

        MapperRepository mapperRepository = new MapperRepository();

        // 获取传入数据Map
        HttpRequestData httpRequestData = mapperRepository.inputParamsMap(SObj, requestObj);

        // 循环判断值对象参数的正确性
        for ( ValueObject valueObject : httpRequestData.getValueObjectList() ) {
            // 判断必填参数
            SimpleException simpleException = mapperRepository.checkValueObjectField(
                    valueObject, httpRequestData.getMethod(), true);
            if (simpleException.getCode() != 0) {
                return responseModel.response(ResponseCode.INVALID_ARGUMENT, simpleException.getMessage());
            }
        }

        /**
         * 执行sql: 生成sql, 执行, 返回结果
         * */
        // 获取SQL执行服务: 获取失败
        ASqlExecService aSqlExecService = SpringContentUtils.getBean(ASqlExecService.class);
        if ( aSqlExecService == null ) {
            return responseModel.response(ResponseCode.INVALID_ARGUMENT, "SQL执行器Bean加载失败");
        }

        // 执行SQL
        int execNumber = 0;
        List<ResponseModel> errInfos = new ArrayList<>();
        for ( ValueObject valueObject : httpRequestData.getValueObjectList() ){
            ResponseModel responseModel1 = aSqlExecService.sqlExec(valueObject, httpRequestData.getPageRecord());
            // 值对象执行失败: 记录
            if ( responseModel1.getCode() != 0 ){
                responseModel1.setData(httpRequestData.getDataObject().get(execNumber));
                errInfos.add(responseModel1);
            } else {
                responseModel = responseModel1;
            }

            // 值对象列表对应的序号
            execNumber++;
        }

        if ( errInfos.size() != 0 ){
            responseModel.response(ResponseCode.EXEC_SQL_FAIL, "写数据库失败");
            responseModel.setData(errInfos);
        }

        return responseModel;
    }
}
