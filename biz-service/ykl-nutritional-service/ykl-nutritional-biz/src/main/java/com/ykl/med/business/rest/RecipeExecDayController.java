package com.ykl.med.business.rest;

import com.ykl.med.business.service.PatientRecipeExecDayService;
import com.ykl.med.nutritional.api.RecipeExecDayFeign;
import com.ykl.med.nutritional.entity.vo.req.PunchTheClockReqVO;
import com.ykl.med.nutritional.entity.vo.req.QueryPunchTheClockReqVO;
import com.ykl.med.nutritional.entity.vo.req.QueryPunchTheClockStatisticsReqVO;
import com.ykl.med.nutritional.entity.vo.resp.PatientRecipeExecDayVO;
import com.ykl.med.nutritional.entity.vo.resp.PunchTheClockStatisticsVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 食谱打卡
 *
 * <AUTHOR>
 * @since 2024/6/27
 */
@RestController
@RequestMapping("/exec/recipe")
public class RecipeExecDayController implements RecipeExecDayFeign {

    @Resource
    private PatientRecipeExecDayService patientRecipeExecDayService;

    /**
     * 今日打卡数据
     *
     * @param param QueryPunchTheClockReqVO
     * @return PatientRecipeExecDayVO
     */
    @Override
    @PostMapping("/queryPunchTheClockToday")
    public PatientRecipeExecDayVO queryPunchTheClockToday(@Valid @RequestBody QueryPunchTheClockReqVO param) {
        return patientRecipeExecDayService.getPunchTheClockToday(param);
    }

    /**
     * 营养打卡
     *
     * @param param PunchTheClockReqVO
     */
    @Override
    @PostMapping("/punchTheClock")
    public void punchTheClock(@Valid @RequestBody PunchTheClockReqVO param) {
        patientRecipeExecDayService.punchTheClock(param);
    }


    /**
     * 打卡列表
     *
     * @param param QueryPunchTheClockReqVO
     */
    @Override
    @PostMapping("/getPunchTheClockList")
    public List<PatientRecipeExecDayVO> getPunchTheClockList(@Valid @RequestBody QueryPunchTheClockReqVO param) {
        return patientRecipeExecDayService.getPunchTheClockList(param);
    }


    /**
     * 打卡统计
     *
     * @param param QueryPunchTheClockStatisticsReqVO
     */
    @Override
    @PostMapping("/punchTheClockStatistics")
    public PunchTheClockStatisticsVO punchTheClockStatistics(@Valid @RequestBody QueryPunchTheClockStatisticsReqVO param) {
        return patientRecipeExecDayService.punchTheClockStatistics(param);
    }

    /**
     * 初始化 食谱-每日打卡 (定时任务)
     */
    @Override
    @GetMapping("/initRecommendRecipeExecDay")
    public void initRecommendRecipeExecDay(@RequestParam(name = "nutritionalId") Long nutritionalId,
                                           @RequestParam(name = "patientId") Long patientId) {
        patientRecipeExecDayService.initRecommendRecipeExecDay(nutritionalId, patientId);
    }





}
