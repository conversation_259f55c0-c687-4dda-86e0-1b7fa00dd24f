package com.ykl.med.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykl.med.business.entity.PatientRecipeExecDayDO;
import com.ykl.med.business.mapper.PatientRecipeExecDayMapper;
import com.ykl.med.business.service.NewNutritionalService;
import com.ykl.med.business.service.PatientRecipeExecDayService;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.nutritional.entity.vo.NewNutritionalVO;
import com.ykl.med.nutritional.entity.vo.req.PunchTheClockReqVO;
import com.ykl.med.nutritional.entity.vo.req.QueryPunchTheClockReqVO;
import com.ykl.med.nutritional.entity.vo.req.QueryPunchTheClockStatisticsReqVO;
import com.ykl.med.nutritional.entity.vo.resp.KilocalorieStatisticsVO;
import com.ykl.med.nutritional.entity.vo.resp.NutritionalStatisticsVO;
import com.ykl.med.nutritional.entity.vo.resp.PatientRecipeExecDayVO;
import com.ykl.med.nutritional.entity.vo.resp.PunchTheClockStatisticsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/25
 */
@Slf4j
@Service
public class PatientRecipeExecDayServiceImpl extends ServiceImpl<PatientRecipeExecDayMapper, PatientRecipeExecDayDO> implements PatientRecipeExecDayService {


    @Resource
    private NewNutritionalService nutritionalService;
    @Resource
    private PatientRecipeExecDayMapper patientRecipeExecDayMapper;

    @Override
    public void punchTheClock(PunchTheClockReqVO param) {
        // 获取患者的营养方案
        NewNutritionalVO nutritionalVO = nutritionalService.getByPatientId(param.getPatientId());
        if (nutritionalVO == null) {
            return;
        }

        LambdaQueryWrapper<PatientRecipeExecDayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatientRecipeExecDayDO::getNutritionalId, nutritionalVO.getId());
        queryWrapper.eq(PatientRecipeExecDayDO::getPlanTime, DateTimeUtils.getNowDay());
        queryWrapper.eq(PatientRecipeExecDayDO::getPatientId, param.getPatientId());
        queryWrapper.eq(PatientRecipeExecDayDO::getDeleteFlag, false);
        queryWrapper.orderByDesc(PatientRecipeExecDayDO::getPlanTime);
        PatientRecipeExecDayDO patientRecipeExecDayDO = patientRecipeExecDayMapper.selectOne(queryWrapper);
        if (patientRecipeExecDayDO == null) {
            create(nutritionalVO.getId(), param.getPatientId(), LocalDate.now(), true, param.getProgressBar());
        } else {
            patientRecipeExecDayDO.setClockInFlag(true);
            patientRecipeExecDayDO.setProgressBar(param.getProgressBar());
            patientRecipeExecDayMapper.updateById(patientRecipeExecDayDO);
        }
    }

    public void create(Long nutritionalId, Long patientId, LocalDate time, Boolean clockFlag, Integer progressBar) {
        PatientRecipeExecDayDO createPatientRecipeExecDay = new PatientRecipeExecDayDO();
        createPatientRecipeExecDay.setNutritionalId(nutritionalId);
        createPatientRecipeExecDay.setPatientId(patientId);
        createPatientRecipeExecDay.setClockInFlag(clockFlag);
        createPatientRecipeExecDay.setPlanTime(time);
        createPatientRecipeExecDay.setProgressBar(progressBar);
        createPatientRecipeExecDay.setDeleteFlag(false);
        patientRecipeExecDayMapper.insert(createPatientRecipeExecDay);
    }

    @Override
    public List<PatientRecipeExecDayVO> getPunchTheClockList(QueryPunchTheClockReqVO param) {
        // 获取患者的营养方案
        NewNutritionalVO nutritionalVO = nutritionalService.getByPatientId(param.getPatientId());
        if (nutritionalVO == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<PatientRecipeExecDayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatientRecipeExecDayDO::getNutritionalId, nutritionalVO.getId());
        if (param.getStartTime() != null) {
            queryWrapper.ge(PatientRecipeExecDayDO::getPlanTime, param.getStartTime());
        }
        if (param.getEndTime() != null) {
            queryWrapper.le(PatientRecipeExecDayDO::getPlanTime, param.getEndTime());
        }
        queryWrapper.eq(PatientRecipeExecDayDO::getDeleteFlag, false);
        queryWrapper.orderByDesc(PatientRecipeExecDayDO::getPlanTime);
        List<PatientRecipeExecDayDO> patientRecipePlanDOS = patientRecipeExecDayMapper.selectList(queryWrapper);

        return CopyPropertiesUtil.normalCopyProperties(patientRecipePlanDOS, PatientRecipeExecDayVO.class);
    }

    @Override
    public void initRecommendRecipeExecDay(Long nutritionalId, Long patientId) {
        // 获取患者的营养方案
        NewNutritionalVO nutritionalVO = nutritionalService.getById(nutritionalId);
        if (nutritionalVO == null) {
            return;
        }

        LocalDate date = DateTimeUtils.getNowDay().minusDays(1);

        LambdaQueryWrapper<PatientRecipeExecDayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatientRecipeExecDayDO::getPlanTime, date);
        queryWrapper.eq(PatientRecipeExecDayDO::getNutritionalId, nutritionalId);
        queryWrapper.eq(PatientRecipeExecDayDO::getPatientId, patientId);
        queryWrapper.eq(PatientRecipeExecDayDO::getDeleteFlag, false);
        queryWrapper.orderByDesc(PatientRecipeExecDayDO::getPlanTime);
        List<PatientRecipeExecDayDO> patientRecipeExecDayDO = patientRecipeExecDayMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(patientRecipeExecDayDO)) {
            create(nutritionalId, patientId, date, false, 0);
        }
    }

    @Override
    public PatientRecipeExecDayVO getPunchTheClockToday(QueryPunchTheClockReqVO param) {
        // 获取患者的营养方案
        NewNutritionalVO nutritionalVO = nutritionalService.getByPatientId(param.getPatientId());
        if (nutritionalVO == null) {
            return new PatientRecipeExecDayVO().setClockInFlag(false).setPlanTime(DateTimeUtils.getNowDay()).setProgressBar(0);
        }

        LambdaQueryWrapper<PatientRecipeExecDayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatientRecipeExecDayDO::getNutritionalId, nutritionalVO.getId());
        queryWrapper.eq(PatientRecipeExecDayDO::getPatientId, param.getPatientId());
        queryWrapper.eq(PatientRecipeExecDayDO::getPlanTime, DateTimeUtils.getNowDay());
        queryWrapper.eq(PatientRecipeExecDayDO::getDeleteFlag, false);
        queryWrapper.orderByDesc(PatientRecipeExecDayDO::getPlanTime);
        List<PatientRecipeExecDayDO> patientRecipePlanDOS = patientRecipeExecDayMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(patientRecipePlanDOS)) {
            return new PatientRecipeExecDayVO().setClockInFlag(false).setPlanTime(DateTimeUtils.getNowDay()).setProgressBar(0);
        }

        return CopyPropertiesUtil.normalCopyProperties(patientRecipePlanDOS.get(0), PatientRecipeExecDayVO.class);
    }

    @Override
    public PunchTheClockStatisticsVO punchTheClockStatistics(QueryPunchTheClockStatisticsReqVO param) {
        PunchTheClockStatisticsVO punchTheClockStatisticsVO = new PunchTheClockStatisticsVO();

        // 获取患者的营养方案
        NewNutritionalVO nutritionalVO = nutritionalService.getByPatientId(param.getPatientId());
        if (nutritionalVO == null) {
            return punchTheClockStatisticsVO;
        }

        List<Long> betweenDates = getBetweenDates(param.getStartTime(), param.getEndTime());
        punchTheClockStatisticsVO.setAxis(betweenDates);

        LocalDate startTime = DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getStartTime()).toLocalDate();
        LocalDate endTime = DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getEndTime()).toLocalDate();

        List<PatientRecipeExecDayVO> punchTheClockList = getPunchTheClockList(new QueryPunchTheClockReqVO().setPatientId(param.getPatientId()).setStartTime(startTime).setEndTime(endTime));


        punchTheClockStatisticsVO.setKilocalorieData(queryKilocalorieData(nutritionalVO, punchTheClockList, betweenDates));
        punchTheClockStatisticsVO.setNutritionalData(queryNutritionalData(nutritionalVO, punchTheClockList, betweenDates));

        return punchTheClockStatisticsVO;
    }

    private List<KilocalorieStatisticsVO> queryKilocalorieData(NewNutritionalVO nutritionalVO, List<PatientRecipeExecDayVO> punchTheClockList, List<Long> betweenDates) {
        BigDecimal kilocalorie = new BigDecimal("0");
        if (nutritionalVO.getKilocalorie() != null) {
            kilocalorie = new BigDecimal(nutritionalVO.getKilocalorie());
        }

        List<KilocalorieStatisticsVO> kilocalorieStatisticsVOS = new ArrayList<>();

        Map<LocalDate, List<PatientRecipeExecDayVO>> collect = punchTheClockList.stream().collect(Collectors.groupingBy(PatientRecipeExecDayVO::getPlanTime));

        for (Long betweenDate : betweenDates) {

            KilocalorieStatisticsVO kilocalorieStatisticsVO = new KilocalorieStatisticsVO();
            kilocalorieStatisticsVO.setTime(betweenDate);

            LocalDate localDate = DateTimeUtils.convertLocalDateTimeOfTimestamp(betweenDate).toLocalDate();
            List<PatientRecipeExecDayVO> patientRecipeExecDayVOS = collect.get(localDate);
            if (!CollectionUtils.isEmpty(patientRecipeExecDayVOS)) {
                kilocalorieStatisticsVO.setValue(kilocalorie.multiply(new BigDecimal(patientRecipeExecDayVOS.get(0).getProgressBar()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)));
            }

            kilocalorieStatisticsVOS.add(kilocalorieStatisticsVO);
        }

        return kilocalorieStatisticsVOS;
    }


    private List<NutritionalStatisticsVO> queryNutritionalData(NewNutritionalVO nutritionalVO, List<PatientRecipeExecDayVO> punchTheClockList, List<Long> betweenDates) {
        BigDecimal protein = new BigDecimal("0");
        BigDecimal fat = new BigDecimal("0");
        BigDecimal carbohydrate = new BigDecimal("0");

        if (nutritionalVO.getProtein() != null) {
            protein = new BigDecimal(nutritionalVO.getProtein());
        }
        if (nutritionalVO.getFat() != null) {
            fat = new BigDecimal(nutritionalVO.getFat());
        }
        if (nutritionalVO.getCarbohydrate() != null) {
            carbohydrate = new BigDecimal(nutritionalVO.getCarbohydrate());
        }

        List<NutritionalStatisticsVO> kilocalorieStatisticsVOS = new ArrayList<>();

        Map<LocalDate, List<PatientRecipeExecDayVO>> collect = punchTheClockList.stream().collect(Collectors.groupingBy(PatientRecipeExecDayVO::getPlanTime));

        for (Long betweenDate : betweenDates) {

            NutritionalStatisticsVO kilocalorieStatisticsVO = new NutritionalStatisticsVO();
            kilocalorieStatisticsVO.setTime(betweenDate);

            LocalDate localDate = DateTimeUtils.convertLocalDateTimeOfTimestamp(betweenDate).toLocalDate();
            List<PatientRecipeExecDayVO> patientRecipeExecDayVOS = collect.get(localDate);
            if (!CollectionUtils.isEmpty(patientRecipeExecDayVOS)) {
                kilocalorieStatisticsVO.setProteinValue(protein.multiply(new BigDecimal(patientRecipeExecDayVOS.get(0).getProgressBar()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)));
                kilocalorieStatisticsVO.setFatValue(fat.multiply(new BigDecimal(patientRecipeExecDayVOS.get(0).getProgressBar()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)));
                kilocalorieStatisticsVO.setCarbohydrateValue(carbohydrate.multiply(new BigDecimal(patientRecipeExecDayVOS.get(0).getProgressBar()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)));
            }

            kilocalorieStatisticsVOS.add(kilocalorieStatisticsVO);
        }

        return kilocalorieStatisticsVOS;
    }


    public static List<Long> getBetweenDates(Long startDate, Long endDate) {
        Date start = new Date(startDate);//定义起始日期
        Date end = new Date(endDate);//定义结束日期

        List<Long> result = new ArrayList<>();

        Date date = start;
        Calendar cd = Calendar.getInstance();//用Calendar 进行日期比较判断
        while (date.getTime() <= end.getTime()) {
            result.add(date.getTime());
            cd.setTime(date);
            cd.add(Calendar.DATE, 1);//增加一天 放入集合
            date = cd.getTime();
        }
        return result;
    }

}
