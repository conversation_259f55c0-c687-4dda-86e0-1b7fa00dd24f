package com.ykl.med.config;

public class SimpleException extends Exception {
    private static final long serialVersionUID = 1L;

    private Integer Code;
    private String Message;

    public SimpleException(){
        this.setCode(0);
        this.setMessage("ok");
    }

    public SimpleException(Integer code, String message) {
        this.setCode(code);
        this.setMessage(message);
    }

    public Integer getCode() {
        return Code;
    }

    public String getMessage() {
        return Message;
    }

    public void setCode(Integer code){
        Code = code;
    }

    public void setMessage(String message) {
        Message = message;
    }
}
