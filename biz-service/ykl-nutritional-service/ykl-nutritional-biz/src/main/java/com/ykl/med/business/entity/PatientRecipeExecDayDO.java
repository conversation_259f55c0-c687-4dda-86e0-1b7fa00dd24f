package com.ykl.med.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *  患者-膳食计划-每天执行
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@TableName(value = "t_patient_recipe_exec_day")
public class PatientRecipeExecDayDO extends BaseDO {

    /** 营养方案id */
    private Long nutritionalId;
    /** 患者id */
    private Long patientId;

    /** 是否打卡(0/false-未打卡,1/true-已打卡) */
    private Boolean clockInFlag;
    /** 打卡时间 */
    private LocalDate planTime;
    /** 进度条(0-100) */
    private Integer progressBar;

    /** 删除标志（false-未删除/true-已删除） */
    private Boolean deleteFlag;

}
