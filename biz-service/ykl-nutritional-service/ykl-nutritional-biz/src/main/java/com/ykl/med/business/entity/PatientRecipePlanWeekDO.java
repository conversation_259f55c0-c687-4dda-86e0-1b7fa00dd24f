package com.ykl.med.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;


/**
 *  患者-膳食计划-周计划
 * <AUTHOR>
 * @since 2024/6/19
 */
@Data
@TableName(value = "t_patient_recipe_plan_week")
public class PatientRecipePlanWeekDO extends BaseDO {

    /** 营养方案id */
    private Long nutritionalId;
    /** 患者id */
    private Long patientId;

    /** 周一的 食谱id */
    private Long recipeMonday;
    /** 周二的 食谱id */
    private Long recipeTuesday;
    /** 周三的 食谱id */
    private Long recipeWednesday;
    /** 周四的 食谱id */
    private Long recipeThursday;
    /** 周五的 食谱id */
    private Long recipeFriday;
    /** 周六的 食谱id */
    private Long recipeSaturday;
    /** 周天的 食谱id */
    private Long recipeSunday;

    /** 删除标志（false-未删除/true-已删除） */
    private Boolean deleteFlag;

}
