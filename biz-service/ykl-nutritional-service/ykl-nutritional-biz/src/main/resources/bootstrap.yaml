spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  application:
    name: ykl-nutritional-service
  profiles:
    active: local
server:
  port: 10026
  servlet:
    context-path: /ykl-nutritional-service


---
spring:
  config:
    activate:
      on-profile: prod
  cloud:
    nacos:
      config:
        server-addr: 192.168.110.183:8848
        namespace: cfaaa5d7-2d9d-4dd7-9407-89b97689f1a2
        file-extension: yaml
        username: nacos
        password: mXiMlPksz78
        prefix: ${spring.application.name}
        group: DEFAULT_GROUP
        refreshEnabled: false
        enabled: true
        shared-configs: shared-config.yaml # 使用的 Nacos 公共配置文件
      discovery:
        server-addr: 192.168.110.183:8848
        namespace: cfaaa5d7-2d9d-4dd7-9407-89b97689f1a2

---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      config:
        server-addr: 192.168.110.183:8848
        namespace: c1976931-f181-4711-b3b6-6de89094f8aa
        file-extension: yaml
        username: nacos
        password: mXiMlPksz78
        prefix: ${spring.application.name}
        group: DEFAULT_GROUP
        refreshEnabled: false
        enabled: true
        shared-configs: shared-config.yaml # 使用的 Nacos 公共配置文件
      discovery:
        server-addr: 192.168.110.183:8848
        namespace: c1976931-f181-4711-b3b6-6de89094f8aa


---
spring:
  config:
    activate:
      on-profile: pre
  cloud:
    nacos:
      config:
        server-addr: 192.168.110.183:8848
        namespace: 3c12be6c-ad01-4e67-9f21-fd5bb28a04e5
        file-extension: yaml
        username: nacos
        password: mXiMlPksz78
        prefix: ${spring.application.name}
        group: DEFAULT_GROUP
        refreshEnabled: false
        enabled: true
        shared-configs: shared-config.yaml # 使用的 Nacos 公共配置文件
      discovery:
        server-addr: 192.168.110.183:8848
        namespace: 3c12be6c-ad01-4e67-9f21-fd5bb28a04e5


---
spring:
  config:
    activate:
      on-profile: local
  cloud:
    nacos:
      config:
        server-addr: 192.168.110.183:8848
        namespace: c1976931-f181-4711-b3b6-6de89094f8aa
        file-extension: yaml
        username: nacos
        password: mXiMlPksz78
        prefix: ${spring.application.name}
        group: DEFAULT_GROUP
        refreshEnabled: false
        enabled: true
        shared-configs: shared-config.yaml # 使用的 Nacos 公共配置文件
      discovery:
        server-addr: 192.168.110.183:8848
        namespace: c1976931-f181-4711-b3b6-6de89094f8aa
