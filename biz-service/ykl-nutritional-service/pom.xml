<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ykl.med</groupId>
        <artifactId>biz-service</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>ykl-nutritional-service</artifactId>
    <version>0.0.1</version>
    <packaging>pom</packaging>

    <modules>
        <module>ykl-nutritional-api</module>
        <module>ykl-nutritional-biz</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.boot.version>2.7.16</spring.boot.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ykl.med</groupId>
                <artifactId>ykl-nutritional-api</artifactId>
                <version>0.0.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>