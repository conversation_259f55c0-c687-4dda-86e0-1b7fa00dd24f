package com.ykl.med.nutritional.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2024-4-1
 */
@Data
@Schema(description = "营养方案评估结果表")
public class NutritionalAssessmentResultVO {

    @Schema(description = "表单ID",example = "")
    private String formId ;

    @Schema(description = "表单名称",example = "")
    private String formName ;

    @Schema(description = "表单类别（如：症状/随访/运动康复/营养康复）",example = "")
    private String formCategory ;

    @Schema(description = "表单类型（如：量表/问卷）",example = "")
    private String formType ;

    @Schema(description = "表单总分数",example = "")
    private String formTotalScore ;

    @Schema(description = "是否必选；0非必选，1必选",example = "")
    private String required ;

    @Schema(description = "线上标志；0线下，1线上",example = "")
    private String online ;

    @Schema(description = "发送标志；0未发送，1已发送",example = "")
    private String sendFlag ;

    @Schema(description = "填报标志；0未填报，1已填报",example = "")
    private String fillFlag ;

    @Schema(description = "填报时间",example = "")
    private String fillTime ;

    @Schema(description = "评估分数",example = "")
    private String scores ;

    @Schema(description = "评估结果",example = "")
    private String result ;
}
