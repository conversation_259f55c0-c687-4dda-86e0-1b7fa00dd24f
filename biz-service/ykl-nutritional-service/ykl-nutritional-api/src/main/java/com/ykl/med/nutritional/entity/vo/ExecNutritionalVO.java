package com.ykl.med.nutritional.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * 营养方案执行表;
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案执行表")
public class ExecNutritionalVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "患者ID",example = "")
    private String patientId ;

    @Schema(description = "营养方案ID",example = "")
    private String nutritionalId ;

    @Schema(description = "热量（单位：kcal，字段及以下为营养素摄入）",example = "")
    private Integer kilocalorie ;

    @Schema(description = "主食（单位：g；字段及以下为膳食摄入）",example = "")
    private Integer stapleFood ;

    @Schema(description = "蔬菜（单位：g）",example = "")
    private Integer vegetable ;

    @Schema(description = "水果（单位：g）",example = "")
    private Integer fruit ;

    @Schema(description = "肉类（单位：g）",example = "")
    private Integer meat ;

    @Schema(description = "鱼虾（单位：g）",example = "")
    private Integer fish ;

    @Schema(description = "蛋类（单位：g）",example = "")
    private Integer egg ;

    @Schema(description = "牛奶（单位：ml）",example = "")
    private Integer milk ;

    @Schema(description = "豆类（单位：g）",example = "")
    private Integer bean ;

    @Schema(description = "油（单位：g）",example = "")
    private Integer oil ;

    @Schema(description = "食盐（单位：g）",example = "")
    private Integer salt ;

    @Schema(description = "碳水化合物（单位：g）",example = "")
    private Integer carbohydrate ;

    @Schema(description = "蛋白质（单位：g）",example = "")
    private Integer protein ;

    @Schema(description = "脂肪（单位：g）",example = "")
    private Integer fat ;

    @Schema(description = "水（单位：ml）",example = "")
    private Integer water ;

    @Schema(description = "备注",example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】",example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】",example = "")
    private String lastUserId ;

    @Schema(description = "创建时间",example = "")
    private String createTime ;

    @Schema(description = "更新时间",example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE",example = "")
    private String status ;

    @Schema(description = "创建者名称",example = "")
    private String createUserName ;

    @Schema(description = "最后一次修改者名称",example = "")
    private String lastUserName ;

}
