package com.ykl.med.nutritional.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "添加营养先买个执行计划对象")
public class ExecNutritionalItemAddByTaskDTO extends CommonDoctorIdVO {

    @Schema(description = "生成时间, 格式：2023-01-01 00:00:00", example = "")
    private String genTime ;

    @Schema(description = "医嘱ID列表", example = "")
    private List<String> nutritionalItemIdList ;
}
