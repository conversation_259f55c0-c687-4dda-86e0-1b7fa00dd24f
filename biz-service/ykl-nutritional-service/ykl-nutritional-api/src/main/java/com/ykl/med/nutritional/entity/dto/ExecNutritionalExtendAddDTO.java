package com.ykl.med.nutritional.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 营养方案执行表;
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案执行表")
public class ExecNutritionalExtendAddDTO extends CommonDoctorIdVO {

    @Schema(description = "患者ID", example = "")
    private String patientId ;

    @Schema(description = "营养方案ID", example = "")
    private String nutritionalId ;

    /**
     * 打卡信息
     * */
    @Schema(description = "执行时间（打卡）", example = "")
    private String execTime ;

    @Schema(description = "数量（单位：g）", example = "")
    private Integer quantity ;

    @Schema(description = "食物ID", example = "")
    private String foodId ;

    @Schema(description = "用餐类别（早餐/午餐/晚餐/加餐/其他）", example = "")
    private String dinnerClass ;

    @Schema(description = "营养素类型（如：蛋白质，碳水化合物）", example = "")
    private String nutrientType ;

}
