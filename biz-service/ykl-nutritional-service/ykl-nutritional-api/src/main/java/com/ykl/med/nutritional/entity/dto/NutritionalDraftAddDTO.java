package com.ykl.med.nutritional.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 营养方案草稿表;
 * <AUTHOR> xkli
 * @date : 2023-12-25
 */
@Data
@Schema(description = "营养方案草稿表")
public class NutritionalDraftAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "患者ID", example = "")
    private String patientId ;

    @Schema(description = "营养方案ID", example = "")
    private String nutritionalId ;

    @Schema(description = "草稿内容（json）", example = "")
    private NutritionalAddDTO content;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

}
