package com.ykl.med.nutritional.entity.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/6/27
 */
@Data
public class PunchTheClockReqVO {

    @NotNull(message = "patientId can not null")
    @Schema(description = "患者id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long patientId;

    @NotNull(message = "progressBar can not null")
    @Schema(description = "进度条", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer progressBar;
}
