package com.ykl.med.nutritional.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "营养方案执行日志")
public class ExecNutritionalLogVO {

    @Schema(description = "项目数量",example = "")
    public int total = 0;

    @Schema(description = "营养方案执行日期",example = "")
    public String dateTime;

    @Schema(description = "营养方案执行日志",example = "")
    public List<ExecNutritionalCardVO> execNutritionalCardList;
}
