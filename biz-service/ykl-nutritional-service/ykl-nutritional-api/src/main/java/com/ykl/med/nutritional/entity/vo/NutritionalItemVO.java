package com.ykl.med.nutritional.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * 营养康复项目表;
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养康复项目表")
public class NutritionalItemVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "患者ID",example = "")
    private String patientId ;

    @Schema(description = "营养方案ID",example = "")
    private String nutritionalId ;

    @Schema(description = "项目ID",example = "")
    private String itemId ;

    @Schema(description = "项目类别（特医食品/功能营养素）", example = "")
    private String itemCategory ;

    @Schema(description = "项目类型（全营养配方/补充营养配方）",example = "")
    private String itemType ;

    @Schema(description = "项目名称",example = "")
    private String itemName ;

    @Schema(description = "包装规格（20mg*5袋/盒）",example = "")
    private String itemPackageSpec ;

    @Schema(description = "项目厂商名称",example = "")
    private String itemProducerName ;

    @Schema(description = "项目用法名称",example = "")
    private String usageName ;

    @Schema(description = "单次用量",example = "")
    private String onceDosage ;

    @Schema(description = "单次用量单位",example = "")
    private String onceDosageUnit ;

    @Schema(description = "频次ID",example = "")
    private String frequencyId ;

    @Schema(description = "频次名称",example = "")
    private String frequencyName ;

    @Schema(description = "频次时间",example = "")
    private String frequencyTimes ;

    @Schema(description = "开立数量",example = "")
    private String quantity ;

    @Schema(description = "开立数量单位",example = "")
    private String quantityUnit ;

    @Schema(description = "备注（项目说明）",example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】",example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】",example = "")
    private String lastUserId ;

    @Schema(description = "创建时间",example = "")
    private String createTime ;

    @Schema(description = "更新时间",example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE",example = "")
    private String status ;

    @Schema(description = "创建者名称",example = "")
    private String createUserName ;

    @Schema(description = "最后一次修改者名称",example = "")
    private String lastUserName ;

    @Schema(description = "开始时间", example = "")
    private String startTime ;

    @Schema(description = "结束时间", example = "")
    private String endTime ;

    @Schema(description = "最后一次生成执行计划的时间", example = "")
    private String lastExecPlanTime ;

    /**
     * 其他数据
     * */
    @Schema(description = "注意事项",example = "")
    private String note ;

}
