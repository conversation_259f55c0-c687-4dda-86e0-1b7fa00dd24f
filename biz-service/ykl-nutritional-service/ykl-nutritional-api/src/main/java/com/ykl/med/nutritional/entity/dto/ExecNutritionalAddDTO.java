package com.ykl.med.nutritional.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 营养方案执行表;
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案执行表")
public class ExecNutritionalAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "患者ID", example = "")
    private String patientId ;

    @Schema(description = "营养方案ID", example = "")
    private String nutritionalId ;

    @Schema(description = "热量（单位：kcal，字段及以下为营养素摄入）", example = "")
    private String kilocalorie ;

    @Schema(description = "主食（单位：g；字段及以下为膳食摄入）", example = "")
    private String stapleFood ;

    @Schema(description = "蔬菜（单位：g）", example = "")
    private String vegetable ;

    @Schema(description = "水果（单位：g）", example = "")
    private String fruit ;

    @Schema(description = "肉类（单位：g）", example = "")
    private String meat ;

    @Schema(description = "鱼虾（单位：g）", example = "")
    private String fish ;

    @Schema(description = "蛋类（单位：g）", example = "")
    private String egg ;

    @Schema(description = "牛奶（单位：ml）", example = "")
    private String milk ;

    @Schema(description = "豆类（单位：g）", example = "")
    private String bean ;

    @Schema(description = "油（单位：g）", example = "")
    private String oil ;

    @Schema(description = "食盐（单位：g）", example = "")
    private String salt ;

    @Schema(description = "碳水化合物（单位：g）", example = "")
    private String carbohydrate ;

    @Schema(description = "蛋白质（单位：g）", example = "")
    private String protein ;

    @Schema(description = "脂肪（单位：g）", example = "")
    private String fat ;

    @Schema(description = "水（单位：ml）", example = "")
    private String water ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

}
