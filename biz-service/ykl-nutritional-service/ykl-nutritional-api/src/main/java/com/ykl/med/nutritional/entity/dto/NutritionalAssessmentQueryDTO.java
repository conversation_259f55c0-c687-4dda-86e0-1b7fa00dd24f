package com.ykl.med.nutritional.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 营养方案评估表;
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案评估表")
public class NutritionalAssessmentQueryDTO extends CommonDoctorIdVO {

    private Integer pageSize;
    private Integer pageNo;

    @Schema(description = "唯一标识", example = "")
    private String  id ;

    @Schema(description = "患者ID", example = "")
    private String patientId ;

    @Schema(description = "营养方案ID, 不传时获取当前评估中的问卷", example = "")
    private String nutritionalId ;

    @Schema(description = "表单ID", example = "")
    private String formId ;

    @Schema(description = "表单名称", example = "")
    private String formName ;

    @Schema(description = "表单类型（如：量表/问卷）", example = "")
    private String formType ;

    @Schema(description = "线上标志；0线下，1线上", example = "")
    private String online ;

    @Schema(description = "发送标志；0未发送，1已发送",example = "")
    private String sendFlag ;

    @Schema(description = "填报标志；0未填报，1已填报", example = "")
    private String fillFlag ;

    @Schema(description = "填报时间", example = "")
    private String fillTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;
}
