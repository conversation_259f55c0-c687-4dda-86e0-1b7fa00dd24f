package com.ykl.med.nutritional.entity.vo;

import com.ykl.med.masterdata.vo.FormVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案评估表单对象")
public class NutritionalAssessmentFormVO {

    @Schema(description = "唯一标识",example = "")
    private String  id ;

    @Schema(description = "表单对象",example = "")
    private FormVO form;

    @Schema(description = "发送标志；0未发送，1已发送",example = "")
    private String sendFlag;

    @Schema(description = "填报标志；0未填报，1已填报",example = "")
    private String fillFlag;

    @Schema(description = "填报时间",example = "")
    private String fillTime;

    @Schema(description = "评估分值",example = "")
    private String scores;

    @Schema(description = "评估结果",example = "")
    private String result;
}
