package com.ykl.med.nutritional.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 营养方案草稿表;
 * <AUTHOR> xkli
 * @date : 2023-12-25
 */
@Data
@Schema(description = "营养方案草稿表")
public class NutritionalDraftQueryDTO extends CommonDoctorIdVO {

    private Integer pageSize;
    private Integer pageNo;

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "患者ID", example = "")
    private String patientId ;

    @Schema(description = "营养方案ID", example = "")
    private String nutritionalId ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "创建时间", example = "")
    private String createTime ;

    @Schema(description = "更新时间", example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;
}
