package com.ykl.med.nutritional.api;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.nutritional.entity.vo.NutritionalDraftVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-nutritional-service", path = "/ykl-nutritional-service/api/nutritionalDraft")
public interface NutritionalDraftFeign {
    /**
     * 营养方案草稿管理增删改查
     * @param httpMethod : http请求的方法: GET/POST/PUT/DELETE
     * @param obj : http请求对象
     * */
    @PostMapping("/crud")
    PageResult<NutritionalDraftVO> crud(@RequestParam(value = "httpMethod") String httpMethod, @RequestBody Object obj);
}
