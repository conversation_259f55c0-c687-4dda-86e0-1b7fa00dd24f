package com.ykl.med.nutritional.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 营养方案营养素执行表;
 * <AUTHOR> xkli
 * @date : 2023-12-12
 */
@Data
@Schema(description = "营养方案营养素执行表")
public class ExecNutritionalNutrientAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "患者ID", example = "")
    private String patientId ;

    @Schema(description = "营养方案ID", example = "")
    private String nutritionalId ;

    @Schema(description = "执行时间（打卡）", example = "")
    private String execTime ;

    @Schema(description = "数量（单位：g）", example = "")
    private String quantity ;

    @Schema(description = "营养素类型（如：蛋白质，碳水化合物）", example = "")
    private String nutrientType ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

}
