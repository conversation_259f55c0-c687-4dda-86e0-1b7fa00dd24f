package com.ykl.med.nutritional.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案处方类型卡片")
public class NutritionalCardVO {

    @Schema(description = "标签",example = "")
    private String label ;

    @Schema(description = "值",example = "")
    private String value ;

    @Schema(description = "字段名称",example = "")
    private String name ;

    @Schema(description = "数量",example = "")
    private String quantity ;
}
