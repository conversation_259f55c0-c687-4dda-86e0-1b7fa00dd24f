package com.ykl.med.nutritional.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 营养康复项目(特医食品)执行表;
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养康复项目(特医食品)执行表")
public class ExecNutritionalItemAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "患者ID", example = "")
    private String patientId ;

    @Schema(description = "营养方案项目ID", example = "")
    private String nutritionalItemId ;

    @Schema(description = "执行时间（打卡）", example = "")
    private String execTime ;

    @Schema(description = "应执行时间", example = "")
    private String shouldExecTime ;

    @Schema(description = "执行标志（0未执行,1已执行）", example = "")
    private String execFlag ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

    @Schema(description = "待办通知标志；0待办未通知，1待办已通知，2待办已执行；", example = "")
    private String todoNotifyFlag ;

}
