package com.ykl.med.nutritional.entity.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/6/25
 */
@Data
@Schema(description = "推荐食谱列表入参")
public class QueryRecommendRecipeWeekAppVO {

    @NotNull(message = "patientId can not null")
    @Schema(description = "患者id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long patientId;

}
