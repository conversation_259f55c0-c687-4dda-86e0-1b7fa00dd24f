package com.ykl.med.nutritional.entity.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import com.ykl.med.masterdata.vo.FormVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-11
 */
@Data
@Schema(description = "营养方案评估表")
public class NutritionalAssessmentAddDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String  id ;

    @Schema(description = "患者ID", example = "")
    private String patientId ;

    @Schema(description = "营养方案ID（评估完成后，可挂载方案）", example = "")
    private String nutritionalId ;

    @Schema(description = "表单ID", example = "")
    private String formId ;

    @Schema(description = "表单名称", example = "")
    private String formName ;

    @Schema(description = "表单类别（如：症状/随访/运动康复/营养康复）", example = "")
    private String formCategory ;

    @Schema(description = "表单类型（如：量表/问卷）", example = "")
    private String formType ;

    @Schema(description = "表单内容（格式自定义）", example = "")
    private FormVO formContent ;

    @Schema(description = "表单问题数量", example = "")
    private String formQuestionCount ;

    @Schema(description = "表单总分数", example = "")
    private String formTotalScore ;

    @Schema(description = "是否必选；0非必选，1必选", example = "")
    private String required ;

    @Schema(description = "线上标志；0线下，1线上", example = "")
    private String online ;

    @Schema(description = "发送标志；0未发送，1已发送",example = "")
    private String sendFlag ;

    @Schema(description = "填报标志；0未填报，1已填报", example = "")
    private String fillFlag ;

    @Schema(description = "填报时间", example = "")
    private String fillTime ;

    @Schema(description = "评估分数", example = "")
    private String scores ;

    @Schema(description = "评估结果", example = "")
    private String result ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private String lastUserId ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    private String status ;

}
