package com.ykl.med.nutritional.entity.vo;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.masterdata.vo.FormVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @since 2025/3/25
 */
@Data
public class NewNutritionalAssessmentVO {
    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "患者ID")
    @Stringify
    private Long patientId;

    @Schema(description = "营养方案ID（评估完成后，可挂载方案）")
    private Long nutritionalId;

    @Schema(description = "表单ID")
    @Stringify
    private Long formId;

    @Schema(description = "表单名称")
    private String formName;

    @Schema(description = "表单类别（如：症状/随访/运动康复/营养康复）")
    private String formCategory;

    @Schema(description = "表单类型（如：量表/问卷）")
    private String formType;

    @Schema(description = "表单内容填报快照（格式自定义 - json）")
    private FormVO formContent;

    @Schema(description = "表单问题数量")
    private Integer formQuestionCount;

    @Schema(description = "表单总分数")
    private Integer formTotalScore;

    @Schema(description = "是否必选；0非必选，1必选")
    private Integer required;

    @Schema(description = "线上标志；0线下，1线上")
    private Boolean online;

    @Schema(description = "发送标志；0未发送，1已发送；默认0")
    private Boolean sendFlag;

    @Schema(description = "填报标志；0未填报，1已填报（该字段及以下为评估结果信息）")
    private Boolean fillFlag;

    @Schema(description = "填报时间")
    @TimestampConvert
    private LocalDateTime fillTime;

    @Schema(description = "评估分数")
    private Integer scores;

    @Schema(description = "评估结果（结果保存形式可json）")
    private String result;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者ID【实体】")
    private Long createUserId;

    @Schema(description = "最后一次操作者ID【实体】")
    private Long lastUserId;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE")
    private String status;

    @Schema(description = "创建时间")
    @TimestampConvert
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @TimestampConvert
    private LocalDateTime updateTime;

}
