package com.ykl.med.shift.vo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-24
 */
@Data
@Schema(description = "排班输出对象")
public class ShiftOnlineByDoctorVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "班次ID",example = "")
    private String shiftNumberId ;

    @Schema(description = "挂号类别ID",example = "")
    private String registerCategoryId ;

    @Schema(description = "医生ID",example = "")
    private String doctorId ;

    @Schema(description = "医生名称",example = "")
    private String doctorName ;

    @Schema(description = "日期",example = "")
    private String dateTime ;

    @Schema(description = "停诊类型枚举值",example = "")
    private String stopType ;

    @Schema(description = "替诊医生ID",example = "")
    private String replaceDoctorId ;

    @Schema(description = "替诊医生名称",example = "")
    private String replaceDoctorName ;

    @Schema(description = "停诊原因",example = "")
    private String stopReason ;

    @Schema(description = "排班状态：0未发布, 1已发布, 2已停诊",example = "")
    private String shiftStatus ;

    /**
     * 其他信息
     * */
    @Schema(description = "门诊类型枚举值",example = "")
    private String outPatientCategoryValue ;

    @Schema(description = "门诊类型枚举标签",example = "")
    private String outPatientCategoryLabel ;

    @Schema(description = "患者预约信息列表",example = "")
    private List<NumberSourceByDoctorVO> numberSourceByDoctorList ;
}
