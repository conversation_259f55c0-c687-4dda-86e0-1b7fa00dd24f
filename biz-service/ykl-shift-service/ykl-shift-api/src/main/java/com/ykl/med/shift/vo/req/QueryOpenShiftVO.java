package com.ykl.med.shift.vo.req;

import com.ykl.med.framework.common.enums.OutpatientCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/12/4
 */
@Data
public class QueryOpenShiftVO {

    @Schema(description = "号源时间")
    private LocalDate localDate;

    @Schema(description = "医生名称搜索")
    private String search;

    @Schema(description = "医生id")
    private Long doctorId;

    @Schema(description = "医院ID")
    private Long hospitalId ;

    @Schema(description = "科室ID")
    private Long sectionId ;

    @Schema(description = "挂号类别")
    private OutpatientCategory outpatientCategory;

}
