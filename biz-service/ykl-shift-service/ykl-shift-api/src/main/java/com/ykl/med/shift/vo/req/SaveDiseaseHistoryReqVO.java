package com.ykl.med.shift.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Data
public class SaveDiseaseHistoryReqVO {

    @NotNull(message = "问诊id 不能为空")
    @Schema(description = "问诊id")
    private Long consultId;

    @NotNull(message = "疾病id 不能为空")
    @Schema(description = "疾病id")
    private Long diseaseId;

    @NotNull(message = "发病时间 不能为空")
    @Schema(description = "发病时间")
    private Long onsetTime;

    @NotNull(message = "病史描述 不能为空")
    @Schema(description = "病史描述")
    private String medicalHistoryDesc;

    @Schema(description = "病情图片")
    private List<SaveDiseaseHistoryImg> diseaseImg;


    @Data
    public static class SaveDiseaseHistoryImg {
        @Schema(description = "文件大小(字节)", example = "11358")
        private String size;

        @Schema(description = "文件路径", example = "/admin/567799619819991040/1700621064794_20231122-102958.jpg")
        private String path;

        @Schema(description = "文件名称", example = "20231122-102958.jpg")
        private String name;

        @Schema(description = "图片宽度", example = "100")
        private String width;

        @Schema(description = "图片高度", example = "100")
        private String height;

        @Schema(description = "视频长度(秒)", example = "100")
        private String videoDuration;

        @Schema(description = "文件类型")
        private String fileType;
    }

}
