package com.ykl.med.shift.vo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-24
 */
@Data
@Schema(description = "号源信息")
public class NumberSourceDoctorVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "排班ID",example = "")
    private String shiftId ;

    @Schema(description = "挂号编号",example = "")
    private String registerNumber ;

    @Schema(description = "预计就诊时间",example = "")
    private String visitTime ;

    @Schema(description = "预计就诊结束时间",example = "")
    private String visitEndTime ;

    @Schema(description = "患者ID",example = "")
    private String patientId ;

    @Schema(description = "患者姓名",example = "")
    private String patientName ;

    @Schema(description = "患者性别",example = "")
    private String patientSex ;

    @Schema(description = "患者手机号",example = "")
    private String patientMobile ;

    @Schema(description = "预约方式名称; 如：微信,APP",example = "")
    private String appointmentModeName;

    @Schema(description = "就诊状态：-1停号，0待预约，1待支付，2支付成功，3待接诊，4接诊中，5已接诊，6已退号",example = "")
    private String registerStatus ;

    @Schema(description = "号源添加方式: 0发布创建, 1加号",example = "")
    private String addMode ;

    @Schema(description = "操作时间",example = "")
    private String operateTime;

    @Schema(description = "支付状态；0全部, 1未支付, 2已支付",example = "")
    private String payStatus;

    @Schema(description = "就诊状态：3待接诊，4已完成",example = "")
    private String visitStatus;

    @Schema(description = "订单号",example = "")
    private String orderCode ;

    @Schema(description = "挂号费(支付金额)",example = "")
    private String payCost;
}
