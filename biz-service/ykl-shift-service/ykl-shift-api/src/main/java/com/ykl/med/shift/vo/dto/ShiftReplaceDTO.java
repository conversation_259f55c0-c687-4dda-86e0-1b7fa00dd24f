package com.ykl.med.shift.vo.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-24
 */
@Data
@Schema(description = "排班替诊对象")
public class ShiftReplaceDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "停诊类型：传:REPLACE_REGISTER", example = "")
    private String stopType ;

    @Schema(description = "转替诊医生ID", example = "")
    private String replaceDoctorId ;

    @Schema(description = "停诊原因", example = "")
    private String stopReason ;
}
