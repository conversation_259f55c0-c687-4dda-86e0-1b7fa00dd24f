package com.ykl.med.shift.vo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-24
 */
@Data
@Schema(description = "医生信息")
public class ShiftDoctorInfoVO {

    @Schema(description = "医生ID",example = "")
    private String doctorId ;

    @Schema(description = "医生姓名",example = "")
    private String doctorName ;

    @Schema(description = "手机号", example = "13800000000")
    private String mobile;

    @Schema(description = "医生头像链接地址",example = "")
    private String doctorAvatar ;

    @Schema(description = "用户性别", example = "MALE")
    private String genderLabel;

    @Schema(description = "医生职称",example = "")
    private String doctorTitle ;

    @Schema(description = "医护类别(专家角色)",example = "")
    private String medicalCategory ;

    @Schema(description = "医生擅长",example = "")
    private String doctorGoodAt ;

    @Schema(description = "医疗人员医院ID",example = "")
    private String hospitalId ;

    @Schema(description = "医疗人员科室ID",example = "")
    private String sectionId ;

    @Schema(description = "医疗组ID",example = "")
    private String medicalTeamId ;

    @Schema(description = "医疗人员医院名称",example = "")
    private String hospitalName ;

    @Schema(description = "科室名称",example = "")
    private String deptName ;

    @Schema(description = "医疗组名称",example = "")
    private String medicalTeamName ;

    @Schema(description = "医疗组所属医院ID（废弃）",example = "")
    private String medicalTeamHospitalId ;

    @Schema(description = "医疗组所属医院名称（废弃）",example = "")
    private String medicalTeamHospitalName ;

    @Schema(description = "账号ID",example = "")
    private String userId ;

    @Schema(description = "人员类型(医生,药师等)", example = "")
    private String personnelType ;


    /**
     * 医生挂号信息
     * */
    @Schema(description = "剩余号源数量",example = "")
    private String leaveQuantity;

    @Schema(description = "号源状态: 有号, 无号",example = "")
    private String numberStatus ;

}
