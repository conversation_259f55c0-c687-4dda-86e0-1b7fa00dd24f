package com.ykl.med.shift.vo.dto;

import com.ykl.med.framework.common.pojo.BaseMqMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@Data
public class ConsultVideoVO extends BaseMqMessage {
    @Schema(description = "问诊id")
    private Long consultId;

    @Schema(description = "是否保存视频")
    private Boolean saveVideoFlag;

    @Schema(description = "视频名称")
    private String videoName;

    @Schema(description = "视频地址")
    private String videoUrl;

}
