package com.ykl.med.shift.vo.req;

import com.ykl.med.shift.enums.consult.ConsultTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@Data
public class QueryConsultListDoctorReqVO {

    @Schema(hidden = true)
    private Long doctorId;

    @Schema(description = "开始日期")
    private Long startTime;

    @Schema(description = "结束日期")
    private Long endTime;

}
