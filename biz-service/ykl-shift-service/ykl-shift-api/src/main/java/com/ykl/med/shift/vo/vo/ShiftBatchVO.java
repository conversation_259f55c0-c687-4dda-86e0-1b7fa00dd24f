package com.ykl.med.shift.vo.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.util.List;

/**
 * 批量排班表;
 * <AUTHOR> xkli
 * @date : 2023-12-26
 */
@Data
@Schema(description = "批量排班表")
public class ShiftBatchVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "班次ID",example = "")
    private String shiftNumberId ;

    @Schema(description = "挂号类别ID",example = "")
    private String registerCategoryId ;

    @Schema(description = "医疗组ID",example = "")
    private String medicalTeamId ;

    @Schema(description = "医疗组名称",example = "")
    private String medicalTeamName ;

    @Schema(description = "科室ID",example = "")
    private String deptId ;

    @Schema(description = "科室名称",example = "")
    private String deptName ;

    @Schema(description = "医生ID",example = "")
    private String doctorId ;

    @Schema(description = "号源数量",example = "")
    private String quantity ;

    @Schema(description = "日期数组（[t1,t2,...]）",example = "")
    private List<String> dateTimes ;

    @Schema(description = "备注",example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】",example = "")
    private String createUserId ;

    @Schema(description = "最后一次操作者ID【实体】",example = "")
    private String lastUserId ;

    @Schema(description = "创建时间",example = "")
    private String createTime ;

    @Schema(description = "更新时间",example = "")
    private String updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE",example = "")
    private String status ;

    @Schema(description = "创建者名称",example = "")
    private String createUserName ;

    @Schema(description = "最后一次修改者名称",example = "")
    private String lastUserName ;

    /**
     * 排班列表
     * */
    @Schema(description = "排班列表",example = "")
    private List<ShiftVO> shiftList;
}
