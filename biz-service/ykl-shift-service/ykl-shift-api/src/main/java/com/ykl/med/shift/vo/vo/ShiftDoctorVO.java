package com.ykl.med.shift.vo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-24
 */
@Data
@Schema(description = "排班医生返回对象")
public class ShiftDoctorVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "医生ID（其实是账号ID）",example = "")
    private String doctorId ;

    @Schema(description = "剩余预约数量",example = "")
    private String leaveQuantity;

    @Schema(description = "排班日期",example = "")
    private String dateTime ;
}
