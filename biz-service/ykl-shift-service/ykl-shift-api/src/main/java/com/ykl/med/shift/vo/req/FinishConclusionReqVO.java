package com.ykl.med.shift.vo.req;

import com.ykl.med.shift.vo.resp.ConsultConclusionDiagnosisVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/9
 */
@Data
public class FinishConclusionReqVO {

    @Schema(hidden = true)
    private Long userId;

    @Schema(description = "问诊id")
    private Long consultId;

    @Schema(description = "病史摘要")
    private String medicalHistoryDesc;

    @Schema(description = "临床诊断")
    private String clinicalDiagnosis;

    @Schema(description = "诊断编码")
    private List<Long> diagnosisIds;

    @Schema(description = "处理意见")
    private String conclusion;

    @Schema(description = "电子病历")
    private ConsultConclusionDiagnosisReqVO electronicsRecords;




}
