package com.ykl.med.shift.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-shift-service", path = "/ykl-shift-service/api/distrbutedLock")
public interface DistrbutedLockFeign {
    /**
     * 获得锁
     * */
    @PostMapping("/acquireLock")
    boolean acquireLock(@RequestParam(value = "httpMethod") String httpMethod, @RequestBody Object obj);

    /**
     * 获得锁
     * */
    @PostMapping("/releaseLock")
    boolean releaseLock(@RequestParam(value = "httpMethod") String httpMethod, @RequestBody Object obj);

    /**
     * 获得锁
     * */
    @PostMapping("/isLocked")
    boolean isLocked(@RequestParam(value = "httpMethod") String httpMethod, @RequestBody Object obj);

}
