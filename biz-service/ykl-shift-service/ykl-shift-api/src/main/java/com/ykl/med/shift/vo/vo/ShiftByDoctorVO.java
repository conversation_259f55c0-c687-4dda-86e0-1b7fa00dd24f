package com.ykl.med.shift.vo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-24
 */
@Data
@Schema(description = "排班输出对象")
public class ShiftByDoctorVO {

    @Schema(description = "唯一标识",example = "")
    private String id ;

    @Schema(description = "批量排班ID",example = "")
    private String shiftBatchId ;

    @Schema(description = "班次ID",example = "")
    private String shiftNumberId ;

    @Schema(description = "挂号类别ID",example = "")
    private String registerCategoryId ;

    @Schema(description = "医生ID",example = "")
    private String doctorId ;

    @Schema(description = "日期",example = "")
    private String dateTime ;

    @Schema(description = "开始时间", example = "")
    private String startTime ;

    @Schema(description = "结束时间", example = "")
    private String endTime ;

    @Schema(description = "号源数量",example = "")
    private String quantity ;

    @Schema(description = "增加号源数量",example = "")
    private String plusQuantity ;

    @Schema(description = "已预约数量",example = "")
    private String haveQuantity;

    @Schema(description = "剩余号源数量",example = "")
    private String leaveQuantity;

    @Schema(description = "停诊类型枚举值",example = "")
    private String stopType ;

    @Schema(description = "替诊医生ID",example = "")
    private String replaceDoctorId ;

    @Schema(description = "替诊医生名称",example = "")
    private String replaceDoctorName ;

    @Schema(description = "停诊原因",example = "")
    private String stopReason ;

    @Schema(description = "排班状态：0未发布, 1已发布, 2已停诊",example = "")
    private String shiftStatus ;

    @Schema(description = "备注",example = "")
    private String remark ;

    @Schema(description = "最后一次操作者ID【实体】",example = "")
    private String lastUserId ;

    @Schema(description = "最后一次修改者名称",example = "")
    private String lastUserName ;

    @Schema(description = "更新时间",example = "")
    private String updateTime ;

    /**
     * 其他信息
     * */
    @Schema(description = "门诊类型枚举值",example = "")
    private String outPatientCategoryValue ;

    @Schema(description = "门诊类型枚举标签",example = "")
    private String outPatientCategoryLabel ;

    @Schema(description = "医生信息",example = "")
    private ShiftDoctorInfoVO shiftDoctorInfo;

    @Schema(description = "班次",example = "")
    private ShiftNumberVO shiftNumber;

    @Schema(description = "挂号类别",example = "")
    private RegisterCategoryVO registerCategory;
}
