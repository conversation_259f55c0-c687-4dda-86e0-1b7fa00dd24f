package com.ykl.med.shift.api;

import com.ykl.med.framework.common.pojo.IdListReqVO;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.shift.vo.dto.ConsultVideoVO;
import com.ykl.med.shift.vo.req.*;
import com.ykl.med.shift.vo.resp.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/9
 */
@FeignClient(name = "ykl-shift-service", path = "/ykl-shift-service/consult")
public interface ConsultFeign {

    /**
     * 问诊-列表(web)
     */
    @PostMapping("/page/web")
    PageResult<ConsultWebPageVO> pageWeb(@Valid @RequestBody QueryConsultPageWebReqVO param);

    /**
     * 问诊-列表(web)
     */
    @PostMapping("/list/web")
    List<ConsultWebPageVO> listWeb(@Valid @RequestBody QueryConsultPageWebReqVO param);


    /**
     * 问诊-列表(患者端)
     */
    @PostMapping("/page/app")
    PageResult<ConsultAppPageVO> pageApp(@Valid @RequestBody QueryConsultPageAppReqVO param);

    /**
     * 问诊-列表(医护端)
     */
    @PostMapping("/list/doctor")
    List<ConsultDoctorPageVO> listDoctor(@Valid @RequestBody QueryConsultListDoctorReqVO param);

    /**
     * 问诊-列表(医护端：患者详情：线上门诊)
     */
    @PostMapping("/list/patient")
    List<ConsultPatientPageVO> listPatient(@Valid @RequestBody QueryConsultListPatientReqVO param);

    /**
     * 问诊-列表
     */
    @PostMapping("/list/bizId")
    List<ConsultVO> listBizId(@RequestParam(name = "bizId") Long bizId);


    /**
     * 问诊-列表
     */
    @PostMapping("/list/ids")
    List<ConsultVO> listIds(@RequestBody IdListReqVO reqVO);


    /**
     * 线上门诊 - 待接诊列表
     */
    @PostMapping("/list/wait")
    PageResult<ConsultDoctorPageVO> listWait(@Valid @RequestBody QueryConsultWaitPageReqVO param);

    /**
     * 问诊-详情
     */
    @GetMapping("/details")
    ConsultVO details(@RequestParam(name = "id") Long id);

    /**
     * 问诊-详情
     */
    @GetMapping("/details/orderCode")
    ConsultVO getByOrderCode(@RequestParam(name = "orderCode") String orderCode);

    /**
     * 问诊-病史详情
     */
    @GetMapping("/diseaseHistory")
    ConsultDiseaseHistoryVO diseaseHistory(@RequestParam(name = "consultId") Long consultId);

    /**
     * 问诊-问诊小结详情
     */
    @GetMapping("/conclusion")
    ConsultConclusionVO conclusion(@RequestParam(name = "consultId") Long consultId);

    /**
     * 问诊-评价详情
     */
    @GetMapping("/comment")
    ConsultCommentVO comment(@RequestParam(name = "consultId") Long consultId);

    /**
     * 问诊-创建（号源创建）
     */
    @PostMapping("/createBySource")
    Long createBySource(@Valid @RequestBody CreateConsultSourceReqVO param);


    /**
     * 问诊-创建（直接创建）
     */
    @PostMapping("/createByDirectly")
    ConsultVO createByDirectly(@Valid @RequestBody CreateByDirectlyReqVO param);


    /**
     * 问诊-接诊
     */
    @PostMapping("/start")
    void start(@Valid @RequestBody StartConclusionReqVO param);

    /**
     * 问诊-完成问诊
     */
    @PostMapping("/finish")
    String finish(@Valid @RequestBody FinishConclusionReqVO param);

    /**
     * 保存问诊快照
     *
     * @param param
     */
    @PostMapping("/saveConsultSnapshot")
    void saveConsultSnapshot(@Valid @RequestBody CreateConsultSnapshotVO param);

    /**
     * 问诊-保存问诊小结
     */
    @PostMapping("/saveConclusion")
    void saveConclusion(@Valid @RequestBody SaveConclusionReqVO param);

    /**
     * 问诊-评价
     */
    @PostMapping("/saveComment")
    void saveComment(@Valid @RequestBody SaveCommentReqVO param);

    /**
     * 问诊-填写电子病历
     */
    @PostMapping("/saveDiseaseHistory")
    void saveDiseaseHistory(@Valid @RequestBody SaveDiseaseHistoryReqVO param);

    /**
     * 问诊-发起视频
     */
    @PostMapping("/startVideo")
    RoomCreateRespVO startVideo(@RequestParam(name = "userId") Long userId, @RequestParam(name = "consultId") Long consultId);

    /**
     * 问诊-保存视频
     */
    @PostMapping("/saveVideo")
    String saveVideo(@Valid @RequestBody SaveVideoReqVO param);


    /**
     * 问诊-关闭视频
     */
    @PostMapping("/closeVideo")
    ConsultVideoVO closeVideo(@RequestParam(name = "consultId") Long consultId,
                              @RequestParam(name = "userId") Long userId,
                              @RequestParam(name = "joinFlag", required = false) Boolean joinFlag);

    @PostMapping("/saveChat")
    void saveChat(@RequestParam(name = "requestUrl") String requestUrl,
                  @RequestParam(name = "consultId") Long consultId,
                  @RequestParam(name = "videoUrl") String videoUrl,
                  @RequestParam(name = "videoName") String videoName);

    /**
     * 问诊-统计医生服务问诊数量
     */
    @GetMapping("/queryConsultCountByDoctorId")
    Integer queryConsultCountByDoctorId(@RequestParam(name = "doctorId") Long doctorId);


    @PostMapping("/checkRefund")
    void checkRefund(@RequestParam(name = "orderCode") String orderCode);

    @PostMapping("/orderNotification")
    void orderNotification(@Valid @RequestBody ConsultNotificationVO param);

    @PostMapping("/updateConsultHisData")
    void updateConsultHisData(@Valid @RequestBody UpdateConsultHisDataVO param);

    @PostMapping("/updateConsult")
    void updateConsult(@Valid @RequestBody UpdateConsultVO param);
}
