package com.ykl.med.shift.vo.resp;

import com.ykl.med.framework.common.json.Stringify;
import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.enums.ConsultChatTypeEnum;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.enums.consult.ConsultTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@Data
public class ConsultDoctorPageVO {

    @Schema(description = "id")
    @Stringify
    private Long id;

    @Schema(description = "业务id，随访-随访id，心理-心理id...")
    @Stringify
    private Long bizId;

    @Schema(description = "聊天类型")
    private ConsultChatTypeEnum chatType;

    @Schema(description = " 问诊时间（预定开始时间）")
    @TimestampConvert
    private LocalDateTime visitTime;

    @Schema(description = " 问诊时间（预定开始时间）")
    @TimestampConvert
    private LocalDateTime planVisitStartTime;

    @Schema(description = " 问诊时间（预定结束时间）")
    @TimestampConvert
    private LocalDateTime planVisitEndTime;

    @Schema(description = "问诊开始时间")
    @TimestampConvert
    private LocalDateTime visitStartTime;

    @Schema(description = "问诊结束时间")
    @TimestampConvert
    private LocalDateTime visitEndTime;

    @Schema(description = "类型,ConsultTypeEnum 枚举")
    private ConsultTypeEnum consultType;

    @Schema(description = "状态,ConsultStateEnum 枚举")
    private ConsultStateEnum consultState;

    @Schema(description = "医生id")
    @Stringify
    private Long doctorId;

    @Schema(description = "医生名称")
    private String doctorName;

    @Schema(description = "就诊人id")
    @Stringify
    private Long patientId;

    @Schema(description = "就诊人名")
    private String patientName;

    @Schema(description = "就诊人手机号码")
    private String patientMobile;

    @Schema(description = "性别")
    private String patientSex;

    @Schema(description = "年龄")
    private Integer patientAge;

    @Schema(description = "病种")
    private String patientDiseases;

    @Schema(description = "阶段")
    private String patientStage;

    @Schema(description = "临床分期")
    private String clinicalStaging;

    @Schema(description = "病理分型")
    private String pathologicalType;

    @Schema(description = "手术日期距离现在多少周或者多少月")
    private String surgeryDateDistance;

    @Schema(description = "备注")
    private String remark;

}
