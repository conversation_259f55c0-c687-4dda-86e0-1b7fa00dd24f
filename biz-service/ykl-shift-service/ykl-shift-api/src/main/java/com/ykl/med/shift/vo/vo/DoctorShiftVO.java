package com.ykl.med.shift.vo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-24
 */
@Data
@Schema(description = "医生排班信息")
public class DoctorShiftVO {

    @Schema(description = "排班ID",example = "")
    private String shiftId ;

    @Schema(description = "班次信息",example = "")
    private ShiftNumberVO shiftNumber ;

    @Schema(description = "挂号类别ID",example = "")
    private RegisterCategoryVO registerCategory ;

    @Schema(description = "号源信息列表",example = "")
    private List<NumberSourcePatientVO> numberSourcePatientList;

}
