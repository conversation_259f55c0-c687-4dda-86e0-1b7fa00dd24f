package com.ykl.med.shift.api;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.shift.vo.vo.ShiftBatchVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ykl-shift-service", path = "/ykl-shift-service/api/shiftBatch")
public interface ShiftBatchFeign {
    /**
     * 批量排班管理增删改查
     * @param httpMethod : http请求的方法: GET/POST/PUT/DELETE
     * @param obj : http请求对象
     * */
    @PostMapping("/crud")
    PageResult<ShiftBatchVO> crud(@RequestParam(value = "httpMethod") String httpMethod, @RequestBody Object obj);
}
