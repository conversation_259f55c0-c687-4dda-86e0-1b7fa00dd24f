package com.ykl.med.shift.vo.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xkli
 * @date : 2023-12-24
 */
@Data
@Schema(description = "批量添加排班")
public class ShiftAddBatchDTO extends CommonDoctorIdVO {
    @Schema(description = "排班数据列表", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ShiftAddDTO> shiftAddList;
}
