package com.ykl.med.shift.vo.dto;

import com.ykl.med.framework.common.pojo.CommonDoctorIdVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "删除排班")
public class DeleteShiftDTO extends CommonDoctorIdVO {

    @Schema(description = "唯一标识", example = "")
    private String id ;

    @Schema(description = "排班ID", example = "")
    private String shiftId ;

    @Schema(description = "就诊状态：0待预约，1待支付，2支付成功，3待接诊，4已接诊；", example = "")
    private String registerStatus ;

    /**
     * 批量ID数组
     * */
    @Schema(description = "批量操作ID数组", example = "")
    private List<String> ids;

}
