package com.ykl.med.shift.constans;


import com.ykl.med.framework.common.exception.ErrorCode;

/**
 * push 错误码枚举类
 */
public interface ShiftErrorCodeConstants {

    // ==========  300000-309999 ==========
    ErrorCode CONSULT_IS_NULL = new ErrorCode(300001, "问诊信息 不存在");
    ErrorCode NUMBER_SOURCE_IS_NULL = new ErrorCode(300002, "号源信息 不存在");
    ErrorCode SHIFT_IS_NULL = new ErrorCode(300003, "排班信息 不存在");
    ErrorCode SHIFT_NUMBER_IS_NULL = new ErrorCode(300004, "班次信息 不存在");
    ErrorCode NUMBER_SOURCE_STATE_NOT_PAY = new ErrorCode(300005, "号源待支付");
    ErrorCode CONSULT_NOT_STARTING = new ErrorCode(300006, "结束问诊：当前问诊状态不是进行中,不能结束问诊");

    ErrorCode CONSULT_IS_EXIST = new ErrorCode(300007, "问诊已创建");
    ErrorCode CONSULT_COMMENT_IS_EXIST = new ErrorCode(300008, "当前问诊已评论");
    ErrorCode CONSULT_DISEASE_HISTORY_IS_EXIST = new ErrorCode(3000009, "电子病历已填写");

    ErrorCode SAVE_CONSULT_ERROR = new ErrorCode(300010, "创建问诊 失败");
    ErrorCode SAVE_CONCLUSION_ERROR = new ErrorCode(300011, "创建问诊结论 失败");
    ErrorCode SAVE_CONSULT_DISEASE_HISTORY_ERROR = new ErrorCode(300012, "创建电子病历 失败");
    ErrorCode DOCTOR_IS_NULL = new ErrorCode(300013, "医生信息不存在");
    ErrorCode CONSULT_STATE_CAN_NOT_WAIT = new ErrorCode(300014, "问诊状态已完成或已取消,不能接诊");
    ErrorCode CONSULT_VIDEO_DOCTOR_STARTING = new ErrorCode(300015, "与 %s 的视频通话还在进行中，请先结束视频");
    ErrorCode CONSULT_VIDEO_PATIENT_STARTING = new ErrorCode(300016, "当前患者的视频通话还在进行中,请稍后再试");
    ErrorCode NUMBER_SOURCE_STATUS_IS_WAIT_CAN_STOP = new ErrorCode(300017, "待预约的号源才能停号或删除");
    ErrorCode CONSULT_STATUS_IS_SUCCESS = new ErrorCode(300020, "问诊状态已修改成功");
    ErrorCode CONSULT_CAN_NOT_REFUND = new ErrorCode(300021, "当前线上门诊不能退款!");
    ErrorCode NUMBER_SOURCE_STATE_NOT_WAT_PAY = new ErrorCode(300022, "号源状态不是待支付");
    ErrorCode NUMBER_SOURCE_STATE_INVALID = new ErrorCode(300023, "号源已预约或已失效");
    ErrorCode NUMBER_SOURCE_IS_TIME_OUT = new ErrorCode(300024, "号源已过期");
    ErrorCode NUMBER_SOURCE_PATIENT_BUY_WAIT = new ErrorCode(300025, "存在此医生待支付的订单，请先支付或取消订单");
    ErrorCode NUMBER_SOURCE_PATIENT_BUY_SUCCESS = new ErrorCode(300026, "同一医生同一班次不允许挂多个号");
    ErrorCode NUMBER_SOURCE_PATIENT_DATE_BUY_ONE = new ErrorCode(300027, "同一时间只能挂一个号");
    ErrorCode CONSULTATION_HAS_STARTED_AND_THE_PATIENT_CANNOT_BE_MODIFIED = new ErrorCode(300027, "问诊已开始,不可修改问诊对象");
    ErrorCode VOICE_TEXT_DOES_NOT_EXIST= new ErrorCode(300028, "语音文本不存在");

    ErrorCode DISEASE_IS_NULL = new ErrorCode(300115, "疾病不存在");

    ErrorCode PATIENT_USER_IS_NULL = new ErrorCode(300016, "患者账号不存在");
}
