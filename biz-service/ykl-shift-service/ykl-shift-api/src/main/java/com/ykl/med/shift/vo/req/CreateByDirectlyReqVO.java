package com.ykl.med.shift.vo.req;

import com.ykl.med.framework.common.json.TimestampConvert;
import com.ykl.med.framework.common.enums.ConsultChatTypeEnum;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.enums.consult.ConsultTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Data
public class CreateByDirectlyReqVO {

    @Schema(description = "业务id")
    @NotNull(message = "业务id 不能为空")
    private Long bizId;

    @Schema(description = "聊天类型")
    @NotNull(message = "聊天类型 不能为空")
    private ConsultChatTypeEnum chatType;

    @Schema(description = "类型,ConsultStateEnum 枚举")
    @NotNull(message = "问诊状态 不能为空")
    private ConsultStateEnum consultState;

    @Schema(description = "类型,ConsultTypeEnum 枚举")
    @NotNull(message = "问诊类型 不能为空")
    private ConsultTypeEnum consultType;

    @Schema(description = "医生id")
    @NotNull(message = "医生id 不能为空")
    private Long doctorId;

    @Schema(description = "患者id")
    @NotNull(message = "患者id 不能为空")
    private Long patientId;

    @Schema(description = "问诊时间（预定开始时间）")
    @NotNull(message = "问诊时间（预定开始时间） 不能为空")
    @TimestampConvert
    private LocalDateTime planVisitStartTime;

    @Schema(description = "问诊时间（预定结束时间）")
    @NotNull(message = "问诊时间（预定结束时间） 不能为空")
    @TimestampConvert
    private LocalDateTime planVisitEndTime;

    @Schema(description = "疾病id")
    private List<Long> diagnosisIds;

    @Schema(description = "病史描述")
    private String medicalHistoryDesc;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "患者userid")
    private Long patientUserId;
}
