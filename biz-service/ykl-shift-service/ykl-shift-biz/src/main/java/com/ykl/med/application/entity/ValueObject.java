package com.ykl.med.application.entity;

import com.ykl.med.shift.enums.LogicValue;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ValueObject {
    // 表名
    private String tableName;
    // 对象名
    private String objectName;
    // 原方法:GET/POST/PUT/DELETE
    private String sourceMethod;
    // 方法:GET/POST/PUT/DELETE
    private String method;
    // 是否有传入值赋值: VObj对象中, 有从前端传入的值
    private boolean isExistValue;
    // 是否允许为
    Map<String, Map<LogicValue, List<String>>> subValueObjectLogicValue;
    // 值对象实体
    private Map<String, ObjectAttribute> VObj;
    // 子对象: 值对象中的其他值对象
    private Map<String, List<ValueObject>> subValueObject;
}
