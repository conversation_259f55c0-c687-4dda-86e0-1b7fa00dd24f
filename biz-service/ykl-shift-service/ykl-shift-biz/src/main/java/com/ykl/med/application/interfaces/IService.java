package com.ykl.med.application.interfaces;

import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.config.SimpleException;

import java.text.ParseException;
import java.util.HashMap;

public interface IService {
    /**
     * 方法：serviceName
     * 描述：返回服务的名称
     * 实现方式: return "user"; user为服务名称
     * */
    String serviceName();

    /**
     * 方法：crud
     * 描述：实现服务的"增删改查"(单表)
     * 实现方式: return MapperRepository.execCrud(new Tag(),requestObj); Tag为操作的实体对象
     * */
    ResponseModel crud(HashMap requestObj) throws IllegalAccessException, SimpleException, InstantiationException, ParseException, NoSuchFieldException;
}
