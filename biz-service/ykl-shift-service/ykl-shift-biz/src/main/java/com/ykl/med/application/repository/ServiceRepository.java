package com.ykl.med.application.repository;

import com.ykl.med.application.entity.ResponseCode;
import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.application.interfaces.IService;
import com.ykl.med.config.DefinitionException;
import com.ykl.med.config.SimpleException;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.ServiceLoader;

/**
 * 服务仓库
 * */
@Component
public class ServiceRepository {
    /**
     * 加载的服务列表
     * */
    private List<IService> rootServiceList = new ArrayList<>();

    @PostConstruct
    void loadTagServices(){
        final ServiceLoader<IService> rootServices = ServiceLoader.load(IService.class);
        for (IService rootService : rootServices) {
            rootServiceList.add(rootService);
        }
    }

    /**
     * 执行服务的方法
     * serviceName: 服务名称
     * method: 方法名称
     * params: 方法参数列表
     * */
    public Object doService(String serviceName, String method, Object[] params) {
        ResponseModel responseModel = new ResponseModel();

        for ( IService rootService : rootServiceList ) {
            if ( serviceName.equals(rootService.serviceName()) ) {

                /* 获取服务参数类型列表 */
                int paramsLength = params.length;
                Class<?>[] paramClass = new Class[paramsLength];
                for (int i = 0; i < paramsLength; i++) {
                    paramClass[i] = params[i].getClass();
                }

                Method md;

                try {
                    // 获取方法
                    md = rootService.getClass().getMethod(method, paramClass);
                } catch ( Exception e){
                    responseModel.response(ResponseCode.INVALID_ARGUMENT, method + "方法不存在");
                    return responseModel;
                }

                Object retObj = null;
                try {
                    /* 执行方法 */
                    retObj = md.invoke(rootService, params);
                } catch (Exception e) {
                    Throwable cause = e.getCause();
                    if(cause instanceof SimpleException){
                        throw new DefinitionException(((SimpleException)cause).getCode(),
                                ((SimpleException)cause).getMessage());
                    } else {
                        e.printStackTrace();
                        throw new DefinitionException(100001,method+ "方法执行错误");
                    }
                }

                return retObj;
            }
        }

        responseModel.response(ResponseCode.INVALID_ARGUMENT,serviceName + "：服务不存在");
        return responseModel;
    }

}
