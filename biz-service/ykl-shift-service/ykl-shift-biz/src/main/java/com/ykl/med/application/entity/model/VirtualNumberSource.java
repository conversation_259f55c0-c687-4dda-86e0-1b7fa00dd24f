package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "虚构号源批量表")
@TableName("t_virtual_number_source")
public class VirtualNumberSource {

    @Schema(description = "排班ID", example = "")
    @TableLogic("NOTNULL:NN_POST,TABLE:TR_O2M:numberSourceList:shift_id")
    private Long shiftId ;

    @Schema(description = "号源列表", example = "")
    List<NumberSource> numberSourceList;
}
