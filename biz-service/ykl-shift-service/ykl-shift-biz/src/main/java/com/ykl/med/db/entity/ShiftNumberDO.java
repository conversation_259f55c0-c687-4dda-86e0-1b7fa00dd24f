package com.ykl.med.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * 班次表 Data Object
 *
 * <AUTHOR>
 * 2024-10-9 15:15:22
 */
@Data
@TableName("t_shift_number")
public class ShiftNumberDO extends BaseDO {

    /*** 班次名称 */
    private String name;
    /*** 开始时间 */
    private String startTime;
    /*** 结束时间 */
    private String endTime;
    /*** 号源数量 */
    private Integer quantity;
    /*** 挂号类别ID */
    private Long registerCategoryId;
    /*** 挂号类别名称 */
    private String registerCategoryName;
    /*** 备注 */
    private String remark;
    /*** 创建者ID【实体】 */
    private Long createUserId;
    /*** 最后一次操作者ID【实体】 */
    private Long lastUserId;
    /*** 状态；启用：ENABLE，禁用：DISABLE */
    private String status;
}