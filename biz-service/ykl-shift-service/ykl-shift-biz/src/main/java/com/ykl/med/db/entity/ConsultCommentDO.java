package com.ykl.med.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * 问诊-评价 Data Object
 *
 * <AUTHOR>
 * 2024-10-9 15:15:22
 */
@Data
@TableName("t_consult_comment")
public class ConsultCommentDO extends BaseDO {

    /*** 问诊id */
    private Long consultId;
    /*** 用户id */
    private Long patientId;
    /*** 用户姓名 */
    private String patientName;
    /*** 评价平分(0-10) */
    private Integer score;
    /*** 评价内容 */
    private String content;
    /** 删除标志；false-未删，true-已删 */
    private Boolean deleteFlag;
}