package com.ykl.med.application.entity.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import java.util.Date;

/**
 * 挂号类别表;
 * <AUTHOR> xkli
 * @date : 2023-12-22
 */
@Data
@Schema(description = "挂号类别表")
@TableName("t_register_category")
public class RegisterCategory {

    @Schema(description = "唯一标识", example = "")
    @TableId("unique")
    @TableLogic("FILLED:SGV_INSERT,NOTNULL:NN_PUT,NOTNULL:NN_DELETE,FIELD_PROC:FP_PUT_WHERE")
    private Long id ;

    @Schema(description = "挂号类别名称", example = "")
    @TableId
    @TableLogic("NOTNULL:NN_POST,WHERE:WL_MATCH:%:%")
    private String name ;

    @Schema(description = "拼音简码", example = "")
    @TableId
    @TableLogic("WHERE:WL_MATCH:%:%")
    private String nameShort ;

    @Schema(description = "门诊类别（枚举）", example = "")
    @TableId
    private String outpatientCategory ;

    @Schema(description = "限定职称ID数组,格式为数组：[1,2]", example = "")
    private Object limitTitle ;

    @Schema(description = "费用（单位：分）", example = "")
    private Integer cost ;

    @Schema(description = "会员价（单位：分）", example = "")
    private Integer memberPrice ;

    @Schema(description = "备注", example = "")
    private String remark ;

    @Schema(description = "创建者ID【实体】", example = "")
    private Long createUserId ;

    @Schema(description = "最后一次操作者ID【实体】", example = "")
    private Long lastUserId ;

    @Schema(description = "创建时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date createTime ;

    @Schema(description = "更新时间", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT,FILLED:SGV_UPDATE,FILLED:SGV_SORT,WHERE:WL_RANGE:>=:<")
    private Date updateTime ;

    @Schema(description = "状态；启用：ENABLE，禁用：DISABLE", example = "")
    @TableId
    @TableLogic("FILLED:SGV_INSERT:ENABLE")
    private String status ;

    @Schema(description = "删除标志；0未删，1已删", example = "")
    @TableId
    @TableLogic("FILLED:SGV_DELETE:1,FILLED:SGV_WHERE:0,FIELD_PROC:FP_GET_HIDE")
    private Byte deleteFlag ;

}
