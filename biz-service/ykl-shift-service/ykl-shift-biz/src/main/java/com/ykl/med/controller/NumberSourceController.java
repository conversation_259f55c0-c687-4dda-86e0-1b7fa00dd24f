package com.ykl.med.controller;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.service.NumberSourceService;
import com.ykl.med.shift.api.NumberSourceFeign;
import com.ykl.med.shift.vo.dto.*;
import com.ykl.med.shift.vo.req.*;
import com.ykl.med.shift.vo.vo.NumberSourceVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 号源
 *
 * <AUTHOR>
 * @since 2024/12/2
 */

@RestController
@RequestMapping("/numberSource")
public class NumberSourceController implements NumberSourceFeign {

    @Resource
    private NumberSourceService numberSourceService;

    @Override
    @PostMapping("/page/admin")
    public PageResult<ConsultAdminPageVO> pageAdmin(@Valid @RequestBody QueryNumberSourcePageAdminReqVO param) {
        return numberSourceService.pageAdmin(param);
    }

    @Override
    @GetMapping("/details")
    public NumberSourceVO details(@RequestParam(name = "id") Long id) {
        return numberSourceService.details(id);
    }


    @Override
    @PostMapping("/stop")
    public void stop(@Valid @RequestBody StopNumberSourceReqVO param) {
        numberSourceService.stop(param);
    }

    @Override
    @PostMapping("/delete")
    public void delete(@Valid @RequestBody DeleteNumberSourceReqVO param) {
        numberSourceService.delete(param);
    }

    @Override
    @PostMapping("/lockRepository")
    public void lockRepository(@Valid @RequestBody NumberSourceLockRepositoryVO param) {
        numberSourceService.lockRepository(param);
    }


    @Override
    @PostMapping("/timeDay")
    public List<NumberSourceTimeVO> timeDay(@Valid @RequestBody QueryTimeDayReqVO param) {
        return numberSourceService.timeDay(param);
    }

    @Override
    @PostMapping("/doctorList")
    public List<NumberSourceAppPageVO> doctorList(@Valid @RequestBody QueryNumberSourceDoctorListReqVO param) {
        return numberSourceService.doctorList(param);
    }

    @Override
    @PostMapping("/doctorNumberSource")
    public List<DoctorNumberSourceVO> doctorNumberSource(@Valid @RequestBody QueryDoctorNumberSourceReqVO param) {
        return numberSourceService.doctorNumberSource(param);
    }

    @Override
    @PostMapping("/doctor")
    public DoctorVO doctor(@RequestParam(name = "doctorId") Long doctorId) {
        return numberSourceService.doctor(doctorId);
    }









}
