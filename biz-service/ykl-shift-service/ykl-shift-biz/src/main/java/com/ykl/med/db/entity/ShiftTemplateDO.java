package com.ykl.med.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ykl.med.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * 排班表 Data Object
 *
 * <AUTHOR>
 * 2024-10-9 15:15:22
 */
@Data
@TableName("t_shift_template")
public class ShiftTemplateDO extends BaseDO {

    /*** 班次ID */
    private Long shiftNumberId;
    /*** 挂号类别ID */
    private Long registerCategoryId;
    /*** 医生ID */
    private Long doctorId;
    /*** 号源数量 */
    private Integer quantity;
    /*** 增加号源数量 */
    private Integer plusQuantity;
    /*** 停诊类型【字典】 */
    private String stopType;
    /*** 停诊原因 */
    private String stopReason;
    /*** 备注 */
    private String remark;
    /*** 创建者ID【实体】 */
    private Long createUserId;
    /*** 最后一次操作者ID【实体】 */
    private Long lastUserId;
    /*** 状态；未发布：DISABLE，已发布：ENABLE，停诊：STOP，停诊 */
    private String status;

}