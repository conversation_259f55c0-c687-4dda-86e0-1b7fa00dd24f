package com.ykl.med.service;

import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.shift.enums.NumberSourceStatus;
import com.ykl.med.shift.vo.dto.*;
import com.ykl.med.shift.vo.req.*;
import com.ykl.med.shift.vo.vo.NumberSourceVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/2
 */
public interface NumberSourceService {
    PageResult<ConsultAdminPageVO> pageAdmin(QueryNumberSourcePageAdminReqVO param);

    List<NumberSourceAppPageVO> doctorList(QueryNumberSourceDoctorListReqVO param);

    List<DoctorNumberSourceVO> doctorNumberSource(QueryDoctorNumberSourceReqVO param);

    NumberSourceVO details(Long id);

    void stop(StopNumberSourceReqVO param);

    void lockRepository(NumberSourceLockRepositoryVO param);

    void delete(DeleteNumberSourceReqVO param);

    void saveNumberResourceState(Long numberSourceId, NumberSourceStatus state);

    DoctorVO doctor(Long doctorId);

    List<NumberSourceTimeVO> timeDay(QueryTimeDayReqVO param);
}
