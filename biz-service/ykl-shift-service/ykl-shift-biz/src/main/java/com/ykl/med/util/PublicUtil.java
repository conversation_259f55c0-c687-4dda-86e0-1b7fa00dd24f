package com.ykl.med.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PublicUtil {
    /**
     * 字符串补齐
     * @ss: 原字符串
     * @Ch: 要补充的字符串
     * @nu: 补齐后的数量
     * @ff: 补齐方式: true表示向后补, false表示向前补
     * */
    public static String padString(String ss, String ch, int num, boolean ff){
        StringBuilder ssBuilder = new StringBuilder(ss);
        int padNum = num - ssBuilder.length();
        if ( padNum > 0 ){
            if ( ff ) {
                for (int i = 0; i < padNum; i++) {
                    ssBuilder.append(ch);
                }
            } else {
                for (int i = 0; i < padNum; i++) {
                    ssBuilder.insert(0, ch);
                }
            }
        }

        return ssBuilder.toString();
    }
    /**
     * 使用正则判断字符串是否为整型或双精度型
     * */
    public static boolean isNumericByReg(String str) {
        if ( PublicUtil.isNumericIntByReg(str)
                || PublicUtil.isNumericDoubleByReg(str)
        ) {
            return true;
        }
        return false;
    }

    /**
     * 使用正则判断字符串是否为整型(int)
     * */
    public static boolean isNumericIntByReg(String str) {
        Pattern pattern = Pattern.compile("-?[0-9]+");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 使用正则判断字符串是否为双精度类型(double)
     * */
    public static boolean isNumericDoubleByReg(String str) {
        Pattern pattern = Pattern.compile("-?[0-9]+.?[0-9]+");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 生成MD5字符串
     * */
    public static String genMd5string(String str)
            throws NoSuchAlgorithmException, UnsupportedEncodingException
    {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(str.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte b : md5.digest()) {
            sb.append(String.format("%02x", b));
        }

        return sb.toString();
    }

    /** 本地時間轉時間 */
    public static Date LocalDateTimeToDate(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /** 時間轉本地時間 */
    public static LocalDateTime DateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /** 日期转字符串 */
    public static String DateToString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        return formatter.format(date);
    }

    /** 字符串转日期 */
    public static Date stringToDate(String date) throws ParseException {
        SimpleDateFormat formatter0 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat formatter3 = new SimpleDateFormat("yyyy");
        SimpleDateFormat formatter4 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat formatter5 = new SimpleDateFormat("yyyy-MM-dd HH");
        SimpleDateFormat formatter6 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        SimpleDateFormat formatter7 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm");
        SimpleDateFormat formatter8 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

        Pattern pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$");
        Matcher matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter0.parse(date);
        }

        pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
        matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter1.parse(date);
        }

        pattern = Pattern.compile("^\\d{4}-\\d{2}$");
        matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter2.parse(date);
        }

        pattern = Pattern.compile("^\\d{4}$");
        matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter3.parse(date);
        }

        pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}$");
        matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter4.parse(date);
        }

        pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}$");
        matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter5.parse(date);
        }

        pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}.\\d{3}$");
        matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter6.parse(date);
        }

        pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}$");
        matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter7.parse(date);
        }

        pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$");
        matcher = pattern.matcher(date);
        if ( matcher.matches() ){
            return formatter8.parse(date);
        }

        return null;
    }

    /** 处理年龄, 如: 15Y、6M、23D; 15岁、6月、24周、23天
     * return：返回天数
     * */
    public static String procAge(String age){

        List<String> strList = Arrays.asList("Y","y","M","m","W","w","D","d","H","h");

        // 返回结果
        String dayStr = "";

        if ( age.length() == 0 ){
            return "";
        }

        // 没有标记时, 默认为Y: 表示年
        if ( PublicUtil.isNumericIntByReg(age) ){
            age += "Y";
        }

        // 类型表示在后面
        if ( strList.contains(age.substring(age.length() - 1)) ) {
            // 截取前面的数字
            String ageStr = age.substring(0,age.length() - 1);
            if ( PublicUtil.isNumericIntByReg(ageStr) ) {
                switch (age.substring(age.length() - 1)) {
                    case "Y":
                    case "y": {
                        dayStr = Integer.toString(Integer.parseInt(ageStr) * 12 * 30 * 24);
                        break;
                    }
                    case "M":
                    case "m": {
                        dayStr = Integer.toString(Integer.parseInt(ageStr) * 30 * 24);
                        break;
                    }
                    case "W":
                    case "w": {
                        dayStr = Integer.toString(Integer.parseInt(ageStr) * 7 * 24);
                        break;
                    }
                    case "D":
                    case "d": {
                        dayStr = Integer.toString(Integer.parseInt(ageStr) * 24);
                        break;
                    }
                    case "H":
                    case "h": {
                        dayStr = ageStr;
                        break;
                    }
                }
            }
        }

        return dayStr;
    }

    /**
     * 生成18位数字uuid
     * */
    public static Long uuidGeneratorLong18(){
        return System.currentTimeMillis()*100000L+ System.nanoTime()%100000L;
    }

    /**
     * 生成32位字符串uuid
     *
     * @return uuid
     */
    public static String uuidGeneratorString32() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 判断请求体,并将请求体转成自己的对象
     * */
    public static <T> T parseObject(Object object, TypeReference<T> type){
        if ( null == object ){
            return null;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper.convertValue(object, type);
    }

}
