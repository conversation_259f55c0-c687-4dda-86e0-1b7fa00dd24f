package com.ykl.med.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.config.config.ConsultConfig;
import com.ykl.med.db.entity.ConsultDO;
import com.ykl.med.db.entity.ShiftDO;
import com.ykl.med.db.entity.ShiftNumberDO;
import com.ykl.med.db.mapper.ConsultMapper;
import com.ykl.med.db.mapper.ShiftDOMapper;
import com.ykl.med.db.mapper.ShiftNumberDOMapper;
import com.ykl.med.doctors.api.*;
import com.ykl.med.doctors.entity.vo.*;
import com.ykl.med.framework.common.enums.ConsultChatTypeEnum;
import com.ykl.med.framework.common.enums.OrderStatus;
import com.ykl.med.framework.common.exception.AssertUtils;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.ykl.med.framework.common.pojo.PageResult;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.framework.common.util.HutHttpUtils;
import com.ykl.med.livekit.enums.LivekitErrorCode;
import com.ykl.med.livekit.feign.LivekitFeign;
import com.ykl.med.livekit.vo.req.RoomCreateReqVO;
import com.ykl.med.livekit.vo.resp.RoomCreateRespVO;
import com.ykl.med.patient.api.PatientFeign;
import com.ykl.med.patient.api.PatientUserFeign;
import com.ykl.med.patient.constants.PatientErrorCodeConstants;
import com.ykl.med.patient.vo.PatientUserVO;
import com.ykl.med.patient.vo.patient.PatientBaseVO;
import com.ykl.med.patient.vo.patient.PatientQueryVO;
import com.ykl.med.push.api.ChatFeign;
import com.ykl.med.push.api.MessageFeign;
import com.ykl.med.push.api.UserChatFeign;
import com.ykl.med.push.enums.ChatType;
import com.ykl.med.push.enums.LiveChatStatus;
import com.ykl.med.push.enums.MessageType;
import com.ykl.med.push.vo.message.*;
import com.ykl.med.service.*;
import com.ykl.med.shift.constans.ShiftErrorCodeConstants;
import com.ykl.med.shift.enums.NumberSourceStatus;
import com.ykl.med.shift.enums.consult.ConsultStateEnum;
import com.ykl.med.shift.enums.consult.ConsultTypeEnum;
import com.ykl.med.shift.vo.dto.ConsultChatVO;
import com.ykl.med.shift.vo.dto.ConsultVideoVO;
import com.ykl.med.shift.vo.req.*;
import com.ykl.med.shift.vo.resp.*;
import com.ykl.med.shift.vo.vo.NumberSourceVO;
import com.ykl.med.user.api.UserFeign;
import com.ykl.med.user.constants.UserErrorCode;
import com.ykl.med.user.vo.UserAuthorityVO;
import com.ykl.med.user.vo.resp.UserSimpleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConsultServiceImpl implements ConsultService {


    private final ConsultMapper consultMapper;
    private final PatientFeign patientFeign;
    private final ConsultCommentService consultCommentService;
    private final ConsultSnapshotService consultSnapshotService;
    private final ConsultConclusionService consultConclusionService;
    private final ConsultDiseaseHistoryService consultDiseaseHistoryService;
    private final ShiftDOMapper shiftMapper;
    private final ShiftNumberDOMapper shiftNumberDOMapper;
    private final IdServiceImpl idService;
    private final StringRedisTemplate stringRedisTemplate;
    private final LivekitFeign livekitFeign;
    private final ChatFeign chatFeign;
    private final UserChatFeign userChatFeign;
    private final MessageFeign messageFeign;
    private final DoctorInfoFeign doctorInfoFeign;
    private final HospitalFeign hospitalFeign;
    private final SectionFeign sectionFeign;
    private final UserFeign userFeign;
    private final MedicalTeamFeign medicalTeamFeign;
    private final ConsultConfig consultConfig;
    private final NumberSourceService numberSourceService;
    private final PatientUserFeign patientUserFeign;
    private final DoctorFeign doctorFeign;

    private static final String SAVE_CONSULT_LOCK = "consult:lock:";
    private static final String CONSULT_REFUND_STATUS_KAY = "consult:refund:status:";
    private static final String PATIENT_USER_RELATION_SELF = "PATIENT_ACCOUNT_RELATIONSHIP_SELF";


    @Override
    public PageResult<ConsultWebPageVO> pageWeb(QueryConsultPageWebReqVO param) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(ConsultDO::getConsultType, ConsultTypeEnum.MEDICAL_ORDER);
        if (StringUtils.isNotBlank(param.getSearch())) {
            queryWrapper.and(e -> e.like(ConsultDO::getPatientName, param.getSearch()).or().like(ConsultDO::getDoctorName, param.getSearch()));
        }
        if (param.getConsultState() != null) {
            queryWrapper.eq(ConsultDO::getConsultState, param.getConsultState());
        }
        if (param.getConsultType() != null) {
            queryWrapper.eq(ConsultDO::getConsultType, param.getConsultType());
        }
        if (param.getStartTime() != null) {
            queryWrapper.ge(ConsultDO::getPlanVisitStartTime, DateTimeUtils.someTimeStart(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getStartTime()).toLocalDate()));
        }
        if (param.getEndTime() != null) {
            queryWrapper.le(ConsultDO::getPlanVisitEndTime, DateTimeUtils.someTimeEnd(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getEndTime()).toLocalDate()));
        }
        queryWrapper.orderByDesc(ConsultDO::getPlanVisitStartTime);
        Page<ConsultDO> page = consultMapper.selectPage(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }

        return new PageResult<>(CopyPropertiesUtil.normalCopyProperties(page.getRecords(), ConsultWebPageVO.class), page.getTotal());
    }

    @Override
    public List<ConsultWebPageVO> listWeb(QueryConsultPageWebReqVO param) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(ConsultDO::getConsultType, ConsultTypeEnum.MEDICAL_ORDER);
        if (StringUtils.isNotBlank(param.getSearch())) {
            queryWrapper.and(e -> e.like(ConsultDO::getPatientName, param.getSearch()).or().like(ConsultDO::getDoctorName, param.getSearch()));
        }
        if (param.getConsultState() != null) {
            queryWrapper.eq(ConsultDO::getConsultState, param.getConsultState());
        }
        if (param.getConsultType() != null) {
            queryWrapper.eq(ConsultDO::getConsultType, param.getConsultType());
        }
        if (param.getStartTime() != null) {
            queryWrapper.ge(ConsultDO::getPlanVisitStartTime, DateTimeUtils.someTimeStart(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getStartTime()).toLocalDate()));
        }
        if (param.getEndTime() != null) {
            queryWrapper.le(ConsultDO::getPlanVisitEndTime, DateTimeUtils.someTimeEnd(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getEndTime()).toLocalDate()));
        }
        queryWrapper.orderByDesc(ConsultDO::getPlanVisitStartTime);
        List<ConsultDO> list = consultMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return CopyPropertiesUtil.normalCopyProperties(list, ConsultWebPageVO.class);
    }

    @Override
    public PageResult<ConsultAppPageVO> pageApp(QueryConsultPageAppReqVO param) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConsultDO::getPatientId, param.getPatientId());
        queryWrapper.ne(ConsultDO::getConsultType, ConsultTypeEnum.MEDICAL_ORDER);
        if (param.getConsultState() != null) {
            queryWrapper.eq(ConsultDO::getConsultState, param.getConsultState());
        }
        if (param.getConsultType() != null) {
            queryWrapper.eq(ConsultDO::getConsultType, param.getConsultType());
        }
        if (param.getStartTime() != null) {
            queryWrapper.ge(ConsultDO::getPlanVisitStartTime, DateTimeUtils.someTimeStart(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getStartTime()).toLocalDate()));
        }
        if (param.getEndTime() != null) {
            queryWrapper.le(ConsultDO::getPlanVisitEndTime, DateTimeUtils.someTimeEnd(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getEndTime()).toLocalDate()));
        }
        if (param.getSortType().equals(2)) {
            queryWrapper.orderByDesc(ConsultDO::getPlanVisitStartTime);
        } else {
            queryWrapper.orderByAsc(ConsultDO::getPlanVisitStartTime);
        }

        Page<ConsultDO> page = consultMapper.selectPage(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }

        List<ConsultAppPageVO> consultList = CopyPropertiesUtil.normalCopyProperties(page.getRecords(), ConsultAppPageVO.class);

        List<Long> collect = consultList.stream().map(ConsultAppPageVO::getId).collect(Collectors.toList());
        List<ConsultDiseaseHistoryVO> diseaseHistoryVOS = consultDiseaseHistoryService.getByConsultIds(collect);
        Map<Long, List<ConsultDiseaseHistoryVO>> diseaseHistoryMap = diseaseHistoryVOS.stream().collect(Collectors.groupingBy(ConsultDiseaseHistoryVO::getConsultId));

        consultList.forEach(e -> {
            List<ConsultDiseaseHistoryVO> diseaseHistoryVOS1 = diseaseHistoryMap.get(e.getId());
            if (CollectionUtils.isNotEmpty(diseaseHistoryVOS1)) {
                e.setDiseaseHistory(diseaseHistoryVOS1.get(0));
            }
        });
        return new PageResult<>(consultList, page.getTotal());
    }

    @Override
    public PageResult<ConsultDoctorPageVO> listWait(QueryConsultWaitPageReqVO param) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConsultDO::getDoctorId, param.getDoctorId());
        if (CollectionUtils.isNotEmpty(param.getConsultState())) {
            queryWrapper.in(ConsultDO::getConsultState, param.getConsultState());
        } else {
            queryWrapper.eq(ConsultDO::getConsultState, ConsultStateEnum.WAIT);
        }
        queryWrapper.in(ConsultDO::getConsultType, Arrays.asList(ConsultTypeEnum.ONLINE_VIDEO_CLINIC, ConsultTypeEnum.MDT_EXPERT_CONSULTATION));
        queryWrapper.orderByAsc(ConsultDO::getPlanVisitStartTime);
        Page<ConsultDO> page = consultMapper.selectPage(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }

        List<ConsultDoctorPageVO> consultList = CopyPropertiesUtil.normalCopyProperties(page.getRecords(), ConsultDoctorPageVO.class);

        List<Long> collect = consultList.stream().map(ConsultDoctorPageVO::getPatientId).collect(Collectors.toList());
        List<PatientBaseVO> patientBaseVOList = patientFeign.queryPatient(new PatientQueryVO().setIds(collect));
        Map<Long, List<PatientBaseVO>> patientBaseVO = patientBaseVOList.stream().collect(Collectors.groupingBy(PatientBaseVO::getPatientId));

        consultList.forEach(e -> {
            List<PatientBaseVO> patientBaseVOS = patientBaseVO.get(e.getPatientId());
            if (CollectionUtils.isNotEmpty(patientBaseVOS)) {
                PatientBaseVO patient = patientBaseVOS.get(0);
                e.setPatientId(patient.getPatientId());
                e.setPatientName(patient.getName());
                e.setPatientSex(patient.getSex());
                e.setPatientAge(patient.getAge());
                e.setPatientMobile(patient.getContactPhone());
            }
        });

        return new PageResult<>(consultList, page.getTotal());
    }

    @Override
    public List<ConsultDoctorPageVO> listDoctor(QueryConsultListDoctorReqVO param) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.notIn(ConsultDO::getConsultState, ConsultStateEnum.TIME_OUT_CANCEL, ConsultStateEnum.CANCEL);
        queryWrapper.in(ConsultDO::getConsultType, Arrays.asList(ConsultTypeEnum.ONLINE_VIDEO_CLINIC, ConsultTypeEnum.MDT_EXPERT_CONSULTATION));
        queryWrapper.eq(ConsultDO::getDoctorId, param.getDoctorId());

        if (param.getStartTime() != null) {
            queryWrapper.ge(ConsultDO::getPlanVisitStartTime, DateTimeUtils.someTimeStart(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getStartTime()).toLocalDate()));
        }
        if (param.getEndTime() != null) {
            queryWrapper.le(ConsultDO::getPlanVisitEndTime, DateTimeUtils.someTimeEnd(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getEndTime()).toLocalDate()));
        }
        queryWrapper.orderByDesc(ConsultDO::getPlanVisitStartTime);
        List<ConsultDO> consultDOS = consultMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(consultDOS)) {
            return new ArrayList<>();
        }

        List<Long> collect = consultDOS.stream().map(ConsultDO::getPatientId).collect(Collectors.toList());
        List<PatientBaseVO> patientBaseVOList = patientFeign.queryPatient(new PatientQueryVO().setIds(collect));
        Map<Long, List<PatientBaseVO>> patientBaseVO = patientBaseVOList.stream().collect(Collectors.groupingBy(PatientBaseVO::getPatientId));

        return consultDOS.stream().map(e -> {
            ConsultDoctorPageVO consultVO = CopyPropertiesUtil.normalCopyProperties(e, ConsultDoctorPageVO.class);
            List<PatientBaseVO> patientBaseVOS = patientBaseVO.get(e.getPatientId());
            if (CollectionUtils.isNotEmpty(patientBaseVOS)) {
                PatientBaseVO patient = patientBaseVOS.get(0);
                consultVO.setPatientId(patient.getPatientId());
                consultVO.setPatientName(patient.getName());
                consultVO.setPatientSex(patient.getSex());
                consultVO.setPatientAge(patient.getAge());
                consultVO.setPatientMobile(patient.getContactPhone());
            }
            return consultVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ConsultPatientPageVO> listPatient(QueryConsultListPatientReqVO param) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConsultDO::getPatientId, param.getPatientId());
        queryWrapper.in(ConsultDO::getConsultType, Arrays.asList(ConsultTypeEnum.ONLINE_VIDEO_CLINIC, ConsultTypeEnum.MDT_EXPERT_CONSULTATION));
        if (param.getConsultState() != null) {
            queryWrapper.eq(ConsultDO::getConsultState, param.getConsultState());
        }
        if (param.getStartTime() != null) {
            queryWrapper.ge(ConsultDO::getPlanVisitStartTime, DateTimeUtils.someTimeStart(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getStartTime()).toLocalDate()));
        }
        if (param.getEndTime() != null) {
            queryWrapper.le(ConsultDO::getPlanVisitEndTime, DateTimeUtils.someTimeEnd(DateTimeUtils.convertLocalDateTimeOfTimestamp(param.getEndTime()).toLocalDate()));
        }
        if (param.getSortType().equals(2)) {
            queryWrapper.orderByDesc(ConsultDO::getPlanVisitStartTime);
        } else {
            queryWrapper.orderByAsc(ConsultDO::getPlanVisitStartTime);
        }

        List<ConsultDO> consultDOS = consultMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(consultDOS)) {
            return new ArrayList<>();
        }

        return CopyPropertiesUtil.normalCopyProperties(consultDOS, ConsultPatientPageVO.class);
    }

    @Override
    public List<ConsultVO> listBizId(Long bizId) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConsultDO::getBizId, bizId);
        List<ConsultDO> consultDOS = consultMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(consultDOS)) {
            return new ArrayList<>();
        }
        List<ConsultVO> result = new ArrayList<>();
        consultDOS.forEach(e -> {
            ConsultVO consultVO = CopyPropertiesUtil.normalCopyProperties(e, ConsultVO.class);
            if (StringUtils.isNotBlank(e.getVideo())) {
                consultVO.setVideo(JSONObject.parseArray(e.getVideo(), ConsultVideoVO.class));
            }
            if (StringUtils.isNotBlank(e.getChat())) {
                consultVO.setChat(JSONObject.parseArray(e.getChat(), ConsultChatVO.class));
            }
            consultVO.setSaveConsultUserIdFlag(e.getConsultState().equals(ConsultStateEnum.WAIT));
            result.add(consultVO);
        });
        return result;
    }

    @Override
    public List<ConsultVO> listIds(List<Long> idList) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ConsultDO::getId, idList);
        List<ConsultDO> consultDOS = consultMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(consultDOS)) {
            return new ArrayList<>();
        }
        return CopyPropertiesUtil.normalCopyProperties(consultDOS, ConsultVO.class);
    }


    @Override
    public ConsultVO details(Long consultId) {
        ConsultDO consultDO = consultMapper.selectById(consultId);
        if (consultDO == null) {
            return null;
        }

        ConsultVO consultVO = CopyPropertiesUtil.normalCopyProperties(consultDO, ConsultVO.class);
        consultVO.setPatientSnapshot(consultSnapshotService.getByConsultId(consultId));
        consultVO.setComment(consultCommentService.getByConsultId(consultId));
        consultVO.setElectronicsRecords(consultConclusionService.getElectronicsRecords(consultDO.getId()));
        if (StringUtils.isNotBlank(consultDO.getVideo())) {
            consultVO.setVideo(buildVideo(consultDO.getVideo()));
        }
        if (StringUtils.isNotBlank(consultDO.getChat())) {
            consultVO.setChat(JSONObject.parseArray(consultDO.getChat(), ConsultChatVO.class));
        }
        return consultVO;
    }

    private List<ConsultVideoVO> buildVideo(String video) {
        if (StringUtils.isBlank(video)) {
            return new ArrayList<>();
        }
        List<ConsultVideoVO> consultVideoVOS = JSON.parseArray(video, ConsultVideoVO.class);
        consultVideoVOS.forEach(e -> e.setVideoUrl(consultConfig.getVideoBackUrl() + e.getVideoUrl()));
        return consultVideoVOS;
    }

    @Override
    public ConsultVO getByOrderCode(String orderCode) {
        ConsultDO consultDO = consultMapper.getByOrderCode(orderCode);
        if (consultDO == null) {
            return null;
        }

        ConsultVO consultVO = CopyPropertiesUtil.normalCopyProperties(consultDO, ConsultVO.class);
        consultVO.setPatientSnapshot(consultSnapshotService.getByConsultId(consultDO.getId()));
        consultVO.setComment(consultCommentService.getByConsultId(consultDO.getId()));
        consultVO.setElectronicsRecords(consultConclusionService.getElectronicsRecords(consultDO.getId()));
        if (StringUtils.isNotBlank(consultDO.getVideo())) {
            consultVO.setVideo(JSONObject.parseArray(consultDO.getVideo(), ConsultVideoVO.class));
        }
        if (StringUtils.isNotBlank(consultDO.getChat())) {
            consultVO.setChat(JSONObject.parseArray(consultDO.getChat(), ConsultChatVO.class));
        }
        return consultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBySource(CreateConsultSourceReqVO param) {
        String orderCode = param.getOrderCode();
        Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(SAVE_CONSULT_LOCK + orderCode, JSONObject.toJSONString(param), 30, TimeUnit.SECONDS);
        if (Boolean.TRUE.equals(setIfAbsent)) {
            try {
                ConsultVO consultVO = getByOrderCode(orderCode);
                AssertUtils.isNull(consultVO, ShiftErrorCodeConstants.CONSULT_IS_EXIST);

                NumberSourceVO numberSourceDO = numberSourceService.details(param.getNumberSourceId());
                AssertUtils.notNull(numberSourceDO, ShiftErrorCodeConstants.NUMBER_SOURCE_IS_NULL);

                AssertUtils.isTrue(numberSourceDO.getRegisterStatus() == 2, ShiftErrorCodeConstants.NUMBER_SOURCE_STATE_NOT_PAY);

                ShiftDO shiftDO = shiftMapper.selectById(numberSourceDO.getShiftId());
                AssertUtils.notNull(shiftDO, ShiftErrorCodeConstants.SHIFT_IS_NULL);

                ShiftNumberDO shiftNumberDO = shiftNumberDOMapper.selectById(shiftDO.getShiftNumberId());
                AssertUtils.notNull(shiftNumberDO, ShiftErrorCodeConstants.SHIFT_NUMBER_IS_NULL);

                DoctorInfoVO doctorInfoVO = doctorInfoFeign.querySingle(shiftDO.getDoctorId());
                AssertUtils.notNull(doctorInfoVO, ShiftErrorCodeConstants.DOCTOR_IS_NULL);

                UserSimpleVO userSimpleVO = userFeign.getByUserId(Long.valueOf(doctorInfoVO.getUserId()));
                AssertUtils.notNull(userSimpleVO, UserErrorCode.USER_NOT_EXIST);

                HospitalVO hospitalVO = null;
                if (StringUtils.isNotBlank(doctorInfoVO.getHospitalId())) {
                    hospitalVO = hospitalFeign.querySingle(Long.valueOf(doctorInfoVO.getHospitalId()));
                }
                SectionVO sectionVO = null;
                if (StringUtils.isNotBlank(doctorInfoVO.getSectionId())) {
                    sectionVO = sectionFeign.querySingle(Long.valueOf(doctorInfoVO.getSectionId()));
                }

                MedicalTeamSimpleRespVO medicalTeamSimpleRespVO = null;
                if (StringUtils.isNotBlank(doctorInfoVO.getMedicalTeamId())) {
                    medicalTeamSimpleRespVO = medicalTeamFeign.getById(Long.valueOf(doctorInfoVO.getMedicalTeamId()));
                }

                PatientBaseVO patient = patientFeign.getPatientBaseById(numberSourceDO.getPatientId());
                AssertUtils.notNull(patient, PatientErrorCodeConstants.PATIENT_NOT_EXISTS);

                ConsultDO consultDO = saveBuild(param, numberSourceDO, shiftNumberDO, doctorInfoVO, patient, hospitalVO, sectionVO, userSimpleVO, medicalTeamSimpleRespVO);
                consultMapper.insert(consultDO);

                // 处理消息
                execMessage(numberSourceDO, consultDO, Long.valueOf(doctorInfoVO.getUserId()));

                return consultDO.getId();
            } catch (Exception e) {
                log.error("创建问诊......失败", e);
                throw new ServiceException(ShiftErrorCodeConstants.SAVE_CONSULT_ERROR.getCode(), e.getMessage());
            } finally {
                stringRedisTemplate.delete(SAVE_CONSULT_LOCK + orderCode);
            }
        } else {
            log.info("......重复提交!");
            throw new ServiceException(ShiftErrorCodeConstants.SAVE_CONSULT_ERROR.getCode(), "重复提交!");
        }
    }

    private void execMessage(NumberSourceVO numberSourceVO, ConsultDO consultDO, Long userId) {
        String consultId = consultDO.getId().toString();

        // 创建聊天室
        Long liveChat = createLiveChat(consultId, consultDO.getPlanVisitStartTime(), userId, consultDO.getUserId(), LiveChatStatus.OPEN);

        // 发送问诊病史 消息
        messageFeign.sendMsg(new MessageSendReqVO()
                .setChatId(liveChat)
                .setType(MessageType.MEDICAL_HISTORY_FORM)
                .setRequestId(String.valueOf(idService.nextId()))
                .setCurrentUserId(userId)
                .setMsg("")
                .setExtra(new BizMessageBaseVO().setBizName("问诊病史消息").setBizId(consultId)));

        // 提示系统信息
        LocalDateTime visitTime = numberSourceVO.getVisitTime();
        LocalDateTime visitEndTime = numberSourceVO.getVisitEndTime();
        messageFeign.sendMsg(new MessageSendReqVO()
                .setChatId(liveChat)
                .setType(MessageType.SYSTEM_MESSAGE)
                .setRequestId(String.valueOf(idService.nextId()))
                .setCurrentUserId(userId)
                .setMsg(String.format("您已成功购买%s医生的视频问诊，医生将于%s月%s日%s:%s-%s:%s按照预候诊顺序依次接诊，请耐心等待。到达预约时间前，系统会第一时间通知您。",
                        consultDO.getDoctorName(), visitTime.getMonthValue(), visitTime.getDayOfMonth(), fillZero(visitTime.getHour()), fillZero(visitTime.getMinute()), fillZero(visitEndTime.getHour()), fillZero(visitEndTime.getMinute())))
                .setExtra(new BizMessageBaseVO().setBizId(consultId).setBizType("WARN")));
    }


    private Long createLiveChat(String consultId, LocalDateTime time, Long doctorUserId, Long patientUserId, LiveChatStatus videoNow) {
        ChatCreateReqVO commonUserIdVO = new ChatCreateReqVO()
                .setCode(consultId)
                .setLiveStartTime(time)
                .setChatType(ChatType.LIVE);
        commonUserIdVO.setCurrentUserId(doctorUserId);
        Long liveChat = chatFeign.createLiveChat(commonUserIdVO);

        commonUserIdVO = new ChatCreateReqVO()
                .setCode(consultId)
                .setLiveStartTime(time)
                .setChatType(ChatType.LIVE);
        commonUserIdVO.setCurrentUserId(patientUserId);
        chatFeign.createLiveChat(commonUserIdVO);

        // 更新聊天室
        chatFeign.changeLiveChatStatus(consultId, videoNow);
        return liveChat;
    }


    private static String fillZero(int i) {
        return i < 10 ? "0" + i : i + "";
    }

    private ConsultDO saveBuild(CreateConsultSourceReqVO param, NumberSourceVO numberSourceVO, ShiftNumberDO shiftNumberDO, DoctorInfoVO doctorInfoVO, PatientBaseVO patient, HospitalVO hospitalVO, SectionVO sectionVO, UserSimpleVO userSimpleVO, MedicalTeamSimpleRespVO medicalTeamSimpleRespVO) {
        ConsultDO consultDO = new ConsultDO();
        consultDO.setId(idService.nextId());
        consultDO.setChatType(param.getChatType());
        consultDO.setConsultNo(String.valueOf(consultDO.getId()));
        consultDO.setShiftId(numberSourceVO.getShiftId());
        consultDO.setNumberSourceId(numberSourceVO.getId());
        consultDO.setShiftNumberName(shiftNumberDO.getName());
        consultDO.setVisitTime(numberSourceVO.getVisitTime());
        consultDO.setPlanVisitStartTime(numberSourceVO.getVisitTime());
        consultDO.setPlanVisitEndTime(numberSourceVO.getVisitEndTime());
        consultDO.setConsultType(param.getConsultType());
        consultDO.setConsultState(ConsultStateEnum.WAIT);
        consultDO.setOrderCode(param.getOrderCode());
        consultDO.setTotalAmount(param.getTotalAmount());
        consultDO.setPayTime(param.getPayTime());
        consultDO.setPatientId(numberSourceVO.getPatientId());
        consultDO.setUserId(param.getUserId());
        consultDO.setPatientName(patient.getName());
        consultDO.setDoctorId(Long.valueOf(doctorInfoVO.getId()));
        consultDO.setDoctorName(userSimpleVO.getName());
        consultDO.setAvatar(userSimpleVO.getAvatar());
        consultDO.setDoctorSignImg(doctorInfoVO.getPhotoSign());
        consultDO.setDoctorTitle(doctorInfoVO.getTitle());
        if (medicalTeamSimpleRespVO != null) {
            consultDO.setMedicalTeamId(Long.valueOf(medicalTeamSimpleRespVO.getId()));
            consultDO.setMedicalTeamName(medicalTeamSimpleRespVO.getName());
        }
        if (hospitalVO != null) {
            consultDO.setHospitalId(Long.valueOf(hospitalVO.getId()));
            consultDO.setHospitalName(hospitalVO.getName());
        }
        if (sectionVO != null) {
            consultDO.setDepartmentId(Long.valueOf(sectionVO.getId()));
            consultDO.setDepartmentName(sectionVO.getName());
        }
        consultDO.setDeleteFlag(false);
        return consultDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void start(StartConclusionReqVO param) {
        ConsultDO consultDO = consultMapper.selectById(param.getConsultId());
        AssertUtils.isTrue(consultDO != null, ShiftErrorCodeConstants.CONSULT_IS_NULL);

        AssertUtils.isTrue(consultDO.getConsultState().equals(ConsultStateEnum.WAIT), ShiftErrorCodeConstants.CONSULT_STATE_CAN_NOT_WAIT);

        consultDO.setConsultState(ConsultStateEnum.STARTING);
        consultDO.setVisitStartTime(DateTimeUtils.getNow());
        consultDO.setUpdateTime(DateTimeUtils.getNow());
        consultMapper.updateById(consultDO);

        // 更新聊天室
        chatFeign.changeLiveChatStatus(String.valueOf(consultDO.getId()), LiveChatStatus.VIDEO_NOW);

        messageFeign.sendLiveMsg(new LiveMessageSendReqVO()
                .setCode(String.valueOf(consultDO.getId()))
                .setType(MessageType.SYSTEM_MESSAGE)
                .setRequestId(String.valueOf(idService.nextId()))
                .setCurrentUserId(param.getUserId())
                .setMsg("医生已接诊")
                .setExtra(new BizMessageBaseVO().setBizType("TEXT").setBizId(String.valueOf(consultDO.getId()))));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String finish(FinishConclusionReqVO param) {
        ConsultDO consultDO = consultMapper.selectById(param.getConsultId());
        AssertUtils.isTrue(consultDO != null, ShiftErrorCodeConstants.CONSULT_IS_NULL);

        AssertUtils.isTrue(consultDO.getConsultState().equals(ConsultStateEnum.STARTING), ShiftErrorCodeConstants.CONSULT_NOT_STARTING);

        consultDO.setConsultState(ConsultStateEnum.FINISH);
        consultDO.setVisitEndTime(DateTimeUtils.getNow());
        consultDO.setUpdateTime(DateTimeUtils.getNow());
        consultMapper.updateById(consultDO);

        switch (consultDO.getConsultType()) {
            case ONLINE_VIDEO_CLINIC:
            case MDT_EXPERT_CONSULTATION:
                // 保存就诊结论
                consultConclusionService.saveConclusion(CopyPropertiesUtil.normalCopyProperties(param, SaveConclusionReqVO.class));
                // 保存电子病历
                if (param.getElectronicsRecords() != null) {
                    consultConclusionService.saveElectronicsRecords(param.getConsultId(), param.getElectronicsRecords());
                }
                break;
        }

        return consultDO.getOrderCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refund(String orderCode) {
        ConsultDO consultDO = consultMapper.getByOrderCode(orderCode);
        AssertUtils.isTrue(consultDO != null, ShiftErrorCodeConstants.CONSULT_IS_NULL);


        ConsultStateEnum consultStateEnum = ConsultStateEnum.CANCEL;
        String consultState = stringRedisTemplate.opsForValue().get(CONSULT_REFUND_STATUS_KAY + consultDO.getId());
        if (StringUtils.isNotBlank(consultState)) {
            consultStateEnum = ConsultStateEnum.getByType(consultState);
        }
        consultDO.setConsultState(consultStateEnum);
        consultDO.setUpdateTime(DateTimeUtils.getNow());
        consultDO.setDeleteFlag(false);
        consultMapper.updateById(consultDO);

        // 关闭聊天室;
        chatFeign.changeLiveChatStatus(String.valueOf(consultDO.getId()), LiveChatStatus.CLOSE);
    }

    @Override
    public RoomCreateRespVO startVideo(Long userId, Long consultId) {
        ConsultDO consultDO = consultMapper.selectById(consultId);
        AssertUtils.isTrue(consultDO != null, ShiftErrorCodeConstants.CONSULT_IS_NULL);

        // 医生是否正在打视频
        RoomCreateRespVO queryDoctorRoom = livekitFeign.getRoomInfoByUserId(userId);
        if (queryDoctorRoom != null) {
            if (queryDoctorRoom.getPatientUserId().equals(consultDO.getUserId())) {
                return queryDoctorRoom;
            } else {
                throw new ServiceException(ShiftErrorCodeConstants.CONSULT_VIDEO_DOCTOR_STARTING.getCode(), String.format(ShiftErrorCodeConstants.CONSULT_VIDEO_DOCTOR_STARTING.getMsg(), queryDoctorRoom.getPatientUserName()));
            }
        }

        // 患者是否正在打视频
        RoomCreateRespVO queryPatientRoom = livekitFeign.getRoomInfoByUserId(consultDO.getUserId());
        if (queryPatientRoom != null) {
            if (!queryPatientRoom.getDoctorId().equals(userId)) {
                throw new ServiceException(ShiftErrorCodeConstants.CONSULT_VIDEO_PATIENT_STARTING);
            } else {
                return queryPatientRoom;
            }
        }
        UserAuthorityVO doctorUser = userFeign.getById(userId);
        UserAuthorityVO patientUser = userFeign.getById(consultDO.getUserId());

        // 没有房间，创建一个视频房间
        return livekitFeign.createRoom(new RoomCreateReqVO()
                .setBizId(consultDO.getId().toString())
                .setPatientUserId(patientUser.getId())
                .setPatientUserName(consultDO.getPatientName())
                .setPatientAvatar(patientUser.getAvatar())
                .setDoctorId(doctorUser.getId())
                .setDoctorName(consultDO.getDoctorName())
                .setDoctorAvatar(doctorUser.getAvatar())
                .setChatType(consultDO.getChatType()));
    }

    @Override
    public String saveVideo(SaveVideoReqVO param) {
        ConsultDO consultDO = consultMapper.selectById(param.getConsultId());
        AssertUtils.isTrue(consultDO != null, ShiftErrorCodeConstants.CONSULT_IS_NULL);

        List<ConsultVideoVO> consultVideoVOS;
        if (StringUtils.isBlank(consultDO.getVideo())) {
            consultVideoVOS = new ArrayList<>();
        } else {
            consultVideoVOS = JSONArray.parseArray(consultDO.getVideo(), ConsultVideoVO.class);
            List<String> collect = consultVideoVOS.stream().map(ConsultVideoVO::getVideoUrl).collect(Collectors.toList());
            if (collect.contains(param.getVideoUrl())) { // 重复文件，直接过滤掉
                return null;
            }
        }
        // 视频
        ConsultVideoVO consultVideoVO = new ConsultVideoVO();
        consultVideoVO.setVideoName(param.getRoomName());
        consultVideoVO.setVideoUrl(param.getVideoUrl());
        consultVideoVOS.add(consultVideoVO);
        consultDO.setVideo(JSONObject.toJSONString(consultVideoVOS));
        consultDO.setUpdateTime(DateTimeUtils.getNow());
        consultMapper.updateById(consultDO);
        return param.getVideoUrl();
    }

    @Override
    public ConsultVideoVO closeVideo(Long consultId, Long userId) {
        // 查询房间
        String consultIdString = consultId.toString();

        RoomCreateRespVO roomInfo = livekitFeign.getRoomInfoByBizId(consultIdString);
        AssertUtils.notNull(roomInfo, LivekitErrorCode.ROOM_IS_NULL);

        // 查询房间的人数，大于2个人才保存视频
        Integer roomUserCount = livekitFeign.getRoomUserCount(consultIdString);

        // 删除房间
        livekitFeign.deleteRoom(consultId.toString());

        ConsultVideoVO consultVideoVO = new ConsultVideoVO();
        consultVideoVO.setConsultId(consultId);
        consultVideoVO.setSaveVideoFlag(roomUserCount > 1);
        consultVideoVO.setVideoUrl(roomInfo.getVideoUrl());
        consultVideoVO.setVideoName(roomInfo.getRoomName());
        return consultVideoVO;
    }

    @Override
    public Integer queryConsultCountByDoctorId(Long doctorId) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConsultDO::getDoctorId, doctorId);
        queryWrapper.eq(ConsultDO::getDeleteFlag, false);
        return Math.toIntExact(consultMapper.selectCount(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConsultVO createByDirectly(CreateByDirectlyReqVO param) {
        DoctorInfoVO doctorInfoVO = doctorInfoFeign.querySingle(param.getDoctorId());
        AssertUtils.notNull(doctorInfoVO, ShiftErrorCodeConstants.DOCTOR_IS_NULL);

        UserSimpleVO userSimpleVO = userFeign.getByUserId(Long.valueOf(doctorInfoVO.getUserId()));
        AssertUtils.notNull(userSimpleVO, UserErrorCode.USER_NOT_EXIST);

        Long userId = param.getPatientUserId();
        if (userId == null) {
            List<PatientUserVO> familyByPatientId = patientUserFeign.getFamilyByPatientId(param.getPatientId());
            Optional<Long> first = familyByPatientId.stream().filter(e -> e.getRelation().equals(PATIENT_USER_RELATION_SELF)).map(PatientUserVO::getUserId).findFirst();
            userId = first.orElse(null);
        }
        HospitalVO hospitalVO = null;
        if (StringUtils.isNotBlank(doctorInfoVO.getHospitalId())) {
            hospitalVO = hospitalFeign.querySingle(Long.valueOf(doctorInfoVO.getHospitalId()));
        }
        SectionVO sectionVO = null;
        if (StringUtils.isNotBlank(doctorInfoVO.getSectionId())) {
            sectionVO = sectionFeign.querySingle(Long.valueOf(doctorInfoVO.getSectionId()));
        }

        MedicalTeamSimpleRespVO medicalTeamSimpleRespVO = null;
        if (StringUtils.isNotBlank(doctorInfoVO.getMedicalTeamId())) {
            medicalTeamSimpleRespVO = medicalTeamFeign.getById(Long.valueOf(doctorInfoVO.getMedicalTeamId()));
        }

        PatientBaseVO patient = patientFeign.getPatientBaseById(param.getPatientId());
        AssertUtils.notNull(patient, PatientErrorCodeConstants.PATIENT_NOT_EXISTS);

        ConsultDO consultDO = saveBuildDirectly(doctorInfoVO, patient, hospitalVO, sectionVO, userSimpleVO, userId, medicalTeamSimpleRespVO, param);
        consultMapper.insert(consultDO);

        if (!consultDO.getConsultType().equals(ConsultTypeEnum.MEDICAL_ORDER)) {
            // 创建聊天室
            createLiveChat(consultDO.getId().toString(), consultDO.getPlanVisitStartTime(), consultDO.getDoctorId(), consultDO.getUserId(), param.getConsultState().equals(ConsultStateEnum.STARTING) ? LiveChatStatus.VIDEO_NOW : LiveChatStatus.OPEN);
        }

        return CopyPropertiesUtil.normalCopyProperties(consultDO, ConsultVO.class);
    }


    private ConsultDO saveBuildDirectly(DoctorInfoVO doctorInfoVO, PatientBaseVO patient, HospitalVO hospitalVO, SectionVO sectionVO, UserSimpleVO doctorUser, Long patientUserId, MedicalTeamSimpleRespVO medicalTeamSimpleRespVO, CreateByDirectlyReqVO reqVO) {
        ConsultDO consultDO = new ConsultDO();
        consultDO.setId(idService.nextId());
        consultDO.setChatType(reqVO.getChatType());
        consultDO.setConsultNo(String.valueOf(consultDO.getId()));
        consultDO.setVisitTime(reqVO.getPlanVisitStartTime());
        consultDO.setPlanVisitStartTime(reqVO.getPlanVisitStartTime());
        consultDO.setPlanVisitEndTime(reqVO.getPlanVisitEndTime());
        consultDO.setShiftNumberName("");
        consultDO.setConsultType(reqVO.getConsultType());
        consultDO.setBizId(reqVO.getBizId());
        if (reqVO.getConsultType().equals(ConsultTypeEnum.MEDICAL_ORDER)) {
            consultDO.setVisitStartTime(reqVO.getPlanVisitStartTime());
            consultDO.setVisitEndTime(reqVO.getPlanVisitEndTime());
        } else { // 其他情况，都是进行中，比如随访、心理 点击开启视频
            consultDO.setVisitStartTime(reqVO.getPlanVisitStartTime());
        }
        consultDO.setConsultState(reqVO.getConsultState());
        consultDO.setTotalAmount(0);
        consultDO.setUserId(patientUserId);
        consultDO.setPatientId(patient.getPatientId());
        consultDO.setPatientName(patient.getName());
        consultDO.setDoctorId(Long.valueOf(doctorInfoVO.getId()));
        consultDO.setDoctorName(doctorUser.getName());
        consultDO.setAvatar(doctorUser.getAvatar());
        consultDO.setDoctorSignImg(doctorInfoVO.getPhotoSign());
        consultDO.setDoctorTitle(doctorInfoVO.getTitle());
        if (medicalTeamSimpleRespVO != null) {
            consultDO.setMedicalTeamId(Long.valueOf(medicalTeamSimpleRespVO.getId()));
            consultDO.setMedicalTeamName(medicalTeamSimpleRespVO.getName());
        }
        if (hospitalVO != null) {
            consultDO.setHospitalId(Long.valueOf(hospitalVO.getId()));
            consultDO.setHospitalName(hospitalVO.getName());
        }
        if (sectionVO != null) {
            consultDO.setDepartmentId(Long.valueOf(sectionVO.getId()));
            consultDO.setDepartmentName(sectionVO.getName());
        }
        consultDO.setRemark(reqVO.getRemark());
        consultDO.setDeleteFlag(false);
        return consultDO;
    }

    @Override
    public void checkRefund(String orderCode) {
        ConsultVO consultVO = getByOrderCode(orderCode);
        AssertUtils.notNull(consultVO, ShiftErrorCodeConstants.CONSULT_IS_NULL);

        AssertUtils.isTrue(consultVO.getConsultState().equals(ConsultStateEnum.WAIT), ShiftErrorCodeConstants.CONSULT_CAN_NOT_REFUND);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderNotification(ConsultNotificationVO param) {
        OrderStatus orderStatus = param.getOrderStatus();
        switch (orderStatus) {
            case CANCELED: // 取消订单
                NumberSourceVO cancelNumberSource = numberSourceService.details(param.getNumberSourceId());
                AssertUtils.notNull(cancelNumberSource, ShiftErrorCodeConstants.NUMBER_SOURCE_IS_NULL);
                AssertUtils.isTrue(!cancelNumberSource.getRegisterStatus().equals(NumberSourceStatus.WAIT.getNumber()), ShiftErrorCodeConstants.CONSULT_STATUS_IS_SUCCESS);
                AssertUtils.isTrue(cancelNumberSource.getRegisterStatus().equals(NumberSourceStatus.WAIT_PAYMENT.getNumber()), ShiftErrorCodeConstants.NUMBER_SOURCE_STATE_NOT_WAT_PAY);
                // 修改号源状态
                numberSourceService.saveNumberResourceState(param.getNumberSourceId(), NumberSourceStatus.WAIT);
                break;
            case ORDERING: // 支付成功
                NumberSourceVO payNumberSource = numberSourceService.details(param.getNumberSourceId());
                AssertUtils.notNull(payNumberSource, ShiftErrorCodeConstants.NUMBER_SOURCE_IS_NULL);
                AssertUtils.isTrue(!payNumberSource.getRegisterStatus().equals(NumberSourceStatus.PAYMENT.getNumber()), ShiftErrorCodeConstants.CONSULT_STATUS_IS_SUCCESS);
                AssertUtils.isTrue(payNumberSource.getRegisterStatus().equals(NumberSourceStatus.WAIT_PAYMENT.getNumber()), ShiftErrorCodeConstants.NUMBER_SOURCE_STATE_NOT_WAT_PAY);
                // 修改号源状态
                numberSourceService.saveNumberResourceState(param.getNumberSourceId(), NumberSourceStatus.PAYMENT);
                // 创建问诊
                CreateConsultSourceReqVO reqVO = new CreateConsultSourceReqVO();
                reqVO.setUserId(param.getUserId());
                reqVO.setNumberSourceId(param.getNumberSourceId());
                reqVO.setConsultType(ConsultTypeEnum.ONLINE_VIDEO_CLINIC);
                reqVO.setOrderCode(param.getOrderCode());
                reqVO.setPayTime(param.getPayTime());
                reqVO.setTotalAmount(param.getPayAmount());
                reqVO.setChatType(ConsultChatTypeEnum.VIDEO_CHAT);
                this.createBySource(reqVO);
                break;
            case REFUND_COMPLETED: // 退款
                ConsultVO consultVO = getByOrderCode(param.getOrderCode());
                AssertUtils.notNull(consultVO, ShiftErrorCodeConstants.CONSULT_IS_NULL);
                AssertUtils.isTrue(!consultVO.getConsultState().equals(ConsultStateEnum.TIME_OUT_CANCEL), ShiftErrorCodeConstants.CONSULT_STATUS_IS_SUCCESS);
                AssertUtils.isTrue(!consultVO.getConsultState().equals(ConsultStateEnum.CANCEL), ShiftErrorCodeConstants.CONSULT_STATUS_IS_SUCCESS);
                AssertUtils.isTrue(consultVO.getConsultState().equals(ConsultStateEnum.WAIT), ShiftErrorCodeConstants.CONSULT_CAN_NOT_REFUND);
                // 关闭号源
                numberSourceService.saveNumberResourceState(consultVO.getNumberSourceId(), NumberSourceStatus.WAIT);
                // 关闭问诊
                refund(param.getOrderCode());
                break;
            default:
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST);
        }
    }

    @Override
    public void updateConsultHisData(UpdateConsultHisDataVO param) {
        ConsultDO consultDO = consultMapper.selectById(param.getId());
        AssertUtils.notNull(consultDO, ShiftErrorCodeConstants.CONSULT_IS_NULL);

        consultDO.setHisVisitId(param.getHisVisitId());
        consultDO.setHisVisitNo(param.getHisVisitNo());
        consultDO.setHisPatientId(param.getHisPatientId());
        consultMapper.updateById(consultDO);
    }


    @Override
    public void saveChat(String requestUrl, Long consultId, String videoUrl, String videoName) {
        ConsultDO consultDO = consultMapper.selectById(consultId);
        AssertUtils.notNull(consultDO, ShiftErrorCodeConstants.CONSULT_IS_NULL);

        List<ConsultChatVO> consultChatVOS;
        if (StringUtils.isBlank(consultDO.getChat())) {
            consultChatVOS = new ArrayList<>();
        } else {
            consultChatVOS = JSONObject.parseArray(consultDO.getChat(), ConsultChatVO.class);
        }
        String aiResult = sendAI(requestUrl, consultDO, consultConfig.getVideoBackUrl() + videoUrl);
        ConsultChatVO consultChatVO = new ConsultChatVO();
        consultChatVO.setChatStr(aiResult);
        consultChatVO.setChatName(videoName);
        consultChatVOS.add(consultChatVO);
        consultMapper.updateChat(consultId, JSONObject.toJSONString(consultChatVO));
    }


    @Override
    public void updateConsult(UpdateConsultVO param) {
        ConsultDO consultDO = consultMapper.selectById(param.getConsultId());
        AssertUtils.notNull(consultDO, ShiftErrorCodeConstants.CONSULT_IS_NULL);
        if (param.getPlanVisitStartTime() != null) {
            consultDO.setPlanVisitStartTime(param.getPlanVisitStartTime());
        }

        if (param.getPatientUserId() != null) {
            AssertUtils.isTrue(consultDO.getConsultState().equals(ConsultStateEnum.WAIT), ShiftErrorCodeConstants.CONSULTATION_HAS_STARTED_AND_THE_PATIENT_CANNOT_BE_MODIFIED);
            consultDO.setUserId(param.getPatientUserId());
            // 加入聊天室
            this.addChatRoom(consultDO, param.getPatientUserId());
        }
        if (param.getConsultState() != null) {
            consultDO.setConsultState(param.getConsultState());
            LiveChatStatus liveChatStatus = null;
            switch (param.getConsultState()) {
                case WAIT:
                    liveChatStatus = LiveChatStatus.OPEN;
                    break;
                case STARTING:
                    liveChatStatus = LiveChatStatus.VIDEO_NOW;
                    consultDO.setVisitStartTime(DateTimeUtils.getNow());
                    break;
                case FINISH:
                case CANCEL:
                case TIME_OUT_CANCEL:
                    liveChatStatus = LiveChatStatus.CLOSE;
                    consultDO.setVisitEndTime(DateTimeUtils.getNow());
                    break;
            }
            if (liveChatStatus != null) {
                // 更新聊天室
                chatFeign.changeLiveChatStatus(String.valueOf(consultDO.getId()), liveChatStatus);
            }
        }
        if (param.getPlanVisitEndTime() != null) {
            consultDO.setPlanVisitEndTime(param.getPlanVisitEndTime());
        }
        if (param.getChatType() != null) {
            consultDO.setChatType(param.getChatType());
        }
        if (param.getConsultType() != null) {
            consultDO.setConsultType(param.getConsultType());
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            consultDO.setRemark(param.getRemark());
        }
        if (param.getDoctorId() != null) {
            DoctorVO doctorVO = doctorFeign.getById(param.getDoctorId());
            consultDO.setDoctorId(param.getDoctorId());
            consultDO.setDoctorName(doctorVO.getName());
            consultDO.setDoctorTitle(doctorVO.getTitle());
            consultDO.setDoctorSignImg(doctorVO.getPhotoSign());
            consultDO.setAvatar(doctorVO.getAvatar());
            consultDO.setDepartmentId(doctorVO.getSectionId());
            consultDO.setDepartmentName(doctorVO.getSectionName());
            consultDO.setHospitalId(doctorVO.getHospitalId());
            consultDO.setHospitalName(doctorVO.getHospitalName());
            // 加入聊天室
            this.addChatRoom(consultDO, param.getDoctorId());
        }
        consultMapper.updateById(consultDO);
    }


    private void addChatRoom(ConsultDO consultDO, Long userId) {
        String consultId = consultDO.getId().toString();
        ChatVO liveChat = chatFeign.getLiveChat(consultId);
        if (liveChat == null) {
            ChatCreateReqVO commonUserIdVO = new ChatCreateReqVO()
                    .setCode(consultId)
                    .setLiveStartTime(consultDO.getPlanVisitStartTime())
                    .setChatType(ChatType.LIVE);
            commonUserIdVO.setCurrentUserId(userId);
            chatFeign.createLiveChat(commonUserIdVO);
            return;
        }
        userChatFeign.addChatUser(liveChat.getId(), userId);
    }


    private String sendAI(String requestUrl, ConsultDO consultDO, String videoUrl) {
        // 调用AI获取视频聊天文本
        Map<String, Object> params = new HashMap<>();
        params.put("request_id", consultDO.getId());
        params.put("video_url", videoUrl);
        log.info("调用AI获取视频聊天文本 ： 发送http请求-----> request:{}", JSON.toJSON(params));
        String str = HutHttpUtils.postFrom(requestUrl, params);
        log.info("调用AI获取视频聊天文本 ： 发送http请求-----> response:{}", str);
        if (StringUtils.isBlank(str)) {
            log.warn("调用AI获取视频聊天文本 : 请求没有详情");
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), "请求没有详情");
        }
        JSONObject jsonObject = JSONObject.parseObject(str);
        Integer code = jsonObject.getInteger("code");
        if (code != 0) {
            log.warn("调用AI获取视频聊天文本 : 失败：{}", jsonObject.getString("msg"));
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), jsonObject.getString("msg"));
        }
        return jsonObject.getString("result");
    }
}
