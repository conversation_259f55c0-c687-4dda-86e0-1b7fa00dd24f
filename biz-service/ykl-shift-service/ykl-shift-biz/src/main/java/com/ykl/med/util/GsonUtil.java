package com.ykl.med.util;

import com.google.common.reflect.TypeToken;
import com.google.gson.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/17 16:53
 */
public abstract class GsonUtil {
    private static final Gson GSON = new GsonBuilder()
            .disableHtmlEscaping()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .setObjectToNumberStrategy(ToNumberPolicy.LAZILY_PARSED_NUMBER)
            .setLongSerializationPolicy(LongSerializationPolicy.STRING).serializeNulls()
            .create();

    /**
     * 将对象转成json格式
     */
    public static String jsonString(Object object) {
        return GSON.toJson(object);
    }

    /**
     * 将json转成特定的cls的对象
     */
    public static <T> T jsonToBean(String gsonString, Class<T> cls) {
        return GSON.fromJson(gsonString, cls);
    }

    /**
     * json字符串转成list
     */
    public static <T> List<T> jsonToList(String json, Class<T> cls) {
        ArrayList<T> mList = new ArrayList<T>();

        JsonArray array = new JsonParser().parse(json).getAsJsonArray();
        for (final JsonElement elem : array) {
            mList.add(GSON.fromJson(elem, cls));
        }
        return mList;
    }

    /**
     * json字符串转成list中有map的
     */
    public static <T> List<Map<String, T>> jsonToListMaps(String gsonString) {
        return GSON.fromJson(gsonString,
                new TypeToken<List<Map<String, T>>>() {
                }.getType());
    }

    /**
     * json字符串转成map的
     */
    public static <T> Map<String, T> jsonToMaps(String gsonString) {
        return GSON.fromJson(gsonString, new TypeToken<Map<String, T>>() {
        }.getType());
    }
}
