package com.ykl.med.service;

import com.ykl.med.shift.vo.req.ConsultConclusionDiagnosisReqVO;
import com.ykl.med.shift.vo.resp.ConsultConclusionDiagnosisVO;
import com.ykl.med.shift.vo.req.SaveConclusionReqVO;
import com.ykl.med.shift.vo.resp.ConsultConclusionVO;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
public interface ConsultConclusionService {
    ConsultConclusionVO getByConsultId(Long consultId);

    void saveConclusion(SaveConclusionReqVO param);

    void saveElectronicsRecords(Long consultId, ConsultConclusionDiagnosisReqVO electronicsRecords);

    ConsultConclusionDiagnosisVO getElectronicsRecords(Long consultId);
}
