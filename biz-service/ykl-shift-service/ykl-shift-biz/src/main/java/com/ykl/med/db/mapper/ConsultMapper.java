package com.ykl.med.db.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ykl.med.db.entity.ConsultDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 默认表 数据访问层
 *
 * <AUTHOR>
 * 2024-10-9 16:59:04
 */
@Mapper
public interface ConsultMapper extends BaseMapper<ConsultDO> {

    default ConsultDO getByOrderCode(String orderCode) {
        LambdaQueryWrapper<ConsultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConsultDO::getOrderCode, orderCode);
        queryWrapper.eq(ConsultDO::getDeleteFlag, false);
        return this.selectOne(queryWrapper);
    }

    @Update("update t_consult set chat=#{jsonString} WHERE id=#{consultId}")
    void updateChat(@Param("consultId") Long consultId, @Param("jsonString") String jsonString);
}