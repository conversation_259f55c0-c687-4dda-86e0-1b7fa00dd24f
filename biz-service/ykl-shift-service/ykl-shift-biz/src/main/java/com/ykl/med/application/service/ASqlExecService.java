package com.ykl.med.application.service;

import com.ykl.med.application.entity.PageRecord;
import com.ykl.med.application.entity.ResponseModel;
import com.ykl.med.application.entity.ValueObject;
import com.ykl.med.config.SimpleException;

import java.text.ParseException;
import java.util.List;

public interface ASqlExecService {
    /**
     * 批量执行SQL
     * */
    ResponseModel sqlExec(ValueObject valueObject, PageRecord page) throws ParseException, SimpleException, NoSuchFieldException, IllegalAccessException;

    /**
     * 批量执行SQL: 传入sql数组(支持事务)
     * */
    ResponseModel sqlListExec(List<String> stringList, PageRecord page);

    /**
     * 执行单条sql
     * */
    ResponseModel sqlExecMapper(String sqlStr, PageRecord page);
}
