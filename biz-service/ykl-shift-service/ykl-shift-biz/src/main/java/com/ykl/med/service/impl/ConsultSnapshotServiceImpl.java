package com.ykl.med.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.db.entity.ConsultSnapshotDO;
import com.ykl.med.db.mapper.ConsultSnapshotMapper;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.service.ConsultSnapshotService;
import com.ykl.med.shift.vo.req.CreateConsultSnapshotVO;
import com.ykl.med.shift.vo.resp.ConsultSnapshotVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Slf4j
@Service
public class ConsultSnapshotServiceImpl implements ConsultSnapshotService {

    @Resource
    private ConsultSnapshotMapper consultSnapshotMapper;

    @Override
    public ConsultSnapshotVO getByConsultId(Long consultId) {
        LambdaQueryWrapper<ConsultSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConsultSnapshotDO::getConsultId, consultId);
        queryWrapper.eq(ConsultSnapshotDO::getDeleteFlag, false);
        ConsultSnapshotDO consultSnapshotDO = consultSnapshotMapper.selectOne(queryWrapper);
        if (consultSnapshotDO == null) {
            return null;
        }

        return CopyPropertiesUtil.normalCopyProperties(consultSnapshotDO, ConsultSnapshotVO.class);
    }

    @Override
    public void save(CreateConsultSnapshotVO param) {
        ConsultSnapshotVO consultSnapshotVO = getByConsultId(param.getConsultId());
        if (consultSnapshotVO != null) {
            return;
        }

        ConsultSnapshotDO consultSnapshotDO = CopyPropertiesUtil.normalCopyProperties(param, ConsultSnapshotDO.class);
        consultSnapshotDO.setDeleteFlag(false);
        consultSnapshotMapper.insert(consultSnapshotDO);
    }
}
