package com.ykl.med.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ykl.med.base.service.IdServiceImpl;
import com.ykl.med.framework.common.util.DateTimeUtils;
import com.ykl.med.db.entity.ConsultConclusionDO;
import com.ykl.med.db.entity.ConsultConclusionDiagnosisDO;
import com.ykl.med.db.mapper.ConsultConclusionDiagnosisMapper;
import com.ykl.med.db.mapper.ConsultConclusionMapper;
import com.ykl.med.framework.common.exception.ServiceException;
import com.ykl.med.framework.common.util.CopyPropertiesUtil;
import com.ykl.med.framework.common.vo.ConsultDiagnosisVO;
import com.ykl.med.masterdata.api.DiseaseFeign;
import com.ykl.med.masterdata.vo.resp.DiseaseVO;
import com.ykl.med.service.ConsultConclusionService;
import com.ykl.med.shift.constans.ShiftErrorCodeConstants;
import com.ykl.med.shift.vo.req.ConsultConclusionDiagnosisReqVO;
import com.ykl.med.shift.vo.resp.ConsultConclusionDiagnosisVO;
import com.ykl.med.shift.vo.req.SaveConclusionReqVO;
import com.ykl.med.shift.vo.resp.ConsultConclusionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Slf4j
@Service
public class ConsultConclusionServiceImpl implements ConsultConclusionService {

    @Resource
    private ConsultConclusionMapper consultConclusionMapper;
    @Resource
    private DiseaseFeign diseaseFeign;
    @Resource
    private ConsultConclusionDiagnosisMapper consultConclusionDiagnosisMapper;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private IdServiceImpl idService;

    private static final String SAVE_CONCLUSION_LOCK = "consult:conclusion:lock:";

    @Override
    public ConsultConclusionVO getByConsultId(Long consultId) {
        LambdaQueryWrapper<ConsultConclusionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConsultConclusionDO::getConsultId, consultId);
        queryWrapper.eq(ConsultConclusionDO::getDeleteFlag, false);
        ConsultConclusionDO consultConclusionDO = consultConclusionMapper.selectOne(queryWrapper);
        if (consultConclusionDO == null) {
            return null;
        }

        ConsultConclusionVO consultConclusionVO = CopyPropertiesUtil.normalCopyProperties(consultConclusionDO, ConsultConclusionVO.class);
        consultConclusionVO.setDiagnosis(JSONArray.parseArray(consultConclusionDO.getStandardDiagnosis(), ConsultDiagnosisVO.class));
        return consultConclusionVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConclusion(SaveConclusionReqVO param) {
        Long consultId = param.getConsultId();
        Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(SAVE_CONCLUSION_LOCK + consultId, JSONObject.toJSONString(param), 30, TimeUnit.SECONDS);
        if (Boolean.TRUE.equals(setIfAbsent)) {
            try {
                ConsultConclusionVO consultConclusionVO = getByConsultId(consultId);
                ConsultConclusionDO save = CopyPropertiesUtil.normalCopyProperties(param, ConsultConclusionDO.class);
                save.setDeleteFlag(false);
                save.setStandardDiagnosis(JSONObject.toJSONString(buildDiagnosis(param.getDiagnosisIds())));
                if (consultConclusionVO != null) {
                    save.setId(consultConclusionVO.getId());
                    save.setUpdateTime(DateTimeUtils.getNow());
                    consultConclusionMapper.updateById(save);
                } else {
                    save.setId(idService.nextId());

                    consultConclusionMapper.insert(save);
                }
            } catch (Exception e) {
                log.error("创建问诊结论......失败", e);
                throw new ServiceException(ShiftErrorCodeConstants.SAVE_CONCLUSION_ERROR);
            } finally {
                redisTemplate.delete(SAVE_CONCLUSION_LOCK + consultId);
            }
        } else {
            log.info("......重复提交!");
        }
    }

    @Override
    public void saveElectronicsRecords(Long consultId, ConsultConclusionDiagnosisReqVO param) {
        ConsultConclusionDiagnosisDO build = new ConsultConclusionDiagnosisDO();
        build.setId(idService.nextId());
        build.setDeleteFlag(false);
        build.setConsultId(consultId);
        build.setDiagnosis(JSONObject.toJSONString(buildDiagnosis(param.getDiagnosisIds())));
        build.setOnsetTime(DateTimeUtils.convertMillisecondToDateTime(param.getOnsetTime()).toLocalDate());
        build.setAppeal(param.getAppeal());
        build.setPastMedicalHistory(param.getPastMedicalHistory());
        build.setAllergyHistory(param.getAllergyHistory());
        build.setMedicalHistoryDesc(param.getMedicalHistoryDesc());
        consultConclusionDiagnosisMapper.insert(build);
    }

    @Override
    public ConsultConclusionDiagnosisVO getElectronicsRecords(Long consultId) {
        LambdaQueryWrapper<ConsultConclusionDiagnosisDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ConsultConclusionDiagnosisDO::getConsultId, consultId);
        queryWrapper.eq(ConsultConclusionDiagnosisDO::getDeleteFlag, false);
        ConsultConclusionDiagnosisDO consultConclusionDiagnosisDO = consultConclusionDiagnosisMapper.selectOne(queryWrapper);
        if (consultConclusionDiagnosisDO == null) {
            return null;
        }

        ConsultConclusionDiagnosisVO result = CopyPropertiesUtil.normalCopyProperties(consultConclusionDiagnosisDO, ConsultConclusionDiagnosisVO.class);
        result.setDiagnosis(JSONObject.parseArray(consultConclusionDiagnosisDO.getDiagnosis(), ConsultDiagnosisVO.class));
        return result;
    }

    private List<ConsultDiagnosisVO> buildDiagnosis(List<Long> diagnosisIds) {
        List<DiseaseVO> diseaseVOS = diseaseFeign.queryByIds(diagnosisIds);
        if (CollectionUtils.isEmpty(diseaseVOS)) {
            return new ArrayList<>();
        }

        return diseaseVOS.stream().map(e -> {
            ConsultDiagnosisVO consultDiagnosisVO = new ConsultDiagnosisVO();
            consultDiagnosisVO.setId(e.getId());
            consultDiagnosisVO.setSystemName(e.getSystemName());
            consultDiagnosisVO.setStandardName(e.getStandardName());
            consultDiagnosisVO.setIcdCode(e.getIcdCode());
            return consultDiagnosisVO;
        }).collect(Collectors.toList());
    }


}
